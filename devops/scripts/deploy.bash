#!/usr/bin/env bash

set -o errexit -o nounset -o pipefail

cluster=plasmic-cluster
region=us-west-2
aws_account_id=************

dir="$(dirname "$0")"

deploy-tag() {
  local tag=$1
  bash -x $dir/cluster.bash apply-yamls;
  bash -x $dir/cluster.bash deploy-all $tag
  bash -x $dir/aws.bash deploy-site $tag
}

rollback() {
  bash -x $dir/cluster.bash rollback
  local tag=`kubectl get deploy codegen-deployment -o jsonpath="{..image}" | cut -d':' -f2`
  bash -x $dir/aws.bash deploy-site $tag
}

"$@"
