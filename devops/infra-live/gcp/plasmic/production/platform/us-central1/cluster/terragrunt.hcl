locals {
  org         = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  env         = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  project     = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  region_vars = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  project_id  = local.project.locals.project_id
  region      = local.region_vars.locals.region
}

terraform {
  source = "tfr:///terraform-google-modules/kubernetes-engine/google//?version=35.0.0"
}

include {
  path = find_in_parent_folders()
}

dependency "networking" {
  config_path = "../../../../common/production-network/global/vpc"
}

dependency "networking_project" {
  config_path = "../../../../common/production-network/project"
}

dependency "project" {
  config_path = "../../project"
}

dependency "gke_node_sa" {
  config_path = "../../global/iam/service-accounts/gke-node"
}

inputs = {
  name                                 = "${dependency.project.outputs.project_name}-${local.region}"
  project_id                           = local.project_id
  region                               = local.region
  zones                                = ["${local.region}-a"]
  network_project_id                   = dependency.networking_project.outputs.project_id
  network                              = dependency.networking.outputs.network_name
  subnetwork                           = dependency.networking.outputs.subnets_names[1]
  ip_range_pods                        = dependency.networking.outputs.subnets_secondary_ranges[1][0].range_name
  additional_ip_range_pods             = [dependency.networking.outputs.subnets_secondary_ranges[1][2].range_name]
  ip_range_services                    = dependency.networking.outputs.subnets_secondary_ranges[1][1].range_name
  gce_pd_csi_driver                    = true
  disable_default_snat                 = false
  enable_cost_allocation               = true
  monitoring_enable_managed_prometheus = true
  logging_enabled_componentes          = ["SYSTEM_COMPONENTS", "APISERVER", "SCHEDULER", "CONTROLLER_MANAGER", "WORKLOADS"]
  monitoring_enabled_components        = ["SYSTEM_COMPONENTS", "APISERVER", "SCHEDULER", "CONTROLLER_MANAGER"]
  cluster_autoscaling = {
    "enabled" : true,
    "auto_repair" : true,
    "auto_upgrade" : true,
    "autoscaling_profile" : "OPTIMIZE_UTILIZATION",
    "disk_size" : 80,
    "disk_type" : "pd-ssd",
    "enable_integrity_monitoring" : true,
    "enable_secure_boot" : true,
    "image_type" : "COS_CONTAINERD",
    "max_cpu_cores" : 1536,
    "max_memory_gb" : 4096,
    "min_cpu_cores" : 0,
    "min_memory_gb" : 0,
    "gpu_resources" : []
  }
  node_pools = [
    {
      name = "default-node-pool"
    },
    {
      name               = "loader-tests-playwright"
      initial_node_count = 0
      max_pods_per_node  = 20
      machine_type       = "e2-custom-24-40960"
      node_locations     = "${local.region}-a"
      min_count          = 0
      max_count          = 5
      disk_size_gb       = 80
      disk_type          = "pd-ssd"
      auto_repair        = true
      auto_upgrade       = true
      service_account    = dependency.gke_node_sa.outputs.service_account.email
    },
    {
      name               = "loader-tests-jest"
      initial_node_count = 0
      max_pods_per_node  = 20
      machine_type       = "e2-standard-16"
      node_locations     = "${local.region}-a"
      min_count          = 0
      max_count          = 5
      disk_size_gb       = 80
      disk_type          = "pd-ssd"
      auto_repair        = true
      auto_upgrade       = true
      service_account    = dependency.gke_node_sa.outputs.service_account.email
    },
    {
      name               = "studio-tests-root"
      initial_node_count = 0
      max_pods_per_node  = 20
      machine_type       = "e2-standard-8"
      node_locations     = "${local.region}-a"
      min_count          = 0
      max_count          = 5
      disk_size_gb       = 80
      disk_type          = "pd-ssd"
      auto_repair        = true
      auto_upgrade       = true
      service_account    = dependency.gke_node_sa.outputs.service_account.email
    },
    {
      name               = "cypress"
      initial_node_count = 0
      max_pods_per_node  = 20
      machine_type       = "c3d-highmem-4"
      node_locations     = "${local.region}-a"
      min_count          = 0
      max_count          = 40
      disk_size_gb       = 80
      disk_type          = "hyperdisk-balanced"
      auto_repair        = true
      auto_upgrade       = true
      service_account    = dependency.gke_node_sa.outputs.service_account.email
    },
    {
      name               = "migrate-bundles"
      initial_node_count = 0
      max_pods_per_node  = 20
      machine_type       = "c3d-highmem-8"
      node_locations     = "${local.region}-a"
      min_count          = 0
      max_count          = 24
      disk_size_gb       = 80
      disk_type          = "pd-ssd"
      auto_repair        = true
      auto_upgrade       = true
      service_account    = dependency.gke_node_sa.outputs.service_account.email
    },
    {
      name               = "tests-storybook"
      initial_node_count = 0
      max_pods_per_node  = 20
      machine_type       = "e2-standard-8"
      node_locations     = "${local.region}-a"
      min_count          = 0
      max_count          = 5
      disk_size_gb       = 80
      disk_type          = "pd-ssd"
      auto_repair        = true
      auto_upgrade       = true
      service_account    = dependency.gke_node_sa.outputs.service_account.email
    }
  ]
  node_pools_labels = {
    all = {
    },
    loader-tests-playwright = {
      loader-tests-playwright = "true"
    },
    loader-tests-jest = {
      loader-tests-jest = "true"
    },
    studio-tests-root = {
      studio-tests-root = "true"
    },
    cypress = {
      cypress = "true"
    },
    migrate-bundles = {
      migrate-bundles = "true"
    },
    tests-storybook = {
      tests-storybook = "true"
    }
  }
  node_pools_taints = {
    loader-tests-playwright = [
      {
        key    = "loader-tests-playwright"
        value  = "true"
        effect = "NO_EXECUTE"
      }
    ]
    loader-tests-jest = [
      {
        key    = "loader-tests-jest"
        value  = "true"
        effect = "NO_EXECUTE"
      }
    ]
    studio-tests-root = [
      {
        key    = "studio-tests-root"
        value  = "true"
        effect = "NO_EXECUTE"
      }
    ]
    cypress = [
      {
        key    = "cypress"
        value  = "true"
        effect = "NO_EXECUTE"
      }
    ]
    migrate-bundles = [
      {
        key    = "migrate-bundles"
        value  = "true"
        effect = "NO_EXECUTE"
      }
    ]
    tests-storybook = [
      {
        key    = "tests-storybook"
        value  = "true"
        effect = "NO_EXECUTE"
      }
    ]
  }
}
