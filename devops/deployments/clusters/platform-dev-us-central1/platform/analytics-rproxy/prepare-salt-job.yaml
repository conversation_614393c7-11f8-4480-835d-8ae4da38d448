apiVersion: batch/v1
kind: CronJob
metadata:
  name: prepare-salt-job
spec:
  schedule: "55 * * * *"
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      activeDeadlineSeconds: 300
      template:
        spec:
          containers:
          - name: prepare-salt-container
            image: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/analytics-rproxy:latest
            command: ["node", "/app/dist/entry-prepare-salt.js"]
            env: 
            - name: NATS_SERVERS
              value: nats://nats:4222
            - name: POSTHOG_KEY
              value: phc_eaI1hFsPRIZkmwrXaSGRNDh4H9J3xdh1j9rgNy27NgP
          restartPolicy: OnFailure
