[["a76RKRQpJHMAbDDNBWmUVs", {"root": "62885001", "map": {"3691501": {"rows": [{"__ref": "3691502"}], "__type": "ArenaFrameGrid"}, "3691502": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "4499401": {"type": {"__ref": "4499403"}, "variable": {"__ref": "4499402"}, "uuid": "C0RBiNByoGb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4499402": {"name": "submitsForm", "uuid": "IKRpxmrn8fL", "__type": "Var"}, "4499403": {"name": "bool", "__type": "BoolType"}, "4499404": {"param": {"__ref": "4499401"}, "expr": {"__ref": "4499405"}, "__type": "Arg"}, "4499405": {"code": "true", "fallback": null, "__type": "CustomCode"}, "4499406": {"param": {"__ref": "4499401"}, "expr": {"__ref": "4499407"}, "__type": "Arg"}, "4499407": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14363001": {"uuid": "g_kAJdKAcN", "name": "hostless-plasmic-head", "params": [{"__ref": "14363003"}, {"__ref": "14363004"}, {"__ref": "14363005"}, {"__ref": "14363006"}], "states": [], "tplTree": {"__ref": "14363007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "14363008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "14363009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "14363002": {"uuid": "sFp5XzlGf-", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "14363010"}, {"__ref": "14363011"}, {"__ref": "14363012"}, {"__ref": "14363013"}, {"__ref": "14363014"}], "states": [], "tplTree": {"__ref": "14363015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "14363016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "14363017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "14363003": {"type": {"__ref": "14363019"}, "variable": {"__ref": "14363018"}, "uuid": "950Z_ur5Lk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363004": {"type": {"__ref": "14363021"}, "variable": {"__ref": "14363020"}, "uuid": "BohKJs6_fT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363005": {"type": {"__ref": "14363023"}, "variable": {"__ref": "14363022"}, "uuid": "Jr4XEl1q4y", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363006": {"type": {"__ref": "14363025"}, "variable": {"__ref": "14363024"}, "uuid": "CqrWUFW243", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2qPJxfusu4", "parent": null, "locked": null, "vsettings": [{"__ref": "14363026"}], "__type": "TplTag"}, "14363008": {"uuid": "2aL1a7vQo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "14363010": {"type": {"__ref": "14363028"}, "variable": {"__ref": "14363027"}, "uuid": "ZWqKK8Ogu7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363011": {"type": {"__ref": "14363030"}, "variable": {"__ref": "14363029"}, "uuid": "zl30J74PQPM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363012": {"type": {"__ref": "14363032"}, "tplSlot": {"__ref": "14363037"}, "variable": {"__ref": "14363031"}, "uuid": "p5FVs5Gsukd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "14363013": {"type": {"__ref": "14363034"}, "variable": {"__ref": "14363033"}, "uuid": "3ZTdKL9TCdP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363014": {"type": {"__ref": "14363036"}, "variable": {"__ref": "14363035"}, "uuid": "5h0oEbI4coe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363015": {"tag": "div", "name": null, "children": [{"__ref": "14363037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "vRYK6dpo7g", "parent": null, "locked": null, "vsettings": [{"__ref": "14363038"}], "__type": "TplTag"}, "14363016": {"uuid": "MuWeo6EUKH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Source Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "14363018": {"name": "title", "uuid": "B20rzmZ_36", "__type": "Var"}, "14363019": {"name": "text", "__type": "Text"}, "14363020": {"name": "description", "uuid": "7aNYtFdCcV", "__type": "Var"}, "14363021": {"name": "text", "__type": "Text"}, "14363022": {"name": "image", "uuid": "RsNgFtyxJy", "__type": "Var"}, "14363023": {"name": "img", "__type": "Img"}, "14363024": {"name": "canonical", "uuid": "SmwOmk6lEl", "__type": "Var"}, "14363025": {"name": "text", "__type": "Text"}, "14363026": {"variants": [{"__ref": "14363008"}], "args": [], "attrs": {}, "rs": {"__ref": "14363039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363027": {"name": "dataOp", "uuid": "wrdCC_EPK-", "__type": "Var"}, "14363028": {"name": "any", "__type": "AnyType"}, "14363029": {"name": "name", "uuid": "WDte5ZupDsQ", "__type": "Var"}, "14363030": {"name": "text", "__type": "Text"}, "14363031": {"name": "children", "uuid": "qdRu96qdNf7", "__type": "Var"}, "14363032": {"name": "renderFunc", "params": [{"__ref": "14363040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "14363033": {"name": "pageSize", "uuid": "h-kVdoe4Eaf", "__type": "Var"}, "14363034": {"name": "num", "__type": "<PERSON><PERSON>"}, "14363035": {"name": "pageIndex", "uuid": "yq3U8MgnK8W", "__type": "Var"}, "14363036": {"name": "num", "__type": "<PERSON><PERSON>"}, "14363037": {"param": {"__ref": "14363012"}, "defaultContents": [], "uuid": "Q2SI3PiZsJa", "parent": {"__ref": "14363015"}, "locked": null, "vsettings": [{"__ref": "14363041"}], "__type": "TplSlot"}, "14363038": {"variants": [{"__ref": "14363016"}], "args": [], "attrs": {}, "rs": {"__ref": "14363042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "14363040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "14363045"}, "__type": "ArgType"}, "14363041": {"variants": [{"__ref": "14363016"}], "args": [], "attrs": {}, "rs": {"__ref": "14363046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "14363045": {"name": "any", "__type": "AnyType"}, "14363046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363049": {"uuid": "iFqCt8OVI9", "name": "Counter", "params": [{"__ref": "14363999"}, {"__ref": "14364004"}, {"__ref": "14364013"}, {"__ref": "14364035"}, {"__ref": "14364254"}, {"__ref": "14364338"}], "states": [{"__ref": "14363998"}, {"__ref": "14364033"}], "tplTree": {"__ref": "14363051"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "14363052"}], "variantGroups": [{"__ref": "14364034"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "14363050": {"component": {"__ref": "14363049"}, "matrix": {"__ref": "14363053"}, "customMatrix": {"__ref": "14363054"}, "__type": "ComponentArena"}, "14363051": {"tag": "div", "name": null, "children": [{"__ref": "14363071"}, {"__ref": "14363921"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T-ytTXQ42", "parent": null, "locked": null, "vsettings": [{"__ref": "14363055"}, {"__ref": "14364070"}], "__type": "TplTag"}, "14363052": {"uuid": "3-xWChNvZ_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363053": {"rows": [{"__ref": "14363056"}, {"__ref": "14364036"}], "__type": "ArenaFrameGrid"}, "14363054": {"rows": [{"__ref": "14363057"}], "__type": "ArenaFrameGrid"}, "14363055": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {"data-testid": {"__ref": "60094030"}}, "rs": {"__ref": "14363058"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363056": {"cols": [{"__ref": "14363059"}], "rowKey": null, "__type": "ArenaFrameRow"}, "14363057": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "14363058": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "wrap", "height": "wrap", "justify-content": "flex-start", "align-items": "center", "flex-row-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "14363059": {"frame": {"__ref": "14363067"}, "cellKey": {"__ref": "14363052"}, "__type": "ArenaFrameCell"}, "14363067": {"uuid": "GvnV4Clmvu", "width": 613, "height": 340, "container": {"__ref": "14363068"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363052"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363068": {"name": null, "component": {"__ref": "14363049"}, "uuid": "aOxL_VqrLX", "parent": null, "locked": null, "vsettings": [{"__ref": "14363069"}], "__type": "TplComponent"}, "14363069": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363070"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363070": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363071": {"tag": "div", "name": null, "children": [{"__ref": "14364010"}, {"__ref": "14364052"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T9c8AlsJF", "parent": {"__ref": "14363051"}, "locked": null, "vsettings": [{"__ref": "14363072"}, {"__ref": "14364050"}], "__type": "TplTag"}, "14363072": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "14363073"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363073": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "14363079": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "6WIvWY_UE", "parent": {"__ref": "14363921"}, "locked": null, "vsettings": [{"__ref": "14363080"}], "__type": "TplTag"}, "14363080": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {"data-testid": {"__ref": "60094043"}}, "rs": {"__ref": "14363081"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364025"}, "columnsConfig": null, "__type": "VariantSetting"}, "14363081": {"values": {"width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "14363087": {"uuid": "LZ8ods6C6lA", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "14363088": {"uuid": "HrhUyOqMxRB", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "14363089": {"uuid": "XNAQ6ZNM_98", "name": "<PERSON><PERSON>", "params": [{"__ref": "14363092"}, {"__ref": "14363093"}, {"__ref": "14363094"}, {"__ref": "14363095"}, {"__ref": "14363096"}, {"__ref": "14363097"}, {"__ref": "14363098"}, {"__ref": "14363099"}, {"__ref": "14363100"}, {"__ref": "14363101"}, {"__ref": "4499401"}, {"__ref": "62885232"}, {"__ref": "62885237"}, {"__ref": "62885242"}, {"__ref": "62885247"}, {"__ref": "62885252"}, {"__ref": "62885257"}], "states": [{"__ref": "14363102"}, {"__ref": "14363103"}, {"__ref": "14363104"}, {"__ref": "14363105"}, {"__ref": "14363106"}, {"__ref": "14363107"}], "tplTree": {"__ref": "14363108"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "14363109"}, {"__ref": "14363110"}, {"__ref": "14363111"}, {"__ref": "14363112"}, {"__ref": "14363113"}], "variantGroups": [{"__ref": "14363114"}, {"__ref": "14363115"}, {"__ref": "14363116"}, {"__ref": "14363117"}, {"__ref": "14363118"}, {"__ref": "14363119"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "14363120"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "14363090": {"component": {"__ref": "14363089"}, "matrix": {"__ref": "14363121"}, "customMatrix": {"__ref": "14363122"}, "__type": "ComponentArena"}, "14363091": {"name": null, "component": {"__ref": "14363089"}, "uuid": "Caa29Hzr9uI", "parent": {"__ref": "14363921"}, "locked": null, "vsettings": [{"__ref": "14363123"}], "__type": "TplComponent"}, "14363092": {"type": {"__ref": "14363125"}, "tplSlot": {"__ref": "14363245"}, "variable": {"__ref": "14363124"}, "uuid": "Jsb74z7Cmw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "14363093": {"type": {"__ref": "14363127"}, "state": {"__ref": "14363102"}, "variable": {"__ref": "14363126"}, "uuid": "NJTCOTiyFb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14363094": {"type": {"__ref": "14363129"}, "state": {"__ref": "14363103"}, "variable": {"__ref": "14363128"}, "uuid": "HGISBSjvlj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14363095": {"type": {"__ref": "14363131"}, "tplSlot": {"__ref": "14363240"}, "variable": {"__ref": "14363130"}, "uuid": "qP0o6w3NL4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "14363096": {"type": {"__ref": "14363133"}, "tplSlot": {"__ref": "14363251"}, "variable": {"__ref": "14363132"}, "uuid": "60PIosystG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "14363097": {"type": {"__ref": "14363135"}, "state": {"__ref": "14363104"}, "variable": {"__ref": "14363134"}, "uuid": "I4IogLI6vl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14363098": {"type": {"__ref": "14363137"}, "variable": {"__ref": "14363136"}, "uuid": "uWZNMG3HgZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14363099": {"type": {"__ref": "14363139"}, "state": {"__ref": "14363107"}, "variable": {"__ref": "14363138"}, "uuid": "mxV522AIti", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14363100": {"type": {"__ref": "14363141"}, "state": {"__ref": "14363106"}, "variable": {"__ref": "14363140"}, "uuid": "3G3u_HA9U33", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14363101": {"type": {"__ref": "14363143"}, "state": {"__ref": "14363105"}, "variable": {"__ref": "14363142"}, "uuid": "InwgVRxyZol", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14363102": {"variantGroup": {"__ref": "14363114"}, "param": {"__ref": "14363093"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "62885232"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "14363103": {"variantGroup": {"__ref": "14363115"}, "param": {"__ref": "14363094"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "62885237"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "14363104": {"variantGroup": {"__ref": "14363116"}, "param": {"__ref": "14363097"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "62885242"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "14363105": {"variantGroup": {"__ref": "14363117"}, "param": {"__ref": "14363101"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "62885247"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "14363106": {"variantGroup": {"__ref": "14363118"}, "param": {"__ref": "14363100"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "62885252"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "14363107": {"variantGroup": {"__ref": "14363119"}, "param": {"__ref": "14363099"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "62885257"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "14363108": {"tag": "button", "name": null, "children": [{"__ref": "14363144"}, {"__ref": "14363145"}, {"__ref": "14363146"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "LIgISvuUUVF", "parent": null, "locked": null, "vsettings": [{"__ref": "14363147"}, {"__ref": "14363148"}, {"__ref": "14363149"}, {"__ref": "14363150"}, {"__ref": "14363151"}, {"__ref": "14363152"}, {"__ref": "14363153"}, {"__ref": "14363154"}, {"__ref": "14363155"}, {"__ref": "14363156"}, {"__ref": "14363157"}, {"__ref": "14363158"}, {"__ref": "14363159"}, {"__ref": "14363160"}, {"__ref": "14363161"}, {"__ref": "14363162"}, {"__ref": "14363163"}, {"__ref": "14363164"}, {"__ref": "14363165"}, {"__ref": "14363166"}, {"__ref": "14363167"}, {"__ref": "14363168"}, {"__ref": "14363169"}, {"__ref": "14363170"}, {"__ref": "14363171"}, {"__ref": "14363172"}, {"__ref": "14363173"}, {"__ref": "14363174"}, {"__ref": "14363175"}, {"__ref": "14363176"}, {"__ref": "14363177"}, {"__ref": "14363178"}, {"__ref": "14363179"}, {"__ref": "14363180"}, {"__ref": "14363181"}, {"__ref": "14363182"}, {"__ref": "14363183"}, {"__ref": "14363184"}, {"__ref": "14363185"}, {"__ref": "14363186"}, {"__ref": "14363187"}, {"__ref": "14363188"}, {"__ref": "14363189"}, {"__ref": "14363190"}, {"__ref": "14363191"}, {"__ref": "14363192"}, {"__ref": "14363193"}, {"__ref": "14363194"}, {"__ref": "14363195"}, {"__ref": "14363196"}, {"__ref": "14363197"}, {"__ref": "14363198"}, {"__ref": "14363199"}, {"__ref": "14363200"}, {"__ref": "14363201"}, {"__ref": "14363202"}, {"__ref": "14363203"}, {"__ref": "14363204"}, {"__ref": "14363205"}, {"__ref": "14363206"}], "__type": "TplTag"}, "14363109": {"uuid": "Y3WLaVVRvbi", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363110": {"uuid": "cS6qD5ku0Qh", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363111": {"uuid": "yvWMMiwA2R_", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363112": {"uuid": "7DL-e9lna8l", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363113": {"uuid": "A1rz3WiPfCc", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363114": {"type": "component", "param": {"__ref": "14363093"}, "linkedState": {"__ref": "14363102"}, "uuid": "VubQvTueU58", "variants": [{"__ref": "14363207"}], "multi": false, "__type": "ComponentVariantGroup"}, "14363115": {"type": "component", "param": {"__ref": "14363094"}, "linkedState": {"__ref": "14363103"}, "uuid": "6LptbFAg-7n", "variants": [{"__ref": "14363208"}], "multi": false, "__type": "ComponentVariantGroup"}, "14363116": {"type": "component", "param": {"__ref": "14363097"}, "linkedState": {"__ref": "14363104"}, "uuid": "JJHeS3LNt5A", "variants": [{"__ref": "14363209"}], "multi": false, "__type": "ComponentVariantGroup"}, "14363117": {"type": "component", "param": {"__ref": "14363101"}, "linkedState": {"__ref": "14363105"}, "uuid": "fsvwDhmKWrf", "variants": [{"__ref": "14363210"}, {"__ref": "14363211"}, {"__ref": "14363212"}], "multi": false, "__type": "ComponentVariantGroup"}, "14363118": {"type": "component", "param": {"__ref": "14363100"}, "linkedState": {"__ref": "14363106"}, "uuid": "5X0kORxaRrm", "variants": [{"__ref": "14363213"}, {"__ref": "14363214"}], "multi": false, "__type": "ComponentVariantGroup"}, "14363119": {"type": "component", "param": {"__ref": "14363099"}, "linkedState": {"__ref": "14363107"}, "uuid": "JbGOtViEP10", "variants": [{"__ref": "14363215"}, {"__ref": "14363216"}, {"__ref": "14363217"}, {"__ref": "14363218"}, {"__ref": "14363219"}, {"__ref": "14363220"}, {"__ref": "14363221"}, {"__ref": "14363222"}, {"__ref": "14363223"}, {"__ref": "14363224"}, {"__ref": "14363225"}, {"__ref": "14363226"}, {"__ref": "14363227"}], "multi": false, "__type": "ComponentVariantGroup"}, "14363120": {"type": "button", "__type": "PlumeInfo"}, "14363121": {"rows": [{"__ref": "14363228"}, {"__ref": "14363229"}, {"__ref": "14363230"}, {"__ref": "14363231"}, {"__ref": "14363232"}, {"__ref": "14363233"}, {"__ref": "14363234"}], "__type": "ArenaFrameGrid"}, "14363122": {"rows": [{"__ref": "14363235"}], "__type": "ArenaFrameGrid"}, "14363123": {"variants": [{"__ref": "14363052"}], "args": [{"__ref": "14363236"}, {"__ref": "14363237"}, {"__ref": "14363238"}, {"__ref": "14364028"}, {"__ref": "4499406"}], "attrs": {"onClick": {"__ref": "14364073"}}, "rs": {"__ref": "14363239"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363124": {"name": "children", "uuid": "MjF4xMUVj", "__type": "Var"}, "14363125": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "14363126": {"name": "Show Start Icon", "uuid": "efSp_zd2yp", "__type": "Var"}, "14363127": {"name": "any", "__type": "AnyType"}, "14363128": {"name": "Show End Icon", "uuid": "MaeEZPNTT1", "__type": "Var"}, "14363129": {"name": "any", "__type": "AnyType"}, "14363130": {"name": "start icon", "uuid": "7zuUaI_6NU", "__type": "Var"}, "14363131": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "14363132": {"name": "end icon", "uuid": "1dlQXpGFzj", "__type": "Var"}, "14363133": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "14363134": {"name": "Is Disabled", "uuid": "KuyPLFKOiO", "__type": "Var"}, "14363135": {"name": "any", "__type": "AnyType"}, "14363136": {"name": "link", "uuid": "2uqUGA9vYU", "__type": "Var"}, "14363137": {"name": "href", "__type": "HrefType"}, "14363138": {"name": "Color", "uuid": "_n1q7OGTs8", "__type": "Var"}, "14363139": {"name": "any", "__type": "AnyType"}, "14363140": {"name": "Size", "uuid": "aYCgYuKd0m2", "__type": "Var"}, "14363141": {"name": "any", "__type": "AnyType"}, "14363142": {"name": "<PERSON><PERSON><PERSON>", "uuid": "s6rg3bdTXLb", "__type": "Var"}, "14363143": {"name": "any", "__type": "AnyType"}, "14363144": {"tag": "div", "name": "start icon container", "children": [{"__ref": "14363240"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "de5ri-GTFMP", "parent": {"__ref": "14363108"}, "locked": null, "vsettings": [{"__ref": "14363241"}, {"__ref": "14363242"}, {"__ref": "14363243"}, {"__ref": "14363244"}], "__type": "TplTag"}, "14363145": {"tag": "div", "name": "content container", "children": [{"__ref": "14363245"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "GaSSa_bhNVr", "parent": {"__ref": "14363108"}, "locked": null, "vsettings": [{"__ref": "14363246"}, {"__ref": "14363247"}, {"__ref": "14363248"}, {"__ref": "14363249"}, {"__ref": "14363250"}], "__type": "TplTag"}, "14363146": {"tag": "div", "name": "end icon container", "children": [{"__ref": "14363251"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "07hTHPu0_2q", "parent": {"__ref": "14363108"}, "locked": null, "vsettings": [{"__ref": "14363252"}, {"__ref": "14363253"}, {"__ref": "14363254"}, {"__ref": "14363255"}], "__type": "TplTag"}, "14363147": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363256"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363148": {"variants": [{"__ref": "14363110"}], "args": [], "attrs": {}, "rs": {"__ref": "14363257"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363149": {"variants": [{"__ref": "14363111"}], "args": [], "attrs": {}, "rs": {"__ref": "14363258"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363150": {"variants": [{"__ref": "14363209"}], "args": [], "attrs": {}, "rs": {"__ref": "14363259"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363151": {"variants": [{"__ref": "14363208"}], "args": [], "attrs": {}, "rs": {"__ref": "14363260"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363152": {"variants": [{"__ref": "14363207"}], "args": [], "attrs": {}, "rs": {"__ref": "14363261"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363153": {"variants": [{"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363262"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363154": {"variants": [{"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363263"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363155": {"variants": [{"__ref": "14363221"}], "args": [], "attrs": {}, "rs": {"__ref": "14363264"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363156": {"variants": [{"__ref": "14363221"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363265"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363157": {"variants": [{"__ref": "14363221"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363266"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363158": {"variants": [{"__ref": "14363222"}], "args": [], "attrs": {}, "rs": {"__ref": "14363267"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363159": {"variants": [{"__ref": "14363222"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363268"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363160": {"variants": [{"__ref": "14363222"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363269"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363161": {"variants": [{"__ref": "14363217"}], "args": [], "attrs": {}, "rs": {"__ref": "14363270"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363162": {"variants": [{"__ref": "14363217"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363271"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363163": {"variants": [{"__ref": "14363217"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363272"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363164": {"variants": [{"__ref": "14363218"}], "args": [], "attrs": {}, "rs": {"__ref": "14363273"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363165": {"variants": [{"__ref": "14363218"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363274"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363166": {"variants": [{"__ref": "14363218"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363275"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363167": {"variants": [{"__ref": "14363223"}], "args": [], "attrs": {}, "rs": {"__ref": "14363276"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363168": {"variants": [{"__ref": "14363223"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363277"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363169": {"variants": [{"__ref": "14363223"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363278"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363170": {"variants": [{"__ref": "14363224"}], "args": [], "attrs": {}, "rs": {"__ref": "14363279"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363171": {"variants": [{"__ref": "14363224"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363280"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363172": {"variants": [{"__ref": "14363224"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363281"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363173": {"variants": [{"__ref": "14363225"}], "args": [], "attrs": {}, "rs": {"__ref": "14363282"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363174": {"variants": [{"__ref": "14363225"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363283"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363175": {"variants": [{"__ref": "14363225"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363284"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363176": {"variants": [{"__ref": "14363215"}], "args": [], "attrs": {}, "rs": {"__ref": "14363285"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363177": {"variants": [{"__ref": "14363215"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363286"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363178": {"variants": [{"__ref": "14363215"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363287"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363179": {"variants": [{"__ref": "14363216"}], "args": [], "attrs": {}, "rs": {"__ref": "14363288"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363180": {"variants": [{"__ref": "14363216"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363289"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363181": {"variants": [{"__ref": "14363216"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363290"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363182": {"variants": [{"__ref": "14363219"}], "args": [], "attrs": {}, "rs": {"__ref": "14363291"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363183": {"variants": [{"__ref": "14363219"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363292"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363184": {"variants": [{"__ref": "14363219"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363293"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363185": {"variants": [{"__ref": "14363213"}], "args": [], "attrs": {}, "rs": {"__ref": "14363294"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363186": {"variants": [{"__ref": "14363213"}, {"__ref": "14363207"}], "args": [], "attrs": {}, "rs": {"__ref": "14363295"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363187": {"variants": [{"__ref": "14363213"}, {"__ref": "14363207"}, {"__ref": "14363208"}], "args": [], "attrs": {}, "rs": {"__ref": "14363296"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363188": {"variants": [{"__ref": "14363213"}, {"__ref": "14363208"}], "args": [], "attrs": {}, "rs": {"__ref": "14363297"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363189": {"variants": [{"__ref": "14363210"}], "args": [], "attrs": {}, "rs": {"__ref": "14363298"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363190": {"variants": [{"__ref": "14363210"}, {"__ref": "14363207"}], "args": [], "attrs": {}, "rs": {"__ref": "14363299"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363191": {"variants": [{"__ref": "14363208"}, {"__ref": "14363210"}], "args": [], "attrs": {}, "rs": {"__ref": "14363300"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363192": {"variants": [{"__ref": "14363213"}, {"__ref": "14363210"}], "args": [], "attrs": {}, "rs": {"__ref": "14363301"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363193": {"variants": [{"__ref": "14363226"}], "args": [], "attrs": {}, "rs": {"__ref": "14363302"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363194": {"variants": [{"__ref": "14363226"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363303"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363195": {"variants": [{"__ref": "14363226"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363304"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363196": {"variants": [{"__ref": "14363211"}], "args": [], "attrs": {}, "rs": {"__ref": "14363305"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363197": {"variants": [{"__ref": "14363211"}, {"__ref": "14363213"}], "args": [], "attrs": {}, "rs": {"__ref": "14363306"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363198": {"variants": [{"__ref": "14363227"}], "args": [], "attrs": {}, "rs": {"__ref": "14363307"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363199": {"variants": [{"__ref": "14363227"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363308"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363200": {"variants": [{"__ref": "14363227"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363309"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363201": {"variants": [{"__ref": "14363227"}, {"__ref": "14363214"}], "args": [], "attrs": {}, "rs": {"__ref": "14363310"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363202": {"variants": [{"__ref": "14363214"}], "args": [], "attrs": {}, "rs": {"__ref": "14363311"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363203": {"variants": [{"__ref": "14363220"}], "args": [], "attrs": {}, "rs": {"__ref": "14363312"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363204": {"variants": [{"__ref": "14363220"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363313"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363205": {"variants": [{"__ref": "14363220"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363314"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363206": {"variants": [{"__ref": "14363212"}], "args": [], "attrs": {}, "rs": {"__ref": "14363315"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363207": {"uuid": "pS1jX0GI9kk", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363114"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363208": {"uuid": "6CVeF3rurwN", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363115"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363209": {"uuid": "-tX4j1chl2V", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363116"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363210": {"uuid": "wL6UtqSmacX", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363117"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363211": {"uuid": "Q9X3lePQDRy", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363117"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363212": {"uuid": "_wPBcDiLpu3", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363117"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363213": {"uuid": "An4NM-wkANb", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363214": {"uuid": "9O6ylRS5l1u", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363118"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363215": {"uuid": "Af4O15Larz_", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363216": {"uuid": "eNM8QiDWMhQ", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363217": {"uuid": "HuKtuEo87Za", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363218": {"uuid": "ZUYSXvaOwSe", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363219": {"uuid": "-eZhlg1ocPn", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363220": {"uuid": "keTOljSqJe6", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363221": {"uuid": "8IpR79rSVB4", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363222": {"uuid": "UoyC8klfREk", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363223": {"uuid": "0ImLi0JM8r0", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363224": {"uuid": "UBrGp9GNK68", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363225": {"uuid": "KImrtaOhAJE", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363226": {"uuid": "bgk5BL_EWYJ", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363227": {"uuid": "iT1ienFzPhR", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14363119"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14363228": {"cols": [{"__ref": "14363316"}, {"__ref": "14363317"}, {"__ref": "14363318"}, {"__ref": "14363319"}, {"__ref": "14363320"}], "rowKey": null, "__type": "ArenaFrameRow"}, "14363229": {"cols": [{"__ref": "14363321"}], "rowKey": {"__ref": "14363114"}, "__type": "ArenaFrameRow"}, "14363230": {"cols": [{"__ref": "14363322"}], "rowKey": {"__ref": "14363115"}, "__type": "ArenaFrameRow"}, "14363231": {"cols": [{"__ref": "14363323"}], "rowKey": {"__ref": "14363116"}, "__type": "ArenaFrameRow"}, "14363232": {"cols": [{"__ref": "14363324"}, {"__ref": "14363325"}, {"__ref": "14363326"}], "rowKey": {"__ref": "14363117"}, "__type": "ArenaFrameRow"}, "14363233": {"cols": [{"__ref": "14363327"}, {"__ref": "14363328"}], "rowKey": {"__ref": "14363118"}, "__type": "ArenaFrameRow"}, "14363234": {"cols": [{"__ref": "14363329"}, {"__ref": "14363330"}, {"__ref": "14363331"}, {"__ref": "14363332"}, {"__ref": "14363333"}, {"__ref": "14363334"}, {"__ref": "14363335"}, {"__ref": "14363336"}, {"__ref": "14363337"}, {"__ref": "14363338"}, {"__ref": "14363339"}, {"__ref": "14363340"}, {"__ref": "14363341"}], "rowKey": {"__ref": "14363119"}, "__type": "ArenaFrameRow"}, "14363235": {"cols": [{"__ref": "14363342"}, {"__ref": "14363343"}, {"__ref": "14363344"}, {"__ref": "14363345"}, {"__ref": "14363346"}, {"__ref": "14363347"}, {"__ref": "14363348"}, {"__ref": "14363349"}, {"__ref": "14363350"}, {"__ref": "14363351"}, {"__ref": "14363352"}, {"__ref": "14363353"}, {"__ref": "14363354"}, {"__ref": "14363355"}, {"__ref": "14363356"}, {"__ref": "14363357"}, {"__ref": "14363358"}, {"__ref": "14363359"}, {"__ref": "14363360"}, {"__ref": "14363361"}, {"__ref": "14363362"}, {"__ref": "14363363"}, {"__ref": "14363364"}, {"__ref": "14363365"}, {"__ref": "14363366"}, {"__ref": "14363367"}, {"__ref": "14363368"}, {"__ref": "14363369"}, {"__ref": "14363370"}], "rowKey": null, "__type": "ArenaFrameRow"}, "14363236": {"param": {"__ref": "14363095"}, "expr": {"__ref": "14363922"}, "__type": "Arg"}, "14363237": {"param": {"__ref": "14363092"}, "expr": {"__ref": "14363997"}, "__type": "Arg"}, "14363238": {"param": {"__ref": "14363096"}, "expr": {"__ref": "14363924"}, "__type": "Arg"}, "14363239": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "14363240": {"param": {"__ref": "14363095"}, "defaultContents": [{"__ref": "14363376"}], "uuid": "qgN_9wQwNMY", "parent": {"__ref": "14363144"}, "locked": null, "vsettings": [{"__ref": "14363377"}, {"__ref": "14363378"}, {"__ref": "14363379"}, {"__ref": "14363380"}, {"__ref": "14363381"}, {"__ref": "14363382"}, {"__ref": "14363383"}, {"__ref": "14363384"}, {"__ref": "14363385"}, {"__ref": "14363386"}, {"__ref": "14363387"}, {"__ref": "14363388"}, {"__ref": "14363389"}, {"__ref": "14363390"}], "__type": "TplSlot"}, "14363241": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363391"}, "dataCond": {"__ref": "14363392"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363242": {"variants": [{"__ref": "14363207"}], "args": [], "attrs": {}, "rs": {"__ref": "14363393"}, "dataCond": {"__ref": "14363394"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363243": {"variants": [{"__ref": "14363215"}], "args": [], "attrs": {}, "rs": {"__ref": "14363395"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363244": {"variants": [{"__ref": "14363210"}, {"__ref": "14363207"}], "args": [], "attrs": {}, "rs": {"__ref": "14363396"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363245": {"param": {"__ref": "14363092"}, "defaultContents": [{"__ref": "14363397"}], "uuid": "Qsq3h26QshW", "parent": {"__ref": "14363145"}, "locked": null, "vsettings": [{"__ref": "14363398"}, {"__ref": "14363399"}, {"__ref": "14363400"}, {"__ref": "14363401"}, {"__ref": "14363402"}, {"__ref": "14363403"}, {"__ref": "14363404"}, {"__ref": "14363405"}, {"__ref": "14363406"}, {"__ref": "14363407"}, {"__ref": "14363408"}, {"__ref": "14363409"}, {"__ref": "14363410"}, {"__ref": "14363411"}, {"__ref": "14363412"}, {"__ref": "14363413"}, {"__ref": "14363414"}, {"__ref": "14363415"}, {"__ref": "14363416"}, {"__ref": "14363417"}, {"__ref": "14363418"}, {"__ref": "14363419"}, {"__ref": "14363420"}, {"__ref": "14363421"}, {"__ref": "14363422"}, {"__ref": "14363423"}], "__type": "TplSlot"}, "14363246": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363424"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363247": {"variants": [{"__ref": "14363209"}], "args": [], "attrs": {}, "rs": {"__ref": "14363425"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363248": {"variants": [{"__ref": "14363208"}], "args": [], "attrs": {}, "rs": {"__ref": "14363426"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363249": {"variants": [{"__ref": "14363110"}], "args": [], "attrs": {}, "rs": {"__ref": "14363427"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363250": {"variants": [{"__ref": "14363210"}], "args": [], "attrs": {}, "rs": {"__ref": "14363428"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363251": {"param": {"__ref": "14363096"}, "defaultContents": [{"__ref": "14363429"}], "uuid": "jdZdWXXDHiO", "parent": {"__ref": "14363146"}, "locked": null, "vsettings": [{"__ref": "14363430"}, {"__ref": "14363431"}, {"__ref": "14363432"}, {"__ref": "14363433"}, {"__ref": "14363434"}, {"__ref": "14363435"}, {"__ref": "14363436"}, {"__ref": "14363437"}, {"__ref": "14363438"}, {"__ref": "14363439"}, {"__ref": "14363440"}, {"__ref": "14363441"}, {"__ref": "14363442"}], "__type": "TplSlot"}, "14363252": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363443"}, "dataCond": {"__ref": "14363444"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363253": {"variants": [{"__ref": "14363208"}], "args": [], "attrs": {}, "rs": {"__ref": "14363445"}, "dataCond": {"__ref": "14363446"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363254": {"variants": [{"__ref": "14363217"}], "args": [], "attrs": {}, "rs": {"__ref": "14363447"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363255": {"variants": [{"__ref": "14363220"}], "args": [], "attrs": {}, "rs": {"__ref": "14363448"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363256": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "flex-column-gap": "8px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px"}, "mixins": [], "__type": "RuleSet"}, "14363257": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "14363258": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "14363259": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "14363260": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "14363261": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "14363262": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "14363263": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "14363264": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "14363265": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "14363266": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "14363267": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "14363268": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "14363269": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "14363270": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "14363271": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "14363272": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "14363273": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "14363274": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "14363275": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "14363276": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "14363277": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "14363278": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "14363279": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "14363280": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "14363281": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "14363282": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "14363283": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "14363284": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "14363285": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "14363286": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "14363287": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "14363288": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "14363289": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "14363290": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "14363291": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "14363292": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "14363293": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "14363294": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "14363295": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363296": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363297": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363298": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "14363299": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "14363300": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "14363301": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363302": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "14363303": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "14363304": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "14363305": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "14363306": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "14363307": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "14363308": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "14363309": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "14363310": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363311": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "14363312": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "14363313": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "14363314": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "14363315": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "14363316": {"frame": {"__ref": "14363551"}, "cellKey": {"__ref": "14363109"}, "__type": "ArenaFrameCell"}, "14363317": {"frame": {"__ref": "14363552"}, "cellKey": {"__ref": "14363110"}, "__type": "ArenaFrameCell"}, "14363318": {"frame": {"__ref": "14363553"}, "cellKey": {"__ref": "14363111"}, "__type": "ArenaFrameCell"}, "14363319": {"frame": {"__ref": "14363554"}, "cellKey": {"__ref": "14363112"}, "__type": "ArenaFrameCell"}, "14363320": {"frame": {"__ref": "14363555"}, "cellKey": {"__ref": "14363113"}, "__type": "ArenaFrameCell"}, "14363321": {"frame": {"__ref": "14363556"}, "cellKey": {"__ref": "14363207"}, "__type": "ArenaFrameCell"}, "14363322": {"frame": {"__ref": "14363557"}, "cellKey": {"__ref": "14363208"}, "__type": "ArenaFrameCell"}, "14363323": {"frame": {"__ref": "14363558"}, "cellKey": {"__ref": "14363209"}, "__type": "ArenaFrameCell"}, "14363324": {"frame": {"__ref": "14363559"}, "cellKey": {"__ref": "14363210"}, "__type": "ArenaFrameCell"}, "14363325": {"frame": {"__ref": "14363560"}, "cellKey": {"__ref": "14363211"}, "__type": "ArenaFrameCell"}, "14363326": {"frame": {"__ref": "14363561"}, "cellKey": {"__ref": "14363212"}, "__type": "ArenaFrameCell"}, "14363327": {"frame": {"__ref": "14363562"}, "cellKey": {"__ref": "14363213"}, "__type": "ArenaFrameCell"}, "14363328": {"frame": {"__ref": "14363563"}, "cellKey": {"__ref": "14363214"}, "__type": "ArenaFrameCell"}, "14363329": {"frame": {"__ref": "14363564"}, "cellKey": {"__ref": "14363215"}, "__type": "ArenaFrameCell"}, "14363330": {"frame": {"__ref": "14363565"}, "cellKey": {"__ref": "14363216"}, "__type": "ArenaFrameCell"}, "14363331": {"frame": {"__ref": "14363566"}, "cellKey": {"__ref": "14363217"}, "__type": "ArenaFrameCell"}, "14363332": {"frame": {"__ref": "14363567"}, "cellKey": {"__ref": "14363218"}, "__type": "ArenaFrameCell"}, "14363333": {"frame": {"__ref": "14363568"}, "cellKey": {"__ref": "14363219"}, "__type": "ArenaFrameCell"}, "14363334": {"frame": {"__ref": "14363569"}, "cellKey": {"__ref": "14363220"}, "__type": "ArenaFrameCell"}, "14363335": {"frame": {"__ref": "14363570"}, "cellKey": {"__ref": "14363221"}, "__type": "ArenaFrameCell"}, "14363336": {"frame": {"__ref": "14363571"}, "cellKey": {"__ref": "14363222"}, "__type": "ArenaFrameCell"}, "14363337": {"frame": {"__ref": "14363572"}, "cellKey": {"__ref": "14363223"}, "__type": "ArenaFrameCell"}, "14363338": {"frame": {"__ref": "14363573"}, "cellKey": {"__ref": "14363224"}, "__type": "ArenaFrameCell"}, "14363339": {"frame": {"__ref": "14363574"}, "cellKey": {"__ref": "14363225"}, "__type": "ArenaFrameCell"}, "14363340": {"frame": {"__ref": "14363575"}, "cellKey": {"__ref": "14363226"}, "__type": "ArenaFrameCell"}, "14363341": {"frame": {"__ref": "14363576"}, "cellKey": {"__ref": "14363227"}, "__type": "ArenaFrameCell"}, "14363342": {"frame": {"__ref": "14363577"}, "cellKey": [{"__ref": "14363221"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363343": {"frame": {"__ref": "14363578"}, "cellKey": [{"__ref": "14363221"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363344": {"frame": {"__ref": "14363579"}, "cellKey": [{"__ref": "14363222"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363345": {"frame": {"__ref": "14363580"}, "cellKey": [{"__ref": "14363222"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363346": {"frame": {"__ref": "14363581"}, "cellKey": [{"__ref": "14363217"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363347": {"frame": {"__ref": "14363582"}, "cellKey": [{"__ref": "14363217"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363348": {"frame": {"__ref": "14363583"}, "cellKey": [{"__ref": "14363218"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363349": {"frame": {"__ref": "14363584"}, "cellKey": [{"__ref": "14363218"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363350": {"frame": {"__ref": "14363585"}, "cellKey": [{"__ref": "14363223"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363351": {"frame": {"__ref": "14363586"}, "cellKey": [{"__ref": "14363223"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363352": {"frame": {"__ref": "14363587"}, "cellKey": [{"__ref": "14363224"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363353": {"frame": {"__ref": "14363588"}, "cellKey": [{"__ref": "14363224"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363354": {"frame": {"__ref": "14363589"}, "cellKey": [{"__ref": "14363225"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363355": {"frame": {"__ref": "14363590"}, "cellKey": [{"__ref": "14363225"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363356": {"frame": {"__ref": "14363591"}, "cellKey": [{"__ref": "14363215"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363357": {"frame": {"__ref": "14363592"}, "cellKey": [{"__ref": "14363215"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363358": {"frame": {"__ref": "14363593"}, "cellKey": [{"__ref": "14363216"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363359": {"frame": {"__ref": "14363594"}, "cellKey": [{"__ref": "14363216"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363360": {"frame": {"__ref": "14363595"}, "cellKey": [{"__ref": "14363219"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363361": {"frame": {"__ref": "14363596"}, "cellKey": [{"__ref": "14363219"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363362": {"frame": {"__ref": "14363597"}, "cellKey": [{"__ref": "14363210"}, {"__ref": "14363207"}], "__type": "ArenaFrameCell"}, "14363363": {"frame": {"__ref": "14363598"}, "cellKey": [{"__ref": "14363208"}, {"__ref": "14363210"}], "__type": "ArenaFrameCell"}, "14363364": {"frame": {"__ref": "14363599"}, "cellKey": [{"__ref": "14363226"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363365": {"frame": {"__ref": "14363600"}, "cellKey": [{"__ref": "14363226"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363366": {"frame": {"__ref": "14363601"}, "cellKey": [{"__ref": "14363211"}, {"__ref": "14363213"}], "__type": "ArenaFrameCell"}, "14363367": {"frame": {"__ref": "14363602"}, "cellKey": [{"__ref": "14363227"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363368": {"frame": {"__ref": "14363603"}, "cellKey": [{"__ref": "14363227"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363369": {"frame": {"__ref": "14363604"}, "cellKey": [{"__ref": "14363220"}, {"__ref": "14363112"}], "__type": "ArenaFrameCell"}, "14363370": {"frame": {"__ref": "14363605"}, "cellKey": [{"__ref": "14363220"}, {"__ref": "14363113"}], "__type": "ArenaFrameCell"}, "14363376": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "-yCHCRpUwSc", "parent": {"__ref": "14363240"}, "locked": null, "vsettings": [{"__ref": "14363609"}], "__type": "TplTag"}, "14363377": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363610"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363378": {"variants": [{"__ref": "14363207"}], "args": [], "attrs": {}, "rs": {"__ref": "14363611"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363379": {"variants": [{"__ref": "14363215"}], "args": [], "attrs": {}, "rs": {"__ref": "14363612"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363380": {"variants": [{"__ref": "14363221"}], "args": [], "attrs": {}, "rs": {"__ref": "14363613"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363381": {"variants": [{"__ref": "14363222"}], "args": [], "attrs": {}, "rs": {"__ref": "14363614"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363382": {"variants": [{"__ref": "14363223"}], "args": [], "attrs": {}, "rs": {"__ref": "14363615"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363383": {"variants": [{"__ref": "14363224"}], "args": [], "attrs": {}, "rs": {"__ref": "14363616"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363384": {"variants": [{"__ref": "14363225"}], "args": [], "attrs": {}, "rs": {"__ref": "14363617"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363385": {"variants": [{"__ref": "14363217"}], "args": [], "attrs": {}, "rs": {"__ref": "14363618"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363386": {"variants": [{"__ref": "14363227"}], "args": [], "attrs": {}, "rs": {"__ref": "14363619"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363387": {"variants": [{"__ref": "14363227"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363620"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363388": {"variants": [{"__ref": "14363227"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363621"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363389": {"variants": [{"__ref": "14363226"}], "args": [], "attrs": {}, "rs": {"__ref": "14363622"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363390": {"variants": [{"__ref": "14363220"}], "args": [], "attrs": {}, "rs": {"__ref": "14363623"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363391": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "14363392": {"code": "false", "fallback": null, "__type": "CustomCode"}, "14363393": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "14363394": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14363395": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363396": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363397": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "eZYqb1M3r9u", "parent": {"__ref": "14363245"}, "locked": null, "vsettings": [{"__ref": "14363630"}], "__type": "TplTag"}, "14363398": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363631"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363399": {"variants": [{"__ref": "14363111"}], "args": [], "attrs": {}, "rs": {"__ref": "14363632"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363400": {"variants": [{"__ref": "14363110"}], "args": [], "attrs": {}, "rs": {"__ref": "14363633"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363401": {"variants": [{"__ref": "14363207"}], "args": [], "attrs": {}, "rs": {"__ref": "14363634"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363402": {"variants": [{"__ref": "14363208"}], "args": [], "attrs": {}, "rs": {"__ref": "14363635"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363403": {"variants": [{"__ref": "14363209"}], "args": [], "attrs": {}, "rs": {"__ref": "14363636"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363404": {"variants": [{"__ref": "14363221"}], "args": [], "attrs": {}, "rs": {"__ref": "14363637"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363405": {"variants": [{"__ref": "14363222"}], "args": [], "attrs": {}, "rs": {"__ref": "14363638"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363406": {"variants": [{"__ref": "14363217"}], "args": [], "attrs": {}, "rs": {"__ref": "14363639"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363407": {"variants": [{"__ref": "14363223"}], "args": [], "attrs": {}, "rs": {"__ref": "14363640"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363408": {"variants": [{"__ref": "14363224"}], "args": [], "attrs": {}, "rs": {"__ref": "14363641"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363409": {"variants": [{"__ref": "14363225"}], "args": [], "attrs": {}, "rs": {"__ref": "14363642"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363410": {"variants": [{"__ref": "14363215"}], "args": [], "attrs": {}, "rs": {"__ref": "14363643"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363411": {"variants": [{"__ref": "14363216"}], "args": [], "attrs": {}, "rs": {"__ref": "14363644"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363412": {"variants": [{"__ref": "14363219"}], "args": [], "attrs": {}, "rs": {"__ref": "14363645"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363413": {"variants": [{"__ref": "14363218"}], "args": [], "attrs": {}, "rs": {"__ref": "14363646"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363414": {"variants": [{"__ref": "14363210"}], "args": [], "attrs": {}, "rs": {"__ref": "14363647"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363415": {"variants": [{"__ref": "14363226"}], "args": [], "attrs": {}, "rs": {"__ref": "14363648"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363416": {"variants": [{"__ref": "14363227"}], "args": [], "attrs": {}, "rs": {"__ref": "14363649"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363417": {"variants": [{"__ref": "14363227"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363650"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363418": {"variants": [{"__ref": "14363227"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363651"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363419": {"variants": [{"__ref": "14363227"}, {"__ref": "14363214"}], "args": [], "attrs": {}, "rs": {"__ref": "14363652"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363420": {"variants": [{"__ref": "14363227"}, {"__ref": "14363214"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363653"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363421": {"variants": [{"__ref": "14363214"}], "args": [], "attrs": {}, "rs": {"__ref": "14363654"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363422": {"variants": [{"__ref": "14363214"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363655"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363423": {"variants": [{"__ref": "14363220"}], "args": [], "attrs": {}, "rs": {"__ref": "14363656"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363424": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "14363425": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363426": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363427": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363428": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363429": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "qmsA3467bug", "parent": {"__ref": "14363251"}, "locked": null, "vsettings": [{"__ref": "14363662"}], "__type": "TplTag"}, "14363430": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363663"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363431": {"variants": [{"__ref": "14363208"}], "args": [], "attrs": {}, "rs": {"__ref": "14363664"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363432": {"variants": [{"__ref": "14363221"}], "args": [], "attrs": {}, "rs": {"__ref": "14363665"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363433": {"variants": [{"__ref": "14363222"}], "args": [], "attrs": {}, "rs": {"__ref": "14363666"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363434": {"variants": [{"__ref": "14363223"}], "args": [], "attrs": {}, "rs": {"__ref": "14363667"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363435": {"variants": [{"__ref": "14363224"}], "args": [], "attrs": {}, "rs": {"__ref": "14363668"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363436": {"variants": [{"__ref": "14363225"}], "args": [], "attrs": {}, "rs": {"__ref": "14363669"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363437": {"variants": [{"__ref": "14363217"}], "args": [], "attrs": {}, "rs": {"__ref": "14363670"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363438": {"variants": [{"__ref": "14363227"}], "args": [], "attrs": {}, "rs": {"__ref": "14363671"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363439": {"variants": [{"__ref": "14363227"}, {"__ref": "14363112"}], "args": [], "attrs": {}, "rs": {"__ref": "14363672"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363440": {"variants": [{"__ref": "14363227"}, {"__ref": "14363113"}], "args": [], "attrs": {}, "rs": {"__ref": "14363673"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363441": {"variants": [{"__ref": "14363226"}], "args": [], "attrs": {}, "rs": {"__ref": "14363674"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363442": {"variants": [{"__ref": "14363220"}], "args": [], "attrs": {}, "rs": {"__ref": "14363675"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363443": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "14363444": {"code": "false", "fallback": null, "__type": "CustomCode"}, "14363445": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "14363446": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14363447": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363448": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363551": {"uuid": "LyCL1S8psTS", "width": 340, "height": 340, "container": {"__ref": "14363682"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363109"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363552": {"uuid": "QPlJ7e4zlmL", "width": 340, "height": 340, "container": {"__ref": "14363683"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363110"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363553": {"uuid": "2m3kKTSMQ55", "width": 340, "height": 340, "container": {"__ref": "14363684"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363111"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363554": {"uuid": "GZxkXo_PiVU", "width": 340, "height": 340, "container": {"__ref": "14363685"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363555": {"uuid": "Is02vyQmWxq", "width": 340, "height": 340, "container": {"__ref": "14363686"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363556": {"uuid": "tgpWYCGgt3w", "width": 340, "height": 340, "container": {"__ref": "14363687"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363207"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363557": {"uuid": "BxB8JQiQHl8", "width": 340, "height": 340, "container": {"__ref": "14363688"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363208"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363558": {"uuid": "STmk1iJx8yA", "width": 340, "height": 340, "container": {"__ref": "14363689"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363209"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363559": {"uuid": "rscNtN2rujk", "width": 340, "height": 340, "container": {"__ref": "14363690"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363210"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363560": {"uuid": "alIo_8mhnTH", "width": 340, "height": 340, "container": {"__ref": "14363691"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363211"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363561": {"uuid": "yAYf5Ky_66p", "width": 340, "height": 340, "container": {"__ref": "14363692"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363212"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363562": {"uuid": "FdWWXSpEfs6", "width": 340, "height": 340, "container": {"__ref": "14363693"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363213"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363563": {"uuid": "mqZWt3T9xrY", "width": 340, "height": 340, "container": {"__ref": "14363694"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363214"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363564": {"uuid": "rgkHYcpTyO7", "width": 340, "height": 340, "container": {"__ref": "14363695"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363215"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363565": {"uuid": "Vsi8dBW30es", "width": 340, "height": 340, "container": {"__ref": "14363696"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363216"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363566": {"uuid": "S6DOBD4YFbo", "width": 340, "height": 340, "container": {"__ref": "14363697"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363217"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363567": {"uuid": "tx4zihMt1N3", "width": 340, "height": 340, "container": {"__ref": "14363698"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363218"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363568": {"uuid": "y8SsqhRDqgS", "width": 340, "height": 340, "container": {"__ref": "14363699"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363219"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363569": {"uuid": "SKYhFdE95VN", "width": 340, "height": 340, "container": {"__ref": "14363700"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363220"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363570": {"uuid": "WhdmbqLi2z2", "width": 340, "height": 340, "container": {"__ref": "14363701"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363221"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363571": {"uuid": "vB8O-A1FmfR", "width": 340, "height": 340, "container": {"__ref": "14363702"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363222"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363572": {"uuid": "2fO4sYFhx7s", "width": 340, "height": 340, "container": {"__ref": "14363703"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363223"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363573": {"uuid": "eY1xYHVDvvo", "width": 340, "height": 340, "container": {"__ref": "14363704"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363224"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363574": {"uuid": "H4ostxc_Hq4", "width": 340, "height": 340, "container": {"__ref": "14363705"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363225"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363575": {"uuid": "IRaMtoTeHSu", "width": 340, "height": 340, "container": {"__ref": "14363706"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363226"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363576": {"uuid": "OnDABTLGs18", "width": 340, "height": 340, "container": {"__ref": "14363707"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363227"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363577": {"uuid": "H8FkWq04HL2", "width": 340, "height": 340, "container": {"__ref": "14363708"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363221"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363578": {"uuid": "rfC7zBFUIbf", "width": 340, "height": 340, "container": {"__ref": "14363709"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363221"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363579": {"uuid": "pzn-CbyLJsE", "width": 340, "height": 340, "container": {"__ref": "14363710"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363222"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363580": {"uuid": "o3MuJkKkXjE", "width": 340, "height": 340, "container": {"__ref": "14363711"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363222"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363581": {"uuid": "DHJiuR1FkTC", "width": 340, "height": 340, "container": {"__ref": "14363712"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363217"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363582": {"uuid": "tRd8BeDqaUj", "width": 340, "height": 340, "container": {"__ref": "14363713"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363217"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363583": {"uuid": "qqsRpHh5Xbo", "width": 340, "height": 340, "container": {"__ref": "14363714"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363218"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363584": {"uuid": "4BvLEj6Cmx7", "width": 340, "height": 340, "container": {"__ref": "14363715"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363218"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363585": {"uuid": "Tnum0_MMgoU", "width": 340, "height": 340, "container": {"__ref": "14363716"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363223"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363586": {"uuid": "rb9CFkYSGaE", "width": 340, "height": 340, "container": {"__ref": "14363717"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363223"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363587": {"uuid": "dVBBQuQt6ge", "width": 340, "height": 340, "container": {"__ref": "14363718"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363224"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363588": {"uuid": "DpkJTU2d8IW", "width": 340, "height": 340, "container": {"__ref": "14363719"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363224"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363589": {"uuid": "a-eOBv6HTxb", "width": 340, "height": 340, "container": {"__ref": "14363720"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363225"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363590": {"uuid": "yE1IkLhUhmm", "width": 340, "height": 340, "container": {"__ref": "14363721"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363225"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363591": {"uuid": "H6IfNsL7JO7", "width": 340, "height": 340, "container": {"__ref": "14363722"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363215"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363592": {"uuid": "FwAR8jo6Ii1", "width": 340, "height": 340, "container": {"__ref": "14363723"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363215"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363593": {"uuid": "JVST2k384S7", "width": 340, "height": 340, "container": {"__ref": "14363724"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363216"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363594": {"uuid": "bQw66aDZFgZ", "width": 340, "height": 340, "container": {"__ref": "14363725"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363216"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363595": {"uuid": "lnJP-msEbS-", "width": 340, "height": 340, "container": {"__ref": "14363726"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363219"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363596": {"uuid": "Io5ksHtWneu", "width": 340, "height": 340, "container": {"__ref": "14363727"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363219"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363597": {"uuid": "m0KRqfT_Lcy", "width": 340, "height": 340, "container": {"__ref": "14363728"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363210"}, {"__ref": "14363207"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363598": {"uuid": "Zxiof16DW_f", "width": 340, "height": 340, "container": {"__ref": "14363729"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363208"}, {"__ref": "14363210"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363599": {"uuid": "Lu1tOSI70Rc", "width": 340, "height": 340, "container": {"__ref": "14363730"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363226"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363600": {"uuid": "V4zcehxzJii", "width": 340, "height": 340, "container": {"__ref": "14363731"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363226"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363601": {"uuid": "aowM8V2UQ6U", "width": 340, "height": 340, "container": {"__ref": "14363732"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363211"}, {"__ref": "14363213"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363602": {"uuid": "kZ3wUEo8-Yj", "width": 340, "height": 340, "container": {"__ref": "14363733"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363227"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363603": {"uuid": "4mGJJwLyQEo", "width": 340, "height": 340, "container": {"__ref": "14363734"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363227"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363604": {"uuid": "2EejiMLBYlX", "width": 340, "height": 340, "container": {"__ref": "14363735"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363220"}, {"__ref": "14363112"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363605": {"uuid": "cCq7XEoLrah", "width": 340, "height": 340, "container": {"__ref": "14363736"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "14363220"}, {"__ref": "14363113"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14363609": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {"outerHTML": {"__ref": "14363740"}}, "rs": {"__ref": "14363741"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363610": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "14363611": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363612": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363613": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "14363614": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "14363615": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "14363616": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "14363617": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "14363618": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "14363619": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "14363620": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "14363621": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "14363622": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "14363623": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "14363630": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {}, "rs": {"__ref": "14363754"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14363755"}, "columnsConfig": null, "__type": "VariantSetting"}, "14363631": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "14363632": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363633": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363634": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363635": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363636": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363637": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "14363638": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "14363639": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "14363640": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "14363641": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "14363642": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "14363643": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "14363644": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "14363645": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "14363646": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "14363647": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363648": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "14363649": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "14363650": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "14363651": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "14363652": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363653": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363654": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363655": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363656": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "14363662": {"variants": [{"__ref": "14363109"}], "args": [], "attrs": {"outerHTML": {"__ref": "14363775"}}, "rs": {"__ref": "14363776"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363663": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "14363664": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363665": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "14363666": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "14363667": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "14363668": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "14363669": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "14363670": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "14363671": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "14363672": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "14363673": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "14363674": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "14363675": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "14363682": {"name": null, "component": {"__ref": "14363089"}, "uuid": "zPItf_ckrSs", "parent": null, "locked": null, "vsettings": [{"__ref": "14363789"}], "__type": "TplComponent"}, "14363683": {"name": null, "component": {"__ref": "14363089"}, "uuid": "4RKZLCsacEY", "parent": null, "locked": null, "vsettings": [{"__ref": "14363790"}], "__type": "TplComponent"}, "14363684": {"name": null, "component": {"__ref": "14363089"}, "uuid": "gFNhV-JXNuP", "parent": null, "locked": null, "vsettings": [{"__ref": "14363791"}], "__type": "TplComponent"}, "14363685": {"name": null, "component": {"__ref": "14363089"}, "uuid": "aM1XR-TO2lG", "parent": null, "locked": null, "vsettings": [{"__ref": "14363792"}], "__type": "TplComponent"}, "14363686": {"name": null, "component": {"__ref": "14363089"}, "uuid": "V5aTQJ47-E-", "parent": null, "locked": null, "vsettings": [{"__ref": "14363793"}], "__type": "TplComponent"}, "14363687": {"name": null, "component": {"__ref": "14363089"}, "uuid": "N-0gaIRrQyQ", "parent": null, "locked": null, "vsettings": [{"__ref": "14363794"}], "__type": "TplComponent"}, "14363688": {"name": null, "component": {"__ref": "14363089"}, "uuid": "SnPknODDCNa", "parent": null, "locked": null, "vsettings": [{"__ref": "14363795"}], "__type": "TplComponent"}, "14363689": {"name": null, "component": {"__ref": "14363089"}, "uuid": "QKVykqj3ZLl", "parent": null, "locked": null, "vsettings": [{"__ref": "14363796"}], "__type": "TplComponent"}, "14363690": {"name": null, "component": {"__ref": "14363089"}, "uuid": "3w_vdz7Qm_j", "parent": null, "locked": null, "vsettings": [{"__ref": "14363797"}], "__type": "TplComponent"}, "14363691": {"name": null, "component": {"__ref": "14363089"}, "uuid": "pdVS786pc4u", "parent": null, "locked": null, "vsettings": [{"__ref": "14363798"}], "__type": "TplComponent"}, "14363692": {"name": null, "component": {"__ref": "14363089"}, "uuid": "nY5kjmEKZh0", "parent": null, "locked": null, "vsettings": [{"__ref": "14363799"}], "__type": "TplComponent"}, "14363693": {"name": null, "component": {"__ref": "14363089"}, "uuid": "SuDB72blINF", "parent": null, "locked": null, "vsettings": [{"__ref": "14363800"}], "__type": "TplComponent"}, "14363694": {"name": null, "component": {"__ref": "14363089"}, "uuid": "CdtFf8mMIe6", "parent": null, "locked": null, "vsettings": [{"__ref": "14363801"}], "__type": "TplComponent"}, "14363695": {"name": null, "component": {"__ref": "14363089"}, "uuid": "AMDg2cnskmf", "parent": null, "locked": null, "vsettings": [{"__ref": "14363802"}], "__type": "TplComponent"}, "14363696": {"name": null, "component": {"__ref": "14363089"}, "uuid": "5TatuUC9jve", "parent": null, "locked": null, "vsettings": [{"__ref": "14363803"}], "__type": "TplComponent"}, "14363697": {"name": null, "component": {"__ref": "14363089"}, "uuid": "KbRQU7X440s", "parent": null, "locked": null, "vsettings": [{"__ref": "14363804"}], "__type": "TplComponent"}, "14363698": {"name": null, "component": {"__ref": "14363089"}, "uuid": "bH0zLd_yX3E", "parent": null, "locked": null, "vsettings": [{"__ref": "14363805"}], "__type": "TplComponent"}, "14363699": {"name": null, "component": {"__ref": "14363089"}, "uuid": "hol6-x5bRrT", "parent": null, "locked": null, "vsettings": [{"__ref": "14363806"}], "__type": "TplComponent"}, "14363700": {"name": null, "component": {"__ref": "14363089"}, "uuid": "6OKo68bo9-4", "parent": null, "locked": null, "vsettings": [{"__ref": "14363807"}], "__type": "TplComponent"}, "14363701": {"name": null, "component": {"__ref": "14363089"}, "uuid": "iCyqSyBERhg", "parent": null, "locked": null, "vsettings": [{"__ref": "14363808"}], "__type": "TplComponent"}, "14363702": {"name": null, "component": {"__ref": "14363089"}, "uuid": "l7YtifWYkOd", "parent": null, "locked": null, "vsettings": [{"__ref": "14363809"}], "__type": "TplComponent"}, "14363703": {"name": null, "component": {"__ref": "14363089"}, "uuid": "w8B2Gu6eC6e", "parent": null, "locked": null, "vsettings": [{"__ref": "14363810"}], "__type": "TplComponent"}, "14363704": {"name": null, "component": {"__ref": "14363089"}, "uuid": "FqXp9Nil4qX", "parent": null, "locked": null, "vsettings": [{"__ref": "14363811"}], "__type": "TplComponent"}, "14363705": {"name": null, "component": {"__ref": "14363089"}, "uuid": "iTIXEtSDvmt", "parent": null, "locked": null, "vsettings": [{"__ref": "14363812"}], "__type": "TplComponent"}, "14363706": {"name": null, "component": {"__ref": "14363089"}, "uuid": "qfEfdtp0w82", "parent": null, "locked": null, "vsettings": [{"__ref": "14363813"}], "__type": "TplComponent"}, "14363707": {"name": null, "component": {"__ref": "14363089"}, "uuid": "y52I0dHVKCV", "parent": null, "locked": null, "vsettings": [{"__ref": "14363814"}], "__type": "TplComponent"}, "14363708": {"name": null, "component": {"__ref": "14363089"}, "uuid": "Cki5NS7Bl0Q", "parent": null, "locked": null, "vsettings": [{"__ref": "14363815"}], "__type": "TplComponent"}, "14363709": {"name": null, "component": {"__ref": "14363089"}, "uuid": "ZYCMTBb3sdu", "parent": null, "locked": null, "vsettings": [{"__ref": "14363816"}], "__type": "TplComponent"}, "14363710": {"name": null, "component": {"__ref": "14363089"}, "uuid": "266RzLIa7U7", "parent": null, "locked": null, "vsettings": [{"__ref": "14363817"}], "__type": "TplComponent"}, "14363711": {"name": null, "component": {"__ref": "14363089"}, "uuid": "L60ul9VT-4P", "parent": null, "locked": null, "vsettings": [{"__ref": "14363818"}], "__type": "TplComponent"}, "14363712": {"name": null, "component": {"__ref": "14363089"}, "uuid": "rj_ZE0rvrmt", "parent": null, "locked": null, "vsettings": [{"__ref": "14363819"}], "__type": "TplComponent"}, "14363713": {"name": null, "component": {"__ref": "14363089"}, "uuid": "GkHVzoC3h8m", "parent": null, "locked": null, "vsettings": [{"__ref": "14363820"}], "__type": "TplComponent"}, "14363714": {"name": null, "component": {"__ref": "14363089"}, "uuid": "PatNS7hFK-T", "parent": null, "locked": null, "vsettings": [{"__ref": "14363821"}], "__type": "TplComponent"}, "14363715": {"name": null, "component": {"__ref": "14363089"}, "uuid": "twGocpkI61L", "parent": null, "locked": null, "vsettings": [{"__ref": "14363822"}], "__type": "TplComponent"}, "14363716": {"name": null, "component": {"__ref": "14363089"}, "uuid": "jZwbB2uVGts", "parent": null, "locked": null, "vsettings": [{"__ref": "14363823"}], "__type": "TplComponent"}, "14363717": {"name": null, "component": {"__ref": "14363089"}, "uuid": "CxLLw2vtZoD", "parent": null, "locked": null, "vsettings": [{"__ref": "14363824"}], "__type": "TplComponent"}, "14363718": {"name": null, "component": {"__ref": "14363089"}, "uuid": "P2eoeUJqXG7", "parent": null, "locked": null, "vsettings": [{"__ref": "14363825"}], "__type": "TplComponent"}, "14363719": {"name": null, "component": {"__ref": "14363089"}, "uuid": "nWZ0xj7xqdz", "parent": null, "locked": null, "vsettings": [{"__ref": "14363826"}], "__type": "TplComponent"}, "14363720": {"name": null, "component": {"__ref": "14363089"}, "uuid": "v_Hzyi9Mnvn", "parent": null, "locked": null, "vsettings": [{"__ref": "14363827"}], "__type": "TplComponent"}, "14363721": {"name": null, "component": {"__ref": "14363089"}, "uuid": "X2Ie1ve3mSc", "parent": null, "locked": null, "vsettings": [{"__ref": "14363828"}], "__type": "TplComponent"}, "14363722": {"name": null, "component": {"__ref": "14363089"}, "uuid": "jqrEBgmWEBe", "parent": null, "locked": null, "vsettings": [{"__ref": "14363829"}], "__type": "TplComponent"}, "14363723": {"name": null, "component": {"__ref": "14363089"}, "uuid": "n2_MhXQrVFR", "parent": null, "locked": null, "vsettings": [{"__ref": "14363830"}], "__type": "TplComponent"}, "14363724": {"name": null, "component": {"__ref": "14363089"}, "uuid": "PRe6OykVC53", "parent": null, "locked": null, "vsettings": [{"__ref": "14363831"}], "__type": "TplComponent"}, "14363725": {"name": null, "component": {"__ref": "14363089"}, "uuid": "uVhLHzEuziM", "parent": null, "locked": null, "vsettings": [{"__ref": "14363832"}], "__type": "TplComponent"}, "14363726": {"name": null, "component": {"__ref": "14363089"}, "uuid": "sm53skzRVmi", "parent": null, "locked": null, "vsettings": [{"__ref": "14363833"}], "__type": "TplComponent"}, "14363727": {"name": null, "component": {"__ref": "14363089"}, "uuid": "X1cpm8KY3C3", "parent": null, "locked": null, "vsettings": [{"__ref": "14363834"}], "__type": "TplComponent"}, "14363728": {"name": null, "component": {"__ref": "14363089"}, "uuid": "6Za6dVHic1g", "parent": null, "locked": null, "vsettings": [{"__ref": "14363835"}], "__type": "TplComponent"}, "14363729": {"name": null, "component": {"__ref": "14363089"}, "uuid": "4zejHxpD9Rq", "parent": null, "locked": null, "vsettings": [{"__ref": "14363836"}], "__type": "TplComponent"}, "14363730": {"name": null, "component": {"__ref": "14363089"}, "uuid": "VWFreKYBtJR", "parent": null, "locked": null, "vsettings": [{"__ref": "14363837"}], "__type": "TplComponent"}, "14363731": {"name": null, "component": {"__ref": "14363089"}, "uuid": "Ug0bSzPZxkt", "parent": null, "locked": null, "vsettings": [{"__ref": "14363838"}], "__type": "TplComponent"}, "14363732": {"name": null, "component": {"__ref": "14363089"}, "uuid": "4hbO1DUsDOX", "parent": null, "locked": null, "vsettings": [{"__ref": "14363839"}], "__type": "TplComponent"}, "14363733": {"name": null, "component": {"__ref": "14363089"}, "uuid": "06jHJwtvsi9", "parent": null, "locked": null, "vsettings": [{"__ref": "14363840"}], "__type": "TplComponent"}, "14363734": {"name": null, "component": {"__ref": "14363089"}, "uuid": "S2r_7k2MxB_", "parent": null, "locked": null, "vsettings": [{"__ref": "14363841"}], "__type": "TplComponent"}, "14363735": {"name": null, "component": {"__ref": "14363089"}, "uuid": "ZVKYuwgG6u5", "parent": null, "locked": null, "vsettings": [{"__ref": "14363842"}], "__type": "TplComponent"}, "14363736": {"name": null, "component": {"__ref": "14363089"}, "uuid": "Z_z0h6Ri8fO", "parent": null, "locked": null, "vsettings": [{"__ref": "14363843"}], "__type": "TplComponent"}, "14363740": {"asset": {"__ref": "14363087"}, "__type": "ImageAssetRef"}, "14363741": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "14363754": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363755": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "14363775": {"asset": {"__ref": "14363088"}, "__type": "ImageAssetRef"}, "14363776": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "14363789": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363858"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363790": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363859"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363791": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363860"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363792": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363861"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363793": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363862"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363794": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363863"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363795": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363864"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363796": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363865"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363797": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363866"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363798": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363867"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363799": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363868"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363800": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363869"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363801": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363870"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363802": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363871"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363803": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363872"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363804": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363873"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363805": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363874"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363806": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363875"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363807": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363876"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363808": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363877"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363809": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363878"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363810": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363879"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363811": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363880"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363812": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363881"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363813": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363882"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363814": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363883"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363815": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363884"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363816": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363885"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363817": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363886"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363818": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363887"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363819": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363888"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363820": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363889"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363821": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363890"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363822": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363891"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363823": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363892"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363824": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363893"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363825": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363894"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363826": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363895"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363827": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363896"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363828": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363897"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363829": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363898"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363830": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363899"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363831": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363900"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363832": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363901"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363833": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363902"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363834": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363903"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363835": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363904"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363836": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363905"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363837": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363906"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363838": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363907"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363839": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363908"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363840": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363909"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363841": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363910"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363842": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363911"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363843": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14363912"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363858": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363859": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363860": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363861": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363862": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363863": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363864": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363865": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363866": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363867": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363868": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363869": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363870": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363871": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363872": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363873": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363874": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363875": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363876": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363877": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363878": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363879": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363880": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363881": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363882": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363883": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363884": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363885": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363886": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363887": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363888": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363889": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363890": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363891": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363892": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363893": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363894": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363895": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363896": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363897": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363898": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363899": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363900": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363901": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363902": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363903": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363904": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363905": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363906": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363907": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363908": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363909": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363910": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363911": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363912": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363921": {"tag": "div", "name": null, "children": [{"__ref": "14363961"}, {"__ref": "14363079"}, {"__ref": "14363091"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "1ysfvS1ri", "parent": {"__ref": "14363051"}, "locked": null, "vsettings": [{"__ref": "14363925"}, {"__ref": "14364069"}], "__type": "TplTag"}, "14363922": {"tpl": [{"__ref": "14363926"}], "__type": "VirtualRenderExpr"}, "14363924": {"tpl": [{"__ref": "14363928"}], "__type": "VirtualRenderExpr"}, "14363925": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "14363929"}, "dataCond": {"__ref": "14363930"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363926": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "cr3woXZu_c", "parent": {"__ref": "14363091"}, "locked": null, "vsettings": [{"__ref": "14363931"}], "__type": "TplTag"}, "14363927": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "pvNBh5JWLL", "parent": {"__ref": "14363091"}, "locked": null, "vsettings": [{"__ref": "14363932"}], "__type": "TplTag"}, "14363928": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "CGFoBmqga4", "parent": {"__ref": "14363091"}, "locked": null, "vsettings": [{"__ref": "14363933"}], "__type": "TplTag"}, "14363929": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "plasmic-display-none": "false", "flex-column-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "14363930": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14363931": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {"outerHTML": {"__ref": "14363947"}}, "rs": {"__ref": "14363948"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363932": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "14363949"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14363996"}, "columnsConfig": null, "__type": "VariantSetting"}, "14363933": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {"outerHTML": {"__ref": "14363951"}}, "rs": {"__ref": "14363952"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363947": {"asset": {"__ref": "14363087"}, "__type": "ImageAssetRef"}, "14363948": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "14363949": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363951": {"asset": {"__ref": "14363088"}, "__type": "ImageAssetRef"}, "14363952": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "14363961": {"name": null, "component": {"__ref": "14363089"}, "uuid": "9RJyxNXJH", "parent": {"__ref": "14363921"}, "locked": null, "vsettings": [{"__ref": "14363962"}], "__type": "TplComponent"}, "14363962": {"variants": [{"__ref": "14363052"}], "args": [{"__ref": "14363963"}, {"__ref": "14363964"}, {"__ref": "14363965"}, {"__ref": "14364031"}, {"__ref": "14364092"}, {"__ref": "4499404"}], "attrs": {"onClick": {"__ref": "14364082"}}, "rs": {"__ref": "14363966"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363963": {"param": {"__ref": "14363095"}, "expr": {"__ref": "14363967"}, "__type": "Arg"}, "14363964": {"param": {"__ref": "14363092"}, "expr": {"__ref": "14363995"}, "__type": "Arg"}, "14363965": {"param": {"__ref": "14363096"}, "expr": {"__ref": "14363969"}, "__type": "Arg"}, "14363966": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "14363967": {"tpl": [{"__ref": "14363972"}], "__type": "VirtualRenderExpr"}, "14363969": {"tpl": [{"__ref": "14363974"}], "__type": "VirtualRenderExpr"}, "14363972": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "wMwoKo7wLt", "parent": {"__ref": "14363961"}, "locked": null, "vsettings": [{"__ref": "14363975"}], "__type": "TplTag"}, "14363973": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Ut8A-HGgHc", "parent": {"__ref": "14363961"}, "locked": null, "vsettings": [{"__ref": "14363976"}], "__type": "TplTag"}, "14363974": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Y6-fe2TO_Q", "parent": {"__ref": "14363961"}, "locked": null, "vsettings": [{"__ref": "14363977"}], "__type": "TplTag"}, "14363975": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {"outerHTML": {"__ref": "14363978"}}, "rs": {"__ref": "14363979"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363976": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "14363980"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14363994"}, "columnsConfig": null, "__type": "VariantSetting"}, "14363977": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {"outerHTML": {"__ref": "14363982"}}, "rs": {"__ref": "14363983"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14363978": {"asset": {"__ref": "14363087"}, "__type": "ImageAssetRef"}, "14363979": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "14363980": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14363982": {"asset": {"__ref": "14363088"}, "__type": "ImageAssetRef"}, "14363983": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "14363994": {"markers": [], "text": "-", "__type": "RawText"}, "14363995": {"tpl": [{"__ref": "14363973"}], "__type": "RenderExpr"}, "14363996": {"markers": [], "text": "+", "__type": "RawText"}, "14363997": {"tpl": [{"__ref": "14363927"}], "__type": "RenderExpr"}, "14363998": {"param": {"__ref": "14363999"}, "accessType": "writable", "variableType": "number", "onChangeParam": {"__ref": "14364004"}, "tplNode": null, "implicitState": null, "__type": "State"}, "14363999": {"type": {"__ref": "14364002"}, "state": {"__ref": "14363998"}, "variable": {"__ref": "14364000"}, "uuid": "2sLGmimCw0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "14364003"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364000": {"name": "count", "uuid": "yVDkcvGu8", "__type": "Var"}, "14364002": {"name": "num", "__type": "<PERSON><PERSON>"}, "14364003": {"code": "0", "fallback": null, "__type": "CustomCode"}, "14364004": {"type": {"__ref": "14364006"}, "state": {"__ref": "14363998"}, "variable": {"__ref": "14364005"}, "uuid": "F_w3ZuWIhM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "14364005": {"name": "On count change", "uuid": "KNdaiOF_s", "__type": "Var"}, "14364006": {"name": "func", "params": [{"__ref": "14364007"}], "__type": "FunctionType"}, "14364007": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "14364008"}, "__type": "ArgType"}, "14364008": {"name": "num", "__type": "<PERSON><PERSON>"}, "14364010": {"param": {"__ref": "14364013"}, "defaultContents": [{"__ref": "14364014"}], "uuid": "tMe_CjlDVt", "parent": {"__ref": "14363071"}, "locked": null, "vsettings": [{"__ref": "14364015"}, {"__ref": "14364047"}], "__type": "TplSlot"}, "14364013": {"type": {"__ref": "14364017"}, "tplSlot": {"__ref": "14364010"}, "variable": {"__ref": "14364016"}, "uuid": "edsrahJZZr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "14364014": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "nuOkpLLoD-", "parent": {"__ref": "14364010"}, "locked": null, "vsettings": [{"__ref": "14364018"}], "__type": "TplTag"}, "14364015": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "14364019"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364016": {"name": "label", "uuid": "4rLkmmGws2", "__type": "Var"}, "14364017": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "14364018": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "14364020"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364021"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364019": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364020": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364021": {"markers": [], "text": "Counter Name", "__type": "RawText"}, "14364025": {"expr": {"__ref": "14364026"}, "html": false, "__type": "ExprText"}, "14364026": {"path": ["$state", "count"], "fallback": {"__ref": "14364027"}, "__type": "ObjectPath"}, "14364027": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "14364028": {"param": {"__ref": "14363099"}, "expr": {"__ref": "14364030"}, "__type": "Arg"}, "14364030": {"variants": [{"__ref": "14363221"}], "__type": "VariantsRef"}, "14364031": {"param": {"__ref": "14363099"}, "expr": {"__ref": "14364096"}, "__type": "Arg"}, "14364033": {"variantGroup": {"__ref": "14364034"}, "param": {"__ref": "14364035"}, "accessType": "readonly", "variableType": "variant", "onChangeParam": {"__ref": "14364254"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "14364034": {"type": "component", "param": {"__ref": "14364035"}, "linkedState": {"__ref": "14364033"}, "uuid": "YCFiECFAW-", "variants": [{"__ref": "14364037"}], "multi": false, "__type": "ComponentVariantGroup"}, "14364035": {"type": {"__ref": "14364039"}, "state": {"__ref": "14364033"}, "variable": {"__ref": "14364038"}, "uuid": "r8x8LyG7sF", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "14364046"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364036": {"cols": [{"__ref": "14364040"}], "rowKey": {"__ref": "14364034"}, "__type": "ArenaFrameRow"}, "14364037": {"uuid": "ZDaky31k3o", "name": "isOdd", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "14364034"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "14364038": {"name": "isOdd", "uuid": "Me9oh3O9T", "__type": "Var"}, "14364039": {"name": "text", "__type": "Text"}, "14364040": {"frame": {"__ref": "14364041"}, "cellKey": {"__ref": "14364037"}, "__type": "ArenaFrameCell"}, "14364041": {"uuid": "IA2LNaO-La", "width": 613, "height": 340, "container": {"__ref": "14364042"}, "lang": "English", "pinnedVariants": {"ZDaky31k3o": true}, "targetVariants": [{"__ref": "14364037"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "14364042": {"name": null, "component": {"__ref": "14363049"}, "uuid": "hVvGF4ATBT", "parent": null, "locked": null, "vsettings": [{"__ref": "14364043"}], "__type": "TplComponent"}, "14364043": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "14364044"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364044": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364046": {"code": "($state.count % 2 === 1)", "fallback": null, "__type": "CustomCode"}, "14364047": {"variants": [{"__ref": "14364037"}], "args": [], "attrs": {}, "rs": {"__ref": "14364048"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364048": {"values": {"color": "#BB0EA9"}, "mixins": [], "__type": "RuleSet"}, "14364050": {"variants": [{"__ref": "14364037"}], "args": [], "attrs": {}, "rs": {"__ref": "14364051"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364051": {"values": {"flex-column-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "14364052": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "lp_zKmQVb", "parent": {"__ref": "14363071"}, "locked": null, "vsettings": [{"__ref": "14364053"}, {"__ref": "14364054"}], "__type": "TplTag"}, "14364053": {"variants": [{"__ref": "14363052"}], "args": [], "attrs": {}, "rs": {"__ref": "14364055"}, "dataCond": {"__ref": "14364056"}, "dataRep": null, "text": {"__ref": "14364066"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364054": {"variants": [{"__ref": "14364037"}], "args": [], "attrs": {}, "rs": {"__ref": "14364058"}, "dataCond": {"__ref": "14364059"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364055": {"values": {"position": "relative", "plasmic-display-none": "true", "width": "wrap", "height": "wrap", "max-width": "800px", "margin-left": "auto"}, "mixins": [], "__type": "RuleSet"}, "14364056": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14364058": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "14364059": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14364066": {"markers": [], "text": "(odd)", "__type": "RawText"}, "14364069": {"variants": [{"__ref": "14364037"}], "args": [], "attrs": {}, "rs": {"__ref": "14364071"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364070": {"variants": [{"__ref": "14364037"}], "args": [], "attrs": {}, "rs": {"__ref": "14364072"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364071": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364072": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364073": {"interactions": [{"__ref": "14364074"}], "__type": "EventHandler"}, "14364074": {"interactionName": "Set count", "actionName": "updateVariable", "args": [{"__ref": "14364079"}, {"__ref": "14364080"}], "condExpr": null, "conditionalMode": "always", "uuid": "JggsZcm_m", "parent": {"__ref": "14364073"}, "__type": "Interaction"}, "14364077": {"path": ["$state", "count"], "fallback": null, "__type": "ObjectPath"}, "14364079": {"name": "variable", "expr": {"__ref": "14364077"}, "__type": "NameArg"}, "14364080": {"name": "operation", "expr": {"__ref": "14364081"}, "__type": "NameArg"}, "14364081": {"code": "2", "fallback": null, "__type": "CustomCode"}, "14364082": {"interactions": [{"__ref": "14364083"}], "__type": "EventHandler"}, "14364083": {"interactionName": "Set count", "actionName": "updateVariable", "args": [{"__ref": "14364088"}, {"__ref": "14364089"}, {"__ref": "14364090"}], "condExpr": null, "conditionalMode": "always", "uuid": "2ZP-AuV7v", "parent": {"__ref": "14364082"}, "__type": "Interaction"}, "14364086": {"path": ["$state", "count"], "fallback": null, "__type": "ObjectPath"}, "14364087": {"code": "0", "fallback": null, "__type": "CustomCode"}, "14364088": {"name": "variable", "expr": {"__ref": "14364086"}, "__type": "NameArg"}, "14364089": {"name": "operation", "expr": {"__ref": "14364087"}, "__type": "NameArg"}, "14364090": {"name": "value", "expr": {"__ref": "14364091"}, "__type": "NameArg"}, "14364091": {"code": "(Math.max(0, $state.count - 1))", "fallback": null, "__type": "CustomCode"}, "14364092": {"param": {"__ref": "14363097"}, "expr": {"__ref": "14364094"}, "__type": "Arg"}, "14364094": {"code": "($state.count === 0)", "fallback": null, "__type": "CustomCode"}, "14364096": {"code": "($state.count === 0 ? \"softRed\" : \"softBlue\")", "fallback": {"__ref": "14364097"}, "__type": "CustomCode"}, "14364097": {"variants": [{"__ref": "14363221"}], "__type": "VariantsRef"}, "14364098": {"markers": [], "text": "Welcome to counter test", "__type": "RawText"}, "14364099": {"name": "Counter", "component": {"__ref": "14363049"}, "uuid": "uIQOXZMdd", "parent": {"__ref": "14364116"}, "locked": null, "vsettings": [{"__ref": "14364102"}], "__type": "TplComponent"}, "14364100": {"type": {"__ref": "14364104"}, "state": {"__ref": "14364101"}, "variable": {"__ref": "14364103"}, "uuid": "7l7dPskBmB", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "number", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364101": {"param": {"__ref": "14364100"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "62885262"}, "tplNode": {"__ref": "14364099"}, "implicitState": {"__ref": "14363998"}, "__type": "State"}, "14364102": {"variants": [{"__ref": "62885007"}], "args": [{"__ref": "14364105"}, {"__ref": "60094001"}, {"__ref": "60094019"}], "attrs": {}, "rs": {"__ref": "14364106"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364103": {"name": "Counter count", "uuid": "GM9_yEkTd-", "__type": "Var"}, "14364104": {"name": "num", "__type": "<PERSON><PERSON>"}, "14364105": {"param": {"__ref": "14364013"}, "expr": {"__ref": "14364115"}, "__type": "Arg"}, "14364106": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "14364110": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8ieAZIkJqZ", "parent": {"__ref": "14364099"}, "locked": null, "vsettings": [{"__ref": "14364111"}], "__type": "TplTag"}, "14364111": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "14364112"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364114"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364112": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364114": {"markers": [], "text": "Counter 1", "__type": "RawText"}, "14364115": {"tpl": [{"__ref": "14364110"}], "__type": "RenderExpr"}, "14364116": {"tag": "div", "name": null, "children": [{"__ref": "14364099"}, {"__ref": "14364133"}, {"__ref": "14364265"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "bLOFe7W1U", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "14364117"}], "__type": "TplTag"}, "14364117": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-testid": {"__ref": "60094025"}}, "rs": {"__ref": "14364118"}, "dataCond": {"__ref": "14364119"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364118": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "plasmic-display-none": "false", "flex-column-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "14364119": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14364133": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "3y1kYwadu", "parent": {"__ref": "14364116"}, "locked": null, "vsettings": [{"__ref": "14364134"}], "__type": "TplTag"}, "14364134": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-count-for": {"__ref": "14364156"}}, "rs": {"__ref": "14364135"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364279"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364135": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "14364156": {"text": ["Counter 1"], "__type": "TemplatedString"}, "14364157": {"tag": "div", "name": null, "children": [{"__ref": "14364160"}, {"__ref": "14364161"}, {"__ref": "14364314"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "79hBBKmuiT", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "14364162"}], "__type": "TplTag"}, "14364158": {"type": {"__ref": "14364164"}, "state": {"__ref": "14364159"}, "variable": {"__ref": "14364163"}, "uuid": "36DYF05nG1", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "number", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364159": {"param": {"__ref": "14364158"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "62885267"}, "tplNode": {"__ref": "14364160"}, "implicitState": {"__ref": "14363998"}, "__type": "State"}, "14364160": {"name": "Counter 2", "component": {"__ref": "14363049"}, "uuid": "v8kQIygrg", "parent": {"__ref": "14364157"}, "locked": null, "vsettings": [{"__ref": "14364165"}], "__type": "TplComponent"}, "14364161": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "hMu15q1zed", "parent": {"__ref": "14364157"}, "locked": null, "vsettings": [{"__ref": "14364166"}], "__type": "TplTag"}, "14364162": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-testid": {"__ref": "60094026"}}, "rs": {"__ref": "14364167"}, "dataCond": {"__ref": "14364168"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364163": {"name": "Counter count2", "uuid": "az0uSfEaqv", "__type": "Var"}, "14364164": {"name": "num", "__type": "<PERSON><PERSON>"}, "14364165": {"variants": [{"__ref": "62885007"}], "args": [{"__ref": "14364169"}, {"__ref": "60094003"}, {"__ref": "60094021"}], "attrs": {}, "rs": {"__ref": "14364170"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364166": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-count-for": {"__ref": "14364202"}}, "rs": {"__ref": "14364172"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364282"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364167": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "plasmic-display-none": "false", "flex-column-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "14364168": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14364169": {"param": {"__ref": "14364013"}, "expr": {"__ref": "14364188"}, "__type": "Arg"}, "14364170": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "14364172": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "14364188": {"tpl": [{"__ref": "14364195"}], "__type": "RenderExpr"}, "14364195": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "hTJBDF26m6", "parent": {"__ref": "14364160"}, "locked": null, "vsettings": [{"__ref": "14364197"}], "__type": "TplTag"}, "14364197": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "14364198"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364201"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364198": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364201": {"markers": [], "text": "Counter 2", "__type": "RawText"}, "14364202": {"text": ["Counter 2"], "__type": "TemplatedString"}, "14364203": {"tag": "div", "name": null, "children": [{"__ref": "14364206"}, {"__ref": "14364207"}, {"__ref": "14364326"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "mGT4dP8gbm", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "14364208"}], "__type": "TplTag"}, "14364204": {"type": {"__ref": "14364210"}, "state": {"__ref": "14364205"}, "variable": {"__ref": "14364209"}, "uuid": "OFXVvygkQZ", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "number", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364205": {"param": {"__ref": "14364204"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "62885272"}, "tplNode": {"__ref": "14364206"}, "implicitState": {"__ref": "14363998"}, "__type": "State"}, "14364206": {"name": "Counter 3", "component": {"__ref": "14363049"}, "uuid": "7iHyviq_H", "parent": {"__ref": "14364203"}, "locked": null, "vsettings": [{"__ref": "14364211"}], "__type": "TplComponent"}, "14364207": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Yrr4Su5aYK", "parent": {"__ref": "14364203"}, "locked": null, "vsettings": [{"__ref": "14364212"}], "__type": "TplTag"}, "14364208": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-testid": {"__ref": "60094027"}}, "rs": {"__ref": "14364213"}, "dataCond": {"__ref": "14364214"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364209": {"name": "Counter 2 count", "uuid": "WVo86CuX8H", "__type": "Var"}, "14364210": {"name": "num", "__type": "<PERSON><PERSON>"}, "14364211": {"variants": [{"__ref": "62885007"}], "args": [{"__ref": "14364215"}, {"__ref": "60094005"}, {"__ref": "60094023"}], "attrs": {}, "rs": {"__ref": "14364216"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14364212": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-count-for": {"__ref": "14364247"}}, "rs": {"__ref": "14364218"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364219"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364213": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "plasmic-display-none": "false", "flex-column-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "14364214": {"code": "true", "fallback": null, "__type": "CustomCode"}, "14364215": {"param": {"__ref": "14364013"}, "expr": {"__ref": "14364234"}, "__type": "Arg"}, "14364216": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "14364218": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "14364219": {"expr": {"__ref": "14364240"}, "html": false, "__type": "ExprText"}, "14364234": {"tpl": [{"__ref": "14364241"}], "__type": "RenderExpr"}, "14364240": {"path": ["$state", "counter3", "count"], "fallback": {"__ref": "14364242"}, "__type": "ObjectPath"}, "14364241": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "pRcjRCpAuI", "parent": {"__ref": "14364206"}, "locked": null, "vsettings": [{"__ref": "14364243"}], "__type": "TplTag"}, "14364242": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "14364243": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "14364244"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364246"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364244": {"values": {}, "mixins": [], "__type": "RuleSet"}, "14364246": {"markers": [], "text": "Counter 3", "__type": "RawText"}, "14364247": {"text": ["Counter 3"], "__type": "TemplatedString"}, "14364248": {"param": {"__ref": "14364251"}, "accessType": "private", "variableType": "boolean", "onChangeParam": {"__ref": "62885277"}, "tplNode": {"__ref": "14364099"}, "implicitState": {"__ref": "14364033"}, "__type": "State"}, "14364249": {"param": {"__ref": "14364252"}, "accessType": "private", "variableType": "boolean", "onChangeParam": {"__ref": "62885282"}, "tplNode": {"__ref": "14364160"}, "implicitState": {"__ref": "14364033"}, "__type": "State"}, "14364250": {"param": {"__ref": "14364253"}, "accessType": "private", "variableType": "boolean", "onChangeParam": {"__ref": "62885287"}, "tplNode": {"__ref": "14364206"}, "implicitState": {"__ref": "14364033"}, "__type": "State"}, "14364251": {"type": {"__ref": "14364256"}, "state": {"__ref": "14364248"}, "variable": {"__ref": "14364255"}, "uuid": "7ACLtX6CdF", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "variant", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364252": {"type": {"__ref": "14364258"}, "state": {"__ref": "14364249"}, "variable": {"__ref": "14364257"}, "uuid": "gNaMP52XJ4", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "variant", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364253": {"type": {"__ref": "14364260"}, "state": {"__ref": "14364250"}, "variable": {"__ref": "14364259"}, "uuid": "VTdOeLyete", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "variant", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "14364254": {"type": {"__ref": "14364262"}, "state": {"__ref": "14364033"}, "variable": {"__ref": "14364261"}, "uuid": "1JjMPgBfYG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "14364255": {"name": "Counter isOdd", "uuid": "V5XY9UFcp", "__type": "Var"}, "14364256": {"name": "any", "__type": "AnyType"}, "14364257": {"name": "Counter 2 isOdd", "uuid": "4XeWy1vmEY", "__type": "Var"}, "14364258": {"name": "any", "__type": "AnyType"}, "14364259": {"name": "Counter 3 isOdd", "uuid": "RaAI05bYAd", "__type": "Var"}, "14364260": {"name": "any", "__type": "AnyType"}, "14364261": {"name": "On isOdd change", "uuid": "Ko8lAO3Ltu", "__type": "Var"}, "14364262": {"name": "func", "params": [{"__ref": "14364263"}], "__type": "FunctionType"}, "14364263": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "14364264"}, "__type": "ArgType"}, "14364264": {"name": "any", "__type": "AnyType"}, "14364265": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "BlcJk8uXh", "parent": {"__ref": "14364116"}, "locked": null, "vsettings": [{"__ref": "14364266"}], "__type": "TplTag"}, "14364266": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-odd-for": {"__ref": "14364313"}}, "rs": {"__ref": "14364268"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60094007"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364268": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "14364279": {"expr": {"__ref": "14364280"}, "html": false, "__type": "ExprText"}, "14364280": {"path": ["$state", "counter", "count"], "fallback": {"__ref": "14364281"}, "__type": "ObjectPath"}, "14364281": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "14364282": {"expr": {"__ref": "14364283"}, "html": false, "__type": "ExprText"}, "14364283": {"path": ["$state", "counter2", "count"], "fallback": {"__ref": "14364284"}, "__type": "ObjectPath"}, "14364284": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "14364313": {"text": ["Counter 1"], "__type": "TemplatedString"}, "14364314": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "LXc9z-D_Wb", "parent": {"__ref": "14364157"}, "locked": null, "vsettings": [{"__ref": "14364315"}], "__type": "TplTag"}, "14364315": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-odd-for": {"__ref": "14364325"}}, "rs": {"__ref": "14364317"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60094013"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364317": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "14364325": {"text": ["Counter 2"], "__type": "TemplatedString"}, "14364326": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "wtJX_Wt_V5", "parent": {"__ref": "14364203"}, "locked": null, "vsettings": [{"__ref": "14364327"}], "__type": "TplTag"}, "14364327": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {"data-odd-for": {"__ref": "14364337"}}, "rs": {"__ref": "14364329"}, "dataCond": null, "dataRep": null, "text": {"__ref": "60094016"}, "columnsConfig": null, "__type": "VariantSetting"}, "14364329": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "14364337": {"text": ["Counter 3"], "__type": "TemplatedString"}, "14364338": {"type": {"__ref": "14364340"}, "variable": {"__ref": "14364339"}, "uuid": "DyFOtLqdID", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "14364339": {"name": "test name", "uuid": "0vB9UMXck", "__type": "Var"}, "14364340": {"name": "text", "__type": "Text"}, "60094001": {"param": {"__ref": "14363999"}, "expr": {"__ref": "60094002"}, "__type": "Arg"}, "60094002": {"code": "0", "fallback": null, "__type": "CustomCode"}, "60094003": {"param": {"__ref": "14363999"}, "expr": {"__ref": "60094004"}, "__type": "Arg"}, "60094004": {"code": "0", "fallback": null, "__type": "CustomCode"}, "60094005": {"param": {"__ref": "14363999"}, "expr": {"__ref": "60094006"}, "__type": "Arg"}, "60094006": {"code": "0", "fallback": null, "__type": "CustomCode"}, "60094007": {"expr": {"__ref": "60094008"}, "html": false, "__type": "ExprText"}, "60094008": {"code": "($state.counter.isOdd ? \"Odd\" : \"Even\")", "fallback": {"__ref": "60094009"}, "__type": "CustomCode"}, "60094009": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "60094013": {"expr": {"__ref": "60094014"}, "html": false, "__type": "ExprText"}, "60094014": {"code": "($state.counter2.isOdd ? \"Odd\" : \"Even\")", "fallback": {"__ref": "60094015"}, "__type": "CustomCode"}, "60094015": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "60094016": {"expr": {"__ref": "60094017"}, "html": false, "__type": "ExprText"}, "60094017": {"code": "($state.counter3.isOdd ? \"Odd\" : \"Even\")", "fallback": {"__ref": "60094018"}, "__type": "CustomCode"}, "60094018": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "60094019": {"param": {"__ref": "14364338"}, "expr": {"__ref": "60094020"}, "__type": "Arg"}, "60094020": {"text": ["counter1"], "__type": "TemplatedString"}, "60094021": {"param": {"__ref": "14364338"}, "expr": {"__ref": "60094022"}, "__type": "Arg"}, "60094022": {"text": ["counter2"], "__type": "TemplatedString"}, "60094023": {"param": {"__ref": "14364338"}, "expr": {"__ref": "60094024"}, "__type": "Arg"}, "60094024": {"text": ["counter3"], "__type": "TemplatedString"}, "60094025": {"text": ["counter1-container"], "__type": "TemplatedString"}, "60094026": {"text": ["counter2-container"], "__type": "TemplatedString"}, "60094027": {"text": ["counter3-container"], "__type": "TemplatedString"}, "60094030": {"code": "($props.testName)", "fallback": {"__ref": "60094031"}, "__type": "CustomCode"}, "60094031": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "60094042": {"code": "($props.testName)", "fallback": null, "__type": "CustomCode"}, "60094043": {"text": ["", {"__ref": "60094042"}, "-count"], "__type": "TemplatedString"}, "62885001": {"components": [{"__ref": "62885002"}, {"__ref": "14363001"}, {"__ref": "14363002"}, {"__ref": "14363049"}, {"__ref": "14363089"}], "arenas": [], "pageArenas": [{"__ref": "62885061"}], "componentArenas": [{"__ref": "14363050"}, {"__ref": "14363090"}], "globalVariantGroups": [{"__ref": "62885055"}], "userManagedFonts": [], "globalVariant": {"__ref": "62885068"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "62885075"}], "activeTheme": {"__ref": "62885075"}, "imageAssets": [{"__ref": "14363087"}, {"__ref": "14363088"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "62885055"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"button": {"__ref": "14363089"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "62885002": {"uuid": "BtG1cJsQymJd", "name": "Homepage", "params": [{"__ref": "14364100"}, {"__ref": "14364158"}, {"__ref": "14364204"}, {"__ref": "14364251"}, {"__ref": "14364252"}, {"__ref": "14364253"}, {"__ref": "62885262"}, {"__ref": "62885267"}, {"__ref": "62885272"}, {"__ref": "62885277"}, {"__ref": "62885282"}, {"__ref": "62885287"}], "states": [{"__ref": "14364101"}, {"__ref": "14364159"}, {"__ref": "14364205"}, {"__ref": "14364248"}, {"__ref": "14364249"}, {"__ref": "14364250"}], "tplTree": {"__ref": "62885003"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "62885007"}], "variantGroups": [], "pageMeta": {"__ref": "62885060"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "62885003": {"tag": "div", "name": null, "children": [{"__ref": "62885004"}, {"__ref": "14364116"}, {"__ref": "14364157"}, {"__ref": "14364203"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "y25OVXFLGLSD", "parent": null, "locked": null, "vsettings": [{"__ref": "62885040"}, {"__ref": "62885053"}], "__type": "TplTag"}, "62885004": {"tag": "section", "name": null, "children": [{"__ref": "62885005"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "1m0ZaovFbAjr", "parent": {"__ref": "62885003"}, "locked": null, "vsettings": [{"__ref": "62885026"}], "__type": "TplTag"}, "62885005": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kpZWMz5kmzEW", "parent": {"__ref": "62885004"}, "locked": null, "vsettings": [{"__ref": "62885006"}], "__type": "TplTag"}, "62885006": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "62885008"}, "dataCond": null, "dataRep": null, "text": {"__ref": "14364098"}, "columnsConfig": null, "__type": "VariantSetting"}, "62885007": {"uuid": "eXaNvHZOkpNm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62885008": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "margin-bottom": "32px"}, "mixins": [], "__type": "RuleSet"}, "62885026": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "62885027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885027": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "padding-left": "24px", "padding-right": "24px", "padding-bottom": "96px", "padding-top": "96px", "flex-row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "62885040": {"variants": [{"__ref": "62885007"}], "args": [], "attrs": {}, "rs": {"__ref": "62885041"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885041": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "flex-start", "padding-top": "24px", "padding-right": "24px", "padding-bottom": "24px", "padding-left": "24px", "flex-row-gap": "24px"}, "mixins": [], "__type": "RuleSet"}, "62885053": {"variants": [{"__ref": "62885054"}], "args": [], "attrs": {}, "rs": {"__ref": "62885059"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885054": {"uuid": "Iw1PegnUDE7A_", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "62885055"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62885055": {"type": "global-screen", "param": {"__ref": "62885056"}, "uuid": "-MvgymA1_MPux", "variants": [{"__ref": "62885054"}], "multi": true, "__type": "GlobalVariantGroup"}, "62885056": {"type": {"__ref": "62885058"}, "variable": {"__ref": "62885057"}, "uuid": "6w4Wg_v6xiFpq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "62885057": {"name": "Screen", "uuid": "ZR6EMjVpcY1gq", "__type": "Var"}, "62885058": {"name": "text", "__type": "Text"}, "62885059": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885060": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "62885061": {"component": {"__ref": "62885002"}, "matrix": {"__ref": "62885062"}, "customMatrix": {"__ref": "3691501"}, "__type": "PageArena"}, "62885062": {"rows": [{"__ref": "62885063"}], "__type": "ArenaFrameGrid"}, "62885063": {"cols": [{"__ref": "62885064"}, {"__ref": "62885070"}], "rowKey": {"__ref": "62885007"}, "__type": "ArenaFrameRow"}, "62885064": {"frame": {"__ref": "62885065"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62885065": {"uuid": "hPzw2-RIAfQ9Q", "width": 1440, "height": 768, "container": {"__ref": "62885066"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "62885007"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "62885066": {"name": null, "component": {"__ref": "62885002"}, "uuid": "6Y9ix7r64Qek6", "parent": null, "locked": null, "vsettings": [{"__ref": "62885067"}], "__type": "TplComponent"}, "62885067": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "62885069"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885068": {"uuid": "14i4PWoFRuvRm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62885069": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885070": {"frame": {"__ref": "62885071"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62885071": {"uuid": "H9ozxQRjzYdPN", "width": 414, "height": 736, "container": {"__ref": "62885072"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"Iw1PegnUDE7A_": true}, "targetGlobalVariants": [{"__ref": "62885054"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "62885072": {"name": null, "component": {"__ref": "62885002"}, "uuid": "YtlcBrGVXDVHI", "parent": null, "locked": null, "vsettings": [{"__ref": "62885073"}], "__type": "TplComponent"}, "62885073": {"variants": [{"__ref": "62885068"}], "args": [], "attrs": {}, "rs": {"__ref": "62885074"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62885074": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885075": {"defaultStyle": {"__ref": "62885076"}, "styles": [{"__ref": "62885091"}, {"__ref": "62885099"}, {"__ref": "62885107"}, {"__ref": "62885111"}, {"__ref": "62885119"}, {"__ref": "62885127"}, {"__ref": "62885152"}, {"__ref": "62885160"}, {"__ref": "62885185"}, {"__ref": "62885196"}, {"__ref": "62885207"}, {"__ref": "62885215"}, {"__ref": "62885222"}, {"__ref": "62885226"}, {"__ref": "62885229"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "62885076": {"name": "Default Typography", "rs": {"__ref": "62885077"}, "preview": null, "uuid": "WrBbwj7AG_dL", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885077": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "62885091": {"selector": "h1", "style": {"__ref": "62885092"}, "__type": "ThemeStyle"}, "62885092": {"name": "Default \"h1\"", "rs": {"__ref": "62885093"}, "preview": null, "uuid": "SOrH2-piLBXf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885093": {"values": {"color": "#000000", "font-weight": "900", "font-size": "72px", "line-height": "1", "letter-spacing": "-4px"}, "mixins": [], "__type": "RuleSet"}, "62885099": {"selector": "h2", "style": {"__ref": "62885100"}, "__type": "ThemeStyle"}, "62885100": {"name": "Default \"h2\"", "rs": {"__ref": "62885101"}, "preview": null, "uuid": "H7zKtFC98GbJ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885101": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "62885107": {"selector": "a", "style": {"__ref": "62885108"}, "__type": "ThemeStyle"}, "62885108": {"name": "Default \"a\"", "rs": {"__ref": "62885109"}, "preview": null, "uuid": "EzvC6JLudAJt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885109": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "62885111": {"selector": "h3", "style": {"__ref": "62885112"}, "__type": "ThemeStyle"}, "62885112": {"name": "Default \"h3\"", "rs": {"__ref": "62885113"}, "preview": null, "uuid": "qloEW3al7ShM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885113": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "62885119": {"selector": "h4", "style": {"__ref": "62885120"}, "__type": "ThemeStyle"}, "62885120": {"name": "Default \"h4\"", "rs": {"__ref": "62885121"}, "preview": null, "uuid": "PTgOftsOldbk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885121": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "62885127": {"selector": "code", "style": {"__ref": "62885128"}, "__type": "ThemeStyle"}, "62885128": {"name": "Default \"code\"", "rs": {"__ref": "62885129"}, "preview": null, "uuid": "Lnva58L1xulY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885129": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "62885152": {"selector": "blockquote", "style": {"__ref": "62885153"}, "__type": "ThemeStyle"}, "62885153": {"name": "Default \"blockquote\"", "rs": {"__ref": "62885154"}, "preview": null, "uuid": "ZYZaxTU8zo8XP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885154": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "62885160": {"selector": "pre", "style": {"__ref": "62885161"}, "__type": "ThemeStyle"}, "62885161": {"name": "Default \"pre\"", "rs": {"__ref": "62885162"}, "preview": null, "uuid": "4EHyE6ul8K5zF", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885162": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "62885185": {"selector": "ul", "style": {"__ref": "62885186"}, "__type": "ThemeStyle"}, "62885186": {"name": "Default \"ul\"", "rs": {"__ref": "62885187"}, "preview": null, "uuid": "whHJCzEF4Exdx", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885187": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "62885196": {"selector": "ol", "style": {"__ref": "62885197"}, "__type": "ThemeStyle"}, "62885197": {"name": "Default \"ol\"", "rs": {"__ref": "62885198"}, "preview": null, "uuid": "muDhaFoygqglX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885198": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "62885207": {"selector": "h5", "style": {"__ref": "62885208"}, "__type": "ThemeStyle"}, "62885208": {"name": "Default \"h5\"", "rs": {"__ref": "62885209"}, "preview": null, "uuid": "w3jaYB6vGoROj", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885209": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "62885215": {"selector": "h6", "style": {"__ref": "62885216"}, "__type": "ThemeStyle"}, "62885216": {"name": "Default \"h6\"", "rs": {"__ref": "62885217"}, "preview": null, "uuid": "F_qpYVRvn54KX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885217": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "62885222": {"selector": "a:hover", "style": {"__ref": "62885223"}, "__type": "ThemeStyle"}, "62885223": {"name": "Default \"a:hover\"", "rs": {"__ref": "62885224"}, "preview": null, "uuid": "FhMazc7GkfuxX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885224": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "62885226": {"selector": "li", "style": {"__ref": "62885227"}, "__type": "ThemeStyle"}, "62885227": {"name": "Default \"li\"", "rs": {"__ref": "62885228"}, "preview": null, "uuid": "HhZGTBvz3oXeH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885228": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885229": {"selector": "p", "style": {"__ref": "62885230"}, "__type": "ThemeStyle"}, "62885230": {"name": "Default \"p\"", "rs": {"__ref": "62885231"}, "preview": null, "uuid": "u07KibhRJ5rmE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "62885231": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62885232": {"type": {"__ref": "62885233"}, "state": {"__ref": "14363102"}, "variable": {"__ref": "62885236"}, "uuid": "S1OtKP_pA", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885233": {"name": "func", "params": [{"__ref": "62885234"}], "__type": "FunctionType"}, "62885234": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885235"}, "__type": "ArgType"}, "62885235": {"name": "any", "__type": "AnyType"}, "62885236": {"name": "On Show Start Icon change", "uuid": "B4gdX2EIE-", "__type": "Var"}, "62885237": {"type": {"__ref": "62885238"}, "state": {"__ref": "14363103"}, "variable": {"__ref": "62885241"}, "uuid": "n7U6pkde9F", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885238": {"name": "func", "params": [{"__ref": "62885239"}], "__type": "FunctionType"}, "62885239": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885240"}, "__type": "ArgType"}, "62885240": {"name": "any", "__type": "AnyType"}, "62885241": {"name": "On Show End Icon change", "uuid": "FhJOw32pHy", "__type": "Var"}, "62885242": {"type": {"__ref": "62885243"}, "state": {"__ref": "14363104"}, "variable": {"__ref": "62885246"}, "uuid": "8Fe6lRtHOk", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885243": {"name": "func", "params": [{"__ref": "62885244"}], "__type": "FunctionType"}, "62885244": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885245"}, "__type": "ArgType"}, "62885245": {"name": "any", "__type": "AnyType"}, "62885246": {"name": "On Is Disabled change", "uuid": "f6d54_KY-i", "__type": "Var"}, "62885247": {"type": {"__ref": "62885248"}, "state": {"__ref": "14363105"}, "variable": {"__ref": "62885251"}, "uuid": "jbIqgOGkRd", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885248": {"name": "func", "params": [{"__ref": "62885249"}], "__type": "FunctionType"}, "62885249": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885250"}, "__type": "ArgType"}, "62885250": {"name": "any", "__type": "AnyType"}, "62885251": {"name": "On Shape change", "uuid": "QUyr1P99_3", "__type": "Var"}, "62885252": {"type": {"__ref": "62885253"}, "state": {"__ref": "14363106"}, "variable": {"__ref": "62885256"}, "uuid": "-9pzkIjs5v", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885253": {"name": "func", "params": [{"__ref": "62885254"}], "__type": "FunctionType"}, "62885254": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885255"}, "__type": "ArgType"}, "62885255": {"name": "any", "__type": "AnyType"}, "62885256": {"name": "On Size change", "uuid": "B2sWug3FWx", "__type": "Var"}, "62885257": {"type": {"__ref": "62885258"}, "state": {"__ref": "14363107"}, "variable": {"__ref": "62885261"}, "uuid": "-1vmR3XXCE", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885258": {"name": "func", "params": [{"__ref": "62885259"}], "__type": "FunctionType"}, "62885259": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885260"}, "__type": "ArgType"}, "62885260": {"name": "any", "__type": "AnyType"}, "62885261": {"name": "On Color change", "uuid": "rc_ZSlhBvp", "__type": "Var"}, "62885262": {"type": {"__ref": "62885263"}, "state": {"__ref": "14364101"}, "variable": {"__ref": "62885266"}, "uuid": "QRH6D56DRT", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885263": {"name": "func", "params": [{"__ref": "62885264"}], "__type": "FunctionType"}, "62885264": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885265"}, "__type": "ArgType"}, "62885265": {"name": "num", "__type": "<PERSON><PERSON>"}, "62885266": {"name": "On Counter count change", "uuid": "ihYtHHtXgw", "__type": "Var"}, "62885267": {"type": {"__ref": "62885268"}, "state": {"__ref": "14364159"}, "variable": {"__ref": "62885271"}, "uuid": "iX7HUH1ziY", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885268": {"name": "func", "params": [{"__ref": "62885269"}], "__type": "FunctionType"}, "62885269": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885270"}, "__type": "ArgType"}, "62885270": {"name": "num", "__type": "<PERSON><PERSON>"}, "62885271": {"name": "On Counter count2 change", "uuid": "KgPTg9wLi-", "__type": "Var"}, "62885272": {"type": {"__ref": "62885273"}, "state": {"__ref": "14364205"}, "variable": {"__ref": "62885276"}, "uuid": "icpgllWb-w3", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885273": {"name": "func", "params": [{"__ref": "62885274"}], "__type": "FunctionType"}, "62885274": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885275"}, "__type": "ArgType"}, "62885275": {"name": "num", "__type": "<PERSON><PERSON>"}, "62885276": {"name": "On Counter 2 count change", "uuid": "ilGfIlQ-BDJ", "__type": "Var"}, "62885277": {"type": {"__ref": "62885278"}, "state": {"__ref": "14364248"}, "variable": {"__ref": "62885281"}, "uuid": "LtUnnKRkxg0", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885278": {"name": "func", "params": [{"__ref": "62885279"}], "__type": "FunctionType"}, "62885279": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885280"}, "__type": "ArgType"}, "62885280": {"name": "bool", "__type": "BoolType"}, "62885281": {"name": "On Counter isOdd change", "uuid": "0dWPzhxhVbz", "__type": "Var"}, "62885282": {"type": {"__ref": "62885283"}, "state": {"__ref": "14364249"}, "variable": {"__ref": "62885286"}, "uuid": "9UXlu0FVObK", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885283": {"name": "func", "params": [{"__ref": "62885284"}], "__type": "FunctionType"}, "62885284": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885285"}, "__type": "ArgType"}, "62885285": {"name": "bool", "__type": "BoolType"}, "62885286": {"name": "On Counter 2 isOdd change", "uuid": "0UxLmStjP_M", "__type": "Var"}, "62885287": {"type": {"__ref": "62885288"}, "state": {"__ref": "14364250"}, "variable": {"__ref": "62885291"}, "uuid": "qrRYR5EAKrE", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "62885288": {"name": "func", "params": [{"__ref": "62885289"}], "__type": "FunctionType"}, "62885289": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "62885290"}, "__type": "ArgType"}, "62885290": {"name": "bool", "__type": "BoolType"}, "62885291": {"name": "On Counter 3 isOdd change", "uuid": "kEG8zDHxZWm", "__type": "Var"}}, "deps": [], "version": "246-add-component-updated-at"}]]