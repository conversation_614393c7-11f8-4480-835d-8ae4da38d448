[["4exAdQDmwJMpRhQYMcGS85", {"root": "5592001", "map": {"138901": {"rows": [{"__ref": "138902"}], "__type": "ArenaFrameGrid"}, "138902": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "743001": {"name": "customFunction", "expr": {"__ref": "743002"}, "__type": "NameArg"}, "743002": {"argNames": [], "bodyExpr": {"__ref": "1567502"}, "__type": "FunctionExpr"}, "1499901": {"type": {"__ref": "1499903"}, "variable": {"__ref": "1499902"}, "uuid": "f_Q741FZoT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "1499902": {"name": "submitsForm", "uuid": "fjAYMm7bg", "__type": "Var"}, "1499903": {"name": "bool", "__type": "BoolType"}, "1499904": {"param": {"__ref": "1499901"}, "expr": {"__ref": "1499905"}, "__type": "Arg"}, "1499905": {"code": "true", "fallback": null, "__type": "CustomCode"}, "1499906": {"param": {"__ref": "1499901"}, "expr": {"__ref": "1499907"}, "__type": "Arg"}, "1499907": {"code": "true", "fallback": null, "__type": "CustomCode"}, "1499908": {"param": {"__ref": "1499901"}, "expr": {"__ref": "1499909"}, "__type": "Arg"}, "1499909": {"code": "true", "fallback": null, "__type": "CustomCode"}, "1499910": {"param": {"__ref": "1499901"}, "expr": {"__ref": "1499911"}, "__type": "Arg"}, "1499911": {"code": "true", "fallback": null, "__type": "CustomCode"}, "1556008": {"param": {"__ref": "5592080"}, "expr": {"__ref": "1556011"}, "__type": "Arg"}, "1556011": {"path": ["<PERSON><PERSON><PERSON>"], "fallback": {"__ref": "1556012"}, "__type": "ObjectPath"}, "1556012": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "1556016": {"code": "0", "fallback": null, "__type": "CustomCode"}, "1567501": {"code": "($state.nicknames.splice(currentIndex, 1))", "fallback": null, "__type": "CustomCode"}, "1567502": {"code": "($state.nicknames[currentIndex] = event.target.value)", "fallback": null, "__type": "CustomCode"}, "1567503": {"code": "($state.people[currentPersonIndex] = val)", "fallback": null, "__type": "CustomCode"}, "5592001": {"components": [{"__ref": "5592002"}, {"__ref": "5592022"}, {"__ref": "5592050"}, {"__ref": "5592073"}, {"__ref": "5592101"}, {"__ref": "5592115"}, {"__ref": "5592486"}, {"__ref": "5592543"}], "arenas": [], "pageArenas": [{"__ref": "5593742"}], "componentArenas": [{"__ref": "5593782"}, {"__ref": "5593792"}, {"__ref": "5593802"}, {"__ref": "5593851"}, {"__ref": "5593861"}], "globalVariantGroups": [{"__ref": "5594147"}], "userManagedFonts": [], "globalVariant": {"__ref": "5593749"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "5594151"}], "activeTheme": {"__ref": "5594151"}, "imageAssets": [{"__ref": "5592170"}, {"__ref": "5592273"}, {"__ref": "5592776"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "5594147"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"text-input": {"__ref": "5592115"}, "button": {"__ref": "5592543"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "5592002": {"uuid": "aR3E6ALK_p", "name": "hostless-plasmic-head", "params": [{"__ref": "5592003"}, {"__ref": "5592006"}, {"__ref": "5592009"}, {"__ref": "5592012"}], "states": [], "tplTree": {"__ref": "5592015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592017"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "5592021"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592003": {"type": {"__ref": "5592005"}, "variable": {"__ref": "5592004"}, "uuid": "-UJ8ot0EmV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592004": {"name": "title", "uuid": "nP1xCP63wq", "__type": "Var"}, "5592005": {"name": "text", "__type": "Text"}, "5592006": {"type": {"__ref": "5592008"}, "variable": {"__ref": "5592007"}, "uuid": "QOIQVGCmgi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592007": {"name": "description", "uuid": "rvCdYnqEnr", "__type": "Var"}, "5592008": {"name": "text", "__type": "Text"}, "5592009": {"type": {"__ref": "5592011"}, "variable": {"__ref": "5592010"}, "uuid": "pMOyHk0Ev1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592010": {"name": "image", "uuid": "YIPrYYaZIU", "__type": "Var"}, "5592011": {"name": "img", "__type": "Img"}, "5592012": {"type": {"__ref": "5592014"}, "variable": {"__ref": "5592013"}, "uuid": "hKby98JVN8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592013": {"name": "canonical", "uuid": "My1T-xO7xv", "__type": "Var"}, "5592014": {"name": "text", "__type": "Text"}, "5592015": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "e5uKtfqAG1", "parent": null, "locked": null, "vsettings": [{"__ref": "5592016"}], "__type": "TplTag"}, "5592016": {"variants": [{"__ref": "5592017"}], "args": [], "attrs": {}, "rs": {"__ref": "5592018"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592017": {"uuid": "HnKhYnCp-M", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592018": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "5592021": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "5592022": {"uuid": "2hnzR1_1GXX", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "5592023"}, {"__ref": "5592026"}, {"__ref": "5592029"}, {"__ref": "5592034"}, {"__ref": "5592037"}], "states": [], "tplTree": {"__ref": "5592040"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592043"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "5592049"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592023": {"type": {"__ref": "5592025"}, "variable": {"__ref": "5592024"}, "uuid": "8GDwK_kAXc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592024": {"name": "dataOp", "uuid": "n-1Od3dN2v", "__type": "Var"}, "5592025": {"name": "any", "__type": "AnyType"}, "5592026": {"type": {"__ref": "5592028"}, "variable": {"__ref": "5592027"}, "uuid": "FYskZRAtCD", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592027": {"name": "name", "uuid": "Fpci0Xh0OQ", "__type": "Var"}, "5592028": {"name": "text", "__type": "Text"}, "5592029": {"type": {"__ref": "5592031"}, "tplSlot": {"__ref": "5592041"}, "variable": {"__ref": "5592030"}, "uuid": "PXjhs3daS9C", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5592030": {"name": "children", "uuid": "fHvCK40OLJM", "__type": "Var"}, "5592031": {"name": "renderFunc", "params": [{"__ref": "5592032"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "5592032": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "5592033"}, "__type": "ArgType"}, "5592033": {"name": "any", "__type": "AnyType"}, "5592034": {"type": {"__ref": "5592036"}, "variable": {"__ref": "5592035"}, "uuid": "yW_7DzlgudG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592035": {"name": "pageSize", "uuid": "AbwlJpIY8Fn", "__type": "Var"}, "5592036": {"name": "num", "__type": "<PERSON><PERSON>"}, "5592037": {"type": {"__ref": "5592039"}, "variable": {"__ref": "5592038"}, "uuid": "r-Jo-DIN3zi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592038": {"name": "pageIndex", "uuid": "TeHcJJKvE9Q", "__type": "Var"}, "5592039": {"name": "num", "__type": "<PERSON><PERSON>"}, "5592040": {"tag": "div", "name": null, "children": [{"__ref": "5592041"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uFf0ozwlgzC", "parent": null, "locked": null, "vsettings": [{"__ref": "5592045"}], "__type": "TplTag"}, "5592041": {"param": {"__ref": "5592029"}, "defaultContents": [], "uuid": "ISYQwPjVG1l", "parent": {"__ref": "5592040"}, "locked": null, "vsettings": [{"__ref": "5592042"}], "__type": "TplSlot"}, "5592042": {"variants": [{"__ref": "5592043"}], "args": [], "attrs": {}, "rs": {"__ref": "5592044"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592043": {"uuid": "oCd9uOcjp3P", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592044": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592045": {"variants": [{"__ref": "5592043"}], "args": [], "attrs": {}, "rs": {"__ref": "5592046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592046": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "5592049": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Source Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "5592050": {"uuid": "qy36JdSFn7Z", "name": "Homepage", "params": [{"__ref": "5592051"}, {"__ref": "34119009"}, {"__ref": "56253005"}, {"__ref": "56253080"}], "states": [{"__ref": "5592055"}, {"__ref": "34119008"}], "tplTree": {"__ref": "5592056"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592059"}], "variantGroups": [], "pageMeta": {"__ref": "5593380"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592051": {"type": {"__ref": "5592053"}, "state": {"__ref": "5592055"}, "variable": {"__ref": "5592052"}, "uuid": "lxjJF3RpSU5", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "28486026"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592052": {"name": "people", "uuid": "DOBlN1PU8bq", "__type": "Var"}, "5592053": {"name": "any", "__type": "AnyType"}, "5592055": {"param": {"__ref": "5592051"}, "accessType": "private", "variableType": "array", "onChangeParam": {"__ref": "56253005"}, "tplNode": null, "implicitState": null, "__type": "State"}, "5592056": {"tag": "div", "name": null, "children": [{"__ref": "5592057"}, {"__ref": "5592063"}, {"__ref": "5592071"}, {"__ref": "5593329"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "F6k2ksQNHF9", "parent": null, "locked": null, "vsettings": [{"__ref": "5593370"}], "__type": "TplTag"}, "5592057": {"tag": "h2", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "M5ZTngfBC3d", "parent": {"__ref": "5592056"}, "locked": null, "vsettings": [{"__ref": "5592058"}], "__type": "TplTag"}, "5592058": {"variants": [{"__ref": "5592059"}], "args": [], "attrs": {}, "rs": {"__ref": "5592060"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5592062"}, "columnsConfig": null, "__type": "VariantSetting"}, "5592059": {"uuid": "qXlZnnVu4QO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592060": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5592062": {"markers": [], "text": "People List", "__type": "RawText"}, "5592063": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "MZ2hkKLsQSq", "parent": {"__ref": "5592056"}, "locked": null, "vsettings": [{"__ref": "5592064"}], "__type": "TplTag"}, "5592064": {"variants": [{"__ref": "5592059"}], "args": [], "attrs": {"data-testid": {"__ref": "22485008"}}, "rs": {"__ref": "5592065"}, "dataCond": null, "dataRep": null, "text": {"__ref": "22485075"}, "columnsConfig": null, "__type": "VariantSetting"}, "5592065": {"values": {"position": "relative", "text-align": "center", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "5592071": {"tag": "div", "name": null, "children": [{"__ref": "5592072"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_oJOYTXKVFN", "parent": {"__ref": "5592056"}, "locked": null, "vsettings": [{"__ref": "5593299"}], "__type": "TplTag"}, "5592072": {"name": "Person", "component": {"__ref": "5592073"}, "uuid": "ACZ9Xk4maZe", "parent": {"__ref": "5592071"}, "locked": null, "vsettings": [{"__ref": "5593280"}], "__type": "TplComponent"}, "5592073": {"uuid": "L3SmigHKeor", "name": "Person", "params": [{"__ref": "5592074"}, {"__ref": "5592077"}, {"__ref": "5592080"}, {"__ref": "5592084"}, {"__ref": "5592096"}, {"__ref": "34119010"}, {"__ref": "56253010"}, {"__ref": "56253070"}, {"__ref": "56253075"}], "states": [{"__ref": "5592099"}, {"__ref": "5593277"}, {"__ref": "5593278"}, {"__ref": "5593279"}], "tplTree": {"__ref": "5592455"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592426"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592074": {"type": {"__ref": "5592076"}, "state": {"__ref": "5592099"}, "variable": {"__ref": "5592075"}, "uuid": "5e3T4DI0hC8", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592075": {"name": "FormField undefined value", "uuid": "ESHR9B7zwLA", "__type": "Var"}, "5592076": {"name": "text", "__type": "Text"}, "5592077": {"type": {"__ref": "5592079"}, "state": {"__ref": "5593277"}, "variable": {"__ref": "5592078"}, "uuid": "9joiW7poDk5", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592078": {"name": "FormField value", "uuid": "GOuyh5ogaYk", "__type": "Var"}, "5592079": {"name": "text", "__type": "Text"}, "5592080": {"type": {"__ref": "5592082"}, "state": {"__ref": "5593278"}, "variable": {"__ref": "5592081"}, "uuid": "4pv321DWO2B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "22485069"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592081": {"name": "person", "uuid": "prJeuE5Att4", "__type": "Var"}, "5592082": {"name": "any", "__type": "AnyType"}, "5592084": {"type": {"__ref": "5592086"}, "state": {"__ref": "5593279"}, "variable": {"__ref": "5592085"}, "uuid": "A_enZRiuXh3", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "array", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592085": {"name": "Nicknames nicknames", "uuid": "ZAlrQNcDACm", "__type": "Var"}, "5592086": {"name": "any", "__type": "AnyType"}, "5592096": {"type": {"__ref": "5592098"}, "variable": {"__ref": "5592097"}, "uuid": "6ernBcrcfkV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592097": {"name": "onDelete", "uuid": "7rfqbKmWmb9", "__type": "Var"}, "5592098": {"name": "func", "params": [], "__type": "FunctionType"}, "5592099": {"param": {"__ref": "5592074"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "56253010"}, "tplNode": {"__ref": "5592100"}, "implicitState": {"__ref": "5592113"}, "__type": "State"}, "5592100": {"name": "FirstName", "component": {"__ref": "5592101"}, "uuid": "TjQcXdkS6TL", "parent": {"__ref": "5592455"}, "locked": null, "vsettings": [{"__ref": "5592425"}], "__type": "TplComponent"}, "5592101": {"uuid": "92RElD4Ym7_", "name": "FormField", "params": [{"__ref": "5592102"}, {"__ref": "5592105"}, {"__ref": "5592110"}, {"__ref": "22485013"}], "states": [{"__ref": "5592113"}], "tplTree": {"__ref": "5592396"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592370"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592102": {"type": {"__ref": "5592104"}, "state": {"__ref": "5592113"}, "variable": {"__ref": "5592103"}, "uuid": "gkwbHmLxPSv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592103": {"name": "value", "uuid": "60T8O8pUzKN", "__type": "Var"}, "5592104": {"name": "text", "__type": "Text"}, "5592105": {"type": {"__ref": "5592107"}, "state": {"__ref": "5592113"}, "variable": {"__ref": "5592106"}, "uuid": "686vIdqfVrR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "5592106": {"name": "onValueChange", "uuid": "Hkcnuvdz_Na", "__type": "Var"}, "5592107": {"name": "func", "params": [{"__ref": "5592108"}], "__type": "FunctionType"}, "5592108": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "5592109"}, "__type": "ArgType"}, "5592109": {"name": "text", "__type": "Text"}, "5592110": {"type": {"__ref": "5592112"}, "tplSlot": {"__ref": "5592398"}, "variable": {"__ref": "5592111"}, "uuid": "LzCddDXPHnk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5592111": {"name": "children", "uuid": "AX0EO5HcwZ7", "__type": "Var"}, "5592112": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "5592113": {"param": {"__ref": "5592102"}, "accessType": "writable", "variableType": "text", "onChangeParam": {"__ref": "5592105"}, "tplNode": {"__ref": "5592114"}, "implicitState": {"__ref": "5592162"}, "__type": "State"}, "5592114": {"name": "TextInput", "component": {"__ref": "5592115"}, "uuid": "Ch3IJDAlHgz", "parent": {"__ref": "5592396"}, "locked": null, "vsettings": [{"__ref": "5592369"}], "__type": "TplComponent"}, "5592115": {"uuid": "xh4qV-qNmpZ", "name": "TextInput", "params": [{"__ref": "5592116"}, {"__ref": "5592120"}, {"__ref": "5592123"}, {"__ref": "5592126"}, {"__ref": "5592129"}, {"__ref": "5592132"}, {"__ref": "5592135"}, {"__ref": "5592138"}, {"__ref": "5592141"}, {"__ref": "5592144"}, {"__ref": "5592147"}, {"__ref": "5592150"}, {"__ref": "5592153"}, {"__ref": "22485009"}, {"__ref": "56253015"}, {"__ref": "56253020"}, {"__ref": "56253025"}, {"__ref": "56253030"}], "states": [{"__ref": "5592158"}, {"__ref": "5592159"}, {"__ref": "5592160"}, {"__ref": "5592161"}, {"__ref": "5592162"}], "tplTree": {"__ref": "5592163"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592168"}, {"__ref": "5592239"}, {"__ref": "5592208"}, {"__ref": "5592256"}, {"__ref": "5592201"}, {"__ref": "5592246"}], "variantGroups": [{"__ref": "5592180"}, {"__ref": "5592283"}, {"__ref": "5592212"}, {"__ref": "5592185"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "5592368"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592116": {"type": {"__ref": "5592118"}, "variable": {"__ref": "5592117"}, "uuid": "bV9ejawXg-D", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "5592119"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592117": {"name": "placeholder", "uuid": "cAb00LZWGhW", "__type": "Var"}, "5592118": {"name": "text", "__type": "Text"}, "5592119": {"code": "\"Enter something…\"", "fallback": null, "__type": "CustomCode"}, "5592120": {"type": {"__ref": "5592122"}, "tplSlot": {"__ref": "5592269"}, "variable": {"__ref": "5592121"}, "uuid": "6ugvMPYsk1k", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5592121": {"name": "end icon", "uuid": "lPgi-o0jciw", "__type": "Var"}, "5592122": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "5592123": {"type": {"__ref": "5592125"}, "tplSlot": {"__ref": "5592165"}, "variable": {"__ref": "5592124"}, "uuid": "zszly4xo8X2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5592124": {"name": "start icon", "uuid": "2yZzRkEsVa1", "__type": "Var"}, "5592125": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "5592126": {"type": {"__ref": "5592128"}, "state": {"__ref": "5592158"}, "variable": {"__ref": "5592127"}, "uuid": "UsKgFcfI2G8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592127": {"name": "Show Start Icon", "uuid": "CJH0rJKppxA", "__type": "Var"}, "5592128": {"name": "any", "__type": "AnyType"}, "5592129": {"type": {"__ref": "5592131"}, "state": {"__ref": "5592159"}, "variable": {"__ref": "5592130"}, "uuid": "Rf4kVx4M_4J", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592130": {"name": "Show End Icon", "uuid": "z9k1RbQL3x7", "__type": "Var"}, "5592131": {"name": "any", "__type": "AnyType"}, "5592132": {"type": {"__ref": "5592134"}, "state": {"__ref": "5592160"}, "variable": {"__ref": "5592133"}, "uuid": "GGEaUVlqceX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592133": {"name": "Is Disabled", "uuid": "h2m6bgxbylc", "__type": "Var"}, "5592134": {"name": "any", "__type": "AnyType"}, "5592135": {"type": {"__ref": "5592137"}, "state": {"__ref": "5592161"}, "variable": {"__ref": "5592136"}, "uuid": "W2uR4XLlxxH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592136": {"name": "Color", "uuid": "1SPsr4iMRPH", "__type": "Var"}, "5592137": {"name": "any", "__type": "AnyType"}, "5592138": {"type": {"__ref": "26472002"}, "state": {"__ref": "5592162"}, "variable": {"__ref": "5592139"}, "uuid": "KC8_MtWUxm_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "26472001"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592139": {"name": "value", "uuid": "JlGlZAdoTvJ", "__type": "Var"}, "5592141": {"type": {"__ref": "5592143"}, "variable": {"__ref": "5592142"}, "uuid": "7TBiHbOVet8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592142": {"name": "name", "uuid": "CM65i8L5Oy-", "__type": "Var"}, "5592143": {"name": "text", "__type": "Text"}, "5592144": {"type": {"__ref": "5592146"}, "variable": {"__ref": "5592145"}, "uuid": "bPzX9OAH_pt", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592145": {"name": "required", "uuid": "xujhMFo7lrq", "__type": "Var"}, "5592146": {"name": "bool", "__type": "BoolType"}, "5592147": {"type": {"__ref": "5592149"}, "variable": {"__ref": "5592148"}, "uuid": "NWOQFnwFt0f", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592148": {"name": "aria-label", "uuid": "HBJBYQGvVTn", "__type": "Var"}, "5592149": {"name": "text", "__type": "Text"}, "5592150": {"type": {"__ref": "5592152"}, "variable": {"__ref": "5592151"}, "uuid": "C7fjR03kjdr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592151": {"name": "aria-<PERSON>by", "uuid": "xAsOO65wVDi", "__type": "Var"}, "5592152": {"name": "text", "__type": "Text"}, "5592153": {"type": {"__ref": "42268002"}, "variable": {"__ref": "5592154"}, "uuid": "nOITCk_YExj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592154": {"name": "onChange", "uuid": "5hRGQecWPP8", "__type": "Var"}, "5592158": {"variantGroup": {"__ref": "5592180"}, "param": {"__ref": "5592126"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253015"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592159": {"variantGroup": {"__ref": "5592283"}, "param": {"__ref": "5592129"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253020"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592160": {"variantGroup": {"__ref": "5592212"}, "param": {"__ref": "5592132"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253025"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592161": {"variantGroup": {"__ref": "5592185"}, "param": {"__ref": "5592135"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253030"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592162": {"param": {"__ref": "5592138"}, "accessType": "writable", "variableType": "text", "onChangeParam": {"__ref": "5592153"}, "tplNode": null, "implicitState": null, "__type": "State"}, "5592163": {"tag": "div", "name": null, "children": [{"__ref": "5592164"}, {"__ref": "5592216"}, {"__ref": "5592268"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "hWZYVmGKtmc", "parent": null, "locked": null, "vsettings": [{"__ref": "5592307"}, {"__ref": "5592337"}, {"__ref": "5592343"}, {"__ref": "5592350"}, {"__ref": "5592357"}, {"__ref": "5592359"}, {"__ref": "5592361"}], "__type": "TplTag"}, "5592164": {"tag": "div", "name": "start icon container", "children": [{"__ref": "5592165"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "A3jF6bpSVBT", "parent": {"__ref": "5592163"}, "locked": null, "vsettings": [{"__ref": "5592188"}, {"__ref": "5592200"}, {"__ref": "5592203"}, {"__ref": "5592207"}, {"__ref": "5592210"}, {"__ref": "5592214"}], "__type": "TplTag"}, "5592165": {"param": {"__ref": "5592123"}, "defaultContents": [{"__ref": "5592166"}], "uuid": "NhOpNSuoAl2", "parent": {"__ref": "5592164"}, "locked": null, "vsettings": [{"__ref": "5592176"}, {"__ref": "5592178"}, {"__ref": "5592183"}], "__type": "TplSlot"}, "5592166": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "O17BwHJP64p", "parent": {"__ref": "5592165"}, "locked": null, "vsettings": [{"__ref": "5592167"}], "__type": "TplTag"}, "5592167": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592169"}}, "rs": {"__ref": "5592171"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592168": {"uuid": "U-0JELAgnHn", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592169": {"asset": {"__ref": "5592170"}, "__type": "ImageAssetRef"}, "5592170": {"uuid": "2TBrrOwIjf9d", "name": "search.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iPgogIDxwYXRoIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS41IiBkPSJNMTkuMjUgMTkuMjVMMTUuNSAxNS41TTQuNzUgMTFhNi4yNSA2LjI1IDAgMTExMi41IDAgNi4yNSA2LjI1IDAgMDEtMTIuNSAweiIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": null, "__type": "ImageAsset"}, "5592171": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592176": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {}, "rs": {"__ref": "5592177"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592177": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592178": {"variants": [{"__ref": "5592179"}], "args": [], "attrs": {}, "rs": {"__ref": "5592181"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592179": {"uuid": "y89bRYK0Mjm", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592180"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592180": {"type": "component", "param": {"__ref": "5592126"}, "linkedState": {"__ref": "5592158"}, "uuid": "mjSsE6Clrp4", "variants": [{"__ref": "5592179"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592181": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "5592183": {"variants": [{"__ref": "5592184"}], "args": [], "attrs": {}, "rs": {"__ref": "5592186"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592184": {"uuid": "uezrh4gSIeb", "name": "Dark", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592185"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592185": {"type": "component", "param": {"__ref": "5592135"}, "linkedState": {"__ref": "5592161"}, "uuid": "dV4pKzjXixS", "variants": [{"__ref": "5592184"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592186": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "5592188": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {}, "rs": {"__ref": "5592189"}, "dataCond": {"__ref": "5592199"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592189": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-right": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "5592199": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592200": {"variants": [{"__ref": "5592201"}], "args": [], "attrs": {}, "rs": {"__ref": "5592202"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592201": {"uuid": "eQ_chuoSLgG", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592202": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592203": {"variants": [{"__ref": "5592179"}], "args": [], "attrs": {}, "rs": {"__ref": "5592204"}, "dataCond": {"__ref": "5592206"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592204": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "5592206": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592207": {"variants": [{"__ref": "5592208"}], "args": [], "attrs": {}, "rs": {"__ref": "5592209"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592208": {"uuid": "oTGRI-qXWBD", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592209": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592210": {"variants": [{"__ref": "5592211"}], "args": [], "attrs": {}, "rs": {"__ref": "5592213"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592211": {"uuid": "bJCMTvd_O5q", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592212"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592212": {"type": "component", "param": {"__ref": "5592132"}, "linkedState": {"__ref": "5592160"}, "uuid": "DoXpJlsCH3n", "variants": [{"__ref": "5592211"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592213": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592214": {"variants": [{"__ref": "5592184"}], "args": [], "attrs": {}, "rs": {"__ref": "5592215"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592215": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592216": {"tag": "input", "name": "input", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fRw5Odh5Y-S", "parent": {"__ref": "5592163"}, "locked": null, "vsettings": [{"__ref": "5592217"}, {"__ref": "5592238"}, {"__ref": "5592241"}, {"__ref": "5592245"}, {"__ref": "5592249"}, {"__ref": "5592251"}, {"__ref": "5592253"}, {"__ref": "5592255"}, {"__ref": "5592258"}, {"__ref": "5592260"}, {"__ref": "5592262"}, {"__ref": "5592265"}], "__type": "TplTag"}, "5592217": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {"type": {"__ref": "5592218"}, "placeholder": {"__ref": "5592219"}, "value": {"__ref": "5592220"}, "name": {"__ref": "5592221"}, "aria-label": {"__ref": "5592222"}, "aria-labelledby": {"__ref": "5592223"}, "required": {"__ref": "5592224"}, "data-testid": {"__ref": "22485010"}}, "rs": {"__ref": "5592225"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592218": {"code": "\"text\"", "fallback": null, "__type": "CustomCode"}, "5592219": {"variable": {"__ref": "5592117"}, "__type": "VarRef"}, "5592220": {"variable": {"__ref": "5592139"}, "__type": "VarRef"}, "5592221": {"variable": {"__ref": "5592142"}, "__type": "VarRef"}, "5592222": {"variable": {"__ref": "5592148"}, "__type": "VarRef"}, "5592223": {"variable": {"__ref": "5592151"}, "__type": "VarRef"}, "5592224": {"variable": {"__ref": "5592145"}, "__type": "VarRef"}, "5592225": {"values": {"width": "stretch", "left": "auto", "top": "auto", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "5592238": {"variants": [{"__ref": "5592239"}], "args": [], "attrs": {}, "rs": {"__ref": "5592240"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592239": {"uuid": "On1oTQNQoS8", "name": "", "selectors": [":focus"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "5592216"}, "__type": "<PERSON><PERSON><PERSON>"}, "5592240": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592241": {"variants": [{"__ref": "5592211"}], "args": [], "attrs": {"disabled": {"__ref": "5592242"}}, "rs": {"__ref": "5592243"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592242": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592243": {"values": {"cursor": "not-allowed"}, "mixins": [], "__type": "RuleSet"}, "5592245": {"variants": [{"__ref": "5592246"}], "args": [], "attrs": {}, "rs": {"__ref": "5592247"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592246": {"uuid": "OiKprhjCGJt", "name": "", "selectors": ["::placeholder"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "5592216"}, "__type": "<PERSON><PERSON><PERSON>"}, "5592247": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "5592249": {"variants": [{"__ref": "5592179"}], "args": [], "attrs": {}, "rs": {"__ref": "5592250"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592250": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592251": {"variants": [{"__ref": "5592208"}], "args": [], "attrs": {}, "rs": {"__ref": "5592252"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592252": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592253": {"variants": [{"__ref": "5592201"}], "args": [], "attrs": {}, "rs": {"__ref": "5592254"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592254": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592255": {"variants": [{"__ref": "5592256"}], "args": [], "attrs": {}, "rs": {"__ref": "5592257"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592256": {"uuid": "7FXFF8sQ4Y8", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592257": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592258": {"variants": [{"__ref": "5592256"}, {"__ref": "5592239"}], "args": [], "attrs": {}, "rs": {"__ref": "5592259"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592259": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592260": {"variants": [{"__ref": "5592256"}, {"__ref": "5592246"}], "args": [], "attrs": {}, "rs": {"__ref": "5592261"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592261": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592262": {"variants": [{"__ref": "5592184"}], "args": [], "attrs": {}, "rs": {"__ref": "5592263"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592263": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "5592265": {"variants": [{"__ref": "5592184"}, {"__ref": "5592246"}], "args": [], "attrs": {}, "rs": {"__ref": "5592266"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592266": {"values": {"color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "5592268": {"tag": "div", "name": "end icon container", "children": [{"__ref": "5592269"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "H1yTpdZPU8k", "parent": {"__ref": "5592163"}, "locked": null, "vsettings": [{"__ref": "5592289"}, {"__ref": "5592301"}, {"__ref": "5592305"}], "__type": "TplTag"}, "5592269": {"param": {"__ref": "5592120"}, "defaultContents": [{"__ref": "5592270"}], "uuid": "yTaNl1UpYsH", "parent": {"__ref": "5592268"}, "locked": null, "vsettings": [{"__ref": "5592279"}, {"__ref": "5592281"}, {"__ref": "5592286"}], "__type": "TplSlot"}, "5592270": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "sQIOD4MDfHz", "parent": {"__ref": "5592269"}, "locked": null, "vsettings": [{"__ref": "5592271"}], "__type": "TplTag"}, "5592271": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592272"}}, "rs": {"__ref": "5592274"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592272": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5592273": {"uuid": "EPHq7HNAJxPY", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "5592274": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592279": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {}, "rs": {"__ref": "5592280"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592280": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592281": {"variants": [{"__ref": "5592282"}], "args": [], "attrs": {}, "rs": {"__ref": "5592284"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592282": {"uuid": "H_jv1ZwDL8Z", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592283"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592283": {"type": "component", "param": {"__ref": "5592129"}, "linkedState": {"__ref": "5592159"}, "uuid": "6-TqSElppJl", "variants": [{"__ref": "5592282"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592284": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "5592286": {"variants": [{"__ref": "5592184"}], "args": [], "attrs": {}, "rs": {"__ref": "5592287"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592287": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "5592289": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {}, "rs": {"__ref": "5592290"}, "dataCond": {"__ref": "5592300"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592290": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-left": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "5592300": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592301": {"variants": [{"__ref": "5592282"}], "args": [], "attrs": {}, "rs": {"__ref": "5592302"}, "dataCond": {"__ref": "5592304"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592302": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "5592304": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592305": {"variants": [{"__ref": "5592184"}], "args": [], "attrs": {}, "rs": {"__ref": "5592306"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592306": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592307": {"variants": [{"__ref": "5592168"}], "args": [], "attrs": {}, "rs": {"__ref": "5592308"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592308": {"values": {"display": "flex", "flex-direction": "row", "width": "stretch", "height": "wrap", "align-items": "center", "justify-content": "flex-start", "border-top-color": "#DBDBD7", "border-right-color": "#DBDBD7", "border-bottom-color": "#DBDBD7", "border-left-color": "#DBDBD7", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "background": "linear-gradient(#FFFFFF, #FFFFFF)", "position": "sticky", "padding-top": "7px", "padding-right": "11px", "padding-bottom": "7px", "padding-left": "11px"}, "mixins": [], "__type": "RuleSet"}, "5592337": {"variants": [{"__ref": "5592208"}], "args": [], "attrs": {}, "rs": {"__ref": "5592338"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592338": {"values": {"border-top-color": "#C8C7C1", "border-right-color": "#C8C7C1", "border-bottom-color": "#C8C7C1", "border-left-color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "5592343": {"variants": [{"__ref": "5592256"}], "args": [], "attrs": {}, "rs": {"__ref": "5592344"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592344": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "5592350": {"variants": [{"__ref": "5592201"}], "args": [], "attrs": {}, "rs": {"__ref": "5592351"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592351": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "5592357": {"variants": [{"__ref": "5592211"}], "args": [], "attrs": {}, "rs": {"__ref": "5592358"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592358": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592359": {"variants": [{"__ref": "5592179"}], "args": [], "attrs": {}, "rs": {"__ref": "5592360"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592360": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592361": {"variants": [{"__ref": "5592184"}], "args": [], "attrs": {}, "rs": {"__ref": "5592362"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592362": {"values": {"background": "linear-gradient(#232320, #232320)", "border-top-color": "#717069", "border-right-color": "#717069", "border-bottom-color": "#717069", "border-left-color": "#717069"}, "mixins": [], "__type": "RuleSet"}, "5592368": {"type": "text-input", "__type": "PlumeInfo"}, "5592369": {"variants": [{"__ref": "5592370"}], "args": [{"__ref": "5592371"}, {"__ref": "5592373"}, {"__ref": "5592383"}, {"__ref": "22485014"}], "attrs": {}, "rs": {"__ref": "5592393"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592370": {"uuid": "dfVYsYqBm-c", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592371": {"param": {"__ref": "5592138"}, "expr": {"__ref": "5592372"}, "__type": "Arg"}, "5592372": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "5592373": {"param": {"__ref": "5592123"}, "expr": {"__ref": "5592374"}, "__type": "Arg"}, "5592374": {"tpl": [{"__ref": "5592375"}], "__type": "VirtualRenderExpr"}, "5592375": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "heTkC3jIy7N", "parent": {"__ref": "5592114"}, "locked": null, "vsettings": [{"__ref": "5592376"}], "__type": "TplTag"}, "5592376": {"variants": [{"__ref": "5592370"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592377"}}, "rs": {"__ref": "5592378"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592377": {"asset": {"__ref": "5592170"}, "__type": "ImageAssetRef"}, "5592378": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592383": {"param": {"__ref": "5592120"}, "expr": {"__ref": "5592384"}, "__type": "Arg"}, "5592384": {"tpl": [{"__ref": "5592385"}], "__type": "VirtualRenderExpr"}, "5592385": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "kClZZ_b9Iyk", "parent": {"__ref": "5592114"}, "locked": null, "vsettings": [{"__ref": "5592386"}], "__type": "TplTag"}, "5592386": {"variants": [{"__ref": "5592370"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592387"}}, "rs": {"__ref": "5592388"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592387": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5592388": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592393": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5592396": {"tag": "div", "name": null, "children": [{"__ref": "5592397"}, {"__ref": "5592114"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3xRC7lj2OiX", "parent": null, "locked": null, "vsettings": [{"__ref": "5592415"}], "__type": "TplTag"}, "5592397": {"tag": "div", "name": null, "children": [{"__ref": "5592398"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "so56Rby0Y_U", "parent": {"__ref": "5592396"}, "locked": null, "vsettings": [{"__ref": "5592405"}], "__type": "TplTag"}, "5592398": {"param": {"__ref": "5592110"}, "defaultContents": [{"__ref": "5592399"}], "uuid": "RW_GpOEgeie", "parent": {"__ref": "5592397"}, "locked": null, "vsettings": [{"__ref": "5592403"}], "__type": "TplSlot"}, "5592399": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "x0GeTFcGUJg", "parent": {"__ref": "5592398"}, "locked": null, "vsettings": [{"__ref": "5592400"}], "__type": "TplTag"}, "5592400": {"variants": [{"__ref": "5592370"}], "args": [], "attrs": {}, "rs": {"__ref": "5592401"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5592402"}, "columnsConfig": null, "__type": "VariantSetting"}, "5592401": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592402": {"markers": [], "text": "Label", "__type": "RawText"}, "5592403": {"variants": [{"__ref": "5592370"}], "args": [], "attrs": {}, "rs": {"__ref": "5592404"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592404": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592405": {"variants": [{"__ref": "5592370"}], "args": [], "attrs": {}, "rs": {"__ref": "5592406"}, "dataCond": {"__ref": "5592414"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592406": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "5592414": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592415": {"variants": [{"__ref": "5592370"}], "args": [], "attrs": {}, "rs": {"__ref": "5592416"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592416": {"values": {"display": "flex", "flex-direction": "row", "position": "relative", "width": "wrap", "height": "wrap", "align-items": "center", "justify-content": "flex-start", "flex-column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "5592425": {"variants": [{"__ref": "5592426"}], "args": [{"__ref": "5592427"}, {"__ref": "5592433"}, {"__ref": "5592436"}, {"__ref": "22485018"}], "attrs": {}, "rs": {"__ref": "5592452"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592426": {"uuid": "V9ZOCISoxmF", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592427": {"param": {"__ref": "5592110"}, "expr": {"__ref": "5592428"}, "__type": "Arg"}, "5592428": {"tpl": [{"__ref": "5592429"}], "__type": "RenderExpr"}, "5592429": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "R8P4Giz5ehm", "parent": {"__ref": "5592100"}, "locked": null, "vsettings": [{"__ref": "5592430"}], "__type": "TplTag"}, "5592430": {"variants": [{"__ref": "5592426"}], "args": [], "attrs": {}, "rs": {"__ref": "5592431"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5592432"}, "columnsConfig": null, "__type": "VariantSetting"}, "5592431": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592432": {"markers": [], "text": "First Name", "__type": "RawText"}, "5592433": {"param": {"__ref": "5592102"}, "expr": {"__ref": "5592434"}, "__type": "Arg"}, "5592434": {"path": ["$state", "person", "firstName"], "fallback": {"__ref": "5592435"}, "__type": "ObjectPath"}, "5592435": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "5592436": {"param": {"__ref": "5592105"}, "expr": {"__ref": "5592437"}, "__type": "Arg"}, "5592437": {"interactions": [{"__ref": "5592438"}], "__type": "EventHandler"}, "5592438": {"interactionName": "Set person ▸ firstName", "actionName": "updateVariable", "args": [{"__ref": "28486004"}, {"__ref": "28486005"}, {"__ref": "28486006"}], "condExpr": null, "conditionalMode": "always", "uuid": "G3VUkFt75Yv", "parent": {"__ref": "5592437"}, "__type": "Interaction"}, "5592452": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5592455": {"tag": "div", "name": null, "children": [{"__ref": "5592100"}, {"__ref": "5592456"}, {"__ref": "5592485"}, {"__ref": "5593216"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "MvcLFMv7pCZ", "parent": null, "locked": null, "vsettings": [{"__ref": "5593269"}], "__type": "TplTag"}, "5592456": {"name": "LastName", "component": {"__ref": "5592101"}, "uuid": "yMZkmnQSQ1o", "parent": {"__ref": "5592455"}, "locked": null, "vsettings": [{"__ref": "5592457"}], "__type": "TplComponent"}, "5592457": {"variants": [{"__ref": "5592426"}], "args": [{"__ref": "5592458"}, {"__ref": "5592460"}, {"__ref": "5592466"}, {"__ref": "22485020"}], "attrs": {}, "rs": {"__ref": "5592482"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592458": {"param": {"__ref": "5592102"}, "expr": {"__ref": "5592459"}, "__type": "Arg"}, "5592459": {"path": ["$state", "person", "lastName"], "fallback": null, "__type": "ObjectPath"}, "5592460": {"param": {"__ref": "5592110"}, "expr": {"__ref": "5592461"}, "__type": "Arg"}, "5592461": {"tpl": [{"__ref": "5592462"}], "__type": "RenderExpr"}, "5592462": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Fh5XcoTxgCC", "parent": {"__ref": "5592456"}, "locked": null, "vsettings": [{"__ref": "5592463"}], "__type": "TplTag"}, "5592463": {"variants": [{"__ref": "5592426"}], "args": [], "attrs": {}, "rs": {"__ref": "5592464"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5592465"}, "columnsConfig": null, "__type": "VariantSetting"}, "5592464": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592465": {"markers": [], "text": "Last Name", "__type": "RawText"}, "5592466": {"param": {"__ref": "5592105"}, "expr": {"__ref": "5592467"}, "__type": "Arg"}, "5592467": {"interactions": [{"__ref": "5592468"}], "__type": "EventHandler"}, "5592468": {"interactionName": "Set person ▸ lastName", "actionName": "updateVariable", "args": [{"__ref": "5592469"}, {"__ref": "5592471"}, {"__ref": "5592473"}], "condExpr": null, "conditionalMode": "always", "uuid": "aaMdOx9ZUd0", "parent": {"__ref": "5592467"}, "__type": "Interaction"}, "5592469": {"name": "variable", "expr": {"__ref": "5592470"}, "__type": "NameArg"}, "5592470": {"path": ["$state", "person", "lastName"], "fallback": null, "__type": "ObjectPath"}, "5592471": {"name": "operation", "expr": {"__ref": "5592472"}, "__type": "NameArg"}, "5592472": {"code": "0", "fallback": null, "__type": "CustomCode"}, "5592473": {"name": "value", "expr": {"__ref": "5592474"}, "__type": "NameArg"}, "5592474": {"path": ["val"], "fallback": null, "__type": "ObjectPath"}, "5592482": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5592485": {"name": "Nicknames", "component": {"__ref": "5592486"}, "uuid": "JHJiGIy5jVM", "parent": {"__ref": "5592455"}, "locked": null, "vsettings": [{"__ref": "5593193"}], "__type": "TplComponent"}, "5592486": {"uuid": "3vQSuoE8sua", "name": "Nicknames", "params": [{"__ref": "5592487"}, {"__ref": "5592490"}, {"__ref": "5592494"}, {"__ref": "56253035"}], "states": [{"__ref": "5592499"}, {"__ref": "5593191"}], "tplTree": {"__ref": "5593174"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592502"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [{"__ref": "5593192"}], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592487": {"type": {"__ref": "5592489"}, "state": {"__ref": "5592499"}, "variable": {"__ref": "5592488"}, "uuid": "BHrsnHXuFDA", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592488": {"name": "undefined value", "uuid": "ZZFU2IBpGtA", "__type": "Var"}, "5592489": {"name": "text", "__type": "Text"}, "5592490": {"type": {"__ref": "5592492"}, "state": {"__ref": "5593191"}, "variable": {"__ref": "5592491"}, "uuid": "t52he216-kY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "5592493"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592491": {"name": "nicknames", "uuid": "Apj4NjwvZCN", "__type": "Var"}, "5592492": {"name": "any", "__type": "AnyType"}, "5592493": {"code": "[\"A\",\"B\",\"C\"]", "fallback": null, "__type": "CustomCode"}, "5592494": {"type": {"__ref": "5592496"}, "state": {"__ref": "5593191"}, "variable": {"__ref": "5592495"}, "uuid": "5ur7AwF50SE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "5592495": {"name": "On nicknames change", "uuid": "t8r7yzhRL7c", "__type": "Var"}, "5592496": {"name": "func", "params": [{"__ref": "5592497"}], "__type": "FunctionType"}, "5592497": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "5592498"}, "__type": "ArgType"}, "5592498": {"name": "any", "__type": "AnyType"}, "5592499": {"param": {"__ref": "5592487"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "56253035"}, "tplNode": {"__ref": "5592500"}, "implicitState": {"__ref": "5592162"}, "__type": "State"}, "5592500": {"name": "TextInput", "component": {"__ref": "5592115"}, "uuid": "fhQI9XQPg6N", "parent": {"__ref": "5592541"}, "locked": null, "vsettings": [{"__ref": "5592501"}], "__type": "TplComponent"}, "5592501": {"variants": [{"__ref": "5592502"}], "args": [{"__ref": "5592503"}, {"__ref": "5592506"}, {"__ref": "5592516"}, {"__ref": "5592526"}, {"__ref": "22485038"}], "attrs": {}, "rs": {"__ref": "5592539"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592502": {"uuid": "8lHBacJ76oL", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592503": {"param": {"__ref": "5592138"}, "expr": {"__ref": "5592504"}, "__type": "Arg"}, "5592504": {"path": ["currentItem"], "fallback": {"__ref": "5592505"}, "__type": "ObjectPath"}, "5592505": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "5592506": {"param": {"__ref": "5592123"}, "expr": {"__ref": "5592507"}, "__type": "Arg"}, "5592507": {"tpl": [{"__ref": "5592508"}], "__type": "VirtualRenderExpr"}, "5592508": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "POWbF_mzmmB", "parent": {"__ref": "5592500"}, "locked": null, "vsettings": [{"__ref": "5592509"}], "__type": "TplTag"}, "5592509": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592510"}}, "rs": {"__ref": "5592511"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592510": {"asset": {"__ref": "5592170"}, "__type": "ImageAssetRef"}, "5592511": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592516": {"param": {"__ref": "5592120"}, "expr": {"__ref": "5592517"}, "__type": "Arg"}, "5592517": {"tpl": [{"__ref": "5592518"}], "__type": "VirtualRenderExpr"}, "5592518": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "r7uV6BK1xKS", "parent": {"__ref": "5592500"}, "locked": null, "vsettings": [{"__ref": "5592519"}], "__type": "TplTag"}, "5592519": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592520"}}, "rs": {"__ref": "5592521"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592520": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5592521": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592526": {"param": {"__ref": "5592153"}, "expr": {"__ref": "5592527"}, "__type": "Arg"}, "5592527": {"interactions": [{"__ref": "5592528"}], "__type": "EventHandler"}, "5592528": {"interactionName": "Custom function", "actionName": "customFunction", "args": [{"__ref": "743001"}], "condExpr": null, "conditionalMode": "always", "uuid": "0eNeGYfSUXF", "parent": {"__ref": "5592527"}, "__type": "Interaction"}, "5592539": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "5592541": {"tag": "div", "name": null, "children": [{"__ref": "5592500"}, {"__ref": "5592542"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OxGsQhkfycd", "parent": {"__ref": "5593118"}, "locked": null, "vsettings": [{"__ref": "5593103"}], "__type": "TplTag"}, "5592542": {"name": null, "component": {"__ref": "5592543"}, "uuid": "QhSHUxxRuR2", "parent": {"__ref": "5592541"}, "locked": null, "vsettings": [{"__ref": "5593059"}], "__type": "TplComponent"}, "5592543": {"uuid": "ywVoAVZBESMk", "name": "<PERSON><PERSON>", "params": [{"__ref": "5592544"}, {"__ref": "5592547"}, {"__ref": "5592550"}, {"__ref": "5592553"}, {"__ref": "5592556"}, {"__ref": "5592559"}, {"__ref": "5592562"}, {"__ref": "5592565"}, {"__ref": "5592568"}, {"__ref": "5592571"}, {"__ref": "22485027"}, {"__ref": "1499901"}, {"__ref": "56253040"}, {"__ref": "56253045"}, {"__ref": "56253050"}, {"__ref": "56253055"}, {"__ref": "56253060"}, {"__ref": "56253065"}], "states": [{"__ref": "5592574"}, {"__ref": "5592575"}, {"__ref": "5592576"}, {"__ref": "5592577"}, {"__ref": "5592578"}, {"__ref": "5592579"}], "tplTree": {"__ref": "5592580"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "5592585"}, {"__ref": "5592685"}, {"__ref": "5592682"}, {"__ref": "5592637"}, {"__ref": "5592641"}], "variantGroups": [{"__ref": "5592597"}, {"__ref": "5592691"}, {"__ref": "5592695"}, {"__ref": "5592666"}, {"__ref": "5592744"}, {"__ref": "5592601"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "5593058"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5592544": {"type": {"__ref": "5592546"}, "tplSlot": {"__ref": "5592671"}, "variable": {"__ref": "5592545"}, "uuid": "BfmIf3ipdJ2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5592545": {"name": "children", "uuid": "d7qROrulz-l", "__type": "Var"}, "5592546": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "5592547": {"type": {"__ref": "5592549"}, "state": {"__ref": "5592574"}, "variable": {"__ref": "5592548"}, "uuid": "3ONVeqec5z3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592548": {"name": "Show Start Icon", "uuid": "_fZf9EKCp8F", "__type": "Var"}, "5592549": {"name": "any", "__type": "AnyType"}, "5592550": {"type": {"__ref": "5592552"}, "state": {"__ref": "5592575"}, "variable": {"__ref": "5592551"}, "uuid": "gcwwDLxJNkQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592551": {"name": "Show End Icon", "uuid": "ekZZhqNZYRB", "__type": "Var"}, "5592552": {"name": "any", "__type": "AnyType"}, "5592553": {"type": {"__ref": "5592555"}, "tplSlot": {"__ref": "5592582"}, "variable": {"__ref": "5592554"}, "uuid": "zvzUYKRlJQ9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5592554": {"name": "start icon", "uuid": "Zas839cjTWs", "__type": "Var"}, "5592555": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "5592556": {"type": {"__ref": "5592558"}, "tplSlot": {"__ref": "5592772"}, "variable": {"__ref": "5592557"}, "uuid": "MbCHIan2rqo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5592557": {"name": "end icon", "uuid": "_4t0EqpeKoN", "__type": "Var"}, "5592558": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "5592559": {"type": {"__ref": "5592561"}, "state": {"__ref": "5592576"}, "variable": {"__ref": "5592560"}, "uuid": "4m-Mcpag2Vy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592560": {"name": "Is Disabled", "uuid": "t4_xRCy5m7Y", "__type": "Var"}, "5592561": {"name": "any", "__type": "AnyType"}, "5592562": {"type": {"__ref": "5592564"}, "variable": {"__ref": "5592563"}, "uuid": "2oSLsu86DH6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5592563": {"name": "link", "uuid": "XmA9ZYZH-Pe", "__type": "Var"}, "5592564": {"name": "href", "__type": "HrefType"}, "5592565": {"type": {"__ref": "5592567"}, "state": {"__ref": "5592579"}, "variable": {"__ref": "5592566"}, "uuid": "J8ILD5XhO-8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592566": {"name": "Color", "uuid": "LKQRJvSmt2X", "__type": "Var"}, "5592567": {"name": "any", "__type": "AnyType"}, "5592568": {"type": {"__ref": "5592570"}, "state": {"__ref": "5592578"}, "variable": {"__ref": "5592569"}, "uuid": "9_l6jg3258k", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592569": {"name": "Size", "uuid": "tMSYbr9tNJS", "__type": "Var"}, "5592570": {"name": "any", "__type": "AnyType"}, "5592571": {"type": {"__ref": "5592573"}, "state": {"__ref": "5592577"}, "variable": {"__ref": "5592572"}, "uuid": "rkFKILY2fQS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5592572": {"name": "<PERSON><PERSON><PERSON>", "uuid": "Bd1PBvnxLsR", "__type": "Var"}, "5592573": {"name": "any", "__type": "AnyType"}, "5592574": {"variantGroup": {"__ref": "5592597"}, "param": {"__ref": "5592547"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253040"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592575": {"variantGroup": {"__ref": "5592691"}, "param": {"__ref": "5592550"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253045"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592576": {"variantGroup": {"__ref": "5592695"}, "param": {"__ref": "5592559"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253050"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592577": {"variantGroup": {"__ref": "5592666"}, "param": {"__ref": "5592571"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253055"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592578": {"variantGroup": {"__ref": "5592744"}, "param": {"__ref": "5592568"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253060"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592579": {"variantGroup": {"__ref": "5592601"}, "param": {"__ref": "5592565"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "56253065"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "5592580": {"tag": "button", "name": null, "children": [{"__ref": "5592581"}, {"__ref": "5592670"}, {"__ref": "5592771"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "zwITrFVCeysV", "parent": null, "locked": null, "vsettings": [{"__ref": "5592836"}, {"__ref": "5592860"}, {"__ref": "5592863"}, {"__ref": "5592866"}, {"__ref": "5592870"}, {"__ref": "5592873"}, {"__ref": "5592876"}, {"__ref": "5592879"}, {"__ref": "5592882"}, {"__ref": "5592885"}, {"__ref": "5592888"}, {"__ref": "5592891"}, {"__ref": "5592894"}, {"__ref": "5592897"}, {"__ref": "5592900"}, {"__ref": "5592903"}, {"__ref": "5592906"}, {"__ref": "5592909"}, {"__ref": "5592912"}, {"__ref": "5592915"}, {"__ref": "5592918"}, {"__ref": "5592921"}, {"__ref": "5592924"}, {"__ref": "5592927"}, {"__ref": "5592930"}, {"__ref": "5592933"}, {"__ref": "5592936"}, {"__ref": "5592939"}, {"__ref": "5592942"}, {"__ref": "5592945"}, {"__ref": "5592948"}, {"__ref": "5592951"}, {"__ref": "5592954"}, {"__ref": "5592957"}, {"__ref": "5592960"}, {"__ref": "5592963"}, {"__ref": "5592966"}, {"__ref": "5592969"}, {"__ref": "5592972"}, {"__ref": "5592978"}, {"__ref": "5592980"}, {"__ref": "5592982"}, {"__ref": "5592984"}, {"__ref": "5592993"}, {"__ref": "5592996"}, {"__ref": "5592999"}, {"__ref": "5593001"}, {"__ref": "5593004"}, {"__ref": "5593007"}, {"__ref": "5593010"}, {"__ref": "5593020"}, {"__ref": "5593026"}, {"__ref": "5593029"}, {"__ref": "5593032"}, {"__ref": "5593035"}, {"__ref": "5593037"}, {"__ref": "5593043"}, {"__ref": "5593046"}, {"__ref": "5593049"}, {"__ref": "5593052"}], "__type": "TplTag"}, "5592581": {"tag": "div", "name": "start icon container", "children": [{"__ref": "5592582"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SYPWs4IIYFnN", "parent": {"__ref": "5592580"}, "locked": null, "vsettings": [{"__ref": "5592650"}, {"__ref": "5592658"}, {"__ref": "5592662"}, {"__ref": "5592664"}], "__type": "TplTag"}, "5592582": {"param": {"__ref": "5592553"}, "defaultContents": [{"__ref": "5592583"}], "uuid": "yY9xfaZZzQFA", "parent": {"__ref": "5592581"}, "locked": null, "vsettings": [{"__ref": "5592592"}, {"__ref": "5592595"}, {"__ref": "5592599"}, {"__ref": "5592615"}, {"__ref": "5592618"}, {"__ref": "5592621"}, {"__ref": "5592624"}, {"__ref": "5592627"}, {"__ref": "5592630"}, {"__ref": "5592633"}, {"__ref": "5592636"}, {"__ref": "5592640"}, {"__ref": "5592644"}, {"__ref": "5592647"}], "__type": "TplSlot"}, "5592583": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Vv7eKQJG8aZf", "parent": {"__ref": "5592582"}, "locked": null, "vsettings": [{"__ref": "5592584"}], "__type": "TplTag"}, "5592584": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592586"}}, "rs": {"__ref": "5592587"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592585": {"uuid": "V_I86DlyP1c", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592586": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5592587": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592592": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {}, "rs": {"__ref": "5592593"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592593": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "5592595": {"variants": [{"__ref": "5592596"}], "args": [], "attrs": {}, "rs": {"__ref": "5592598"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592596": {"uuid": "fpLBzG60COa", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592597"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592597": {"type": "component", "param": {"__ref": "5592547"}, "linkedState": {"__ref": "5592574"}, "uuid": "AhV3lh_a5_P", "variants": [{"__ref": "5592596"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592598": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592599": {"variants": [{"__ref": "5592600"}], "args": [], "attrs": {}, "rs": {"__ref": "5592614"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592600": {"uuid": "h0JmHkCVI4Pv", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592601": {"type": "component", "param": {"__ref": "5592565"}, "linkedState": {"__ref": "5592579"}, "uuid": "nE1aFsqw6Lfj", "variants": [{"__ref": "5592600"}, {"__ref": "5592602"}, {"__ref": "5592603"}, {"__ref": "5592604"}, {"__ref": "5592605"}, {"__ref": "5592606"}, {"__ref": "5592607"}, {"__ref": "5592608"}, {"__ref": "5592609"}, {"__ref": "5592610"}, {"__ref": "5592611"}, {"__ref": "5592612"}, {"__ref": "5592613"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592602": {"uuid": "by_Bk3cziNC5", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592603": {"uuid": "RWtlZ5R8dSYx", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592604": {"uuid": "lSqx89FaLWdL", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592605": {"uuid": "0pCrNzLmXfW0", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592606": {"uuid": "i_R2QIdmDHpt", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592607": {"uuid": "EBxMPhGLQZ3N", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592608": {"uuid": "upl1jHPGPVB5", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592609": {"uuid": "msGB_RxWydfp", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592610": {"uuid": "D8DHW17nP4Qc", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592611": {"uuid": "854qRDH-VhNp", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592612": {"uuid": "oImEyQ_5DIII", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592613": {"uuid": "cJkWqEUQJPOV", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592601"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592614": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592615": {"variants": [{"__ref": "5592607"}], "args": [], "attrs": {}, "rs": {"__ref": "5592616"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592616": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "5592618": {"variants": [{"__ref": "5592608"}], "args": [], "attrs": {}, "rs": {"__ref": "5592619"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592619": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "5592621": {"variants": [{"__ref": "5592609"}], "args": [], "attrs": {}, "rs": {"__ref": "5592622"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592622": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "5592624": {"variants": [{"__ref": "5592610"}], "args": [], "attrs": {}, "rs": {"__ref": "5592625"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592625": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "5592627": {"variants": [{"__ref": "5592611"}], "args": [], "attrs": {}, "rs": {"__ref": "5592628"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592628": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "5592630": {"variants": [{"__ref": "5592603"}], "args": [], "attrs": {}, "rs": {"__ref": "5592631"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592631": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "5592633": {"variants": [{"__ref": "5592613"}], "args": [], "attrs": {}, "rs": {"__ref": "5592634"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592634": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "5592636": {"variants": [{"__ref": "5592613"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592638"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592637": {"uuid": "3IVJQ1D69tj", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592638": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "5592640": {"variants": [{"__ref": "5592613"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592642"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592641": {"uuid": "Hkeu9-vXE6N", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592642": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "5592644": {"variants": [{"__ref": "5592612"}], "args": [], "attrs": {}, "rs": {"__ref": "5592645"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592645": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "5592647": {"variants": [{"__ref": "5592606"}], "args": [], "attrs": {}, "rs": {"__ref": "5592648"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592648": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "5592650": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {}, "rs": {"__ref": "5592651"}, "dataCond": {"__ref": "5592657"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592651": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "5592657": {"code": "false", "fallback": null, "__type": "CustomCode"}, "5592658": {"variants": [{"__ref": "5592596"}], "args": [], "attrs": {}, "rs": {"__ref": "5592659"}, "dataCond": {"__ref": "5592661"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592659": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "5592661": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592662": {"variants": [{"__ref": "5592600"}], "args": [], "attrs": {}, "rs": {"__ref": "5592663"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592663": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592664": {"variants": [{"__ref": "5592665"}, {"__ref": "5592596"}], "args": [], "attrs": {}, "rs": {"__ref": "5592669"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592665": {"uuid": "azo_rKcr0xg6", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592666"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592666": {"type": "component", "param": {"__ref": "5592571"}, "linkedState": {"__ref": "5592577"}, "uuid": "IrvYEpj_gJ6P", "variants": [{"__ref": "5592665"}, {"__ref": "5592667"}, {"__ref": "5592668"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592667": {"uuid": "EvFRe0QOuFlq", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592666"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592668": {"uuid": "-kOBdeNp-R_e", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592666"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592669": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592670": {"tag": "div", "name": "content container", "children": [{"__ref": "5592671"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "6gWXLpEg-mOv", "parent": {"__ref": "5592580"}, "locked": null, "vsettings": [{"__ref": "5592756"}, {"__ref": "5592763"}, {"__ref": "5592765"}, {"__ref": "5592767"}, {"__ref": "5592769"}], "__type": "TplTag"}, "5592671": {"param": {"__ref": "5592544"}, "defaultContents": [{"__ref": "5592672"}], "uuid": "ve4TDpXpBpMZ", "parent": {"__ref": "5592670"}, "locked": null, "vsettings": [{"__ref": "5592676"}, {"__ref": "5592681"}, {"__ref": "5592684"}, {"__ref": "5592687"}, {"__ref": "5592689"}, {"__ref": "5592693"}, {"__ref": "5592697"}, {"__ref": "5592700"}, {"__ref": "5592703"}, {"__ref": "5592706"}, {"__ref": "5592709"}, {"__ref": "5592712"}, {"__ref": "5592715"}, {"__ref": "5592718"}, {"__ref": "5592721"}, {"__ref": "5592724"}, {"__ref": "5592727"}, {"__ref": "5592729"}, {"__ref": "5592732"}, {"__ref": "5592735"}, {"__ref": "5592739"}, {"__ref": "5592742"}, {"__ref": "5592747"}, {"__ref": "5592749"}, {"__ref": "5592751"}, {"__ref": "5592753"}], "__type": "TplSlot"}, "5592672": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Ydu0Oz7cKcMv", "parent": {"__ref": "5592671"}, "locked": null, "vsettings": [{"__ref": "5592673"}], "__type": "TplTag"}, "5592673": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {}, "rs": {"__ref": "5592674"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5592675"}, "columnsConfig": null, "__type": "VariantSetting"}, "5592674": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592675": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "5592676": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {}, "rs": {"__ref": "5592677"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592677": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "5592681": {"variants": [{"__ref": "5592682"}], "args": [], "attrs": {}, "rs": {"__ref": "5592683"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592682": {"uuid": "GIzrwpGr7ra", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592683": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592684": {"variants": [{"__ref": "5592685"}], "args": [], "attrs": {}, "rs": {"__ref": "5592686"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592685": {"uuid": "n15fjVZQLE5", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592686": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592687": {"variants": [{"__ref": "5592596"}], "args": [], "attrs": {}, "rs": {"__ref": "5592688"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592688": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592689": {"variants": [{"__ref": "5592690"}], "args": [], "attrs": {}, "rs": {"__ref": "5592692"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592690": {"uuid": "NuWYQ7rCYKz", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592691"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592691": {"type": "component", "param": {"__ref": "5592550"}, "linkedState": {"__ref": "5592575"}, "uuid": "m8x3MZzXG-J", "variants": [{"__ref": "5592690"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592692": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592693": {"variants": [{"__ref": "5592694"}], "args": [], "attrs": {}, "rs": {"__ref": "5592696"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592694": {"uuid": "cGa30ekVocGF", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592695"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592695": {"type": "component", "param": {"__ref": "5592559"}, "linkedState": {"__ref": "5592576"}, "uuid": "xFB6hx1CGkVz", "variants": [{"__ref": "5592694"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592696": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592697": {"variants": [{"__ref": "5592607"}], "args": [], "attrs": {}, "rs": {"__ref": "5592698"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592698": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "5592700": {"variants": [{"__ref": "5592608"}], "args": [], "attrs": {}, "rs": {"__ref": "5592701"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592701": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "5592703": {"variants": [{"__ref": "5592603"}], "args": [], "attrs": {}, "rs": {"__ref": "5592704"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592704": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "5592706": {"variants": [{"__ref": "5592609"}], "args": [], "attrs": {}, "rs": {"__ref": "5592707"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592707": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "5592709": {"variants": [{"__ref": "5592610"}], "args": [], "attrs": {}, "rs": {"__ref": "5592710"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592710": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "5592712": {"variants": [{"__ref": "5592611"}], "args": [], "attrs": {}, "rs": {"__ref": "5592713"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592713": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "5592715": {"variants": [{"__ref": "5592600"}], "args": [], "attrs": {}, "rs": {"__ref": "5592716"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592716": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "5592718": {"variants": [{"__ref": "5592602"}], "args": [], "attrs": {}, "rs": {"__ref": "5592719"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592719": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "5592721": {"variants": [{"__ref": "5592605"}], "args": [], "attrs": {}, "rs": {"__ref": "5592722"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592722": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "5592724": {"variants": [{"__ref": "5592604"}], "args": [], "attrs": {}, "rs": {"__ref": "5592725"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592725": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "5592727": {"variants": [{"__ref": "5592665"}], "args": [], "attrs": {}, "rs": {"__ref": "5592728"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592728": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592729": {"variants": [{"__ref": "5592612"}], "args": [], "attrs": {}, "rs": {"__ref": "5592730"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592730": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "5592732": {"variants": [{"__ref": "5592613"}], "args": [], "attrs": {}, "rs": {"__ref": "5592733"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592733": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "5592735": {"variants": [{"__ref": "5592613"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592736"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592736": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "5592739": {"variants": [{"__ref": "5592613"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592740"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592740": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "5592742": {"variants": [{"__ref": "5592613"}, {"__ref": "5592743"}], "args": [], "attrs": {}, "rs": {"__ref": "5592746"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592743": {"uuid": "9NEHHLZu7RL0", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592744"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592744": {"type": "component", "param": {"__ref": "5592568"}, "linkedState": {"__ref": "5592578"}, "uuid": "CPhMn-EQMfSC", "variants": [{"__ref": "5592745"}, {"__ref": "5592743"}], "multi": false, "__type": "ComponentVariantGroup"}, "5592745": {"uuid": "Ewrn1SMw1nDu", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "5592744"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5592746": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592747": {"variants": [{"__ref": "5592613"}, {"__ref": "5592743"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592748"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592748": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592749": {"variants": [{"__ref": "5592743"}], "args": [], "attrs": {}, "rs": {"__ref": "5592750"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592750": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592751": {"variants": [{"__ref": "5592743"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592752"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592752": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592753": {"variants": [{"__ref": "5592606"}], "args": [], "attrs": {}, "rs": {"__ref": "5592754"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592754": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "5592756": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {}, "rs": {"__ref": "5592757"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592757": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "5592763": {"variants": [{"__ref": "5592694"}], "args": [], "attrs": {}, "rs": {"__ref": "5592764"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592764": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592765": {"variants": [{"__ref": "5592690"}], "args": [], "attrs": {}, "rs": {"__ref": "5592766"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592766": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592767": {"variants": [{"__ref": "5592685"}], "args": [], "attrs": {}, "rs": {"__ref": "5592768"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592768": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592769": {"variants": [{"__ref": "5592665"}], "args": [], "attrs": {}, "rs": {"__ref": "5592770"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592770": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592771": {"tag": "div", "name": "end icon container", "children": [{"__ref": "5592772"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ndXuzqm_hRTG", "parent": {"__ref": "5592580"}, "locked": null, "vsettings": [{"__ref": "5592820"}, {"__ref": "5592828"}, {"__ref": "5592832"}, {"__ref": "5592834"}], "__type": "TplTag"}, "5592772": {"param": {"__ref": "5592556"}, "defaultContents": [{"__ref": "5592773"}], "uuid": "qRIOL-sCDG1z", "parent": {"__ref": "5592771"}, "locked": null, "vsettings": [{"__ref": "5592782"}, {"__ref": "5592785"}, {"__ref": "5592787"}, {"__ref": "5592790"}, {"__ref": "5592793"}, {"__ref": "5592796"}, {"__ref": "5592799"}, {"__ref": "5592802"}, {"__ref": "5592805"}, {"__ref": "5592808"}, {"__ref": "5592811"}, {"__ref": "5592814"}, {"__ref": "5592817"}], "__type": "TplSlot"}, "5592773": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "mAnPJ3iTLB5K", "parent": {"__ref": "5592772"}, "locked": null, "vsettings": [{"__ref": "5592774"}], "__type": "TplTag"}, "5592774": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {"outerHTML": {"__ref": "5592775"}}, "rs": {"__ref": "5592777"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592775": {"asset": {"__ref": "5592776"}, "__type": "ImageAssetRef"}, "5592776": {"uuid": "pVujLZiB-bTK", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "5592777": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5592782": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {}, "rs": {"__ref": "5592783"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592783": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "5592785": {"variants": [{"__ref": "5592690"}], "args": [], "attrs": {}, "rs": {"__ref": "5592786"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592786": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592787": {"variants": [{"__ref": "5592607"}], "args": [], "attrs": {}, "rs": {"__ref": "5592788"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592788": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "5592790": {"variants": [{"__ref": "5592608"}], "args": [], "attrs": {}, "rs": {"__ref": "5592791"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592791": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "5592793": {"variants": [{"__ref": "5592609"}], "args": [], "attrs": {}, "rs": {"__ref": "5592794"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592794": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "5592796": {"variants": [{"__ref": "5592610"}], "args": [], "attrs": {}, "rs": {"__ref": "5592797"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592797": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "5592799": {"variants": [{"__ref": "5592611"}], "args": [], "attrs": {}, "rs": {"__ref": "5592800"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592800": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "5592802": {"variants": [{"__ref": "5592603"}], "args": [], "attrs": {}, "rs": {"__ref": "5592803"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592803": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "5592805": {"variants": [{"__ref": "5592613"}], "args": [], "attrs": {}, "rs": {"__ref": "5592806"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592806": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "5592808": {"variants": [{"__ref": "5592613"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592809"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592809": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "5592811": {"variants": [{"__ref": "5592613"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592812"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592812": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "5592814": {"variants": [{"__ref": "5592612"}], "args": [], "attrs": {}, "rs": {"__ref": "5592815"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592815": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "5592817": {"variants": [{"__ref": "5592606"}], "args": [], "attrs": {}, "rs": {"__ref": "5592818"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592818": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "5592820": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {}, "rs": {"__ref": "5592821"}, "dataCond": {"__ref": "5592827"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592821": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "5592827": {"code": "false", "fallback": null, "__type": "CustomCode"}, "5592828": {"variants": [{"__ref": "5592690"}], "args": [], "attrs": {}, "rs": {"__ref": "5592829"}, "dataCond": {"__ref": "5592831"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592829": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "5592831": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5592832": {"variants": [{"__ref": "5592603"}], "args": [], "attrs": {}, "rs": {"__ref": "5592833"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592833": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592834": {"variants": [{"__ref": "5592606"}], "args": [], "attrs": {}, "rs": {"__ref": "5592835"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592835": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592836": {"variants": [{"__ref": "5592585"}], "args": [], "attrs": {"data-testid": {"__ref": "22485028"}}, "rs": {"__ref": "5592837"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592837": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "flex-column-gap": "8px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px"}, "mixins": [], "__type": "RuleSet"}, "5592860": {"variants": [{"__ref": "5592685"}], "args": [], "attrs": {}, "rs": {"__ref": "5592861"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592861": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "5592863": {"variants": [{"__ref": "5592682"}], "args": [], "attrs": {}, "rs": {"__ref": "5592864"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592864": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "5592866": {"variants": [{"__ref": "5592694"}], "args": [], "attrs": {}, "rs": {"__ref": "5592867"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592867": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "5592870": {"variants": [{"__ref": "5592690"}], "args": [], "attrs": {}, "rs": {"__ref": "5592871"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592871": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "5592873": {"variants": [{"__ref": "5592596"}], "args": [], "attrs": {}, "rs": {"__ref": "5592874"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592874": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "5592876": {"variants": [{"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592877"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592877": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "5592879": {"variants": [{"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592880"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592880": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "5592882": {"variants": [{"__ref": "5592607"}], "args": [], "attrs": {}, "rs": {"__ref": "5592883"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592883": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "5592885": {"variants": [{"__ref": "5592607"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592886"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592886": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "5592888": {"variants": [{"__ref": "5592607"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592889"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592889": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "5592891": {"variants": [{"__ref": "5592608"}], "args": [], "attrs": {}, "rs": {"__ref": "5592892"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592892": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "5592894": {"variants": [{"__ref": "5592608"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592895"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592895": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "5592897": {"variants": [{"__ref": "5592608"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592898"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592898": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "5592900": {"variants": [{"__ref": "5592603"}], "args": [], "attrs": {}, "rs": {"__ref": "5592901"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592901": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "5592903": {"variants": [{"__ref": "5592603"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592904"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592904": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "5592906": {"variants": [{"__ref": "5592603"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592907"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592907": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "5592909": {"variants": [{"__ref": "5592604"}], "args": [], "attrs": {}, "rs": {"__ref": "5592910"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592910": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "5592912": {"variants": [{"__ref": "5592604"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592913"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592913": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "5592915": {"variants": [{"__ref": "5592604"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592916"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592916": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "5592918": {"variants": [{"__ref": "5592609"}], "args": [], "attrs": {}, "rs": {"__ref": "5592919"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592919": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "5592921": {"variants": [{"__ref": "5592609"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592922"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592922": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "5592924": {"variants": [{"__ref": "5592609"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592925"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592925": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "5592927": {"variants": [{"__ref": "5592610"}], "args": [], "attrs": {}, "rs": {"__ref": "5592928"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592928": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "5592930": {"variants": [{"__ref": "5592610"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592931"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592931": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "5592933": {"variants": [{"__ref": "5592610"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592934"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592934": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "5592936": {"variants": [{"__ref": "5592611"}], "args": [], "attrs": {}, "rs": {"__ref": "5592937"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592937": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "5592939": {"variants": [{"__ref": "5592611"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592940"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592940": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "5592942": {"variants": [{"__ref": "5592611"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592943"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592943": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "5592945": {"variants": [{"__ref": "5592600"}], "args": [], "attrs": {}, "rs": {"__ref": "5592946"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592946": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "5592948": {"variants": [{"__ref": "5592600"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592949"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592949": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "5592951": {"variants": [{"__ref": "5592600"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592952"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592952": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "5592954": {"variants": [{"__ref": "5592602"}], "args": [], "attrs": {}, "rs": {"__ref": "5592955"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592955": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "5592957": {"variants": [{"__ref": "5592602"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592958"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592958": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "5592960": {"variants": [{"__ref": "5592602"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592961"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592961": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "5592963": {"variants": [{"__ref": "5592605"}], "args": [], "attrs": {}, "rs": {"__ref": "5592964"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592964": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "5592966": {"variants": [{"__ref": "5592605"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5592967"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592967": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "5592969": {"variants": [{"__ref": "5592605"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5592970"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592970": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "5592972": {"variants": [{"__ref": "5592745"}], "args": [], "attrs": {}, "rs": {"__ref": "5592973"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592973": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "5592978": {"variants": [{"__ref": "5592745"}, {"__ref": "5592596"}], "args": [], "attrs": {}, "rs": {"__ref": "5592979"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592979": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592980": {"variants": [{"__ref": "5592745"}, {"__ref": "5592596"}, {"__ref": "5592690"}], "args": [], "attrs": {}, "rs": {"__ref": "5592981"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592981": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592982": {"variants": [{"__ref": "5592745"}, {"__ref": "5592690"}], "args": [], "attrs": {}, "rs": {"__ref": "5592983"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592983": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5592984": {"variants": [{"__ref": "5592665"}], "args": [], "attrs": {}, "rs": {"__ref": "5592985"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592985": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "5592993": {"variants": [{"__ref": "5592665"}, {"__ref": "5592596"}], "args": [], "attrs": {}, "rs": {"__ref": "5592994"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592994": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "5592996": {"variants": [{"__ref": "5592690"}, {"__ref": "5592665"}], "args": [], "attrs": {}, "rs": {"__ref": "5592997"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5592997": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "5592999": {"variants": [{"__ref": "5592745"}, {"__ref": "5592665"}], "args": [], "attrs": {}, "rs": {"__ref": "5593000"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593000": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593001": {"variants": [{"__ref": "5592612"}], "args": [], "attrs": {}, "rs": {"__ref": "5593002"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593002": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "5593004": {"variants": [{"__ref": "5592612"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5593005"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593005": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "5593007": {"variants": [{"__ref": "5592612"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5593008"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593008": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "5593010": {"variants": [{"__ref": "5592667"}], "args": [], "attrs": {}, "rs": {"__ref": "5593011"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593011": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "5593020": {"variants": [{"__ref": "5592667"}, {"__ref": "5592745"}], "args": [], "attrs": {}, "rs": {"__ref": "5593021"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593021": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "5593026": {"variants": [{"__ref": "5592613"}], "args": [], "attrs": {}, "rs": {"__ref": "5593027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593027": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "5593029": {"variants": [{"__ref": "5592613"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5593030"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593030": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "5593032": {"variants": [{"__ref": "5592613"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5593033"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593033": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "5593035": {"variants": [{"__ref": "5592613"}, {"__ref": "5592743"}], "args": [], "attrs": {}, "rs": {"__ref": "5593036"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593036": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593037": {"variants": [{"__ref": "5592743"}], "args": [], "attrs": {}, "rs": {"__ref": "5593038"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593038": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "5593043": {"variants": [{"__ref": "5592606"}], "args": [], "attrs": {}, "rs": {"__ref": "5593044"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593044": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "5593046": {"variants": [{"__ref": "5592606"}, {"__ref": "5592637"}], "args": [], "attrs": {}, "rs": {"__ref": "5593047"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593047": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "5593049": {"variants": [{"__ref": "5592606"}, {"__ref": "5592641"}], "args": [], "attrs": {}, "rs": {"__ref": "5593050"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593050": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "5593052": {"variants": [{"__ref": "5592668"}], "args": [], "attrs": {}, "rs": {"__ref": "5593053"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593053": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "5593058": {"type": "button", "__type": "PlumeInfo"}, "5593059": {"variants": [{"__ref": "5592502"}], "args": [{"__ref": "5593060"}, {"__ref": "5593070"}, {"__ref": "5593076"}, {"__ref": "5593086"}, {"__ref": "22485040"}, {"__ref": "1499904"}], "attrs": {"onClick": {"__ref": "5593088"}}, "rs": {"__ref": "5593100"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593060": {"param": {"__ref": "5592553"}, "expr": {"__ref": "5593061"}, "__type": "Arg"}, "5593061": {"tpl": [{"__ref": "5593062"}], "__type": "VirtualRenderExpr"}, "5593062": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "zsFEiPKXvzS", "parent": {"__ref": "5592542"}, "locked": null, "vsettings": [{"__ref": "5593063"}], "__type": "TplTag"}, "5593063": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593064"}}, "rs": {"__ref": "5593065"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593064": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5593065": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593070": {"param": {"__ref": "5592544"}, "expr": {"__ref": "5593071"}, "__type": "Arg"}, "5593071": {"tpl": [{"__ref": "5593072"}], "__type": "RenderExpr"}, "5593072": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Bx2qh63kKxh", "parent": {"__ref": "5592542"}, "locked": null, "vsettings": [{"__ref": "5593073"}], "__type": "TplTag"}, "5593073": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {}, "rs": {"__ref": "5593074"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5593075"}, "columnsConfig": null, "__type": "VariantSetting"}, "5593074": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593075": {"markers": [], "text": "X", "__type": "RawText"}, "5593076": {"param": {"__ref": "5592556"}, "expr": {"__ref": "5593077"}, "__type": "Arg"}, "5593077": {"tpl": [{"__ref": "5593078"}], "__type": "VirtualRenderExpr"}, "5593078": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "D2YiyngOrB5", "parent": {"__ref": "5592542"}, "locked": null, "vsettings": [{"__ref": "5593079"}], "__type": "TplTag"}, "5593079": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593080"}}, "rs": {"__ref": "5593081"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593080": {"asset": {"__ref": "5592776"}, "__type": "ImageAssetRef"}, "5593081": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593086": {"param": {"__ref": "5592565"}, "expr": {"__ref": "5593087"}, "__type": "Arg"}, "5593087": {"variants": [{"__ref": "5592604"}], "__type": "VariantsRef"}, "5593088": {"interactions": [{"__ref": "5593089"}, {"__ref": "5593093"}], "__type": "EventHandler"}, "5593089": {"interactionName": "Custom function", "actionName": "customFunction", "args": [{"__ref": "5593090"}], "condExpr": null, "conditionalMode": "always", "uuid": "Y5SLRZzqG_r", "parent": {"__ref": "5593088"}, "__type": "Interaction"}, "5593090": {"name": "customFunction", "expr": {"__ref": "5593091"}, "__type": "NameArg"}, "5593091": {"argNames": [], "bodyExpr": {"__ref": "1567501"}, "__type": "FunctionExpr"}, "5593093": {"interactionName": "Invoke On nicknames change", "actionName": "invokeEventHandler", "args": [{"__ref": "5593094"}, {"__ref": "5593096"}], "condExpr": null, "conditionalMode": "always", "uuid": "UVhoCeztRnc", "parent": {"__ref": "5593088"}, "__type": "Interaction"}, "5593094": {"name": "eventRef", "expr": {"__ref": "5593095"}, "__type": "NameArg"}, "5593095": {"variable": {"__ref": "5592495"}, "__type": "VarRef"}, "5593096": {"name": "args", "expr": {"__ref": "5593097"}, "__type": "NameArg"}, "5593097": {"exprs": [{"__ref": "5593098"}], "__type": "CollectionExpr"}, "5593098": {"uuid": "sdm0uD-7K0B", "argType": {"__ref": "5592497"}, "expr": {"__ref": "5593099"}, "__type": "FunctionArg"}, "5593099": {"code": "($state.nicknames)", "fallback": null, "__type": "CustomCode"}, "5593100": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5593103": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {}, "rs": {"__ref": "5593104"}, "dataCond": {"__ref": "5593112"}, "dataRep": {"__ref": "5593113"}, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593104": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "flex-column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "5593112": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5593113": {"element": {"__ref": "5593114"}, "index": {"__ref": "5593115"}, "collection": {"__ref": "5593116"}, "__type": "Rep"}, "5593114": {"name": "currentItem", "uuid": "tfCv4wbgPG5", "__type": "Var"}, "5593115": {"name": "currentIndex", "uuid": "JBQjgA0PABP", "__type": "Var"}, "5593116": {"path": ["$state", "nicknames"], "fallback": {"__ref": "5593117"}, "__type": "ObjectPath"}, "5593117": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "5593118": {"tag": "div", "name": null, "children": [{"__ref": "5592541"}, {"__ref": "5593119"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "LK3ub9PVMus", "parent": {"__ref": "5593174"}, "locked": null, "vsettings": [{"__ref": "5593167"}], "__type": "TplTag"}, "5593119": {"name": null, "component": {"__ref": "5592543"}, "uuid": "OGXSvSpSLUj", "parent": {"__ref": "5593118"}, "locked": null, "vsettings": [{"__ref": "5593120"}], "__type": "TplComponent"}, "5593120": {"variants": [{"__ref": "5592502"}], "args": [{"__ref": "5593121"}, {"__ref": "5593131"}, {"__ref": "5593137"}, {"__ref": "5593147"}, {"__ref": "22485031"}, {"__ref": "1499906"}], "attrs": {"onClick": {"__ref": "5593149"}}, "rs": {"__ref": "5593164"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593121": {"param": {"__ref": "5592553"}, "expr": {"__ref": "5593122"}, "__type": "Arg"}, "5593122": {"tpl": [{"__ref": "5593123"}], "__type": "VirtualRenderExpr"}, "5593123": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "laHon55Gisj", "parent": {"__ref": "5593119"}, "locked": null, "vsettings": [{"__ref": "5593124"}], "__type": "TplTag"}, "5593124": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593125"}}, "rs": {"__ref": "5593126"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593125": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5593126": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593131": {"param": {"__ref": "5592544"}, "expr": {"__ref": "5593132"}, "__type": "Arg"}, "5593132": {"tpl": [{"__ref": "5593133"}], "__type": "RenderExpr"}, "5593133": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "VKQ9PaUMMAj", "parent": {"__ref": "5593119"}, "locked": null, "vsettings": [{"__ref": "5593134"}], "__type": "TplTag"}, "5593134": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {}, "rs": {"__ref": "5593135"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5593136"}, "columnsConfig": null, "__type": "VariantSetting"}, "5593135": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593136": {"markers": [], "text": "Add", "__type": "RawText"}, "5593137": {"param": {"__ref": "5592556"}, "expr": {"__ref": "5593138"}, "__type": "Arg"}, "5593138": {"tpl": [{"__ref": "5593139"}], "__type": "VirtualRenderExpr"}, "5593139": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "eWLpSE2bySm", "parent": {"__ref": "5593119"}, "locked": null, "vsettings": [{"__ref": "5593140"}], "__type": "TplTag"}, "5593140": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593141"}}, "rs": {"__ref": "5593142"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593141": {"asset": {"__ref": "5592776"}, "__type": "ImageAssetRef"}, "5593142": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593147": {"param": {"__ref": "5592565"}, "expr": {"__ref": "5593148"}, "__type": "Arg"}, "5593148": {"variants": [{"__ref": "5592600"}], "__type": "VariantsRef"}, "5593149": {"interactions": [{"__ref": "5593150"}, {"__ref": "5593157"}], "__type": "EventHandler"}, "5593150": {"interactionName": "Set nicknames", "actionName": "updateVariable", "args": [{"__ref": "5593151"}, {"__ref": "5593153"}, {"__ref": "5593155"}], "condExpr": null, "conditionalMode": "always", "uuid": "0kw8bzwymLf", "parent": {"__ref": "5593149"}, "__type": "Interaction"}, "5593151": {"name": "variable", "expr": {"__ref": "5593152"}, "__type": "NameArg"}, "5593152": {"path": ["$state", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "5593153": {"name": "operation", "expr": {"__ref": "5593154"}, "__type": "NameArg"}, "5593154": {"code": "5", "fallback": null, "__type": "CustomCode"}, "5593155": {"name": "value", "expr": {"__ref": "5593156"}, "__type": "NameArg"}, "5593156": {"code": "(\"\")", "fallback": null, "__type": "CustomCode"}, "5593157": {"interactionName": "Invoke On nicknames change", "actionName": "invokeEventHandler", "args": [{"__ref": "5593158"}, {"__ref": "5593160"}], "condExpr": null, "conditionalMode": "always", "uuid": "7Me1NYmyUwS", "parent": {"__ref": "5593149"}, "__type": "Interaction"}, "5593158": {"name": "eventRef", "expr": {"__ref": "5593159"}, "__type": "NameArg"}, "5593159": {"variable": {"__ref": "5592495"}, "__type": "VarRef"}, "5593160": {"name": "args", "expr": {"__ref": "5593161"}, "__type": "NameArg"}, "5593161": {"exprs": [{"__ref": "5593162"}], "__type": "CollectionExpr"}, "5593162": {"uuid": "lVzmqMmURox", "argType": {"__ref": "5592497"}, "expr": {"__ref": "5593163"}, "__type": "FunctionArg"}, "5593163": {"code": "($state.nicknames)", "fallback": null, "__type": "CustomCode"}, "5593164": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5593167": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {"data-testid": {"__ref": "16997003"}}, "rs": {"__ref": "5593168"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593168": {"values": {"display": "flex", "flex-direction": "column", "width": "stretch", "height": "wrap", "flex-row-gap": "10px"}, "mixins": [], "__type": "RuleSet"}, "5593174": {"tag": "div", "name": null, "children": [{"__ref": "5593175"}, {"__ref": "5593118"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7cSI8rDG9VC", "parent": null, "locked": null, "vsettings": [{"__ref": "5593181"}], "__type": "TplTag"}, "5593175": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "f8UgZyUUlUQ", "parent": {"__ref": "5593174"}, "locked": null, "vsettings": [{"__ref": "5593176"}], "__type": "TplTag"}, "5593176": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {}, "rs": {"__ref": "5593177"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5593180"}, "columnsConfig": null, "__type": "VariantSetting"}, "5593177": {"values": {"position": "relative", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "5593180": {"markers": [], "text": "Nicknames", "__type": "RawText"}, "5593181": {"variants": [{"__ref": "5592502"}], "args": [], "attrs": {}, "rs": {"__ref": "5593182"}, "dataCond": {"__ref": "5593190"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593182": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "flex-column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "5593190": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5593191": {"param": {"__ref": "5592490"}, "accessType": "writable", "variableType": "array", "onChangeParam": {"__ref": "5592494"}, "tplNode": null, "implicitState": null, "__type": "State"}, "5593192": {"uuid": "SCJHCc7Ejxq", "name": "componentData", "op": null, "__type": "ComponentDataQuery"}, "5593193": {"variants": [{"__ref": "5592426"}], "args": [{"__ref": "5593194"}, {"__ref": "5593210"}], "attrs": {}, "rs": {"__ref": "5593213"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593194": {"param": {"__ref": "5592494"}, "expr": {"__ref": "5593195"}, "__type": "Arg"}, "5593195": {"interactions": [{"__ref": "5593196"}], "__type": "EventHandler"}, "5593196": {"interactionName": "Set person ▸ nicknames", "actionName": "updateVariable", "args": [{"__ref": "56253001"}, {"__ref": "56253002"}, {"__ref": "56253003"}], "condExpr": null, "conditionalMode": "always", "uuid": "qJkIKurYXb0", "parent": {"__ref": "5593195"}, "__type": "Interaction"}, "5593200": {"code": "0", "fallback": null, "__type": "CustomCode"}, "5593202": {"path": ["val"], "fallback": null, "__type": "ObjectPath"}, "5593210": {"param": {"__ref": "5592490"}, "expr": {"__ref": "5593211"}, "__type": "Arg"}, "5593211": {"path": ["$state", "person", "nicknames"], "fallback": {"__ref": "5593212"}, "__type": "ObjectPath"}, "5593212": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "5593213": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5593216": {"tag": "div", "name": null, "children": [{"__ref": "5593217"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "s52yetP5NpI", "parent": {"__ref": "5592455"}, "locked": null, "vsettings": [{"__ref": "5593253"}], "__type": "TplTag"}, "5593217": {"name": null, "component": {"__ref": "5592543"}, "uuid": "3jKI2MvOCjc", "parent": {"__ref": "5593216"}, "locked": null, "vsettings": [{"__ref": "5593218"}], "__type": "TplComponent"}, "5593218": {"variants": [{"__ref": "5592426"}], "args": [{"__ref": "5593219"}, {"__ref": "5593229"}, {"__ref": "5593235"}, {"__ref": "5593245"}, {"__ref": "22485042"}, {"__ref": "1499908"}], "attrs": {"onClick": {"__ref": "5593247"}}, "rs": {"__ref": "5593251"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593219": {"param": {"__ref": "5592553"}, "expr": {"__ref": "5593220"}, "__type": "Arg"}, "5593220": {"tpl": [{"__ref": "5593221"}], "__type": "VirtualRenderExpr"}, "5593221": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "SvxPh0nZHP0", "parent": {"__ref": "5593217"}, "locked": null, "vsettings": [{"__ref": "5593222"}], "__type": "TplTag"}, "5593222": {"variants": [{"__ref": "5592426"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593223"}}, "rs": {"__ref": "5593224"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593223": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5593224": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593229": {"param": {"__ref": "5592544"}, "expr": {"__ref": "5593230"}, "__type": "Arg"}, "5593230": {"tpl": [{"__ref": "5593231"}], "__type": "RenderExpr"}, "5593231": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "iVQIPxkKhKW", "parent": {"__ref": "5593217"}, "locked": null, "vsettings": [{"__ref": "5593232"}], "__type": "TplTag"}, "5593232": {"variants": [{"__ref": "5592426"}], "args": [], "attrs": {}, "rs": {"__ref": "5593233"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5593234"}, "columnsConfig": null, "__type": "VariantSetting"}, "5593233": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593234": {"markers": [], "text": "Remove", "__type": "RawText"}, "5593235": {"param": {"__ref": "5592556"}, "expr": {"__ref": "5593236"}, "__type": "Arg"}, "5593236": {"tpl": [{"__ref": "5593237"}], "__type": "VirtualRenderExpr"}, "5593237": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "pgsMn96mzcx", "parent": {"__ref": "5593217"}, "locked": null, "vsettings": [{"__ref": "5593238"}], "__type": "TplTag"}, "5593238": {"variants": [{"__ref": "5592426"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593239"}}, "rs": {"__ref": "5593240"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593239": {"asset": {"__ref": "5592776"}, "__type": "ImageAssetRef"}, "5593240": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593245": {"param": {"__ref": "5592565"}, "expr": {"__ref": "5593246"}, "__type": "Arg"}, "5593246": {"variants": [{"__ref": "5592604"}], "__type": "VariantsRef"}, "5593247": {"interactions": [{"__ref": "5593248"}], "__type": "EventHandler"}, "5593248": {"interactionName": "Invoke onDelete", "actionName": "invokeEventHandler", "args": [{"__ref": "5593249"}], "condExpr": null, "conditionalMode": "always", "uuid": "AjCQq-d3O_w", "parent": {"__ref": "5593247"}, "__type": "Interaction"}, "5593249": {"name": "eventRef", "expr": {"__ref": "5593250"}, "__type": "NameArg"}, "5593250": {"variable": {"__ref": "5592097"}, "__type": "VarRef"}, "5593251": {"values": {"max-width": "200px"}, "mixins": [], "__type": "RuleSet"}, "5593253": {"variants": [{"__ref": "5592426"}], "args": [], "attrs": {}, "rs": {"__ref": "5593254"}, "dataCond": {"__ref": "5593261"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593254": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "5593261": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5593269": {"variants": [{"__ref": "5592426"}], "args": [], "attrs": {"data-testid": {"__ref": "16997002"}}, "rs": {"__ref": "5593270"}, "dataCond": {"__ref": "34119006"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593270": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "wrap", "height": "wrap", "flex-row-gap": "10px", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "5593277": {"param": {"__ref": "5592077"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "56253070"}, "tplNode": {"__ref": "5592456"}, "implicitState": {"__ref": "5592113"}, "__type": "State"}, "5593278": {"param": {"__ref": "5592080"}, "accessType": "writable", "variableType": "object", "onChangeParam": {"__ref": "34119010"}, "tplNode": null, "implicitState": null, "__type": "State"}, "5593279": {"param": {"__ref": "5592084"}, "accessType": "private", "variableType": "array", "onChangeParam": {"__ref": "56253075"}, "tplNode": {"__ref": "5592485"}, "implicitState": {"__ref": "5593191"}, "__type": "State"}, "5593280": {"variants": [{"__ref": "5592059"}], "args": [{"__ref": "5593290"}, {"__ref": "34119025"}, {"__ref": "1556008"}], "attrs": {}, "rs": {"__ref": "5593296"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593290": {"param": {"__ref": "5592096"}, "expr": {"__ref": "5593291"}, "__type": "Arg"}, "5593291": {"interactions": [{"__ref": "5593292"}], "__type": "EventHandler"}, "5593292": {"interactionName": "Set people", "actionName": "updateVariable", "args": [{"__ref": "28486020"}, {"__ref": "28486021"}, {"__ref": "28486022"}, {"__ref": "28486023"}], "condExpr": null, "conditionalMode": "always", "uuid": "0z_X-CSyv05", "parent": {"__ref": "5593291"}, "__type": "Interaction"}, "5593296": {"values": {"max-width": "100%", "width": "stretch"}, "mixins": [], "__type": "RuleSet"}, "5593299": {"variants": [{"__ref": "5592059"}], "args": [], "attrs": {}, "rs": {"__ref": "5593300"}, "dataCond": {"__ref": "5593313"}, "dataRep": {"__ref": "5593314"}, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593300": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "width": "stretch", "background": "linear-gradient(#DAD9D9, #DAD9D9)", "padding-top": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-left": "8px", "max-width": "1000px"}, "mixins": [], "__type": "RuleSet"}, "5593313": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5593314": {"element": {"__ref": "34119032"}, "index": {"__ref": "34119034"}, "collection": {"__ref": "5593317"}, "__type": "Rep"}, "5593317": {"path": ["$state", "people"], "fallback": {"__ref": "5593318"}, "__type": "ObjectPath"}, "5593318": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "5593329": {"name": null, "component": {"__ref": "5592543"}, "uuid": "3rhoVH5eVJV", "parent": {"__ref": "5592056"}, "locked": null, "vsettings": [{"__ref": "5593330"}], "__type": "TplComponent"}, "5593330": {"variants": [{"__ref": "5592059"}], "args": [{"__ref": "5593331"}, {"__ref": "5593341"}, {"__ref": "5593347"}, {"__ref": "5593357"}, {"__ref": "22485044"}, {"__ref": "1499910"}], "attrs": {"onClick": {"__ref": "5593359"}}, "rs": {"__ref": "5593367"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593331": {"param": {"__ref": "5592553"}, "expr": {"__ref": "5593332"}, "__type": "Arg"}, "5593332": {"tpl": [{"__ref": "5593333"}], "__type": "VirtualRenderExpr"}, "5593333": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "rhZzfdY75ZS", "parent": {"__ref": "5593329"}, "locked": null, "vsettings": [{"__ref": "5593334"}], "__type": "TplTag"}, "5593334": {"variants": [{"__ref": "5592059"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593335"}}, "rs": {"__ref": "5593336"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593335": {"asset": {"__ref": "5592273"}, "__type": "ImageAssetRef"}, "5593336": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593341": {"param": {"__ref": "5592544"}, "expr": {"__ref": "5593342"}, "__type": "Arg"}, "5593342": {"tpl": [{"__ref": "5593343"}], "__type": "RenderExpr"}, "5593343": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "0V9ynmmPMCf", "parent": {"__ref": "5593329"}, "locked": null, "vsettings": [{"__ref": "5593344"}], "__type": "TplTag"}, "5593344": {"variants": [{"__ref": "5592059"}], "args": [], "attrs": {}, "rs": {"__ref": "5593345"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5593346"}, "columnsConfig": null, "__type": "VariantSetting"}, "5593345": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593346": {"markers": [], "text": "Add", "__type": "RawText"}, "5593347": {"param": {"__ref": "5592556"}, "expr": {"__ref": "5593348"}, "__type": "Arg"}, "5593348": {"tpl": [{"__ref": "5593349"}], "__type": "VirtualRenderExpr"}, "5593349": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "qQ6IbfXlkuo", "parent": {"__ref": "5593329"}, "locked": null, "vsettings": [{"__ref": "5593350"}], "__type": "TplTag"}, "5593350": {"variants": [{"__ref": "5592059"}], "args": [], "attrs": {"outerHTML": {"__ref": "5593351"}}, "rs": {"__ref": "5593352"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593351": {"asset": {"__ref": "5592776"}, "__type": "ImageAssetRef"}, "5593352": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "5593357": {"param": {"__ref": "5592565"}, "expr": {"__ref": "5593358"}, "__type": "Arg"}, "5593358": {"variants": [{"__ref": "5592600"}], "__type": "VariantsRef"}, "5593359": {"interactions": [{"__ref": "5593360"}], "__type": "EventHandler"}, "5593360": {"interactionName": "Set people", "actionName": "updateVariable", "args": [{"__ref": "5593361"}, {"__ref": "5593363"}, {"__ref": "5593365"}], "condExpr": null, "conditionalMode": "always", "uuid": "TfxR7TsmP4G", "parent": {"__ref": "5593359"}, "__type": "Interaction"}, "5593361": {"name": "variable", "expr": {"__ref": "5593362"}, "__type": "NameArg"}, "5593362": {"path": ["$state", "people"], "fallback": null, "__type": "ObjectPath"}, "5593363": {"name": "operation", "expr": {"__ref": "5593364"}, "__type": "NameArg"}, "5593364": {"code": "5", "fallback": null, "__type": "CustomCode"}, "5593365": {"name": "value", "expr": {"__ref": "5593366"}, "__type": "NameArg"}, "5593366": {"code": "(({\n    firstName: \"\",\n    lastName: \"\",\n    nicknames: []\n}))", "fallback": null, "__type": "CustomCode"}, "5593367": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "5593370": {"variants": [{"__ref": "5592059"}], "args": [], "attrs": {"data-testid": {"__ref": "28486027"}}, "rs": {"__ref": "5593371"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593371": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "flex-row-gap": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "5593380": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "5593742": {"component": {"__ref": "5592050"}, "matrix": {"__ref": "5593743"}, "customMatrix": {"__ref": "138901"}, "__type": "PageArena"}, "5593743": {"rows": [{"__ref": "5593744"}], "__type": "ArenaFrameGrid"}, "5593744": {"cols": [{"__ref": "5593745"}, {"__ref": "5593751"}], "rowKey": {"__ref": "5592059"}, "__type": "ArenaFrameRow"}, "5593745": {"frame": {"__ref": "5593746"}, "cellKey": null, "__type": "ArenaFrameCell"}, "5593746": {"uuid": "vQwOcC2pczbK", "width": 1366, "height": 768, "container": {"__ref": "5593747"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592059"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593747": {"name": null, "component": {"__ref": "5592050"}, "uuid": "D3g4IhbtAUc2", "parent": null, "locked": null, "vsettings": [{"__ref": "5593748"}], "__type": "TplComponent"}, "5593748": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593750"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593749": {"uuid": "6os-OvNxflCg", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "5593750": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593751": {"frame": {"__ref": "5593752"}, "cellKey": null, "__type": "ArenaFrameCell"}, "5593752": {"uuid": "wFCL9lsEb7iy", "width": 414, "height": 736, "container": {"__ref": "5593753"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592059"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593753": {"name": null, "component": {"__ref": "5592050"}, "uuid": "9k3H2bsXzKy4", "parent": null, "locked": null, "vsettings": [{"__ref": "5593754"}], "__type": "TplComponent"}, "5593754": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593755"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593755": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593782": {"component": {"__ref": "5592073"}, "matrix": {"__ref": "5593783"}, "customMatrix": {"__ref": "5593790"}, "__type": "ComponentArena"}, "5593783": {"rows": [{"__ref": "5593784"}], "__type": "ArenaFrameGrid"}, "5593784": {"cols": [{"__ref": "5593785"}], "rowKey": null, "__type": "ArenaFrameRow"}, "5593785": {"frame": {"__ref": "5593786"}, "cellKey": {"__ref": "5592426"}, "__type": "ArenaFrameCell"}, "5593786": {"uuid": "NG9aOUdUHpU_", "width": 800, "height": 600, "container": {"__ref": "5593787"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592426"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593787": {"name": null, "component": {"__ref": "5592073"}, "uuid": "RQjxbWLk2Z9F", "parent": null, "locked": null, "vsettings": [{"__ref": "5593788"}], "__type": "TplComponent"}, "5593788": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593789"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593789": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593790": {"rows": [{"__ref": "5593791"}], "__type": "ArenaFrameGrid"}, "5593791": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "5593792": {"component": {"__ref": "5592101"}, "matrix": {"__ref": "5593793"}, "customMatrix": {"__ref": "5593800"}, "__type": "ComponentArena"}, "5593793": {"rows": [{"__ref": "5593794"}], "__type": "ArenaFrameGrid"}, "5593794": {"cols": [{"__ref": "5593795"}], "rowKey": null, "__type": "ArenaFrameRow"}, "5593795": {"frame": {"__ref": "5593796"}, "cellKey": {"__ref": "5592370"}, "__type": "ArenaFrameCell"}, "5593796": {"uuid": "dANSH__S0ZPH", "width": 340, "height": 340, "container": {"__ref": "5593797"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592370"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593797": {"name": null, "component": {"__ref": "5592101"}, "uuid": "1sDAH4fMA2Vj", "parent": null, "locked": null, "vsettings": [{"__ref": "5593798"}], "__type": "TplComponent"}, "5593798": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593799"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593799": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593800": {"rows": [{"__ref": "5593801"}], "__type": "ArenaFrameGrid"}, "5593801": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "5593802": {"component": {"__ref": "5592115"}, "matrix": {"__ref": "5593803"}, "customMatrix": {"__ref": "5593849"}, "__type": "ComponentArena"}, "5593803": {"rows": [{"__ref": "5593804"}, {"__ref": "5593825"}, {"__ref": "5593831"}, {"__ref": "5593837"}, {"__ref": "5593843"}], "__type": "ArenaFrameGrid"}, "5593804": {"cols": [{"__ref": "5593805"}, {"__ref": "5593810"}, {"__ref": "5593815"}, {"__ref": "5593820"}], "rowKey": null, "__type": "ArenaFrameRow"}, "5593805": {"frame": {"__ref": "5593806"}, "cellKey": {"__ref": "5592168"}, "__type": "ArenaFrameCell"}, "5593806": {"uuid": "S_I2Sr-pFxi3", "width": 340, "height": 340, "container": {"__ref": "5593807"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592168"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593807": {"name": null, "component": {"__ref": "5592115"}, "uuid": "Ceh0W3Aerxbx", "parent": null, "locked": null, "vsettings": [{"__ref": "5593808"}], "__type": "TplComponent"}, "5593808": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593809"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593809": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593810": {"frame": {"__ref": "5593811"}, "cellKey": {"__ref": "5592208"}, "__type": "ArenaFrameCell"}, "5593811": {"uuid": "eief0mjz-7qw", "width": 340, "height": 340, "container": {"__ref": "5593812"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592208"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593812": {"name": null, "component": {"__ref": "5592115"}, "uuid": "eU-Kbl6pI0Iy", "parent": null, "locked": null, "vsettings": [{"__ref": "5593813"}], "__type": "TplComponent"}, "5593813": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593814"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593814": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593815": {"frame": {"__ref": "5593816"}, "cellKey": {"__ref": "5592256"}, "__type": "ArenaFrameCell"}, "5593816": {"uuid": "fz2YeExQ4qO6", "width": 340, "height": 340, "container": {"__ref": "5593817"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592256"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593817": {"name": null, "component": {"__ref": "5592115"}, "uuid": "muLIigEdUJbH", "parent": null, "locked": null, "vsettings": [{"__ref": "5593818"}], "__type": "TplComponent"}, "5593818": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593819"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593819": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593820": {"frame": {"__ref": "5593821"}, "cellKey": {"__ref": "5592201"}, "__type": "ArenaFrameCell"}, "5593821": {"uuid": "_qfcfwcYzejQ", "width": 340, "height": 340, "container": {"__ref": "5593822"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592201"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593822": {"name": null, "component": {"__ref": "5592115"}, "uuid": "S8RzKRCBglu9", "parent": null, "locked": null, "vsettings": [{"__ref": "5593823"}], "__type": "TplComponent"}, "5593823": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593824"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593824": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593825": {"cols": [{"__ref": "5593826"}], "rowKey": {"__ref": "5592180"}, "__type": "ArenaFrameRow"}, "5593826": {"frame": {"__ref": "5593827"}, "cellKey": {"__ref": "5592179"}, "__type": "ArenaFrameCell"}, "5593827": {"uuid": "4ZVHbC5WsP9U", "width": 340, "height": 340, "container": {"__ref": "5593828"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592179"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593828": {"name": null, "component": {"__ref": "5592115"}, "uuid": "LQRHqlEyehGi", "parent": null, "locked": null, "vsettings": [{"__ref": "5593829"}], "__type": "TplComponent"}, "5593829": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593830"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593830": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593831": {"cols": [{"__ref": "5593832"}], "rowKey": {"__ref": "5592283"}, "__type": "ArenaFrameRow"}, "5593832": {"frame": {"__ref": "5593833"}, "cellKey": {"__ref": "5592282"}, "__type": "ArenaFrameCell"}, "5593833": {"uuid": "svHO2SVpeU3a", "width": 340, "height": 340, "container": {"__ref": "5593834"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592282"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593834": {"name": null, "component": {"__ref": "5592115"}, "uuid": "ivqqDQ7VQio9", "parent": null, "locked": null, "vsettings": [{"__ref": "5593835"}], "__type": "TplComponent"}, "5593835": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593836"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593836": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593837": {"cols": [{"__ref": "5593838"}], "rowKey": {"__ref": "5592212"}, "__type": "ArenaFrameRow"}, "5593838": {"frame": {"__ref": "5593839"}, "cellKey": {"__ref": "5592211"}, "__type": "ArenaFrameCell"}, "5593839": {"uuid": "9ogc86BA38nt", "width": 340, "height": 340, "container": {"__ref": "5593840"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592211"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593840": {"name": null, "component": {"__ref": "5592115"}, "uuid": "3ylsh7eVO3fA", "parent": null, "locked": null, "vsettings": [{"__ref": "5593841"}], "__type": "TplComponent"}, "5593841": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593842"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593842": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593843": {"cols": [{"__ref": "5593844"}], "rowKey": {"__ref": "5592185"}, "__type": "ArenaFrameRow"}, "5593844": {"frame": {"__ref": "5593845"}, "cellKey": {"__ref": "5592184"}, "__type": "ArenaFrameCell"}, "5593845": {"uuid": "HdQZYUduiqDT", "width": 340, "height": 340, "container": {"__ref": "5593846"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592184"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593846": {"name": null, "component": {"__ref": "5592115"}, "uuid": "mThTSM-JQSRR", "parent": null, "locked": null, "vsettings": [{"__ref": "5593847"}], "__type": "TplComponent"}, "5593847": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593848"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593848": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593849": {"rows": [{"__ref": "5593850"}], "__type": "ArenaFrameGrid"}, "5593850": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "5593851": {"component": {"__ref": "5592486"}, "matrix": {"__ref": "5593852"}, "customMatrix": {"__ref": "5593859"}, "__type": "ComponentArena"}, "5593852": {"rows": [{"__ref": "5593853"}], "__type": "ArenaFrameGrid"}, "5593853": {"cols": [{"__ref": "5593854"}], "rowKey": null, "__type": "ArenaFrameRow"}, "5593854": {"frame": {"__ref": "5593855"}, "cellKey": {"__ref": "5592502"}, "__type": "ArenaFrameCell"}, "5593855": {"uuid": "9mawPp1NC9Vd", "width": 340, "height": 340, "container": {"__ref": "5593856"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592502"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593856": {"name": null, "component": {"__ref": "5592486"}, "uuid": "oT0YQaEF-E9w", "parent": null, "locked": null, "vsettings": [{"__ref": "5593857"}], "__type": "TplComponent"}, "5593857": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593858"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593858": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593859": {"rows": [{"__ref": "5593860"}], "__type": "ArenaFrameGrid"}, "5593860": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "5593861": {"component": {"__ref": "5592543"}, "matrix": {"__ref": "5593862"}, "customMatrix": {"__ref": "5594000"}, "__type": "ComponentArena"}, "5593862": {"rows": [{"__ref": "5593863"}, {"__ref": "5593889"}, {"__ref": "5593895"}, {"__ref": "5593901"}, {"__ref": "5593907"}, {"__ref": "5593923"}, {"__ref": "5593934"}], "__type": "ArenaFrameGrid"}, "5593863": {"cols": [{"__ref": "5593864"}, {"__ref": "5593869"}, {"__ref": "5593874"}, {"__ref": "5593879"}, {"__ref": "5593884"}], "rowKey": null, "__type": "ArenaFrameRow"}, "5593864": {"frame": {"__ref": "5593865"}, "cellKey": {"__ref": "5592585"}, "__type": "ArenaFrameCell"}, "5593865": {"uuid": "iGqkyTBQXjhO", "width": 340, "height": 340, "container": {"__ref": "5593866"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592585"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593866": {"name": null, "component": {"__ref": "5592543"}, "uuid": "PR32l0UibBdy", "parent": null, "locked": null, "vsettings": [{"__ref": "5593867"}], "__type": "TplComponent"}, "5593867": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593868"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593868": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593869": {"frame": {"__ref": "5593870"}, "cellKey": {"__ref": "5592685"}, "__type": "ArenaFrameCell"}, "5593870": {"uuid": "vXb7SSCUpeth", "width": 340, "height": 340, "container": {"__ref": "5593871"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592685"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593871": {"name": null, "component": {"__ref": "5592543"}, "uuid": "QZeaAk8Bh_Vd", "parent": null, "locked": null, "vsettings": [{"__ref": "5593872"}], "__type": "TplComponent"}, "5593872": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593873"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593873": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593874": {"frame": {"__ref": "5593875"}, "cellKey": {"__ref": "5592682"}, "__type": "ArenaFrameCell"}, "5593875": {"uuid": "3qoHbjMrZwOL", "width": 340, "height": 340, "container": {"__ref": "5593876"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592682"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593876": {"name": null, "component": {"__ref": "5592543"}, "uuid": "vV6AI4-FZ1X7", "parent": null, "locked": null, "vsettings": [{"__ref": "5593877"}], "__type": "TplComponent"}, "5593877": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593878"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593878": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593879": {"frame": {"__ref": "5593880"}, "cellKey": {"__ref": "5592637"}, "__type": "ArenaFrameCell"}, "5593880": {"uuid": "aRUmAShwHoSj", "width": 340, "height": 340, "container": {"__ref": "5593881"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593881": {"name": null, "component": {"__ref": "5592543"}, "uuid": "Sp6AWzVJ5xsp", "parent": null, "locked": null, "vsettings": [{"__ref": "5593882"}], "__type": "TplComponent"}, "5593882": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593883"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593883": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593884": {"frame": {"__ref": "5593885"}, "cellKey": {"__ref": "5592641"}, "__type": "ArenaFrameCell"}, "5593885": {"uuid": "RtVVQ-0gWSCV", "width": 340, "height": 340, "container": {"__ref": "5593886"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593886": {"name": null, "component": {"__ref": "5592543"}, "uuid": "J75_8hwU-Jc_", "parent": null, "locked": null, "vsettings": [{"__ref": "5593887"}], "__type": "TplComponent"}, "5593887": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593888"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593888": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593889": {"cols": [{"__ref": "5593890"}], "rowKey": {"__ref": "5592597"}, "__type": "ArenaFrameRow"}, "5593890": {"frame": {"__ref": "5593891"}, "cellKey": {"__ref": "5592596"}, "__type": "ArenaFrameCell"}, "5593891": {"uuid": "tTjlBDPZhtM1", "width": 340, "height": 340, "container": {"__ref": "5593892"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592596"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593892": {"name": null, "component": {"__ref": "5592543"}, "uuid": "j2JoWoVXPyfc", "parent": null, "locked": null, "vsettings": [{"__ref": "5593893"}], "__type": "TplComponent"}, "5593893": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593894"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593894": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593895": {"cols": [{"__ref": "5593896"}], "rowKey": {"__ref": "5592691"}, "__type": "ArenaFrameRow"}, "5593896": {"frame": {"__ref": "5593897"}, "cellKey": {"__ref": "5592690"}, "__type": "ArenaFrameCell"}, "5593897": {"uuid": "1brwkODson9J", "width": 340, "height": 340, "container": {"__ref": "5593898"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592690"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593898": {"name": null, "component": {"__ref": "5592543"}, "uuid": "dJ0fxEMdflUj", "parent": null, "locked": null, "vsettings": [{"__ref": "5593899"}], "__type": "TplComponent"}, "5593899": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593900"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593900": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593901": {"cols": [{"__ref": "5593902"}], "rowKey": {"__ref": "5592695"}, "__type": "ArenaFrameRow"}, "5593902": {"frame": {"__ref": "5593903"}, "cellKey": {"__ref": "5592694"}, "__type": "ArenaFrameCell"}, "5593903": {"uuid": "CbahST5M5CGD", "width": 340, "height": 340, "container": {"__ref": "5593904"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592694"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593904": {"name": null, "component": {"__ref": "5592543"}, "uuid": "mGvDXirjT5bx", "parent": null, "locked": null, "vsettings": [{"__ref": "5593905"}], "__type": "TplComponent"}, "5593905": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593906"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593906": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593907": {"cols": [{"__ref": "5593908"}, {"__ref": "5593913"}, {"__ref": "5593918"}], "rowKey": {"__ref": "5592666"}, "__type": "ArenaFrameRow"}, "5593908": {"frame": {"__ref": "5593909"}, "cellKey": {"__ref": "5592665"}, "__type": "ArenaFrameCell"}, "5593909": {"uuid": "YbJS-eO2qiXt", "width": 340, "height": 340, "container": {"__ref": "5593910"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592665"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593910": {"name": null, "component": {"__ref": "5592543"}, "uuid": "5OYaXbB_vZ7e", "parent": null, "locked": null, "vsettings": [{"__ref": "5593911"}], "__type": "TplComponent"}, "5593911": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593912"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593912": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593913": {"frame": {"__ref": "5593914"}, "cellKey": {"__ref": "5592667"}, "__type": "ArenaFrameCell"}, "5593914": {"uuid": "rK1rUP1EmKzP", "width": 340, "height": 340, "container": {"__ref": "5593915"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592667"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593915": {"name": null, "component": {"__ref": "5592543"}, "uuid": "gHz4ov9QV4U1", "parent": null, "locked": null, "vsettings": [{"__ref": "5593916"}], "__type": "TplComponent"}, "5593916": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593917"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593917": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593918": {"frame": {"__ref": "5593919"}, "cellKey": {"__ref": "5592668"}, "__type": "ArenaFrameCell"}, "5593919": {"uuid": "bjGN5CtjpO0V", "width": 340, "height": 340, "container": {"__ref": "5593920"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592668"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593920": {"name": null, "component": {"__ref": "5592543"}, "uuid": "7aLPH8K7ul-7", "parent": null, "locked": null, "vsettings": [{"__ref": "5593921"}], "__type": "TplComponent"}, "5593921": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593922"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593922": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593923": {"cols": [{"__ref": "5593924"}, {"__ref": "5593929"}], "rowKey": {"__ref": "5592744"}, "__type": "ArenaFrameRow"}, "5593924": {"frame": {"__ref": "5593925"}, "cellKey": {"__ref": "5592745"}, "__type": "ArenaFrameCell"}, "5593925": {"uuid": "M8CqCW9tLDig", "width": 340, "height": 340, "container": {"__ref": "5593926"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592745"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593926": {"name": null, "component": {"__ref": "5592543"}, "uuid": "LxuZsFqvyZ13", "parent": null, "locked": null, "vsettings": [{"__ref": "5593927"}], "__type": "TplComponent"}, "5593927": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593928"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593928": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593929": {"frame": {"__ref": "5593930"}, "cellKey": {"__ref": "5592743"}, "__type": "ArenaFrameCell"}, "5593930": {"uuid": "m_kj30X2p3-4", "width": 340, "height": 340, "container": {"__ref": "5593931"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592743"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593931": {"name": null, "component": {"__ref": "5592543"}, "uuid": "LUGIHprVJB6F", "parent": null, "locked": null, "vsettings": [{"__ref": "5593932"}], "__type": "TplComponent"}, "5593932": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593933"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593933": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593934": {"cols": [{"__ref": "5593935"}, {"__ref": "5593940"}, {"__ref": "5593945"}, {"__ref": "5593950"}, {"__ref": "5593955"}, {"__ref": "5593960"}, {"__ref": "5593965"}, {"__ref": "5593970"}, {"__ref": "5593975"}, {"__ref": "5593980"}, {"__ref": "5593985"}, {"__ref": "5593990"}, {"__ref": "5593995"}], "rowKey": {"__ref": "5592601"}, "__type": "ArenaFrameRow"}, "5593935": {"frame": {"__ref": "5593936"}, "cellKey": {"__ref": "5592600"}, "__type": "ArenaFrameCell"}, "5593936": {"uuid": "kagraoIJ8gHv", "width": 340, "height": 340, "container": {"__ref": "5593937"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592600"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593937": {"name": null, "component": {"__ref": "5592543"}, "uuid": "nMW6V0wze_-u", "parent": null, "locked": null, "vsettings": [{"__ref": "5593938"}], "__type": "TplComponent"}, "5593938": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593939"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593939": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593940": {"frame": {"__ref": "5593941"}, "cellKey": {"__ref": "5592602"}, "__type": "ArenaFrameCell"}, "5593941": {"uuid": "AeQENYwTlYB-", "width": 340, "height": 340, "container": {"__ref": "5593942"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592602"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593942": {"name": null, "component": {"__ref": "5592543"}, "uuid": "bhIhJE1hsspE", "parent": null, "locked": null, "vsettings": [{"__ref": "5593943"}], "__type": "TplComponent"}, "5593943": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593944"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593944": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593945": {"frame": {"__ref": "5593946"}, "cellKey": {"__ref": "5592603"}, "__type": "ArenaFrameCell"}, "5593946": {"uuid": "_ZmHJHXDgknv", "width": 340, "height": 340, "container": {"__ref": "5593947"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592603"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593947": {"name": null, "component": {"__ref": "5592543"}, "uuid": "ObDXMiepLMpJ", "parent": null, "locked": null, "vsettings": [{"__ref": "5593948"}], "__type": "TplComponent"}, "5593948": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593949"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593949": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593950": {"frame": {"__ref": "5593951"}, "cellKey": {"__ref": "5592604"}, "__type": "ArenaFrameCell"}, "5593951": {"uuid": "xvy6S470cYyp", "width": 340, "height": 340, "container": {"__ref": "5593952"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592604"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593952": {"name": null, "component": {"__ref": "5592543"}, "uuid": "O4smYTgsiwJ3", "parent": null, "locked": null, "vsettings": [{"__ref": "5593953"}], "__type": "TplComponent"}, "5593953": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593954"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593954": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593955": {"frame": {"__ref": "5593956"}, "cellKey": {"__ref": "5592605"}, "__type": "ArenaFrameCell"}, "5593956": {"uuid": "Oee5wnCSJ-xj", "width": 340, "height": 340, "container": {"__ref": "5593957"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592605"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593957": {"name": null, "component": {"__ref": "5592543"}, "uuid": "r-3tQisJJ2Pj", "parent": null, "locked": null, "vsettings": [{"__ref": "5593958"}], "__type": "TplComponent"}, "5593958": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593959"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593959": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593960": {"frame": {"__ref": "5593961"}, "cellKey": {"__ref": "5592606"}, "__type": "ArenaFrameCell"}, "5593961": {"uuid": "QoSx9ioQEiao", "width": 340, "height": 340, "container": {"__ref": "5593962"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592606"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593962": {"name": null, "component": {"__ref": "5592543"}, "uuid": "RUQ_fWjB5c4-", "parent": null, "locked": null, "vsettings": [{"__ref": "5593963"}], "__type": "TplComponent"}, "5593963": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593964"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593964": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593965": {"frame": {"__ref": "5593966"}, "cellKey": {"__ref": "5592607"}, "__type": "ArenaFrameCell"}, "5593966": {"uuid": "cyOen6K1Wac1", "width": 340, "height": 340, "container": {"__ref": "5593967"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592607"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593967": {"name": null, "component": {"__ref": "5592543"}, "uuid": "X51fRVYu4rql", "parent": null, "locked": null, "vsettings": [{"__ref": "5593968"}], "__type": "TplComponent"}, "5593968": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593969"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593969": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593970": {"frame": {"__ref": "5593971"}, "cellKey": {"__ref": "5592608"}, "__type": "ArenaFrameCell"}, "5593971": {"uuid": "ch-Sr4KbGsge", "width": 340, "height": 340, "container": {"__ref": "5593972"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592608"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593972": {"name": null, "component": {"__ref": "5592543"}, "uuid": "GM0sPWnct6xt", "parent": null, "locked": null, "vsettings": [{"__ref": "5593973"}], "__type": "TplComponent"}, "5593973": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593974"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593974": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593975": {"frame": {"__ref": "5593976"}, "cellKey": {"__ref": "5592609"}, "__type": "ArenaFrameCell"}, "5593976": {"uuid": "BotB3J7f4pcO", "width": 340, "height": 340, "container": {"__ref": "5593977"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592609"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593977": {"name": null, "component": {"__ref": "5592543"}, "uuid": "AtzNEhuhczzZ", "parent": null, "locked": null, "vsettings": [{"__ref": "5593978"}], "__type": "TplComponent"}, "5593978": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593979"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593979": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593980": {"frame": {"__ref": "5593981"}, "cellKey": {"__ref": "5592610"}, "__type": "ArenaFrameCell"}, "5593981": {"uuid": "ovksujGS1YL7", "width": 340, "height": 340, "container": {"__ref": "5593982"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592610"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593982": {"name": null, "component": {"__ref": "5592543"}, "uuid": "VeoJ2QRHLgX1", "parent": null, "locked": null, "vsettings": [{"__ref": "5593983"}], "__type": "TplComponent"}, "5593983": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593984"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593984": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593985": {"frame": {"__ref": "5593986"}, "cellKey": {"__ref": "5592611"}, "__type": "ArenaFrameCell"}, "5593986": {"uuid": "FN3vYHoNd3SE", "width": 340, "height": 340, "container": {"__ref": "5593987"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592611"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593987": {"name": null, "component": {"__ref": "5592543"}, "uuid": "oftwXs3t6vt2", "parent": null, "locked": null, "vsettings": [{"__ref": "5593988"}], "__type": "TplComponent"}, "5593988": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593989"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593989": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593990": {"frame": {"__ref": "5593991"}, "cellKey": {"__ref": "5592612"}, "__type": "ArenaFrameCell"}, "5593991": {"uuid": "f_Q7Sf44LL4l", "width": 340, "height": 340, "container": {"__ref": "5593992"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592612"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593992": {"name": null, "component": {"__ref": "5592543"}, "uuid": "6I2M4YgQfQ28", "parent": null, "locked": null, "vsettings": [{"__ref": "5593993"}], "__type": "TplComponent"}, "5593993": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593994"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593994": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5593995": {"frame": {"__ref": "5593996"}, "cellKey": {"__ref": "5592613"}, "__type": "ArenaFrameCell"}, "5593996": {"uuid": "Q7ttOvmdLY5A", "width": 340, "height": 340, "container": {"__ref": "5593997"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592613"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5593997": {"name": null, "component": {"__ref": "5592543"}, "uuid": "GYAQPz_NS4UI", "parent": null, "locked": null, "vsettings": [{"__ref": "5593998"}], "__type": "TplComponent"}, "5593998": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5593999"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5593999": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594000": {"rows": [{"__ref": "5594001"}], "__type": "ArenaFrameGrid"}, "5594001": {"cols": [{"__ref": "5594002"}, {"__ref": "5594007"}, {"__ref": "5594012"}, {"__ref": "5594017"}, {"__ref": "5594022"}, {"__ref": "5594027"}, {"__ref": "5594032"}, {"__ref": "5594037"}, {"__ref": "5594042"}, {"__ref": "5594047"}, {"__ref": "5594052"}, {"__ref": "5594057"}, {"__ref": "5594062"}, {"__ref": "5594067"}, {"__ref": "5594072"}, {"__ref": "5594077"}, {"__ref": "5594082"}, {"__ref": "5594087"}, {"__ref": "5594092"}, {"__ref": "5594097"}, {"__ref": "5594102"}, {"__ref": "5594107"}, {"__ref": "5594112"}, {"__ref": "5594117"}, {"__ref": "5594122"}, {"__ref": "5594127"}, {"__ref": "5594132"}, {"__ref": "5594137"}, {"__ref": "5594142"}], "rowKey": null, "__type": "ArenaFrameRow"}, "5594002": {"frame": {"__ref": "5594003"}, "cellKey": [{"__ref": "5592607"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594003": {"uuid": "gWwQYTwNjO4m", "width": 340, "height": 340, "container": {"__ref": "5594004"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592607"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594004": {"name": null, "component": {"__ref": "5592543"}, "uuid": "sidrD_N5MwBz", "parent": null, "locked": null, "vsettings": [{"__ref": "5594005"}], "__type": "TplComponent"}, "5594005": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594006"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594006": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594007": {"frame": {"__ref": "5594008"}, "cellKey": [{"__ref": "5592607"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594008": {"uuid": "DowFhn-S1Vnp", "width": 340, "height": 340, "container": {"__ref": "5594009"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592607"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594009": {"name": null, "component": {"__ref": "5592543"}, "uuid": "PNhuOMczx6wy", "parent": null, "locked": null, "vsettings": [{"__ref": "5594010"}], "__type": "TplComponent"}, "5594010": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594011"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594011": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594012": {"frame": {"__ref": "5594013"}, "cellKey": [{"__ref": "5592608"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594013": {"uuid": "gGJ41Njuy0Ji", "width": 340, "height": 340, "container": {"__ref": "5594014"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592608"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594014": {"name": null, "component": {"__ref": "5592543"}, "uuid": "kpmCrdULRKG1", "parent": null, "locked": null, "vsettings": [{"__ref": "5594015"}], "__type": "TplComponent"}, "5594015": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594016"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594016": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594017": {"frame": {"__ref": "5594018"}, "cellKey": [{"__ref": "5592608"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594018": {"uuid": "RIks42k5Njhy", "width": 340, "height": 340, "container": {"__ref": "5594019"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592608"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594019": {"name": null, "component": {"__ref": "5592543"}, "uuid": "hFc9ZGYb7qwm", "parent": null, "locked": null, "vsettings": [{"__ref": "5594020"}], "__type": "TplComponent"}, "5594020": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594021"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594021": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594022": {"frame": {"__ref": "5594023"}, "cellKey": [{"__ref": "5592603"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594023": {"uuid": "2v9oIU-QTQa2", "width": 340, "height": 340, "container": {"__ref": "5594024"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592603"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594024": {"name": null, "component": {"__ref": "5592543"}, "uuid": "-3uwUFwwy7Jh", "parent": null, "locked": null, "vsettings": [{"__ref": "5594025"}], "__type": "TplComponent"}, "5594025": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594026"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594026": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594027": {"frame": {"__ref": "5594028"}, "cellKey": [{"__ref": "5592603"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594028": {"uuid": "rXtUixiONDkk", "width": 340, "height": 340, "container": {"__ref": "5594029"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592603"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594029": {"name": null, "component": {"__ref": "5592543"}, "uuid": "BitD9JI0XBB0", "parent": null, "locked": null, "vsettings": [{"__ref": "5594030"}], "__type": "TplComponent"}, "5594030": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594031"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594031": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594032": {"frame": {"__ref": "5594033"}, "cellKey": [{"__ref": "5592604"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594033": {"uuid": "Ta_dIFio3AQR", "width": 340, "height": 340, "container": {"__ref": "5594034"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592604"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594034": {"name": null, "component": {"__ref": "5592543"}, "uuid": "yT_DDqTUcCmo", "parent": null, "locked": null, "vsettings": [{"__ref": "5594035"}], "__type": "TplComponent"}, "5594035": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594036"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594036": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594037": {"frame": {"__ref": "5594038"}, "cellKey": [{"__ref": "5592604"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594038": {"uuid": "TNzj9oZU91GT", "width": 340, "height": 340, "container": {"__ref": "5594039"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592604"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594039": {"name": null, "component": {"__ref": "5592543"}, "uuid": "YugiDCYf48c0", "parent": null, "locked": null, "vsettings": [{"__ref": "5594040"}], "__type": "TplComponent"}, "5594040": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594041"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594041": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594042": {"frame": {"__ref": "5594043"}, "cellKey": [{"__ref": "5592609"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594043": {"uuid": "ONdqC11Mve5A", "width": 340, "height": 340, "container": {"__ref": "5594044"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592609"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594044": {"name": null, "component": {"__ref": "5592543"}, "uuid": "pAQ6x7pQrLhR", "parent": null, "locked": null, "vsettings": [{"__ref": "5594045"}], "__type": "TplComponent"}, "5594045": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594047": {"frame": {"__ref": "5594048"}, "cellKey": [{"__ref": "5592609"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594048": {"uuid": "Jn9QWE8VXPcu", "width": 340, "height": 340, "container": {"__ref": "5594049"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592609"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594049": {"name": null, "component": {"__ref": "5592543"}, "uuid": "SbyQvLG2WZjO", "parent": null, "locked": null, "vsettings": [{"__ref": "5594050"}], "__type": "TplComponent"}, "5594050": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594051"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594051": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594052": {"frame": {"__ref": "5594053"}, "cellKey": [{"__ref": "5592610"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594053": {"uuid": "jCanupe3_0JL", "width": 340, "height": 340, "container": {"__ref": "5594054"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592610"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594054": {"name": null, "component": {"__ref": "5592543"}, "uuid": "q6VfZWba7X8J", "parent": null, "locked": null, "vsettings": [{"__ref": "5594055"}], "__type": "TplComponent"}, "5594055": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594056"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594056": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594057": {"frame": {"__ref": "5594058"}, "cellKey": [{"__ref": "5592610"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594058": {"uuid": "I8QZmPXKRDhn", "width": 340, "height": 340, "container": {"__ref": "5594059"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592610"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594059": {"name": null, "component": {"__ref": "5592543"}, "uuid": "nPJIc1go09jb", "parent": null, "locked": null, "vsettings": [{"__ref": "5594060"}], "__type": "TplComponent"}, "5594060": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594061"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594061": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594062": {"frame": {"__ref": "5594063"}, "cellKey": [{"__ref": "5592611"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594063": {"uuid": "LIVGAay_-zVl", "width": 340, "height": 340, "container": {"__ref": "5594064"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592611"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594064": {"name": null, "component": {"__ref": "5592543"}, "uuid": "8F_phAeMB-Jm", "parent": null, "locked": null, "vsettings": [{"__ref": "5594065"}], "__type": "TplComponent"}, "5594065": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594066"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594066": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594067": {"frame": {"__ref": "5594068"}, "cellKey": [{"__ref": "5592611"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594068": {"uuid": "68tA9OtYDQJK", "width": 340, "height": 340, "container": {"__ref": "5594069"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592611"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594069": {"name": null, "component": {"__ref": "5592543"}, "uuid": "E5cjNQJj29d4", "parent": null, "locked": null, "vsettings": [{"__ref": "5594070"}], "__type": "TplComponent"}, "5594070": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594071"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594071": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594072": {"frame": {"__ref": "5594073"}, "cellKey": [{"__ref": "5592600"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594073": {"uuid": "cJ02wcuU7Qj8", "width": 340, "height": 340, "container": {"__ref": "5594074"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592600"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594074": {"name": null, "component": {"__ref": "5592543"}, "uuid": "UhZ7XAqHyucN", "parent": null, "locked": null, "vsettings": [{"__ref": "5594075"}], "__type": "TplComponent"}, "5594075": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594076"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594076": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594077": {"frame": {"__ref": "5594078"}, "cellKey": [{"__ref": "5592600"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594078": {"uuid": "MbXzwH9T_92z", "width": 340, "height": 340, "container": {"__ref": "5594079"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592600"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594079": {"name": null, "component": {"__ref": "5592543"}, "uuid": "UD-T1AV4c0BM", "parent": null, "locked": null, "vsettings": [{"__ref": "5594080"}], "__type": "TplComponent"}, "5594080": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594081"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594081": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594082": {"frame": {"__ref": "5594083"}, "cellKey": [{"__ref": "5592602"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594083": {"uuid": "IQMTTF7XTavk", "width": 340, "height": 340, "container": {"__ref": "5594084"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592602"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594084": {"name": null, "component": {"__ref": "5592543"}, "uuid": "91Ztx5dx2FeI", "parent": null, "locked": null, "vsettings": [{"__ref": "5594085"}], "__type": "TplComponent"}, "5594085": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594086"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594086": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594087": {"frame": {"__ref": "5594088"}, "cellKey": [{"__ref": "5592602"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594088": {"uuid": "pfKXg_Nvvaxj", "width": 340, "height": 340, "container": {"__ref": "5594089"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592602"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594089": {"name": null, "component": {"__ref": "5592543"}, "uuid": "m-jJ8TwFpDas", "parent": null, "locked": null, "vsettings": [{"__ref": "5594090"}], "__type": "TplComponent"}, "5594090": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594091"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594091": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594092": {"frame": {"__ref": "5594093"}, "cellKey": [{"__ref": "5592605"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594093": {"uuid": "p_ZRna26NizM", "width": 340, "height": 340, "container": {"__ref": "5594094"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592605"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594094": {"name": null, "component": {"__ref": "5592543"}, "uuid": "xHXxuQAkq-x9", "parent": null, "locked": null, "vsettings": [{"__ref": "5594095"}], "__type": "TplComponent"}, "5594095": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594096"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594096": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594097": {"frame": {"__ref": "5594098"}, "cellKey": [{"__ref": "5592605"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594098": {"uuid": "u_y6L7ExiAb4", "width": 340, "height": 340, "container": {"__ref": "5594099"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592605"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594099": {"name": null, "component": {"__ref": "5592543"}, "uuid": "w_f5IXtwsGNZ", "parent": null, "locked": null, "vsettings": [{"__ref": "5594100"}], "__type": "TplComponent"}, "5594100": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594101"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594101": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594102": {"frame": {"__ref": "5594103"}, "cellKey": [{"__ref": "5592665"}, {"__ref": "5592596"}], "__type": "ArenaFrameCell"}, "5594103": {"uuid": "mX7hDsQB7Wj0", "width": 340, "height": 340, "container": {"__ref": "5594104"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592665"}, {"__ref": "5592596"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594104": {"name": null, "component": {"__ref": "5592543"}, "uuid": "EvmdtnXniMTn", "parent": null, "locked": null, "vsettings": [{"__ref": "5594105"}], "__type": "TplComponent"}, "5594105": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594106"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594106": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594107": {"frame": {"__ref": "5594108"}, "cellKey": [{"__ref": "5592690"}, {"__ref": "5592665"}], "__type": "ArenaFrameCell"}, "5594108": {"uuid": "s1J15-__s51W", "width": 340, "height": 340, "container": {"__ref": "5594109"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592690"}, {"__ref": "5592665"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594109": {"name": null, "component": {"__ref": "5592543"}, "uuid": "6VFPX9OyZKv0", "parent": null, "locked": null, "vsettings": [{"__ref": "5594110"}], "__type": "TplComponent"}, "5594110": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594111"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594111": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594112": {"frame": {"__ref": "5594113"}, "cellKey": [{"__ref": "5592612"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594113": {"uuid": "eChijnX_rCA2", "width": 340, "height": 340, "container": {"__ref": "5594114"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592612"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594114": {"name": null, "component": {"__ref": "5592543"}, "uuid": "lXagJprAFBaA", "parent": null, "locked": null, "vsettings": [{"__ref": "5594115"}], "__type": "TplComponent"}, "5594115": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594116"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594116": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594117": {"frame": {"__ref": "5594118"}, "cellKey": [{"__ref": "5592612"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594118": {"uuid": "oqt7dr1NXzyo", "width": 340, "height": 340, "container": {"__ref": "5594119"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592612"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594119": {"name": null, "component": {"__ref": "5592543"}, "uuid": "fP-JpsVPSPBn", "parent": null, "locked": null, "vsettings": [{"__ref": "5594120"}], "__type": "TplComponent"}, "5594120": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594121"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594121": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594122": {"frame": {"__ref": "5594123"}, "cellKey": [{"__ref": "5592667"}, {"__ref": "5592745"}], "__type": "ArenaFrameCell"}, "5594123": {"uuid": "62qKiASM4TPs", "width": 340, "height": 340, "container": {"__ref": "5594124"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592667"}, {"__ref": "5592745"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594124": {"name": null, "component": {"__ref": "5592543"}, "uuid": "AP2OqhUQMekF", "parent": null, "locked": null, "vsettings": [{"__ref": "5594125"}], "__type": "TplComponent"}, "5594125": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594126"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594126": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594127": {"frame": {"__ref": "5594128"}, "cellKey": [{"__ref": "5592613"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594128": {"uuid": "93Pt3g0aBZOU", "width": 340, "height": 340, "container": {"__ref": "5594129"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592613"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594129": {"name": null, "component": {"__ref": "5592543"}, "uuid": "X4gCefuizmBS", "parent": null, "locked": null, "vsettings": [{"__ref": "5594130"}], "__type": "TplComponent"}, "5594130": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594131"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594131": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594132": {"frame": {"__ref": "5594133"}, "cellKey": [{"__ref": "5592613"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594133": {"uuid": "1OAqffH_-tOj", "width": 340, "height": 340, "container": {"__ref": "5594134"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592613"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594134": {"name": null, "component": {"__ref": "5592543"}, "uuid": "IlGIDLQEENoD", "parent": null, "locked": null, "vsettings": [{"__ref": "5594135"}], "__type": "TplComponent"}, "5594135": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594136"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594136": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594137": {"frame": {"__ref": "5594138"}, "cellKey": [{"__ref": "5592606"}, {"__ref": "5592637"}], "__type": "ArenaFrameCell"}, "5594138": {"uuid": "2F12huOv44TA", "width": 340, "height": 340, "container": {"__ref": "5594139"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592606"}, {"__ref": "5592637"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594139": {"name": null, "component": {"__ref": "5592543"}, "uuid": "rm13IxJHVupm", "parent": null, "locked": null, "vsettings": [{"__ref": "5594140"}], "__type": "TplComponent"}, "5594140": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594141"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594141": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594142": {"frame": {"__ref": "5594143"}, "cellKey": [{"__ref": "5592606"}, {"__ref": "5592641"}], "__type": "ArenaFrameCell"}, "5594143": {"uuid": "EtezSvlW9IuU", "width": 340, "height": 340, "container": {"__ref": "5594144"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "5592606"}, {"__ref": "5592641"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5594144": {"name": null, "component": {"__ref": "5592543"}, "uuid": "4U9DyMwyo1l2", "parent": null, "locked": null, "vsettings": [{"__ref": "5594145"}], "__type": "TplComponent"}, "5594145": {"variants": [{"__ref": "5593749"}], "args": [], "attrs": {}, "rs": {"__ref": "5594146"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5594146": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5594147": {"type": "global-screen", "param": {"__ref": "5594148"}, "uuid": "YY6asSsmiS1T", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "5594148": {"type": {"__ref": "5594150"}, "variable": {"__ref": "5594149"}, "uuid": "OUfHKyZu0h77", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "5594149": {"name": "Screen", "uuid": "TDE9819soqJz", "__type": "Var"}, "5594150": {"name": "text", "__type": "Text"}, "5594151": {"defaultStyle": {"__ref": "5594152"}, "styles": [{"__ref": "5594167"}, {"__ref": "5594176"}, {"__ref": "5594185"}, {"__ref": "5594194"}, {"__ref": "5594203"}, {"__ref": "5594212"}, {"__ref": "5594220"}, {"__ref": "5594224"}, {"__ref": "5594228"}, {"__ref": "5594236"}, {"__ref": "5594261"}, {"__ref": "5594286"}, {"__ref": "5594297"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "5594152": {"name": "Default Typography", "rs": {"__ref": "5594153"}, "preview": null, "uuid": "nhDxgV7byXwP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594153": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "5594167": {"selector": "h1", "style": {"__ref": "5594168"}, "__type": "ThemeStyle"}, "5594168": {"name": "Default \"h1\"", "rs": {"__ref": "5594169"}, "preview": null, "uuid": "z8i8HLoR9kj4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594169": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "5594176": {"selector": "h2", "style": {"__ref": "5594177"}, "__type": "ThemeStyle"}, "5594177": {"name": "Default \"h2\"", "rs": {"__ref": "5594178"}, "preview": null, "uuid": "qSsQx6rUJ9U7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594178": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "5594185": {"selector": "h3", "style": {"__ref": "5594186"}, "__type": "ThemeStyle"}, "5594186": {"name": "Default \"h3\"", "rs": {"__ref": "5594187"}, "preview": null, "uuid": "_6a-iAWs9mnS", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594187": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "5594194": {"selector": "h4", "style": {"__ref": "5594195"}, "__type": "ThemeStyle"}, "5594195": {"name": "Default \"h4\"", "rs": {"__ref": "5594196"}, "preview": null, "uuid": "LJVW7qSQh49M", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594196": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "5594203": {"selector": "h5", "style": {"__ref": "5594204"}, "__type": "ThemeStyle"}, "5594204": {"name": "Default \"h5\"", "rs": {"__ref": "5594205"}, "preview": null, "uuid": "uVqfogO6MJ1u", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594205": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "5594212": {"selector": "h6", "style": {"__ref": "5594213"}, "__type": "ThemeStyle"}, "5594213": {"name": "Default \"h6\"", "rs": {"__ref": "5594214"}, "preview": null, "uuid": "1X5Tbpt7jF7m", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594214": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "5594220": {"selector": "a", "style": {"__ref": "5594221"}, "__type": "ThemeStyle"}, "5594221": {"name": "Default \"a\"", "rs": {"__ref": "5594222"}, "preview": null, "uuid": "oLyYxD38JY8a", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594222": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "5594224": {"selector": "a:hover", "style": {"__ref": "5594225"}, "__type": "ThemeStyle"}, "5594225": {"name": "Default \"a:hover\"", "rs": {"__ref": "5594226"}, "preview": null, "uuid": "PMPuHxDencAa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594226": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "5594228": {"selector": "blockquote", "style": {"__ref": "5594229"}, "__type": "ThemeStyle"}, "5594229": {"name": "Default \"blockquote\"", "rs": {"__ref": "5594230"}, "preview": null, "uuid": "xYhPEcOAECnR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594230": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "5594236": {"selector": "code", "style": {"__ref": "5594237"}, "__type": "ThemeStyle"}, "5594237": {"name": "Default \"code\"", "rs": {"__ref": "5594238"}, "preview": null, "uuid": "oWvcG2XQmVcf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594238": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "5594261": {"selector": "pre", "style": {"__ref": "5594262"}, "__type": "ThemeStyle"}, "5594262": {"name": "Default \"pre\"", "rs": {"__ref": "5594263"}, "preview": null, "uuid": "e6AmZOwEKRw0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594263": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "5594286": {"selector": "ol", "style": {"__ref": "5594287"}, "__type": "ThemeStyle"}, "5594287": {"name": "Default \"ol\"", "rs": {"__ref": "5594288"}, "preview": null, "uuid": "fk56BsjO0b28", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594288": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "5594297": {"selector": "ul", "style": {"__ref": "5594298"}, "__type": "ThemeStyle"}, "5594298": {"name": "Default \"ul\"", "rs": {"__ref": "5594299"}, "preview": null, "uuid": "eQYBx_85nCqZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5594299": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "16997002": {"text": ["person-container"], "__type": "TemplatedString"}, "16997003": {"text": ["nicknames-container"], "__type": "TemplatedString"}, "22485008": {"text": ["stringified-state"], "__type": "TemplatedString"}, "22485009": {"type": {"__ref": "22485012"}, "variable": {"__ref": "22485011"}, "uuid": "G1MK-NpyeL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "22485010": {"variable": {"__ref": "22485011"}, "__type": "VarRef"}, "22485011": {"name": "data-testid", "uuid": "itvyhAX8Q", "__type": "Var"}, "22485012": {"name": "text", "__type": "Text"}, "22485013": {"type": {"__ref": "22485016"}, "variable": {"__ref": "22485015"}, "uuid": "v2DDVgNdVd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "22485014": {"param": {"__ref": "22485009"}, "expr": {"__ref": "22485017"}, "__type": "Arg"}, "22485015": {"name": "data-testid", "uuid": "wJtWx-bkl", "__type": "Var"}, "22485016": {"name": "text", "__type": "Text"}, "22485017": {"variable": {"__ref": "22485015"}, "__type": "VarRef"}, "22485018": {"param": {"__ref": "22485013"}, "expr": {"__ref": "22485019"}, "__type": "Arg"}, "22485019": {"text": ["firstName-input"], "__type": "TemplatedString"}, "22485020": {"param": {"__ref": "22485013"}, "expr": {"__ref": "22485021"}, "__type": "Arg"}, "22485021": {"text": ["lastName-input"], "__type": "TemplatedString"}, "22485027": {"type": {"__ref": "22485030"}, "variable": {"__ref": "22485029"}, "uuid": "NBYyrely9k", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "22485028": {"variable": {"__ref": "22485029"}, "__type": "VarRef"}, "22485029": {"name": "data-testid", "uuid": "TaJXQLScZ", "__type": "Var"}, "22485030": {"name": "text", "__type": "Text"}, "22485031": {"param": {"__ref": "22485027"}, "expr": {"__ref": "22485032"}, "__type": "Arg"}, "22485032": {"text": ["add-nickname"], "__type": "TemplatedString"}, "22485038": {"param": {"__ref": "22485009"}, "expr": {"__ref": "22485039"}, "__type": "Arg"}, "22485039": {"text": ["nickname-input"], "__type": "TemplatedString"}, "22485040": {"param": {"__ref": "22485027"}, "expr": {"__ref": "22485041"}, "__type": "Arg"}, "22485041": {"text": ["remove-nickname"], "__type": "TemplatedString"}, "22485042": {"param": {"__ref": "22485027"}, "expr": {"__ref": "22485043"}, "__type": "Arg"}, "22485043": {"text": ["remove-person"], "__type": "TemplatedString"}, "22485044": {"param": {"__ref": "22485027"}, "expr": {"__ref": "22485045"}, "__type": "Arg"}, "22485045": {"text": ["add-person"], "__type": "TemplatedString"}, "22485048": {"path": ["$state", "people"], "fallback": null, "__type": "ObjectPath"}, "22485052": {"code": "6", "fallback": null, "__type": "CustomCode"}, "22485063": {"code": "1", "fallback": null, "__type": "CustomCode"}, "22485069": {"code": "{\"firstName\":\"First\",\"lastName\":\"Last\",\"nicknames\":[\"A\",\"B\",\"C\"]}", "fallback": null, "__type": "CustomCode"}, "22485075": {"expr": {"__ref": "22485076"}, "html": false, "__type": "ExprText"}, "22485076": {"code": "(JSON.stringify($state.people))", "fallback": {"__ref": "22485077"}, "__type": "CustomCode"}, "22485077": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "26472001": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "26472002": {"name": "text", "__type": "Text"}, "28486003": {"path": ["$state", "person", "firstName"], "fallback": null, "__type": "ObjectPath"}, "28486004": {"name": "operation", "expr": {"__ref": "1556016"}, "__type": "NameArg"}, "28486005": {"name": "variable", "expr": {"__ref": "28486003"}, "__type": "NameArg"}, "28486006": {"name": "value", "expr": {"__ref": "28486007"}, "__type": "NameArg"}, "28486007": {"path": ["val"], "fallback": null, "__type": "ObjectPath"}, "28486017": {"name": "customFunction", "expr": {"__ref": "28486018"}, "__type": "NameArg"}, "28486018": {"argNames": [], "bodyExpr": {"__ref": "1567503"}, "__type": "FunctionExpr"}, "28486020": {"name": "variable", "expr": {"__ref": "22485048"}, "__type": "NameArg"}, "28486021": {"name": "operation", "expr": {"__ref": "22485052"}, "__type": "NameArg"}, "28486022": {"name": "startIndex", "expr": {"__ref": "28486024"}, "__type": "NameArg"}, "28486023": {"name": "deleteCount", "expr": {"__ref": "22485063"}, "__type": "NameArg"}, "28486024": {"path": ["currentPersonIndex"], "fallback": {"__ref": "28486025"}, "__type": "ObjectPath"}, "28486025": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "28486026": {"code": "[{\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON><PERSON><PERSON>\",\"nicknames\":[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]},{\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON><PERSON><PERSON>\",\"nicknames\":[]},{\"firstName\":\"<PERSON><PERSON><PERSON>\",\"lastName\":\"<PERSON>\",\"nicknames\":[\"<PERSON><PERSON>\"]}]", "fallback": null, "__type": "CustomCode"}, "28486027": {"text": ["root"], "__type": "TemplatedString"}, "34119006": {"code": "(!!$state.person)", "fallback": {"__ref": "34119007"}, "__type": "CustomCode"}, "34119007": {"code": "true", "fallback": null, "__type": "CustomCode"}, "34119008": {"param": {"__ref": "34119009"}, "accessType": "private", "variableType": "object", "onChangeParam": {"__ref": "56253080"}, "tplNode": {"__ref": "5592072"}, "implicitState": {"__ref": "5593278"}, "__type": "State"}, "34119009": {"type": {"__ref": "34119012"}, "state": {"__ref": "34119008"}, "variable": {"__ref": "34119011"}, "uuid": "tu2FQiJJ41", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "object", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "34119010": {"type": {"__ref": "34119014"}, "state": {"__ref": "5593278"}, "variable": {"__ref": "34119013"}, "uuid": "Bh2wBoqtk2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "34119011": {"name": "Person person", "uuid": "vwHxRS_TK", "__type": "Var"}, "34119012": {"name": "any", "__type": "AnyType"}, "34119013": {"name": "On person change", "uuid": "F3OO60hYcg", "__type": "Var"}, "34119014": {"name": "func", "params": [{"__ref": "34119015"}], "__type": "FunctionType"}, "34119015": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "34119016"}, "__type": "ArgType"}, "34119016": {"name": "any", "__type": "AnyType"}, "34119025": {"param": {"__ref": "34119010"}, "expr": {"__ref": "34119026"}, "__type": "Arg"}, "34119026": {"interactions": [{"__ref": "34119027"}], "__type": "EventHandler"}, "34119027": {"interactionName": "Custom function", "actionName": "customFunction", "args": [{"__ref": "28486017"}], "condExpr": null, "conditionalMode": "always", "uuid": "_hMouFK8o", "parent": {"__ref": "34119026"}, "__type": "Interaction"}, "34119032": {"name": "<PERSON><PERSON><PERSON>", "uuid": "DH_6t0Vkn", "__type": "Var"}, "34119034": {"name": "currentPersonIndex", "uuid": "6-b<PERSON><PERSON><PERSON><PERSON>", "__type": "Var"}, "42268002": {"name": "func", "params": [{"__ref": "42268003"}], "__type": "FunctionType"}, "42268003": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "42268004"}, "__type": "ArgType"}, "42268004": {"name": "any", "__type": "AnyType"}, "56253001": {"name": "variable", "expr": {"__ref": "56253004"}, "__type": "NameArg"}, "56253002": {"name": "operation", "expr": {"__ref": "5593200"}, "__type": "NameArg"}, "56253003": {"name": "value", "expr": {"__ref": "5593202"}, "__type": "NameArg"}, "56253004": {"path": ["$state", "person", "nicknames"], "fallback": null, "__type": "ObjectPath"}, "56253005": {"type": {"__ref": "56253006"}, "state": {"__ref": "5592055"}, "variable": {"__ref": "56253009"}, "uuid": "vl0qOKF9Q", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253006": {"name": "func", "params": [{"__ref": "56253007"}], "__type": "FunctionType"}, "56253007": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253008"}, "__type": "ArgType"}, "56253008": {"name": "any", "__type": "AnyType"}, "56253009": {"name": "On people change", "uuid": "JZzEyNOA5s", "__type": "Var"}, "56253010": {"type": {"__ref": "56253011"}, "state": {"__ref": "5592099"}, "variable": {"__ref": "56253014"}, "uuid": "c2dVRHm4q0", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253011": {"name": "func", "params": [{"__ref": "56253012"}], "__type": "FunctionType"}, "56253012": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253013"}, "__type": "ArgType"}, "56253013": {"name": "text", "__type": "Text"}, "56253014": {"name": "On FormField undefined value change", "uuid": "FWGL7GsLE4", "__type": "Var"}, "56253015": {"type": {"__ref": "56253016"}, "state": {"__ref": "5592158"}, "variable": {"__ref": "56253019"}, "uuid": "QTMXF0-p40", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253016": {"name": "func", "params": [{"__ref": "56253017"}], "__type": "FunctionType"}, "56253017": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253018"}, "__type": "ArgType"}, "56253018": {"name": "any", "__type": "AnyType"}, "56253019": {"name": "On Show Start Icon change", "uuid": "8a8KcrWLPr", "__type": "Var"}, "56253020": {"type": {"__ref": "56253021"}, "state": {"__ref": "5592159"}, "variable": {"__ref": "56253024"}, "uuid": "j_13TPZ2F2", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253021": {"name": "func", "params": [{"__ref": "56253022"}], "__type": "FunctionType"}, "56253022": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253023"}, "__type": "ArgType"}, "56253023": {"name": "any", "__type": "AnyType"}, "56253024": {"name": "On Show End Icon change", "uuid": "HAxs7C4E4B", "__type": "Var"}, "56253025": {"type": {"__ref": "56253026"}, "state": {"__ref": "5592160"}, "variable": {"__ref": "56253029"}, "uuid": "ItmJt_Qoav", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253026": {"name": "func", "params": [{"__ref": "56253027"}], "__type": "FunctionType"}, "56253027": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253028"}, "__type": "ArgType"}, "56253028": {"name": "any", "__type": "AnyType"}, "56253029": {"name": "On Is Disabled change", "uuid": "ibvsFez3IB", "__type": "Var"}, "56253030": {"type": {"__ref": "56253031"}, "state": {"__ref": "5592161"}, "variable": {"__ref": "56253034"}, "uuid": "dGcnASBCjm", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253031": {"name": "func", "params": [{"__ref": "56253032"}], "__type": "FunctionType"}, "56253032": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253033"}, "__type": "ArgType"}, "56253033": {"name": "any", "__type": "AnyType"}, "56253034": {"name": "On Color change", "uuid": "VYv_yQGFeB", "__type": "Var"}, "56253035": {"type": {"__ref": "56253036"}, "state": {"__ref": "5592499"}, "variable": {"__ref": "56253039"}, "uuid": "1Tohi62di-", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253036": {"name": "func", "params": [{"__ref": "56253037"}], "__type": "FunctionType"}, "56253037": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253038"}, "__type": "ArgType"}, "56253038": {"name": "text", "__type": "Text"}, "56253039": {"name": "On undefined value change", "uuid": "8hk1sen-El", "__type": "Var"}, "56253040": {"type": {"__ref": "56253041"}, "state": {"__ref": "5592574"}, "variable": {"__ref": "56253044"}, "uuid": "7ajCiJ9x2b", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253041": {"name": "func", "params": [{"__ref": "56253042"}], "__type": "FunctionType"}, "56253042": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253043"}, "__type": "ArgType"}, "56253043": {"name": "any", "__type": "AnyType"}, "56253044": {"name": "On Show Start Icon change", "uuid": "jWOSBA59l8", "__type": "Var"}, "56253045": {"type": {"__ref": "56253046"}, "state": {"__ref": "5592575"}, "variable": {"__ref": "56253049"}, "uuid": "EIh9aYqPYBS", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253046": {"name": "func", "params": [{"__ref": "56253047"}], "__type": "FunctionType"}, "56253047": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253048"}, "__type": "ArgType"}, "56253048": {"name": "any", "__type": "AnyType"}, "56253049": {"name": "On Show End Icon change", "uuid": "rdhOY_hR6Hp", "__type": "Var"}, "56253050": {"type": {"__ref": "56253051"}, "state": {"__ref": "5592576"}, "variable": {"__ref": "56253054"}, "uuid": "Aw-vj-vxrLb", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253051": {"name": "func", "params": [{"__ref": "56253052"}], "__type": "FunctionType"}, "56253052": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253053"}, "__type": "ArgType"}, "56253053": {"name": "any", "__type": "AnyType"}, "56253054": {"name": "On Is Disabled change", "uuid": "hTIL4I47B2a", "__type": "Var"}, "56253055": {"type": {"__ref": "56253056"}, "state": {"__ref": "5592577"}, "variable": {"__ref": "56253059"}, "uuid": "dgXGeqtdI8-", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253056": {"name": "func", "params": [{"__ref": "56253057"}], "__type": "FunctionType"}, "56253057": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253058"}, "__type": "ArgType"}, "56253058": {"name": "any", "__type": "AnyType"}, "56253059": {"name": "On Shape change", "uuid": "3VZYJyn49Oj", "__type": "Var"}, "56253060": {"type": {"__ref": "56253061"}, "state": {"__ref": "5592578"}, "variable": {"__ref": "56253064"}, "uuid": "s875X1SR5Od", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253061": {"name": "func", "params": [{"__ref": "56253062"}], "__type": "FunctionType"}, "56253062": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253063"}, "__type": "ArgType"}, "56253063": {"name": "any", "__type": "AnyType"}, "56253064": {"name": "On Size change", "uuid": "ILPkCL8RKfS", "__type": "Var"}, "56253065": {"type": {"__ref": "56253066"}, "state": {"__ref": "5592579"}, "variable": {"__ref": "56253069"}, "uuid": "c-Ig5qH8D8_", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253066": {"name": "func", "params": [{"__ref": "56253067"}], "__type": "FunctionType"}, "56253067": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253068"}, "__type": "ArgType"}, "56253068": {"name": "any", "__type": "AnyType"}, "56253069": {"name": "On Color change", "uuid": "9EMk0jG0zLw", "__type": "Var"}, "56253070": {"type": {"__ref": "56253071"}, "state": {"__ref": "5593277"}, "variable": {"__ref": "56253074"}, "uuid": "o0l032aJqzS", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253071": {"name": "func", "params": [{"__ref": "56253072"}], "__type": "FunctionType"}, "56253072": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253073"}, "__type": "ArgType"}, "56253073": {"name": "text", "__type": "Text"}, "56253074": {"name": "On FormField value change", "uuid": "TyWriRMC-sb", "__type": "Var"}, "56253075": {"type": {"__ref": "56253076"}, "state": {"__ref": "5593279"}, "variable": {"__ref": "56253079"}, "uuid": "Q0OKcwqhgqA", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253076": {"name": "func", "params": [{"__ref": "56253077"}], "__type": "FunctionType"}, "56253077": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253078"}, "__type": "ArgType"}, "56253078": {"name": "any", "__type": "AnyType"}, "56253079": {"name": "On Nicknames nicknames change", "uuid": "Jn8fZ4adPAH", "__type": "Var"}, "56253080": {"type": {"__ref": "56253081"}, "state": {"__ref": "34119008"}, "variable": {"__ref": "56253084"}, "uuid": "tbHA1A6IeaF", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "56253081": {"name": "func", "params": [{"__ref": "56253082"}], "__type": "FunctionType"}, "56253082": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "56253083"}, "__type": "ArgType"}, "56253083": {"name": "any", "__type": "AnyType"}, "56253084": {"name": "On Person person change", "uuid": "a1DJKqiMifC", "__type": "Var"}}, "deps": [], "version": "246-add-component-updated-at"}]]