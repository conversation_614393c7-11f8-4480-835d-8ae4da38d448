[["4MeW2sWknF78x2psqN7Ers", {"root": "17636001", "map": {"4649401": {"rows": [{"__ref": "4649402"}], "__type": "ArenaFrameGrid"}, "4649402": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "4842201": {"type": {"__ref": "4842203"}, "variable": {"__ref": "4842202"}, "uuid": "qhdMhm_0J5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4842202": {"name": "submitsForm", "uuid": "CPX0LvwRR", "__type": "Var"}, "4842203": {"name": "bool", "__type": "BoolType"}, "4842204": {"param": {"__ref": "4842201"}, "expr": {"__ref": "4842205"}, "__type": "Arg"}, "4842205": {"code": "true", "fallback": null, "__type": "CustomCode"}, "4842206": {"param": {"__ref": "4842201"}, "expr": {"__ref": "4842207"}, "__type": "Arg"}, "4842207": {"code": "true", "fallback": null, "__type": "CustomCode"}, "4882007": {"path": ["$state", "editingIndex"], "fallback": null, "__type": "ObjectPath"}, "4882008": {"name": "variable", "expr": {"__ref": "4882007"}, "__type": "NameArg"}, "4882009": {"name": "operation", "expr": {"__ref": "4882010"}, "__type": "NameArg"}, "4882010": {"code": "1", "fallback": null, "__type": "CustomCode"}, "4882020": {"code": "(event.key === \"Enter\")", "fallback": null, "__type": "CustomCode"}, "12613001": {"interactions": [{"__ref": "12613002"}], "__type": "EventHandler"}, "12613002": {"interactionName": "Set tasks", "actionName": "updateVariable", "args": [{"__ref": "12613020"}, {"__ref": "12613021"}, {"__ref": "12613022"}, {"__ref": "12613023"}], "condExpr": null, "conditionalMode": "always", "uuid": "wycVK8WFz", "parent": {"__ref": "12613001"}, "__type": "Interaction"}, "12613005": {"path": ["$state", "tasks"], "fallback": null, "__type": "ObjectPath"}, "12613009": {"code": "6", "fallback": null, "__type": "CustomCode"}, "12613018": {"path": ["currentIndex"], "fallback": {"__ref": "12613019"}, "__type": "ObjectPath"}, "12613019": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "12613020": {"name": "variable", "expr": {"__ref": "12613005"}, "__type": "NameArg"}, "12613021": {"name": "operation", "expr": {"__ref": "12613009"}, "__type": "NameArg"}, "12613022": {"name": "startIndex", "expr": {"__ref": "12613018"}, "__type": "NameArg"}, "12613023": {"name": "deleteCount", "expr": {"__ref": "12613024"}, "__type": "NameArg"}, "12613024": {"code": "1", "fallback": null, "__type": "CustomCode"}, "12613025": {"name": "variable", "expr": {"__ref": "33381720"}, "__type": "NameArg"}, "12613026": {"name": "operation", "expr": {"__ref": "33381717"}, "__type": "NameArg"}, "12613027": {"name": "value", "expr": {"__ref": "12613028"}, "__type": "NameArg"}, "12613028": {"code": "(event.target.value)", "fallback": null, "__type": "CustomCode"}, "12613029": {"interactionName": "Set editingIndex", "actionName": "updateVariable", "args": [{"__ref": "4882008"}, {"__ref": "4882009"}], "condExpr": {"__ref": "4882020"}, "conditionalMode": "expression", "uuid": "2YR-gPSJn", "parent": {"__ref": "33381750"}, "__type": "Interaction"}, "12613037": {"code": "(!currentItem.done)", "fallback": null, "__type": "CustomCode"}, "12613038": {"param": {"__ref": "12613039"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "64282003"}, "tplNode": null, "implicitState": null, "__type": "State"}, "12613039": {"type": {"__ref": "12613043"}, "state": {"__ref": "12613038"}, "variable": {"__ref": "12613040"}, "uuid": "H3msvrXSnr", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "12613058"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "12613040": {"name": "uid", "uuid": "0hM-B1ZIp", "__type": "Var"}, "12613043": {"name": "num", "__type": "<PERSON><PERSON>"}, "12613047": {"type": {"__ref": "12613049"}, "variable": {"__ref": "12613048"}, "uuid": "7RgWFhl64-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "12613060"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "12613048": {"name": "tasksList", "uuid": "3EUzWf0mZ", "__type": "Var"}, "12613049": {"name": "any", "__type": "AnyType"}, "12613054": {"code": "($props.tasksList)", "fallback": {"__ref": "12613055"}, "__type": "CustomCode"}, "12613055": {"code": "[{\"id\":1,\"title\":\"Task 1\",\"done\":false},{\"id\":2,\"title\":\"Task 2\",\"done\":true},{\"id\":3,\"title\":\"Task 3\",\"done\":false},{\"id\":4,\"title\":\"Task 4\",\"done\":false}]", "fallback": null, "__type": "CustomCode"}, "12613058": {"code": "($props.tasksList.length)", "fallback": {"__ref": "12613059"}, "__type": "CustomCode"}, "12613059": {"code": "0", "fallback": null, "__type": "CustomCode"}, "12613060": {"code": "[{\"id\":0,\"title\":\"Task 1\",\"done\":false},{\"id\":1,\"title\":\"Task 2\",\"done\":true},{\"id\":2,\"title\":\"Task 3\",\"done\":false},{\"id\":3,\"title\":\"Task 4\",\"done\":false}]", "fallback": null, "__type": "CustomCode"}, "12613061": {"name": "variable", "expr": {"__ref": "33380904"}, "__type": "NameArg"}, "12613062": {"name": "operation", "expr": {"__ref": "33380908"}, "__type": "NameArg"}, "12613063": {"name": "value", "expr": {"__ref": "12613064"}, "__type": "NameArg"}, "12613064": {"code": "({\n    title: \"New task\",\n    done: false,\n    id: $state.uid++\n})", "fallback": null, "__type": "CustomCode"}, "12613093": {"code": "(`row-${currentItem.id}`)", "fallback": {"__ref": "12613094"}, "__type": "CustomCode"}, "12613094": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "12613095": {"type": {"__ref": "12613098"}, "variable": {"__ref": "12613097"}, "uuid": "dsvxhFwVSh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "12613096": {"variable": {"__ref": "12613097"}, "__type": "VarRef"}, "12613097": {"name": "testId", "uuid": "fzlVwVk-f", "__type": "Var"}, "12613098": {"name": "text", "__type": "Text"}, "12613099": {"param": {"__ref": "12613095"}, "expr": {"__ref": "12613102"}, "__type": "Arg"}, "12613101": {"code": "(`switch-${currentItem.id}`)", "fallback": null, "__type": "CustomCode"}, "12613102": {"text": ["", {"__ref": "12613101"}, ""], "__type": "TemplatedString"}, "12613103": {"type": {"__ref": "12613106"}, "variable": {"__ref": "12613105"}, "uuid": "kNG1D8VaiB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "12613104": {"variable": {"__ref": "12613105"}, "__type": "VarRef"}, "12613105": {"name": "testId", "uuid": "PqmhkKP_L", "__type": "Var"}, "12613106": {"name": "text", "__type": "Text"}, "12613107": {"param": {"__ref": "12613103"}, "expr": {"__ref": "12613110"}, "__type": "Arg"}, "12613109": {"code": "(`remove-${currentItem.id}`)", "fallback": null, "__type": "CustomCode"}, "12613110": {"text": ["", {"__ref": "12613109"}, ""], "__type": "TemplatedString"}, "12613111": {"param": {"__ref": "12613103"}, "expr": {"__ref": "12613114"}, "__type": "Arg"}, "12613114": {"code": "(`add-task`)", "fallback": {"__ref": "12613115"}, "__type": "CustomCode"}, "12613115": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "12613118": {"code": "(`text-${currentItem.id}`)", "fallback": {"__ref": "12613119"}, "__type": "CustomCode"}, "12613119": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "12613120": {"text": ["root"], "__type": "TemplatedString"}, "12613126": {"code": "(currentItem.done ? {\n    \"textDecoration\": \"line-through\"\n} : {\n    \"textDecoration\": \"none\"\n})", "fallback": {"__ref": "12613127"}, "__type": "CustomCode"}, "12613127": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "12613128": {"tag": "div", "name": null, "children": [{"__ref": "33380019"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "nhIyRDpDf", "parent": {"__ref": "23230003"}, "locked": null, "vsettings": [{"__ref": "12613133"}], "__type": "TplTag"}, "12613129": {"tpl": [{"__ref": "12613134"}], "__type": "VirtualRenderExpr"}, "12613130": {"tpl": [{"__ref": "12613135"}], "__type": "VirtualRenderExpr"}, "12613131": {"tpl": [{"__ref": "12613136"}], "__type": "VirtualRenderExpr"}, "12613132": {"tpl": [{"__ref": "12613137"}], "__type": "VirtualRenderExpr"}, "12613133": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"data-testid": {"__ref": "12613176"}}, "rs": {"__ref": "12613138"}, "dataCond": {"__ref": "12613139"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "12613134": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "4oXgsPEjk-", "parent": {"__ref": "33381353"}, "locked": null, "vsettings": [{"__ref": "12613140"}], "__type": "TplTag"}, "12613135": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "8QiAPe7d8D", "parent": {"__ref": "33381353"}, "locked": null, "vsettings": [{"__ref": "12613141"}], "__type": "TplTag"}, "12613136": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "xY0behgZfW", "parent": {"__ref": "33380914"}, "locked": null, "vsettings": [{"__ref": "12613142"}], "__type": "TplTag"}, "12613137": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "u14-0Ytf35", "parent": {"__ref": "33380914"}, "locked": null, "vsettings": [{"__ref": "12613143"}], "__type": "TplTag"}, "12613138": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "width": "stretch", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "12613139": {"code": "true", "fallback": null, "__type": "CustomCode"}, "12613140": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"outerHTML": {"__ref": "12613150"}}, "rs": {"__ref": "12613151"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "12613141": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"outerHTML": {"__ref": "12613152"}}, "rs": {"__ref": "12613153"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "12613142": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"outerHTML": {"__ref": "12613154"}}, "rs": {"__ref": "12613155"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "12613143": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"outerHTML": {"__ref": "12613156"}}, "rs": {"__ref": "12613157"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "12613150": {"asset": {"__ref": "33381350"}, "__type": "ImageAssetRef"}, "12613151": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "12613152": {"asset": {"__ref": "33380064"}, "__type": "ImageAssetRef"}, "12613153": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "12613154": {"asset": {"__ref": "33380064"}, "__type": "ImageAssetRef"}, "12613155": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "12613156": {"asset": {"__ref": "33380065"}, "__type": "ImageAssetRef"}, "12613157": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "12613176": {"text": ["tasks-container"], "__type": "TemplatedString"}, "12613177": {"type": {"__ref": "12613180"}, "variable": {"__ref": "12613179"}, "uuid": "4uhAFq0wWP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "12613179": {"name": "testId", "uuid": "k1XqKkFfg", "__type": "Var"}, "12613180": {"name": "text", "__type": "Text"}, "12613181": {"param": {"__ref": "12613177"}, "expr": {"__ref": "12613184"}, "__type": "Arg"}, "12613184": {"code": "(`textInput-${currentItem.id}`)", "fallback": {"__ref": "12613185"}, "__type": "CustomCode"}, "12613185": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "12613186": {"variable": {"__ref": "12613179"}, "__type": "VarRef"}, "17636001": {"components": [{"__ref": "26369001"}, {"__ref": "26369002"}, {"__ref": "23230001"}, {"__ref": "33380066"}, {"__ref": "33381016"}, {"__ref": "33381351"}], "arenas": [{"__ref": "17636002"}], "pageArenas": [{"__ref": "23230002"}], "componentArenas": [{"__ref": "33380067"}, {"__ref": "33381017"}, {"__ref": "33381352"}], "globalVariantGroups": [{"__ref": "17636003"}], "userManagedFonts": [], "globalVariant": {"__ref": "17636007"}, "styleTokens": [{"__ref": "33778008"}, {"__ref": "33778014"}, {"__ref": "50519001"}], "mixins": [], "themes": [{"__ref": "17636008"}], "activeTheme": {"__ref": "17636008"}, "imageAssets": [{"__ref": "33380064"}, {"__ref": "33380065"}, {"__ref": "33381350"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "17636003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"button": {"__ref": "33380066"}, "switch": {"__ref": "33381016"}, "text-input": {"__ref": "33381351"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "17636002": {"name": "Custom arena 1", "children": [], "__type": "Arena"}, "17636003": {"type": "global-screen", "param": {"__ref": "17636004"}, "uuid": "RwT8xRFtJMT", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "17636004": {"type": {"__ref": "17636006"}, "variable": {"__ref": "17636005"}, "uuid": "T0ekr7-511t", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "17636005": {"name": "Screen", "uuid": "mgstjf5bOE", "__type": "Var"}, "17636006": {"name": "text", "__type": "Text"}, "17636007": {"uuid": "8XXjjMUfDRd", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "17636008": {"defaultStyle": {"__ref": "17636009"}, "styles": [{"__ref": "17636024"}, {"__ref": "17636033"}, {"__ref": "17636042"}, {"__ref": "17636051"}, {"__ref": "17636060"}, {"__ref": "17636069"}, {"__ref": "17636077"}, {"__ref": "17636081"}, {"__ref": "17636085"}, {"__ref": "17636093"}, {"__ref": "17636118"}, {"__ref": "17636143"}, {"__ref": "17636154"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "17636009": {"name": "Default Typography", "rs": {"__ref": "17636010"}, "preview": null, "uuid": "P4JGGGpyXo", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "17636024": {"selector": "h1", "style": {"__ref": "17636025"}, "__type": "ThemeStyle"}, "17636025": {"name": "Default \"h1\"", "rs": {"__ref": "17636026"}, "preview": null, "uuid": "yx4_tYnVsP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "17636033": {"selector": "h2", "style": {"__ref": "17636034"}, "__type": "ThemeStyle"}, "17636034": {"name": "Default \"h2\"", "rs": {"__ref": "17636035"}, "preview": null, "uuid": "hJ_FCqPl4U", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "17636042": {"selector": "h3", "style": {"__ref": "17636043"}, "__type": "ThemeStyle"}, "17636043": {"name": "Default \"h3\"", "rs": {"__ref": "17636044"}, "preview": null, "uuid": "p4fLB3G2TK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "17636051": {"selector": "h4", "style": {"__ref": "17636052"}, "__type": "ThemeStyle"}, "17636052": {"name": "Default \"h4\"", "rs": {"__ref": "17636053"}, "preview": null, "uuid": "3OGzKkdNeA", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "17636060": {"selector": "h5", "style": {"__ref": "17636061"}, "__type": "ThemeStyle"}, "17636061": {"name": "Default \"h5\"", "rs": {"__ref": "17636062"}, "preview": null, "uuid": "HttqaywXCt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "17636069": {"selector": "h6", "style": {"__ref": "17636070"}, "__type": "ThemeStyle"}, "17636070": {"name": "Default \"h6\"", "rs": {"__ref": "17636071"}, "preview": null, "uuid": "vIKZWyY2aU", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "17636077": {"selector": "a", "style": {"__ref": "17636078"}, "__type": "ThemeStyle"}, "17636078": {"name": "Default \"a\"", "rs": {"__ref": "17636079"}, "preview": null, "uuid": "lHIYm4dz4M", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "17636081": {"selector": "a:hover", "style": {"__ref": "17636082"}, "__type": "ThemeStyle"}, "17636082": {"name": "Default \"a:hover\"", "rs": {"__ref": "17636083"}, "preview": null, "uuid": "wC0bNXi_yj", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "17636085": {"selector": "blockquote", "style": {"__ref": "17636086"}, "__type": "ThemeStyle"}, "17636086": {"name": "Default \"blockquote\"", "rs": {"__ref": "17636087"}, "preview": null, "uuid": "noXWy_aH6q", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "17636093": {"selector": "code", "style": {"__ref": "17636094"}, "__type": "ThemeStyle"}, "17636094": {"name": "Default \"code\"", "rs": {"__ref": "17636095"}, "preview": null, "uuid": "PV5W6uKVhA", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "17636118": {"selector": "pre", "style": {"__ref": "17636119"}, "__type": "ThemeStyle"}, "17636119": {"name": "Default \"pre\"", "rs": {"__ref": "17636120"}, "preview": null, "uuid": "TZuFgOwiUK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "17636143": {"selector": "ol", "style": {"__ref": "17636144"}, "__type": "ThemeStyle"}, "17636144": {"name": "Default \"ol\"", "rs": {"__ref": "17636145"}, "preview": null, "uuid": "RAnRw4H1oB", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "17636154": {"selector": "ul", "style": {"__ref": "17636155"}, "__type": "ThemeStyle"}, "17636155": {"name": "Default \"ul\"", "rs": {"__ref": "17636156"}, "preview": null, "uuid": "4UQO86oCtW", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "17636156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "23230001": {"uuid": "9VXZdpulPk", "name": "Homepage", "params": [{"__ref": "23230028"}, {"__ref": "33381019"}, {"__ref": "33381354"}, {"__ref": "33381686"}, {"__ref": "12613039"}, {"__ref": "12613047"}, {"__ref": "64282003"}, {"__ref": "64282008"}, {"__ref": "64282043"}, {"__ref": "64282058"}, {"__ref": "64282083"}], "states": [{"__ref": "23230027"}, {"__ref": "33381020"}, {"__ref": "33381355"}, {"__ref": "33381685"}, {"__ref": "12613038"}], "tplTree": {"__ref": "23230003"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "23230004"}], "variantGroups": [], "pageMeta": {"__ref": "23230005"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "23230002": {"component": {"__ref": "23230001"}, "matrix": {"__ref": "23230006"}, "customMatrix": {"__ref": "4649401"}, "__type": "PageArena"}, "23230003": {"tag": "div", "name": null, "children": [{"__ref": "33380001"}, {"__ref": "12613128"}, {"__ref": "33380068"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "6PrWnU7bG", "parent": null, "locked": null, "vsettings": [{"__ref": "23230007"}], "__type": "TplTag"}, "23230004": {"uuid": "rCHoSXd3k-", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "23230005": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "23230006": {"rows": [{"__ref": "23230008"}], "__type": "ArenaFrameGrid"}, "23230007": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"data-testid": {"__ref": "12613120"}}, "rs": {"__ref": "23230009"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "23230008": {"cols": [{"__ref": "23230010"}, {"__ref": "23230011"}], "rowKey": {"__ref": "23230004"}, "__type": "ArenaFrameRow"}, "23230009": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "center", "flex-row-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "23230010": {"frame": {"__ref": "23230019"}, "cellKey": null, "__type": "ArenaFrameCell"}, "23230011": {"frame": {"__ref": "23230020"}, "cellKey": null, "__type": "ArenaFrameCell"}, "23230019": {"uuid": "9pOaTCADEK", "width": 1366, "height": 768, "container": {"__ref": "23230021"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "23230004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "23230020": {"uuid": "j30RTC-Vsu", "width": 414, "height": 736, "container": {"__ref": "23230022"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "23230004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "23230021": {"name": null, "component": {"__ref": "23230001"}, "uuid": "XzLjtGSypw", "parent": null, "locked": null, "vsettings": [{"__ref": "23230023"}], "__type": "TplComponent"}, "23230022": {"name": null, "component": {"__ref": "23230001"}, "uuid": "vVFdHfU-Xj", "parent": null, "locked": null, "vsettings": [{"__ref": "23230024"}], "__type": "TplComponent"}, "23230023": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "23230025"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "23230024": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "23230026"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "23230025": {"values": {}, "mixins": [], "__type": "RuleSet"}, "23230026": {"values": {}, "mixins": [], "__type": "RuleSet"}, "23230027": {"param": {"__ref": "23230028"}, "accessType": "private", "variableType": "array", "onChangeParam": {"__ref": "64282008"}, "tplNode": null, "implicitState": null, "__type": "State"}, "23230028": {"type": {"__ref": "33380017"}, "state": {"__ref": "23230027"}, "variable": {"__ref": "23230029"}, "uuid": "bg4F23EEYl", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "12613054"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "23230029": {"name": "tasks", "uuid": "XaIiMroYn", "__type": "Var"}, "26369001": {"uuid": "oA0-wuK-YC", "name": "hostless-plasmic-head", "params": [{"__ref": "26369003"}, {"__ref": "26369004"}, {"__ref": "26369005"}, {"__ref": "26369006"}], "states": [], "tplTree": {"__ref": "26369007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "26369008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "26369009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "26369002": {"uuid": "ygu6MkaL4W", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "26369010"}, {"__ref": "26369011"}, {"__ref": "26369012"}, {"__ref": "26369013"}, {"__ref": "26369014"}], "states": [], "tplTree": {"__ref": "26369015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "26369016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "26369017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "26369003": {"type": {"__ref": "26369019"}, "variable": {"__ref": "26369018"}, "uuid": "jALTEmFdtx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369004": {"type": {"__ref": "26369021"}, "variable": {"__ref": "26369020"}, "uuid": "7XqsYkJ8Np", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369005": {"type": {"__ref": "26369023"}, "variable": {"__ref": "26369022"}, "uuid": "8YHrz2uFkO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369006": {"type": {"__ref": "26369025"}, "variable": {"__ref": "26369024"}, "uuid": "A210WcsAar", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ttjMxZ5igD", "parent": null, "locked": null, "vsettings": [{"__ref": "26369026"}], "__type": "TplTag"}, "26369008": {"uuid": "yh3YvEviE", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "26369009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "26369010": {"type": {"__ref": "26369028"}, "variable": {"__ref": "26369027"}, "uuid": "YDw2xgQdXp", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369011": {"type": {"__ref": "26369030"}, "variable": {"__ref": "26369029"}, "uuid": "JKKJ0ysmpkU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369012": {"type": {"__ref": "26369032"}, "tplSlot": {"__ref": "26369037"}, "variable": {"__ref": "26369031"}, "uuid": "lSHSUr_7DYF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "26369013": {"type": {"__ref": "26369034"}, "variable": {"__ref": "26369033"}, "uuid": "U0GyVhT1vkU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369014": {"type": {"__ref": "26369036"}, "variable": {"__ref": "26369035"}, "uuid": "125TGg6KwlC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "26369015": {"tag": "div", "name": null, "children": [{"__ref": "26369037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "VbEnT-Z08w", "parent": null, "locked": null, "vsettings": [{"__ref": "26369038"}], "__type": "TplTag"}, "26369016": {"uuid": "pFcJnyMIuJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "26369017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Source Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "26369018": {"name": "title", "uuid": "2p6AcT23Gi", "__type": "Var"}, "26369019": {"name": "text", "__type": "Text"}, "26369020": {"name": "description", "uuid": "rTr6FYrRTI", "__type": "Var"}, "26369021": {"name": "text", "__type": "Text"}, "26369022": {"name": "image", "uuid": "vnNe8iRE4c", "__type": "Var"}, "26369023": {"name": "img", "__type": "Img"}, "26369024": {"name": "canonical", "uuid": "O_U3PZKR1C", "__type": "Var"}, "26369025": {"name": "text", "__type": "Text"}, "26369026": {"variants": [{"__ref": "26369008"}], "args": [], "attrs": {}, "rs": {"__ref": "26369039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "26369027": {"name": "dataOp", "uuid": "Hg8BH1yo6b", "__type": "Var"}, "26369028": {"name": "any", "__type": "AnyType"}, "26369029": {"name": "name", "uuid": "cqevHxSixww", "__type": "Var"}, "26369030": {"name": "text", "__type": "Text"}, "26369031": {"name": "children", "uuid": "aNuLg9ghQMg", "__type": "Var"}, "26369032": {"name": "renderFunc", "params": [{"__ref": "26369040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "26369033": {"name": "pageSize", "uuid": "WXM-QSPHhPd", "__type": "Var"}, "26369034": {"name": "num", "__type": "<PERSON><PERSON>"}, "26369035": {"name": "pageIndex", "uuid": "3U-ObKX5uG-", "__type": "Var"}, "26369036": {"name": "num", "__type": "<PERSON><PERSON>"}, "26369037": {"param": {"__ref": "26369012"}, "defaultContents": [], "uuid": "2uhKrxB5bQ3", "parent": {"__ref": "26369015"}, "locked": null, "vsettings": [{"__ref": "26369041"}], "__type": "TplSlot"}, "26369038": {"variants": [{"__ref": "26369016"}], "args": [], "attrs": {}, "rs": {"__ref": "26369042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "26369039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "26369040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "26369045"}, "__type": "ArgType"}, "26369041": {"variants": [{"__ref": "26369016"}], "args": [], "attrs": {}, "rs": {"__ref": "26369046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "26369042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "26369045": {"name": "any", "__type": "AnyType"}, "26369046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380001": {"tag": "div", "name": null, "children": [{"__ref": "33380011"}], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "bh9Disnow", "parent": {"__ref": "23230003"}, "locked": null, "vsettings": [{"__ref": "33380002"}], "__type": "TplTag"}, "33380002": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {}, "rs": {"__ref": "33380003"}, "dataCond": null, "dataRep": null, "text": {"__ref": "33380010"}, "columnsConfig": null, "__type": "VariantSetting"}, "33380003": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "text-align": "center"}, "mixins": [], "__type": "RuleSet"}, "33380010": {"markers": [{"__ref": "33380012"}], "text": "[child]", "__type": "RawText"}, "33380011": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "rxwSsxsm2", "parent": {"__ref": "33380001"}, "locked": null, "vsettings": [{"__ref": "33380013"}], "__type": "TplTag"}, "33380012": {"tpl": {"__ref": "33380011"}, "position": 0, "length": 7, "__type": "NodeMarker"}, "33380013": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {}, "rs": {"__ref": "33380014"}, "dataCond": null, "dataRep": null, "text": {"__ref": "33380016"}, "columnsConfig": null, "__type": "VariantSetting"}, "33380014": {"values": {"padding-bottom": "20px"}, "mixins": [], "__type": "RuleSet"}, "33380016": {"markers": [], "text": "Todo App", "__type": "RawText"}, "33380017": {"name": "any", "__type": "AnyType"}, "33380019": {"tag": "div", "name": null, "children": [{"__ref": "33380040"}, {"__ref": "33381353"}, {"__ref": "33381018"}, {"__ref": "33380914"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "6svomxQ5S", "parent": {"__ref": "12613128"}, "locked": null, "vsettings": [{"__ref": "33380020"}], "__type": "TplTag"}, "33380020": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"data-testid": {"__ref": "12613093"}}, "rs": {"__ref": "33380021"}, "dataCond": null, "dataRep": {"__ref": "33380034"}, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380021": {"values": {"display": "flex", "flex-direction": "row", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "800px", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "background": "linear-gradient(#F1EDED, #F1EDED)", "flex-column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "33380034": {"element": {"__ref": "33380035"}, "index": {"__ref": "33380036"}, "collection": {"__ref": "33380038"}, "__type": "Rep"}, "33380035": {"name": "currentItem", "uuid": "WlaS5R1kl", "__type": "Var"}, "33380036": {"name": "currentIndex", "uuid": "sXz6a4WVKB", "__type": "Var"}, "33380038": {"path": ["$state", "tasks"], "fallback": {"__ref": "33380039"}, "__type": "ObjectPath"}, "33380039": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "33380040": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "7Ux7yqphK", "parent": {"__ref": "33380019"}, "locked": null, "vsettings": [{"__ref": "33380041"}], "__type": "TplTag"}, "33380041": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"style": {"__ref": "12613126"}, "onDoubleClick": {"__ref": "33381698"}, "data-testid": {"__ref": "12613118"}}, "rs": {"__ref": "33380042"}, "dataCond": {"__ref": "33381692"}, "dataRep": null, "text": {"__ref": "33380052"}, "columnsConfig": null, "__type": "VariantSetting"}, "33380042": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "33380052": {"expr": {"__ref": "33380053"}, "html": false, "__type": "ExprText"}, "33380053": {"path": ["currentItem", "title"], "fallback": {"__ref": "33380054"}, "__type": "ObjectPath"}, "33380054": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "33380064": {"uuid": "CZD-RDye8tr", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "33380065": {"uuid": "aAB6jyKLKxx", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "33380066": {"uuid": "zybmzsG04D_", "name": "<PERSON><PERSON>", "params": [{"__ref": "33380069"}, {"__ref": "33380070"}, {"__ref": "33380071"}, {"__ref": "33380072"}, {"__ref": "33380073"}, {"__ref": "33380074"}, {"__ref": "33380075"}, {"__ref": "33380076"}, {"__ref": "33380077"}, {"__ref": "33380078"}, {"__ref": "12613103"}, {"__ref": "4842201"}, {"__ref": "64282013"}, {"__ref": "64282018"}, {"__ref": "64282023"}, {"__ref": "64282028"}, {"__ref": "64282033"}, {"__ref": "64282038"}], "states": [{"__ref": "33380079"}, {"__ref": "33380080"}, {"__ref": "33380081"}, {"__ref": "33380082"}, {"__ref": "33380083"}, {"__ref": "33380084"}], "tplTree": {"__ref": "33380085"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "33380086"}, {"__ref": "33380087"}, {"__ref": "33380088"}, {"__ref": "33380089"}, {"__ref": "33380090"}], "variantGroups": [{"__ref": "33380091"}, {"__ref": "33380092"}, {"__ref": "33380093"}, {"__ref": "33380094"}, {"__ref": "33380095"}, {"__ref": "33380096"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "33380097"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "33380067": {"component": {"__ref": "33380066"}, "matrix": {"__ref": "33380098"}, "customMatrix": {"__ref": "33380099"}, "__type": "ComponentArena"}, "33380068": {"name": null, "component": {"__ref": "33380066"}, "uuid": "qmovW_VUK7y", "parent": {"__ref": "23230003"}, "locked": null, "vsettings": [{"__ref": "33380100"}], "__type": "TplComponent"}, "33380069": {"type": {"__ref": "33380102"}, "tplSlot": {"__ref": "33380222"}, "variable": {"__ref": "33380101"}, "uuid": "lnaXrlizAU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "33380070": {"type": {"__ref": "33380104"}, "state": {"__ref": "33380079"}, "variable": {"__ref": "33380103"}, "uuid": "MHxWhaY7_D", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33380071": {"type": {"__ref": "33380106"}, "state": {"__ref": "33380080"}, "variable": {"__ref": "33380105"}, "uuid": "KrcNR1xdJT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33380072": {"type": {"__ref": "33380108"}, "tplSlot": {"__ref": "33380217"}, "variable": {"__ref": "33380107"}, "uuid": "rte8P_qIYf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "33380073": {"type": {"__ref": "33380110"}, "tplSlot": {"__ref": "33380228"}, "variable": {"__ref": "33380109"}, "uuid": "RbdAm5d7JD", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "33380074": {"type": {"__ref": "33380112"}, "state": {"__ref": "33380081"}, "variable": {"__ref": "33380111"}, "uuid": "z1HFPGktAq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33380075": {"type": {"__ref": "33380114"}, "variable": {"__ref": "33380113"}, "uuid": "nKW7J7hXy2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33380076": {"type": {"__ref": "33380116"}, "state": {"__ref": "33380084"}, "variable": {"__ref": "33380115"}, "uuid": "bSh_luEvyV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33380077": {"type": {"__ref": "33380118"}, "state": {"__ref": "33380083"}, "variable": {"__ref": "33380117"}, "uuid": "YmvJI6pmb8o", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33380078": {"type": {"__ref": "33380120"}, "state": {"__ref": "33380082"}, "variable": {"__ref": "33380119"}, "uuid": "yaiOQJ23Vpo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33380079": {"variantGroup": {"__ref": "33380091"}, "param": {"__ref": "33380070"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282013"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33380080": {"variantGroup": {"__ref": "33380092"}, "param": {"__ref": "33380071"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282018"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33380081": {"variantGroup": {"__ref": "33380093"}, "param": {"__ref": "33380074"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282023"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33380082": {"variantGroup": {"__ref": "33380094"}, "param": {"__ref": "33380078"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282028"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33380083": {"variantGroup": {"__ref": "33380095"}, "param": {"__ref": "33380077"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282033"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33380084": {"variantGroup": {"__ref": "33380096"}, "param": {"__ref": "33380076"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282038"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33380085": {"tag": "button", "name": null, "children": [{"__ref": "33380121"}, {"__ref": "33380122"}, {"__ref": "33380123"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "TAeN_iFLPSZ", "parent": null, "locked": null, "vsettings": [{"__ref": "33380124"}, {"__ref": "33380125"}, {"__ref": "33380126"}, {"__ref": "33380127"}, {"__ref": "33380128"}, {"__ref": "33380129"}, {"__ref": "33380130"}, {"__ref": "33380131"}, {"__ref": "33380132"}, {"__ref": "33380133"}, {"__ref": "33380134"}, {"__ref": "33380135"}, {"__ref": "33380136"}, {"__ref": "33380137"}, {"__ref": "33380138"}, {"__ref": "33380139"}, {"__ref": "33380140"}, {"__ref": "33380141"}, {"__ref": "33380142"}, {"__ref": "33380143"}, {"__ref": "33380144"}, {"__ref": "33380145"}, {"__ref": "33380146"}, {"__ref": "33380147"}, {"__ref": "33380148"}, {"__ref": "33380149"}, {"__ref": "33380150"}, {"__ref": "33380151"}, {"__ref": "33380152"}, {"__ref": "33380153"}, {"__ref": "33380154"}, {"__ref": "33380155"}, {"__ref": "33380156"}, {"__ref": "33380157"}, {"__ref": "33380158"}, {"__ref": "33380159"}, {"__ref": "33380160"}, {"__ref": "33380161"}, {"__ref": "33380162"}, {"__ref": "33380163"}, {"__ref": "33380164"}, {"__ref": "33380165"}, {"__ref": "33380166"}, {"__ref": "33380167"}, {"__ref": "33380168"}, {"__ref": "33380169"}, {"__ref": "33380170"}, {"__ref": "33380171"}, {"__ref": "33380172"}, {"__ref": "33380173"}, {"__ref": "33380174"}, {"__ref": "33380175"}, {"__ref": "33380176"}, {"__ref": "33380177"}, {"__ref": "33380178"}, {"__ref": "33380179"}, {"__ref": "33380180"}, {"__ref": "33380181"}, {"__ref": "33380182"}, {"__ref": "33380183"}], "__type": "TplTag"}, "33380086": {"uuid": "tqfYMsaQstO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380087": {"uuid": "eMiWfcIhwBH", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380088": {"uuid": "8PQWfYAmClG", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380089": {"uuid": "FgHOg1wGPtF", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380090": {"uuid": "f4teH1FRo8r", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380091": {"type": "component", "param": {"__ref": "33380070"}, "linkedState": {"__ref": "33380079"}, "uuid": "0hvvS4kpVXk", "variants": [{"__ref": "33380184"}], "multi": false, "__type": "ComponentVariantGroup"}, "33380092": {"type": "component", "param": {"__ref": "33380071"}, "linkedState": {"__ref": "33380080"}, "uuid": "SfmEhlC03wH", "variants": [{"__ref": "33380185"}], "multi": false, "__type": "ComponentVariantGroup"}, "33380093": {"type": "component", "param": {"__ref": "33380074"}, "linkedState": {"__ref": "33380081"}, "uuid": "qCo8EAHsb4s", "variants": [{"__ref": "33380186"}], "multi": false, "__type": "ComponentVariantGroup"}, "33380094": {"type": "component", "param": {"__ref": "33380078"}, "linkedState": {"__ref": "33380082"}, "uuid": "KcsM4oOcHp5", "variants": [{"__ref": "33380187"}, {"__ref": "33380188"}, {"__ref": "33380189"}], "multi": false, "__type": "ComponentVariantGroup"}, "33380095": {"type": "component", "param": {"__ref": "33380077"}, "linkedState": {"__ref": "33380083"}, "uuid": "59z31JbmT-i", "variants": [{"__ref": "33380190"}, {"__ref": "33380191"}], "multi": false, "__type": "ComponentVariantGroup"}, "33380096": {"type": "component", "param": {"__ref": "33380076"}, "linkedState": {"__ref": "33380084"}, "uuid": "qrCkQkV7M14", "variants": [{"__ref": "33380192"}, {"__ref": "33380193"}, {"__ref": "33380194"}, {"__ref": "33380195"}, {"__ref": "33380196"}, {"__ref": "33380197"}, {"__ref": "33380198"}, {"__ref": "33380199"}, {"__ref": "33380200"}, {"__ref": "33380201"}, {"__ref": "33380202"}, {"__ref": "33380203"}, {"__ref": "33380204"}], "multi": false, "__type": "ComponentVariantGroup"}, "33380097": {"type": "button", "__type": "PlumeInfo"}, "33380098": {"rows": [{"__ref": "33380205"}, {"__ref": "33380206"}, {"__ref": "33380207"}, {"__ref": "33380208"}, {"__ref": "33380209"}, {"__ref": "33380210"}, {"__ref": "33380211"}], "__type": "ArenaFrameGrid"}, "33380099": {"rows": [{"__ref": "33380212"}], "__type": "ArenaFrameGrid"}, "33380100": {"variants": [{"__ref": "23230004"}], "args": [{"__ref": "33380213"}, {"__ref": "33380214"}, {"__ref": "33380215"}, {"__ref": "12613111"}, {"__ref": "4842206"}], "attrs": {"onClick": {"__ref": "33380900"}}, "rs": {"__ref": "33380216"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380101": {"name": "children", "uuid": "OgARr_-lr", "__type": "Var"}, "33380102": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "33380103": {"name": "Show Start Icon", "uuid": "fhIzFYxMd_", "__type": "Var"}, "33380104": {"name": "any", "__type": "AnyType"}, "33380105": {"name": "Show End Icon", "uuid": "ZnO8sXk8vE", "__type": "Var"}, "33380106": {"name": "any", "__type": "AnyType"}, "33380107": {"name": "start icon", "uuid": "9e8hE5r_Sr", "__type": "Var"}, "33380108": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "33380109": {"name": "end icon", "uuid": "2uyCHKnn0j", "__type": "Var"}, "33380110": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "33380111": {"name": "Is Disabled", "uuid": "TM-L3oVkkZ", "__type": "Var"}, "33380112": {"name": "any", "__type": "AnyType"}, "33380113": {"name": "link", "uuid": "F-tkC-tUIK", "__type": "Var"}, "33380114": {"name": "href", "__type": "HrefType"}, "33380115": {"name": "Color", "uuid": "GEl9abScfg", "__type": "Var"}, "33380116": {"name": "any", "__type": "AnyType"}, "33380117": {"name": "Size", "uuid": "ujah8hfPnR4", "__type": "Var"}, "33380118": {"name": "any", "__type": "AnyType"}, "33380119": {"name": "<PERSON><PERSON><PERSON>", "uuid": "1hOSwRM3rGF", "__type": "Var"}, "33380120": {"name": "any", "__type": "AnyType"}, "33380121": {"tag": "div", "name": "start icon container", "children": [{"__ref": "33380217"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ff2sC-3zYmc", "parent": {"__ref": "33380085"}, "locked": null, "vsettings": [{"__ref": "33380218"}, {"__ref": "33380219"}, {"__ref": "33380220"}, {"__ref": "33380221"}], "__type": "TplTag"}, "33380122": {"tag": "div", "name": "content container", "children": [{"__ref": "33380222"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Ad2rIfp0oex", "parent": {"__ref": "33380085"}, "locked": null, "vsettings": [{"__ref": "33380223"}, {"__ref": "33380224"}, {"__ref": "33380225"}, {"__ref": "33380226"}, {"__ref": "33380227"}], "__type": "TplTag"}, "33380123": {"tag": "div", "name": "end icon container", "children": [{"__ref": "33380228"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_aZax0h0N8b", "parent": {"__ref": "33380085"}, "locked": null, "vsettings": [{"__ref": "33380229"}, {"__ref": "33380230"}, {"__ref": "33380231"}, {"__ref": "33380232"}], "__type": "TplTag"}, "33380124": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {"data-testid": {"__ref": "12613104"}}, "rs": {"__ref": "33380233"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380125": {"variants": [{"__ref": "33380087"}], "args": [], "attrs": {}, "rs": {"__ref": "33380234"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380126": {"variants": [{"__ref": "33380088"}], "args": [], "attrs": {}, "rs": {"__ref": "33380235"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380127": {"variants": [{"__ref": "33380186"}], "args": [], "attrs": {}, "rs": {"__ref": "33380236"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380128": {"variants": [{"__ref": "33380185"}], "args": [], "attrs": {}, "rs": {"__ref": "33380237"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380129": {"variants": [{"__ref": "33380184"}], "args": [], "attrs": {}, "rs": {"__ref": "33380238"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380130": {"variants": [{"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380239"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380131": {"variants": [{"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380240"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380132": {"variants": [{"__ref": "33380198"}], "args": [], "attrs": {}, "rs": {"__ref": "33380241"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380133": {"variants": [{"__ref": "33380198"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380242"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380134": {"variants": [{"__ref": "33380198"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380243"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380135": {"variants": [{"__ref": "33380199"}], "args": [], "attrs": {}, "rs": {"__ref": "33380244"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380136": {"variants": [{"__ref": "33380199"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380245"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380137": {"variants": [{"__ref": "33380199"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380246"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380138": {"variants": [{"__ref": "33380194"}], "args": [], "attrs": {}, "rs": {"__ref": "33380247"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380139": {"variants": [{"__ref": "33380194"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380248"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380140": {"variants": [{"__ref": "33380194"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380249"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380141": {"variants": [{"__ref": "33380195"}], "args": [], "attrs": {}, "rs": {"__ref": "33380250"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380142": {"variants": [{"__ref": "33380195"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380251"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380143": {"variants": [{"__ref": "33380195"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380252"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380144": {"variants": [{"__ref": "33380200"}], "args": [], "attrs": {}, "rs": {"__ref": "33380253"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380145": {"variants": [{"__ref": "33380200"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380254"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380146": {"variants": [{"__ref": "33380200"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380255"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380147": {"variants": [{"__ref": "33380201"}], "args": [], "attrs": {}, "rs": {"__ref": "33380256"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380148": {"variants": [{"__ref": "33380201"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380257"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380149": {"variants": [{"__ref": "33380201"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380258"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380150": {"variants": [{"__ref": "33380202"}], "args": [], "attrs": {}, "rs": {"__ref": "33380259"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380151": {"variants": [{"__ref": "33380202"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380260"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380152": {"variants": [{"__ref": "33380202"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380261"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380153": {"variants": [{"__ref": "33380192"}], "args": [], "attrs": {}, "rs": {"__ref": "33380262"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380154": {"variants": [{"__ref": "33380192"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380263"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380155": {"variants": [{"__ref": "33380192"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380264"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380156": {"variants": [{"__ref": "33380193"}], "args": [], "attrs": {}, "rs": {"__ref": "33380265"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380157": {"variants": [{"__ref": "33380193"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380266"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380158": {"variants": [{"__ref": "33380193"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380267"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380159": {"variants": [{"__ref": "33380196"}], "args": [], "attrs": {}, "rs": {"__ref": "33380268"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380160": {"variants": [{"__ref": "33380196"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380269"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380161": {"variants": [{"__ref": "33380196"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380270"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380162": {"variants": [{"__ref": "33380190"}], "args": [], "attrs": {}, "rs": {"__ref": "33380271"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380163": {"variants": [{"__ref": "33380190"}, {"__ref": "33380184"}], "args": [], "attrs": {}, "rs": {"__ref": "33380272"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380164": {"variants": [{"__ref": "33380190"}, {"__ref": "33380184"}, {"__ref": "33380185"}], "args": [], "attrs": {}, "rs": {"__ref": "33380273"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380165": {"variants": [{"__ref": "33380190"}, {"__ref": "33380185"}], "args": [], "attrs": {}, "rs": {"__ref": "33380274"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380166": {"variants": [{"__ref": "33380187"}], "args": [], "attrs": {}, "rs": {"__ref": "33380275"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380167": {"variants": [{"__ref": "33380187"}, {"__ref": "33380184"}], "args": [], "attrs": {}, "rs": {"__ref": "33380276"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380168": {"variants": [{"__ref": "33380185"}, {"__ref": "33380187"}], "args": [], "attrs": {}, "rs": {"__ref": "33380277"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380169": {"variants": [{"__ref": "33380190"}, {"__ref": "33380187"}], "args": [], "attrs": {}, "rs": {"__ref": "33380278"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380170": {"variants": [{"__ref": "33380203"}], "args": [], "attrs": {}, "rs": {"__ref": "33380279"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380171": {"variants": [{"__ref": "33380203"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380280"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380172": {"variants": [{"__ref": "33380203"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380281"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380173": {"variants": [{"__ref": "33380188"}], "args": [], "attrs": {}, "rs": {"__ref": "33380282"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380174": {"variants": [{"__ref": "33380188"}, {"__ref": "33380190"}], "args": [], "attrs": {}, "rs": {"__ref": "33380283"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380175": {"variants": [{"__ref": "33380204"}], "args": [], "attrs": {}, "rs": {"__ref": "33380284"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380176": {"variants": [{"__ref": "33380204"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380285"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380177": {"variants": [{"__ref": "33380204"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380286"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380178": {"variants": [{"__ref": "33380204"}, {"__ref": "33380191"}], "args": [], "attrs": {}, "rs": {"__ref": "33380287"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380179": {"variants": [{"__ref": "33380191"}], "args": [], "attrs": {}, "rs": {"__ref": "33380288"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380180": {"variants": [{"__ref": "33380197"}], "args": [], "attrs": {}, "rs": {"__ref": "33380289"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380181": {"variants": [{"__ref": "33380197"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380290"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380182": {"variants": [{"__ref": "33380197"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380291"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380183": {"variants": [{"__ref": "33380189"}], "args": [], "attrs": {}, "rs": {"__ref": "33380292"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380184": {"uuid": "-ztGwNOUqlu", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380091"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380185": {"uuid": "igRMX7NKfb9", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380092"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380186": {"uuid": "RQ8EQRWA_Ty", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380093"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380187": {"uuid": "YntxCaShwkn", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380094"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380188": {"uuid": "dO3HhqI4Y10", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380094"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380189": {"uuid": "EgBgbpX9gJl", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380094"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380190": {"uuid": "PAWstXD8pAD", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380095"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380191": {"uuid": "1N71YNI2BoA", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380095"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380192": {"uuid": "_hKJqq-ZXZa", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380193": {"uuid": "1UF67IEolfS", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380194": {"uuid": "xUKoYOkIluw", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380195": {"uuid": "PlXlVwY_BsA", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380196": {"uuid": "jbwuU5F5PJS", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380197": {"uuid": "54WsjX8OGmV", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380198": {"uuid": "lVGbfOibF_p", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380199": {"uuid": "lRWbcVy-qvk", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380200": {"uuid": "YwU2Wx6Uro5", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380201": {"uuid": "gVY2fFg_9m5", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380202": {"uuid": "wPmhi3DIlmm", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380203": {"uuid": "YrJDCB9V-jq", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380204": {"uuid": "fd_7r5NbBp4", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33380096"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33380205": {"cols": [{"__ref": "33380293"}, {"__ref": "33380294"}, {"__ref": "33380295"}, {"__ref": "33380296"}, {"__ref": "33380297"}], "rowKey": null, "__type": "ArenaFrameRow"}, "33380206": {"cols": [{"__ref": "33380298"}], "rowKey": {"__ref": "33380091"}, "__type": "ArenaFrameRow"}, "33380207": {"cols": [{"__ref": "33380299"}], "rowKey": {"__ref": "33380092"}, "__type": "ArenaFrameRow"}, "33380208": {"cols": [{"__ref": "33380300"}], "rowKey": {"__ref": "33380093"}, "__type": "ArenaFrameRow"}, "33380209": {"cols": [{"__ref": "33380301"}, {"__ref": "33380302"}, {"__ref": "33380303"}], "rowKey": {"__ref": "33380094"}, "__type": "ArenaFrameRow"}, "33380210": {"cols": [{"__ref": "33380304"}, {"__ref": "33380305"}], "rowKey": {"__ref": "33380095"}, "__type": "ArenaFrameRow"}, "33380211": {"cols": [{"__ref": "33380306"}, {"__ref": "33380307"}, {"__ref": "33380308"}, {"__ref": "33380309"}, {"__ref": "33380310"}, {"__ref": "33380311"}, {"__ref": "33380312"}, {"__ref": "33380313"}, {"__ref": "33380314"}, {"__ref": "33380315"}, {"__ref": "33380316"}, {"__ref": "33380317"}, {"__ref": "33380318"}], "rowKey": {"__ref": "33380096"}, "__type": "ArenaFrameRow"}, "33380212": {"cols": [{"__ref": "33380319"}, {"__ref": "33380320"}, {"__ref": "33380321"}, {"__ref": "33380322"}, {"__ref": "33380323"}, {"__ref": "33380324"}, {"__ref": "33380325"}, {"__ref": "33380326"}, {"__ref": "33380327"}, {"__ref": "33380328"}, {"__ref": "33380329"}, {"__ref": "33380330"}, {"__ref": "33380331"}, {"__ref": "33380332"}, {"__ref": "33380333"}, {"__ref": "33380334"}, {"__ref": "33380335"}, {"__ref": "33380336"}, {"__ref": "33380337"}, {"__ref": "33380338"}, {"__ref": "33380339"}, {"__ref": "33380340"}, {"__ref": "33380341"}, {"__ref": "33380342"}, {"__ref": "33380343"}, {"__ref": "33380344"}, {"__ref": "33380345"}, {"__ref": "33380346"}, {"__ref": "33380347"}], "rowKey": null, "__type": "ArenaFrameRow"}, "33380213": {"param": {"__ref": "33380072"}, "expr": {"__ref": "33380348"}, "__type": "Arg"}, "33380214": {"param": {"__ref": "33380069"}, "expr": {"__ref": "33380899"}, "__type": "Arg"}, "33380215": {"param": {"__ref": "33380073"}, "expr": {"__ref": "33380350"}, "__type": "Arg"}, "33380216": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "33380217": {"param": {"__ref": "33380072"}, "defaultContents": [{"__ref": "33380353"}], "uuid": "BbjAEIYTCKp", "parent": {"__ref": "33380121"}, "locked": null, "vsettings": [{"__ref": "33380354"}, {"__ref": "33380355"}, {"__ref": "33380356"}, {"__ref": "33380357"}, {"__ref": "33380358"}, {"__ref": "33380359"}, {"__ref": "33380360"}, {"__ref": "33380361"}, {"__ref": "33380362"}, {"__ref": "33380363"}, {"__ref": "33380364"}, {"__ref": "33380365"}, {"__ref": "33380366"}, {"__ref": "33380367"}], "__type": "TplSlot"}, "33380218": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {}, "rs": {"__ref": "33380368"}, "dataCond": {"__ref": "33380369"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380219": {"variants": [{"__ref": "33380184"}], "args": [], "attrs": {}, "rs": {"__ref": "33380370"}, "dataCond": {"__ref": "33380371"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380220": {"variants": [{"__ref": "33380192"}], "args": [], "attrs": {}, "rs": {"__ref": "33380372"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380221": {"variants": [{"__ref": "33380187"}, {"__ref": "33380184"}], "args": [], "attrs": {}, "rs": {"__ref": "33380373"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380222": {"param": {"__ref": "33380069"}, "defaultContents": [{"__ref": "33380374"}], "uuid": "2jTV6EraFoZ", "parent": {"__ref": "33380122"}, "locked": null, "vsettings": [{"__ref": "33380375"}, {"__ref": "33380376"}, {"__ref": "33380377"}, {"__ref": "33380378"}, {"__ref": "33380379"}, {"__ref": "33380380"}, {"__ref": "33380381"}, {"__ref": "33380382"}, {"__ref": "33380383"}, {"__ref": "33380384"}, {"__ref": "33380385"}, {"__ref": "33380386"}, {"__ref": "33380387"}, {"__ref": "33380388"}, {"__ref": "33380389"}, {"__ref": "33380390"}, {"__ref": "33380391"}, {"__ref": "33380392"}, {"__ref": "33380393"}, {"__ref": "33380394"}, {"__ref": "33380395"}, {"__ref": "33380396"}, {"__ref": "33380397"}, {"__ref": "33380398"}, {"__ref": "33380399"}, {"__ref": "33380400"}], "__type": "TplSlot"}, "33380223": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {}, "rs": {"__ref": "33380401"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380224": {"variants": [{"__ref": "33380186"}], "args": [], "attrs": {}, "rs": {"__ref": "33380402"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380225": {"variants": [{"__ref": "33380185"}], "args": [], "attrs": {}, "rs": {"__ref": "33380403"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380226": {"variants": [{"__ref": "33380087"}], "args": [], "attrs": {}, "rs": {"__ref": "33380404"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380227": {"variants": [{"__ref": "33380187"}], "args": [], "attrs": {}, "rs": {"__ref": "33380405"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380228": {"param": {"__ref": "33380073"}, "defaultContents": [{"__ref": "33380406"}], "uuid": "t3sNP8sbbSa", "parent": {"__ref": "33380123"}, "locked": null, "vsettings": [{"__ref": "33380407"}, {"__ref": "33380408"}, {"__ref": "33380409"}, {"__ref": "33380410"}, {"__ref": "33380411"}, {"__ref": "33380412"}, {"__ref": "33380413"}, {"__ref": "33380414"}, {"__ref": "33380415"}, {"__ref": "33380416"}, {"__ref": "33380417"}, {"__ref": "33380418"}, {"__ref": "33380419"}], "__type": "TplSlot"}, "33380229": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {}, "rs": {"__ref": "33380420"}, "dataCond": {"__ref": "33380421"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380230": {"variants": [{"__ref": "33380185"}], "args": [], "attrs": {}, "rs": {"__ref": "33380422"}, "dataCond": {"__ref": "33380423"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380231": {"variants": [{"__ref": "33380194"}], "args": [], "attrs": {}, "rs": {"__ref": "33380424"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380232": {"variants": [{"__ref": "33380197"}], "args": [], "attrs": {}, "rs": {"__ref": "33380425"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380233": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "flex-column-gap": "8px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px"}, "mixins": [], "__type": "RuleSet"}, "33380234": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "33380235": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "33380236": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "33380237": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "33380238": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "33380239": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "33380240": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "33380241": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "33380242": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "33380243": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "33380244": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "33380245": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "33380246": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "33380247": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "33380248": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "33380249": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "33380250": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "33380251": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "33380252": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "33380253": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "33380254": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "33380255": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "33380256": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "33380257": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "33380258": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "33380259": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "33380260": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "33380261": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "33380262": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "33380263": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "33380264": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "33380265": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "33380266": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "33380267": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "33380268": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "33380269": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "33380270": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "33380271": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "33380272": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380273": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380274": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380275": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "33380276": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "33380277": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "33380278": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380279": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "33380280": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "33380281": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "33380282": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "33380283": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "33380284": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "33380285": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "33380286": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "33380287": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380288": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "33380289": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "33380290": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "33380291": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "33380292": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "33380293": {"frame": {"__ref": "33380528"}, "cellKey": {"__ref": "33380086"}, "__type": "ArenaFrameCell"}, "33380294": {"frame": {"__ref": "33380529"}, "cellKey": {"__ref": "33380087"}, "__type": "ArenaFrameCell"}, "33380295": {"frame": {"__ref": "33380530"}, "cellKey": {"__ref": "33380088"}, "__type": "ArenaFrameCell"}, "33380296": {"frame": {"__ref": "33380531"}, "cellKey": {"__ref": "33380089"}, "__type": "ArenaFrameCell"}, "33380297": {"frame": {"__ref": "33380532"}, "cellKey": {"__ref": "33380090"}, "__type": "ArenaFrameCell"}, "33380298": {"frame": {"__ref": "33380533"}, "cellKey": {"__ref": "33380184"}, "__type": "ArenaFrameCell"}, "33380299": {"frame": {"__ref": "33380534"}, "cellKey": {"__ref": "33380185"}, "__type": "ArenaFrameCell"}, "33380300": {"frame": {"__ref": "33380535"}, "cellKey": {"__ref": "33380186"}, "__type": "ArenaFrameCell"}, "33380301": {"frame": {"__ref": "33380536"}, "cellKey": {"__ref": "33380187"}, "__type": "ArenaFrameCell"}, "33380302": {"frame": {"__ref": "33380537"}, "cellKey": {"__ref": "33380188"}, "__type": "ArenaFrameCell"}, "33380303": {"frame": {"__ref": "33380538"}, "cellKey": {"__ref": "33380189"}, "__type": "ArenaFrameCell"}, "33380304": {"frame": {"__ref": "33380539"}, "cellKey": {"__ref": "33380190"}, "__type": "ArenaFrameCell"}, "33380305": {"frame": {"__ref": "33380540"}, "cellKey": {"__ref": "33380191"}, "__type": "ArenaFrameCell"}, "33380306": {"frame": {"__ref": "33380541"}, "cellKey": {"__ref": "33380192"}, "__type": "ArenaFrameCell"}, "33380307": {"frame": {"__ref": "33380542"}, "cellKey": {"__ref": "33380193"}, "__type": "ArenaFrameCell"}, "33380308": {"frame": {"__ref": "33380543"}, "cellKey": {"__ref": "33380194"}, "__type": "ArenaFrameCell"}, "33380309": {"frame": {"__ref": "33380544"}, "cellKey": {"__ref": "33380195"}, "__type": "ArenaFrameCell"}, "33380310": {"frame": {"__ref": "33380545"}, "cellKey": {"__ref": "33380196"}, "__type": "ArenaFrameCell"}, "33380311": {"frame": {"__ref": "33380546"}, "cellKey": {"__ref": "33380197"}, "__type": "ArenaFrameCell"}, "33380312": {"frame": {"__ref": "33380547"}, "cellKey": {"__ref": "33380198"}, "__type": "ArenaFrameCell"}, "33380313": {"frame": {"__ref": "33380548"}, "cellKey": {"__ref": "33380199"}, "__type": "ArenaFrameCell"}, "33380314": {"frame": {"__ref": "33380549"}, "cellKey": {"__ref": "33380200"}, "__type": "ArenaFrameCell"}, "33380315": {"frame": {"__ref": "33380550"}, "cellKey": {"__ref": "33380201"}, "__type": "ArenaFrameCell"}, "33380316": {"frame": {"__ref": "33380551"}, "cellKey": {"__ref": "33380202"}, "__type": "ArenaFrameCell"}, "33380317": {"frame": {"__ref": "33380552"}, "cellKey": {"__ref": "33380203"}, "__type": "ArenaFrameCell"}, "33380318": {"frame": {"__ref": "33380553"}, "cellKey": {"__ref": "33380204"}, "__type": "ArenaFrameCell"}, "33380319": {"frame": {"__ref": "33380554"}, "cellKey": [{"__ref": "33380198"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380320": {"frame": {"__ref": "33380555"}, "cellKey": [{"__ref": "33380198"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380321": {"frame": {"__ref": "33380556"}, "cellKey": [{"__ref": "33380199"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380322": {"frame": {"__ref": "33380557"}, "cellKey": [{"__ref": "33380199"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380323": {"frame": {"__ref": "33380558"}, "cellKey": [{"__ref": "33380194"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380324": {"frame": {"__ref": "33380559"}, "cellKey": [{"__ref": "33380194"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380325": {"frame": {"__ref": "33380560"}, "cellKey": [{"__ref": "33380195"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380326": {"frame": {"__ref": "33380561"}, "cellKey": [{"__ref": "33380195"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380327": {"frame": {"__ref": "33380562"}, "cellKey": [{"__ref": "33380200"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380328": {"frame": {"__ref": "33380563"}, "cellKey": [{"__ref": "33380200"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380329": {"frame": {"__ref": "33380564"}, "cellKey": [{"__ref": "33380201"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380330": {"frame": {"__ref": "33380565"}, "cellKey": [{"__ref": "33380201"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380331": {"frame": {"__ref": "33380566"}, "cellKey": [{"__ref": "33380202"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380332": {"frame": {"__ref": "33380567"}, "cellKey": [{"__ref": "33380202"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380333": {"frame": {"__ref": "33380568"}, "cellKey": [{"__ref": "33380192"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380334": {"frame": {"__ref": "33380569"}, "cellKey": [{"__ref": "33380192"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380335": {"frame": {"__ref": "33380570"}, "cellKey": [{"__ref": "33380193"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380336": {"frame": {"__ref": "33380571"}, "cellKey": [{"__ref": "33380193"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380337": {"frame": {"__ref": "33380572"}, "cellKey": [{"__ref": "33380196"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380338": {"frame": {"__ref": "33380573"}, "cellKey": [{"__ref": "33380196"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380339": {"frame": {"__ref": "33380574"}, "cellKey": [{"__ref": "33380187"}, {"__ref": "33380184"}], "__type": "ArenaFrameCell"}, "33380340": {"frame": {"__ref": "33380575"}, "cellKey": [{"__ref": "33380185"}, {"__ref": "33380187"}], "__type": "ArenaFrameCell"}, "33380341": {"frame": {"__ref": "33380576"}, "cellKey": [{"__ref": "33380203"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380342": {"frame": {"__ref": "33380577"}, "cellKey": [{"__ref": "33380203"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380343": {"frame": {"__ref": "33380578"}, "cellKey": [{"__ref": "33380188"}, {"__ref": "33380190"}], "__type": "ArenaFrameCell"}, "33380344": {"frame": {"__ref": "33380579"}, "cellKey": [{"__ref": "33380204"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380345": {"frame": {"__ref": "33380580"}, "cellKey": [{"__ref": "33380204"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380346": {"frame": {"__ref": "33380581"}, "cellKey": [{"__ref": "33380197"}, {"__ref": "33380089"}], "__type": "ArenaFrameCell"}, "33380347": {"frame": {"__ref": "33380582"}, "cellKey": [{"__ref": "33380197"}, {"__ref": "33380090"}], "__type": "ArenaFrameCell"}, "33380348": {"tpl": [{"__ref": "33380583"}], "__type": "VirtualRenderExpr"}, "33380350": {"tpl": [{"__ref": "33380585"}], "__type": "VirtualRenderExpr"}, "33380353": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "_5_r7xFVPpR", "parent": {"__ref": "33380217"}, "locked": null, "vsettings": [{"__ref": "33380586"}], "__type": "TplTag"}, "33380354": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {}, "rs": {"__ref": "33380587"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380355": {"variants": [{"__ref": "33380184"}], "args": [], "attrs": {}, "rs": {"__ref": "33380588"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380356": {"variants": [{"__ref": "33380192"}], "args": [], "attrs": {}, "rs": {"__ref": "33380589"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380357": {"variants": [{"__ref": "33380198"}], "args": [], "attrs": {}, "rs": {"__ref": "33380590"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380358": {"variants": [{"__ref": "33380199"}], "args": [], "attrs": {}, "rs": {"__ref": "33380591"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380359": {"variants": [{"__ref": "33380200"}], "args": [], "attrs": {}, "rs": {"__ref": "33380592"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380360": {"variants": [{"__ref": "33380201"}], "args": [], "attrs": {}, "rs": {"__ref": "33380593"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380361": {"variants": [{"__ref": "33380202"}], "args": [], "attrs": {}, "rs": {"__ref": "33380594"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380362": {"variants": [{"__ref": "33380194"}], "args": [], "attrs": {}, "rs": {"__ref": "33380595"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380363": {"variants": [{"__ref": "33380204"}], "args": [], "attrs": {}, "rs": {"__ref": "33380596"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380364": {"variants": [{"__ref": "33380204"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380597"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380365": {"variants": [{"__ref": "33380204"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380598"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380366": {"variants": [{"__ref": "33380203"}], "args": [], "attrs": {}, "rs": {"__ref": "33380599"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380367": {"variants": [{"__ref": "33380197"}], "args": [], "attrs": {}, "rs": {"__ref": "33380600"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380368": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "33380369": {"code": "false", "fallback": null, "__type": "CustomCode"}, "33380370": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "33380371": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33380372": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380373": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380374": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "t_vsVqKa8CS", "parent": {"__ref": "33380222"}, "locked": null, "vsettings": [{"__ref": "33380607"}], "__type": "TplTag"}, "33380375": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {}, "rs": {"__ref": "33380608"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380376": {"variants": [{"__ref": "33380088"}], "args": [], "attrs": {}, "rs": {"__ref": "33380609"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380377": {"variants": [{"__ref": "33380087"}], "args": [], "attrs": {}, "rs": {"__ref": "33380610"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380378": {"variants": [{"__ref": "33380184"}], "args": [], "attrs": {}, "rs": {"__ref": "33380611"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380379": {"variants": [{"__ref": "33380185"}], "args": [], "attrs": {}, "rs": {"__ref": "33380612"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380380": {"variants": [{"__ref": "33380186"}], "args": [], "attrs": {}, "rs": {"__ref": "33380613"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380381": {"variants": [{"__ref": "33380198"}], "args": [], "attrs": {}, "rs": {"__ref": "33380614"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380382": {"variants": [{"__ref": "33380199"}], "args": [], "attrs": {}, "rs": {"__ref": "33380615"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380383": {"variants": [{"__ref": "33380194"}], "args": [], "attrs": {}, "rs": {"__ref": "33380616"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380384": {"variants": [{"__ref": "33380200"}], "args": [], "attrs": {}, "rs": {"__ref": "33380617"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380385": {"variants": [{"__ref": "33380201"}], "args": [], "attrs": {}, "rs": {"__ref": "33380618"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380386": {"variants": [{"__ref": "33380202"}], "args": [], "attrs": {}, "rs": {"__ref": "33380619"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380387": {"variants": [{"__ref": "33380192"}], "args": [], "attrs": {}, "rs": {"__ref": "33380620"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380388": {"variants": [{"__ref": "33380193"}], "args": [], "attrs": {}, "rs": {"__ref": "33380621"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380389": {"variants": [{"__ref": "33380196"}], "args": [], "attrs": {}, "rs": {"__ref": "33380622"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380390": {"variants": [{"__ref": "33380195"}], "args": [], "attrs": {}, "rs": {"__ref": "33380623"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380391": {"variants": [{"__ref": "33380187"}], "args": [], "attrs": {}, "rs": {"__ref": "33380624"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380392": {"variants": [{"__ref": "33380203"}], "args": [], "attrs": {}, "rs": {"__ref": "33380625"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380393": {"variants": [{"__ref": "33380204"}], "args": [], "attrs": {}, "rs": {"__ref": "33380626"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380394": {"variants": [{"__ref": "33380204"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380627"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380395": {"variants": [{"__ref": "33380204"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380628"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380396": {"variants": [{"__ref": "33380204"}, {"__ref": "33380191"}], "args": [], "attrs": {}, "rs": {"__ref": "33380629"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380397": {"variants": [{"__ref": "33380204"}, {"__ref": "33380191"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380630"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380398": {"variants": [{"__ref": "33380191"}], "args": [], "attrs": {}, "rs": {"__ref": "33380631"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380399": {"variants": [{"__ref": "33380191"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380632"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380400": {"variants": [{"__ref": "33380197"}], "args": [], "attrs": {}, "rs": {"__ref": "33380633"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380401": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "33380402": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380403": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380404": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380405": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380406": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "vI6myOuzXJz", "parent": {"__ref": "33380228"}, "locked": null, "vsettings": [{"__ref": "33380639"}], "__type": "TplTag"}, "33380407": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {}, "rs": {"__ref": "33380640"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380408": {"variants": [{"__ref": "33380185"}], "args": [], "attrs": {}, "rs": {"__ref": "33380641"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380409": {"variants": [{"__ref": "33380198"}], "args": [], "attrs": {}, "rs": {"__ref": "33380642"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380410": {"variants": [{"__ref": "33380199"}], "args": [], "attrs": {}, "rs": {"__ref": "33380643"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380411": {"variants": [{"__ref": "33380200"}], "args": [], "attrs": {}, "rs": {"__ref": "33380644"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380412": {"variants": [{"__ref": "33380201"}], "args": [], "attrs": {}, "rs": {"__ref": "33380645"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380413": {"variants": [{"__ref": "33380202"}], "args": [], "attrs": {}, "rs": {"__ref": "33380646"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380414": {"variants": [{"__ref": "33380194"}], "args": [], "attrs": {}, "rs": {"__ref": "33380647"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380415": {"variants": [{"__ref": "33380204"}], "args": [], "attrs": {}, "rs": {"__ref": "33380648"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380416": {"variants": [{"__ref": "33380204"}, {"__ref": "33380089"}], "args": [], "attrs": {}, "rs": {"__ref": "33380649"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380417": {"variants": [{"__ref": "33380204"}, {"__ref": "33380090"}], "args": [], "attrs": {}, "rs": {"__ref": "33380650"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380418": {"variants": [{"__ref": "33380203"}], "args": [], "attrs": {}, "rs": {"__ref": "33380651"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380419": {"variants": [{"__ref": "33380197"}], "args": [], "attrs": {}, "rs": {"__ref": "33380652"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380420": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "33380421": {"code": "false", "fallback": null, "__type": "CustomCode"}, "33380422": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "33380423": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33380424": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380425": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380528": {"uuid": "cobCDN-hQKD", "width": 340, "height": 340, "container": {"__ref": "33380659"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380086"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380529": {"uuid": "CKsOsXFHJfi", "width": 340, "height": 340, "container": {"__ref": "33380660"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380087"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380530": {"uuid": "3BGWeDdLHIC", "width": 340, "height": 340, "container": {"__ref": "33380661"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380088"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380531": {"uuid": "wZ9fu70E6rn", "width": 340, "height": 340, "container": {"__ref": "33380662"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380532": {"uuid": "PMtGrhTEN1N", "width": 340, "height": 340, "container": {"__ref": "33380663"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380533": {"uuid": "WbJyn_Jj7_4", "width": 340, "height": 340, "container": {"__ref": "33380664"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380184"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380534": {"uuid": "R8VkCszZORi", "width": 340, "height": 340, "container": {"__ref": "33380665"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380185"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380535": {"uuid": "BHjkZTQ2pHL", "width": 340, "height": 340, "container": {"__ref": "33380666"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380186"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380536": {"uuid": "zrHSTruVhbP", "width": 340, "height": 340, "container": {"__ref": "33380667"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380187"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380537": {"uuid": "vXooOA_ujOw", "width": 340, "height": 340, "container": {"__ref": "33380668"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380188"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380538": {"uuid": "jy73fhgsz_l", "width": 340, "height": 340, "container": {"__ref": "33380669"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380189"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380539": {"uuid": "wUD6glH6Irh", "width": 340, "height": 340, "container": {"__ref": "33380670"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380190"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380540": {"uuid": "wpf_QHcX0KT", "width": 340, "height": 340, "container": {"__ref": "33380671"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380191"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380541": {"uuid": "4VhBg9EYuX9", "width": 340, "height": 340, "container": {"__ref": "33380672"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380192"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380542": {"uuid": "Ltj-6nwLNb6", "width": 340, "height": 340, "container": {"__ref": "33380673"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380193"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380543": {"uuid": "ZtzOJLh1eVq", "width": 340, "height": 340, "container": {"__ref": "33380674"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380194"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380544": {"uuid": "H-Gkjgw7N42", "width": 340, "height": 340, "container": {"__ref": "33380675"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380195"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380545": {"uuid": "08A-3zJFq2q", "width": 340, "height": 340, "container": {"__ref": "33380676"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380196"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380546": {"uuid": "Z1iwBFY2m24", "width": 340, "height": 340, "container": {"__ref": "33380677"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380197"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380547": {"uuid": "_AEj225MDpA", "width": 340, "height": 340, "container": {"__ref": "33380678"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380198"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380548": {"uuid": "-jj95YpYOL5", "width": 340, "height": 340, "container": {"__ref": "33380679"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380199"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380549": {"uuid": "sSyRt_WaHHA", "width": 340, "height": 340, "container": {"__ref": "33380680"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380200"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380550": {"uuid": "JcnajxhzkDD", "width": 340, "height": 340, "container": {"__ref": "33380681"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380201"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380551": {"uuid": "a84mXOVJQpm", "width": 340, "height": 340, "container": {"__ref": "33380682"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380202"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380552": {"uuid": "nlBAig8mbls", "width": 340, "height": 340, "container": {"__ref": "33380683"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380203"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380553": {"uuid": "-DnJXNF7IyW", "width": 340, "height": 340, "container": {"__ref": "33380684"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380204"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380554": {"uuid": "xOvKVmdxpoS", "width": 340, "height": 340, "container": {"__ref": "33380685"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380198"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380555": {"uuid": "3qIkCzg2iJL", "width": 340, "height": 340, "container": {"__ref": "33380686"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380198"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380556": {"uuid": "JocZY5s0e_5", "width": 340, "height": 340, "container": {"__ref": "33380687"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380199"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380557": {"uuid": "_3LBq4zAEik", "width": 340, "height": 340, "container": {"__ref": "33380688"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380199"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380558": {"uuid": "oQcbdKI-pja", "width": 340, "height": 340, "container": {"__ref": "33380689"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380194"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380559": {"uuid": "eewrVnswHvU", "width": 340, "height": 340, "container": {"__ref": "33380690"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380194"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380560": {"uuid": "xTscVxITE9F", "width": 340, "height": 340, "container": {"__ref": "33380691"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380195"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380561": {"uuid": "dHCKSTuCE8F", "width": 340, "height": 340, "container": {"__ref": "33380692"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380195"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380562": {"uuid": "XuRZs_d6mNt", "width": 340, "height": 340, "container": {"__ref": "33380693"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380200"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380563": {"uuid": "Rw0hh0bAT4V", "width": 340, "height": 340, "container": {"__ref": "33380694"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380200"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380564": {"uuid": "1DaQ6eaNEO0", "width": 340, "height": 340, "container": {"__ref": "33380695"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380201"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380565": {"uuid": "i5ajwFZnu8n", "width": 340, "height": 340, "container": {"__ref": "33380696"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380201"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380566": {"uuid": "u9_yvVqVGC6", "width": 340, "height": 340, "container": {"__ref": "33380697"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380202"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380567": {"uuid": "F9_q4gtvwkA", "width": 340, "height": 340, "container": {"__ref": "33380698"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380202"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380568": {"uuid": "3Ok5hOoB1ol", "width": 340, "height": 340, "container": {"__ref": "33380699"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380192"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380569": {"uuid": "YmSbsO328rk", "width": 340, "height": 340, "container": {"__ref": "33380700"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380192"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380570": {"uuid": "WNxEs3PevlQ", "width": 340, "height": 340, "container": {"__ref": "33380701"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380193"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380571": {"uuid": "hN92ut87wif", "width": 340, "height": 340, "container": {"__ref": "33380702"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380193"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380572": {"uuid": "MJKfLId2u23", "width": 340, "height": 340, "container": {"__ref": "33380703"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380196"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380573": {"uuid": "cQG6luyXWjD", "width": 340, "height": 340, "container": {"__ref": "33380704"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380196"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380574": {"uuid": "m8jEaaBnT2_", "width": 340, "height": 340, "container": {"__ref": "33380705"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380187"}, {"__ref": "33380184"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380575": {"uuid": "0Yw63wZJhTr", "width": 340, "height": 340, "container": {"__ref": "33380706"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380185"}, {"__ref": "33380187"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380576": {"uuid": "Hr8Qw3Bwqcm", "width": 340, "height": 340, "container": {"__ref": "33380707"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380203"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380577": {"uuid": "IKeEx6xW_JV", "width": 340, "height": 340, "container": {"__ref": "33380708"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380203"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380578": {"uuid": "0ZlgRaAyPdi", "width": 340, "height": 340, "container": {"__ref": "33380709"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380188"}, {"__ref": "33380190"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380579": {"uuid": "gKCdYsXmM_P", "width": 340, "height": 340, "container": {"__ref": "33380710"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380204"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380580": {"uuid": "l5qN92-z7Ue", "width": 340, "height": 340, "container": {"__ref": "33380711"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380204"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380581": {"uuid": "G6NtnMdwN4q", "width": 340, "height": 340, "container": {"__ref": "33380712"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380197"}, {"__ref": "33380089"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380582": {"uuid": "a5MYQghAvNS", "width": 340, "height": 340, "container": {"__ref": "33380713"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33380197"}, {"__ref": "33380090"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33380583": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "tVaCtn-U3jZ", "parent": {"__ref": "33380068"}, "locked": null, "vsettings": [{"__ref": "33380714"}], "__type": "TplTag"}, "33380584": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "NjdsCYXg2iU", "parent": {"__ref": "33380068"}, "locked": null, "vsettings": [{"__ref": "33380715"}], "__type": "TplTag"}, "33380585": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "NzPffsW6zm7", "parent": {"__ref": "33380068"}, "locked": null, "vsettings": [{"__ref": "33380716"}], "__type": "TplTag"}, "33380586": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {"outerHTML": {"__ref": "33380717"}}, "rs": {"__ref": "33380718"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380587": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "33380588": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380589": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380590": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "33380591": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "33380592": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "33380593": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "33380594": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "33380595": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "33380596": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "33380597": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "33380598": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "33380599": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "33380600": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "33380607": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {}, "rs": {"__ref": "33380731"}, "dataCond": null, "dataRep": null, "text": {"__ref": "33380732"}, "columnsConfig": null, "__type": "VariantSetting"}, "33380608": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "33380609": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380610": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380611": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380612": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380613": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380614": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "33380615": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "33380616": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "33380617": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "33380618": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "33380619": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "33380620": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "33380621": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "33380622": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "33380623": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "33380624": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380625": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "33380626": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "33380627": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "33380628": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "33380629": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380630": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380631": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380632": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380633": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "33380639": {"variants": [{"__ref": "33380086"}], "args": [], "attrs": {"outerHTML": {"__ref": "33380752"}}, "rs": {"__ref": "33380753"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380640": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "33380641": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380642": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "33380643": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "33380644": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "33380645": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "33380646": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "33380647": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "33380648": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "33380649": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "33380650": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "33380651": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "33380652": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "33380659": {"name": null, "component": {"__ref": "33380066"}, "uuid": "v8WVu-Fjnvf", "parent": null, "locked": null, "vsettings": [{"__ref": "33380766"}], "__type": "TplComponent"}, "33380660": {"name": null, "component": {"__ref": "33380066"}, "uuid": "g5bL5b9MTkm", "parent": null, "locked": null, "vsettings": [{"__ref": "33380767"}], "__type": "TplComponent"}, "33380661": {"name": null, "component": {"__ref": "33380066"}, "uuid": "j8mR-oH_DXS", "parent": null, "locked": null, "vsettings": [{"__ref": "33380768"}], "__type": "TplComponent"}, "33380662": {"name": null, "component": {"__ref": "33380066"}, "uuid": "jDddFSyB25w", "parent": null, "locked": null, "vsettings": [{"__ref": "33380769"}], "__type": "TplComponent"}, "33380663": {"name": null, "component": {"__ref": "33380066"}, "uuid": "diizJzEizFu", "parent": null, "locked": null, "vsettings": [{"__ref": "33380770"}], "__type": "TplComponent"}, "33380664": {"name": null, "component": {"__ref": "33380066"}, "uuid": "_DjZ_pOV5Mn", "parent": null, "locked": null, "vsettings": [{"__ref": "33380771"}], "__type": "TplComponent"}, "33380665": {"name": null, "component": {"__ref": "33380066"}, "uuid": "WppkXBz3eY4", "parent": null, "locked": null, "vsettings": [{"__ref": "33380772"}], "__type": "TplComponent"}, "33380666": {"name": null, "component": {"__ref": "33380066"}, "uuid": "G-S-h68fkxS", "parent": null, "locked": null, "vsettings": [{"__ref": "33380773"}], "__type": "TplComponent"}, "33380667": {"name": null, "component": {"__ref": "33380066"}, "uuid": "s5CpL9qf8Gg", "parent": null, "locked": null, "vsettings": [{"__ref": "33380774"}], "__type": "TplComponent"}, "33380668": {"name": null, "component": {"__ref": "33380066"}, "uuid": "hZr9Xy6aZmh", "parent": null, "locked": null, "vsettings": [{"__ref": "33380775"}], "__type": "TplComponent"}, "33380669": {"name": null, "component": {"__ref": "33380066"}, "uuid": "8Fuehqbfl0C", "parent": null, "locked": null, "vsettings": [{"__ref": "33380776"}], "__type": "TplComponent"}, "33380670": {"name": null, "component": {"__ref": "33380066"}, "uuid": "-9mMC48ob1F", "parent": null, "locked": null, "vsettings": [{"__ref": "33380777"}], "__type": "TplComponent"}, "33380671": {"name": null, "component": {"__ref": "33380066"}, "uuid": "1bRofLqeya5", "parent": null, "locked": null, "vsettings": [{"__ref": "33380778"}], "__type": "TplComponent"}, "33380672": {"name": null, "component": {"__ref": "33380066"}, "uuid": "tB6Lr4-Yv08", "parent": null, "locked": null, "vsettings": [{"__ref": "33380779"}], "__type": "TplComponent"}, "33380673": {"name": null, "component": {"__ref": "33380066"}, "uuid": "tHZBAScZYH7", "parent": null, "locked": null, "vsettings": [{"__ref": "33380780"}], "__type": "TplComponent"}, "33380674": {"name": null, "component": {"__ref": "33380066"}, "uuid": "bGsGQVegxpU", "parent": null, "locked": null, "vsettings": [{"__ref": "33380781"}], "__type": "TplComponent"}, "33380675": {"name": null, "component": {"__ref": "33380066"}, "uuid": "hMlZLEub5B6", "parent": null, "locked": null, "vsettings": [{"__ref": "33380782"}], "__type": "TplComponent"}, "33380676": {"name": null, "component": {"__ref": "33380066"}, "uuid": "CEjIX1wRtOy", "parent": null, "locked": null, "vsettings": [{"__ref": "33380783"}], "__type": "TplComponent"}, "33380677": {"name": null, "component": {"__ref": "33380066"}, "uuid": "SNxfOWfoJuY", "parent": null, "locked": null, "vsettings": [{"__ref": "33380784"}], "__type": "TplComponent"}, "33380678": {"name": null, "component": {"__ref": "33380066"}, "uuid": "cgYO2HJBl2J", "parent": null, "locked": null, "vsettings": [{"__ref": "33380785"}], "__type": "TplComponent"}, "33380679": {"name": null, "component": {"__ref": "33380066"}, "uuid": "kWK9bDkyIK5", "parent": null, "locked": null, "vsettings": [{"__ref": "33380786"}], "__type": "TplComponent"}, "33380680": {"name": null, "component": {"__ref": "33380066"}, "uuid": "wCSwJQihl6X", "parent": null, "locked": null, "vsettings": [{"__ref": "33380787"}], "__type": "TplComponent"}, "33380681": {"name": null, "component": {"__ref": "33380066"}, "uuid": "mp_R7fpdk9s", "parent": null, "locked": null, "vsettings": [{"__ref": "33380788"}], "__type": "TplComponent"}, "33380682": {"name": null, "component": {"__ref": "33380066"}, "uuid": "vqfhi_PFjlc", "parent": null, "locked": null, "vsettings": [{"__ref": "33380789"}], "__type": "TplComponent"}, "33380683": {"name": null, "component": {"__ref": "33380066"}, "uuid": "ne4NYUYhW8i", "parent": null, "locked": null, "vsettings": [{"__ref": "33380790"}], "__type": "TplComponent"}, "33380684": {"name": null, "component": {"__ref": "33380066"}, "uuid": "quAoee3l00w", "parent": null, "locked": null, "vsettings": [{"__ref": "33380791"}], "__type": "TplComponent"}, "33380685": {"name": null, "component": {"__ref": "33380066"}, "uuid": "mu0Fg2wcKHL", "parent": null, "locked": null, "vsettings": [{"__ref": "33380792"}], "__type": "TplComponent"}, "33380686": {"name": null, "component": {"__ref": "33380066"}, "uuid": "cdJoE-z0qaK", "parent": null, "locked": null, "vsettings": [{"__ref": "33380793"}], "__type": "TplComponent"}, "33380687": {"name": null, "component": {"__ref": "33380066"}, "uuid": "_eDky3SaFb5", "parent": null, "locked": null, "vsettings": [{"__ref": "33380794"}], "__type": "TplComponent"}, "33380688": {"name": null, "component": {"__ref": "33380066"}, "uuid": "vLkUeJ-Jk3v", "parent": null, "locked": null, "vsettings": [{"__ref": "33380795"}], "__type": "TplComponent"}, "33380689": {"name": null, "component": {"__ref": "33380066"}, "uuid": "USj5oTFzrIr", "parent": null, "locked": null, "vsettings": [{"__ref": "33380796"}], "__type": "TplComponent"}, "33380690": {"name": null, "component": {"__ref": "33380066"}, "uuid": "4qoVJvNUrpj", "parent": null, "locked": null, "vsettings": [{"__ref": "33380797"}], "__type": "TplComponent"}, "33380691": {"name": null, "component": {"__ref": "33380066"}, "uuid": "AGtbWV2VUkv", "parent": null, "locked": null, "vsettings": [{"__ref": "33380798"}], "__type": "TplComponent"}, "33380692": {"name": null, "component": {"__ref": "33380066"}, "uuid": "RiBPqiwFzRc", "parent": null, "locked": null, "vsettings": [{"__ref": "33380799"}], "__type": "TplComponent"}, "33380693": {"name": null, "component": {"__ref": "33380066"}, "uuid": "p7W7UEWOLgd", "parent": null, "locked": null, "vsettings": [{"__ref": "33380800"}], "__type": "TplComponent"}, "33380694": {"name": null, "component": {"__ref": "33380066"}, "uuid": "WLPbq2ouE4O", "parent": null, "locked": null, "vsettings": [{"__ref": "33380801"}], "__type": "TplComponent"}, "33380695": {"name": null, "component": {"__ref": "33380066"}, "uuid": "z75_OQ3Jxan", "parent": null, "locked": null, "vsettings": [{"__ref": "33380802"}], "__type": "TplComponent"}, "33380696": {"name": null, "component": {"__ref": "33380066"}, "uuid": "mboQwnOdJQ5", "parent": null, "locked": null, "vsettings": [{"__ref": "33380803"}], "__type": "TplComponent"}, "33380697": {"name": null, "component": {"__ref": "33380066"}, "uuid": "IKhVSa-aN_g", "parent": null, "locked": null, "vsettings": [{"__ref": "33380804"}], "__type": "TplComponent"}, "33380698": {"name": null, "component": {"__ref": "33380066"}, "uuid": "maL270JHOeN", "parent": null, "locked": null, "vsettings": [{"__ref": "33380805"}], "__type": "TplComponent"}, "33380699": {"name": null, "component": {"__ref": "33380066"}, "uuid": "sPBcpkeVSOz", "parent": null, "locked": null, "vsettings": [{"__ref": "33380806"}], "__type": "TplComponent"}, "33380700": {"name": null, "component": {"__ref": "33380066"}, "uuid": "zLp4IWaoK9D", "parent": null, "locked": null, "vsettings": [{"__ref": "33380807"}], "__type": "TplComponent"}, "33380701": {"name": null, "component": {"__ref": "33380066"}, "uuid": "RtYkVOa5wbQ", "parent": null, "locked": null, "vsettings": [{"__ref": "33380808"}], "__type": "TplComponent"}, "33380702": {"name": null, "component": {"__ref": "33380066"}, "uuid": "PZM5-jenng<PERSON>", "parent": null, "locked": null, "vsettings": [{"__ref": "33380809"}], "__type": "TplComponent"}, "33380703": {"name": null, "component": {"__ref": "33380066"}, "uuid": "Xlw7ekZQbcJ", "parent": null, "locked": null, "vsettings": [{"__ref": "33380810"}], "__type": "TplComponent"}, "33380704": {"name": null, "component": {"__ref": "33380066"}, "uuid": "huDpKpYNFQb", "parent": null, "locked": null, "vsettings": [{"__ref": "33380811"}], "__type": "TplComponent"}, "33380705": {"name": null, "component": {"__ref": "33380066"}, "uuid": "IHNauwVDan1", "parent": null, "locked": null, "vsettings": [{"__ref": "33380812"}], "__type": "TplComponent"}, "33380706": {"name": null, "component": {"__ref": "33380066"}, "uuid": "TLKer2dAQv-", "parent": null, "locked": null, "vsettings": [{"__ref": "33380813"}], "__type": "TplComponent"}, "33380707": {"name": null, "component": {"__ref": "33380066"}, "uuid": "cal_SWdJgpc", "parent": null, "locked": null, "vsettings": [{"__ref": "33380814"}], "__type": "TplComponent"}, "33380708": {"name": null, "component": {"__ref": "33380066"}, "uuid": "4y3vBpqPPI_", "parent": null, "locked": null, "vsettings": [{"__ref": "33380815"}], "__type": "TplComponent"}, "33380709": {"name": null, "component": {"__ref": "33380066"}, "uuid": "zxSrOS-iIdY", "parent": null, "locked": null, "vsettings": [{"__ref": "33380816"}], "__type": "TplComponent"}, "33380710": {"name": null, "component": {"__ref": "33380066"}, "uuid": "YJBOk4U4U40", "parent": null, "locked": null, "vsettings": [{"__ref": "33380817"}], "__type": "TplComponent"}, "33380711": {"name": null, "component": {"__ref": "33380066"}, "uuid": "YAxHd7dP14Y", "parent": null, "locked": null, "vsettings": [{"__ref": "33380818"}], "__type": "TplComponent"}, "33380712": {"name": null, "component": {"__ref": "33380066"}, "uuid": "xxavlzzAfN_", "parent": null, "locked": null, "vsettings": [{"__ref": "33380819"}], "__type": "TplComponent"}, "33380713": {"name": null, "component": {"__ref": "33380066"}, "uuid": "myJd5lGWDQ4", "parent": null, "locked": null, "vsettings": [{"__ref": "33380820"}], "__type": "TplComponent"}, "33380714": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"outerHTML": {"__ref": "33380821"}}, "rs": {"__ref": "33380822"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380715": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {}, "rs": {"__ref": "33380823"}, "dataCond": null, "dataRep": null, "text": {"__ref": "33380898"}, "columnsConfig": null, "__type": "VariantSetting"}, "33380716": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {"outerHTML": {"__ref": "33380825"}}, "rs": {"__ref": "33380826"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380717": {"asset": {"__ref": "33380064"}, "__type": "ImageAssetRef"}, "33380718": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "33380731": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380732": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "33380752": {"asset": {"__ref": "33380065"}, "__type": "ImageAssetRef"}, "33380753": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "33380766": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380835"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380767": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380836"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380768": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380837"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380769": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380838"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380770": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380839"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380771": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380840"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380772": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380841"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380773": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380842"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380774": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380843"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380775": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380844"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380776": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380845"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380777": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380846"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380778": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380847"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380779": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380848"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380780": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380849"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380781": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380850"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380782": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380851"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380783": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380852"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380784": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380853"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380785": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380854"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380786": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380855"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380787": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380856"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380788": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380857"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380789": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380858"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380790": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380859"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380791": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380860"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380792": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380861"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380793": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380862"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380794": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380863"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380795": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380864"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380796": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380865"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380797": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380866"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380798": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380867"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380799": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380868"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380800": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380869"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380801": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380870"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380802": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380871"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380803": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380872"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380804": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380873"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380805": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380874"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380806": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380875"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380807": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380876"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380808": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380877"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380809": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380878"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380810": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380879"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380811": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380880"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380812": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380881"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380813": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380882"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380814": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380883"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380815": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380884"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380816": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380885"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380817": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380886"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380818": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380887"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380819": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380888"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380820": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33380889"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380821": {"asset": {"__ref": "33380064"}, "__type": "ImageAssetRef"}, "33380822": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "33380823": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380825": {"asset": {"__ref": "33380065"}, "__type": "ImageAssetRef"}, "33380826": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "33380835": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380836": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380837": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380838": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380839": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380840": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380841": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380842": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380843": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380844": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380845": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380846": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380847": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380848": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380849": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380850": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380851": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380852": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380853": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380854": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380855": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380856": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380857": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380858": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380859": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380860": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380861": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380862": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380863": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380864": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380865": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380866": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380867": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380868": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380869": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380870": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380871": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380872": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380873": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380874": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380875": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380876": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380877": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380878": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380879": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380880": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380881": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380882": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380883": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380884": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380885": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380886": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380887": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380888": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380889": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33380898": {"markers": [], "text": "Add task", "__type": "RawText"}, "33380899": {"tpl": [{"__ref": "33380584"}], "__type": "RenderExpr"}, "33380900": {"interactions": [{"__ref": "33380901"}], "__type": "EventHandler"}, "33380901": {"interactionName": "Set tasks", "actionName": "updateVariable", "args": [{"__ref": "12613061"}, {"__ref": "12613062"}, {"__ref": "12613063"}], "condExpr": null, "conditionalMode": "always", "uuid": "SjrnnmpbY", "parent": {"__ref": "33380900"}, "__type": "Interaction"}, "33380904": {"path": ["$state", "tasks"], "fallback": null, "__type": "ObjectPath"}, "33380908": {"code": "5", "fallback": null, "__type": "CustomCode"}, "33380914": {"name": null, "component": {"__ref": "33380066"}, "uuid": "Y2oFbZZ8fe", "parent": {"__ref": "33380019"}, "locked": null, "vsettings": [{"__ref": "33380916"}], "__type": "TplComponent"}, "33380916": {"variants": [{"__ref": "23230004"}], "args": [{"__ref": "33380921"}, {"__ref": "33380922"}, {"__ref": "33380923"}, {"__ref": "33381007"}, {"__ref": "12613107"}, {"__ref": "4842204"}], "attrs": {"onClick": {"__ref": "12613001"}}, "rs": {"__ref": "33380924"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33380921": {"param": {"__ref": "33380072"}, "expr": {"__ref": "12613131"}, "__type": "Arg"}, "33380922": {"param": {"__ref": "33380069"}, "expr": {"__ref": "33381011"}, "__type": "Arg"}, "33380923": {"param": {"__ref": "33380073"}, "expr": {"__ref": "12613132"}, "__type": "Arg"}, "33380924": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "33380939": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "2gVDmcYACZ", "parent": {"__ref": "33380914"}, "locked": null, "vsettings": [{"__ref": "33380945"}], "__type": "TplTag"}, "33380945": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {}, "rs": {"__ref": "33380955"}, "dataCond": null, "dataRep": null, "text": {"__ref": "33381010"}, "columnsConfig": null, "__type": "VariantSetting"}, "33380955": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381007": {"param": {"__ref": "33380076"}, "expr": {"__ref": "33381009"}, "__type": "Arg"}, "33381009": {"variants": [{"__ref": "33380195"}], "__type": "VariantsRef"}, "33381010": {"markers": [], "text": "X", "__type": "RawText"}, "33381011": {"tpl": [{"__ref": "33380939"}], "__type": "RenderExpr"}, "33381016": {"uuid": "Denj1a89V5n", "name": "Switch", "params": [{"__ref": "33381021"}, {"__ref": "33381022"}, {"__ref": "33381023"}, {"__ref": "33381024"}, {"__ref": "33381025"}, {"__ref": "33381026"}, {"__ref": "33381027"}, {"__ref": "33381028"}, {"__ref": "33381029"}, {"__ref": "12613095"}, {"__ref": "64282048"}, {"__ref": "64282053"}], "states": [{"__ref": "33381030"}, {"__ref": "33381031"}, {"__ref": "33381032"}], "tplTree": {"__ref": "33381033"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "33381034"}, {"__ref": "33381035"}, {"__ref": "33381036"}, {"__ref": "33381037"}, {"__ref": "33381038"}], "variantGroups": [{"__ref": "33381039"}, {"__ref": "33381040"}, {"__ref": "33381041"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "33381042"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "33381017": {"component": {"__ref": "33381016"}, "matrix": {"__ref": "33381043"}, "customMatrix": {"__ref": "33381044"}, "__type": "ComponentArena"}, "33381018": {"name": "Switch", "component": {"__ref": "33381016"}, "uuid": "Ol2YIOHIrhg", "parent": {"__ref": "33380019"}, "locked": null, "vsettings": [{"__ref": "33381045"}], "__type": "TplComponent"}, "33381019": {"type": {"__ref": "33381047"}, "state": {"__ref": "33381020"}, "variable": {"__ref": "33381046"}, "uuid": "LstFowwoUBe", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "variant", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381020": {"param": {"__ref": "33381019"}, "accessType": "private", "variableType": "boolean", "onChangeParam": {"__ref": "64282043"}, "tplNode": {"__ref": "33381018"}, "implicitState": {"__ref": "33381032"}, "__type": "State"}, "33381021": {"type": {"__ref": "33381049"}, "tplSlot": {"__ref": "33381096"}, "variable": {"__ref": "33381048"}, "uuid": "46rvgtZYOj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "33381022": {"type": {"__ref": "33381051"}, "state": {"__ref": "33381030"}, "variable": {"__ref": "33381050"}, "uuid": "cw7toKQLvV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381023": {"type": {"__ref": "33381053"}, "state": {"__ref": "33381031"}, "variable": {"__ref": "33381052"}, "uuid": "AwHXSrUeni", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381024": {"type": {"__ref": "33381055"}, "state": {"__ref": "33381032"}, "variable": {"__ref": "33381054"}, "uuid": "0LxDO1TNH0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381025": {"type": {"__ref": "33381057"}, "variable": {"__ref": "33381056"}, "uuid": "a6IwVd9Gvp", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381026": {"type": {"__ref": "33381059"}, "variable": {"__ref": "33381058"}, "uuid": "bQk2XlsnrW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381027": {"type": {"__ref": "33381061"}, "variable": {"__ref": "33381060"}, "uuid": "so6Jtispid", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381028": {"type": {"__ref": "33381063"}, "variable": {"__ref": "33381062"}, "uuid": "H5OyIctF_K", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381029": {"type": {"__ref": "33778001"}, "variable": {"__ref": "33381064"}, "uuid": "Gmgyln6pLkh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381030": {"variantGroup": {"__ref": "33381039"}, "param": {"__ref": "33381022"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282048"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33381031": {"variantGroup": {"__ref": "33381040"}, "param": {"__ref": "33381023"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282053"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33381032": {"variantGroup": {"__ref": "33381041"}, "param": {"__ref": "33381024"}, "accessType": "writable", "variableType": "text", "onChangeParam": {"__ref": "33381029"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33381033": {"tag": "div", "name": null, "children": [{"__ref": "33381066"}, {"__ref": "33381067"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "pc-r3u3nxv7", "parent": null, "locked": null, "vsettings": [{"__ref": "33381068"}, {"__ref": "33381069"}, {"__ref": "33381070"}, {"__ref": "33381071"}, {"__ref": "33381072"}, {"__ref": "33381073"}, {"__ref": "33381074"}, {"__ref": "33381075"}], "__type": "TplTag"}, "33381034": {"uuid": "oXf20X07GIJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381035": {"uuid": "dXVfU33JeMn", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381036": {"uuid": "__r_t29xvCm", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381037": {"uuid": "d4YyvCOa6LK", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381038": {"uuid": "VpbqskYgWID", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381039": {"type": "component", "param": {"__ref": "33381022"}, "linkedState": {"__ref": "33381030"}, "uuid": "89xaI9QYQhW", "variants": [{"__ref": "33381076"}], "multi": false, "__type": "ComponentVariantGroup"}, "33381040": {"type": "component", "param": {"__ref": "33381023"}, "linkedState": {"__ref": "33381031"}, "uuid": "MwJBeBQuJgc", "variants": [{"__ref": "33381077"}], "multi": false, "__type": "ComponentVariantGroup"}, "33381041": {"type": "component", "param": {"__ref": "33381024"}, "linkedState": {"__ref": "33381032"}, "uuid": "-Lge3FF4atx", "variants": [{"__ref": "33381078"}], "multi": false, "__type": "ComponentVariantGroup"}, "33381042": {"type": "switch", "__type": "PlumeInfo"}, "33381043": {"rows": [{"__ref": "33381079"}, {"__ref": "33381080"}, {"__ref": "33381081"}, {"__ref": "33381082"}], "__type": "ArenaFrameGrid"}, "33381044": {"rows": [{"__ref": "33381083"}], "__type": "ArenaFrameGrid"}, "33381045": {"variants": [{"__ref": "23230004"}], "args": [{"__ref": "33381084"}, {"__ref": "33381305"}, {"__ref": "33381308"}, {"__ref": "12613099"}], "attrs": {}, "rs": {"__ref": "33381085"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381046": {"name": "Switch Is checked", "uuid": "sk8uzfIWom_", "__type": "Var"}, "33381047": {"name": "any", "__type": "AnyType"}, "33381048": {"name": "children", "uuid": "Hxo2A0Oy6", "__type": "Var"}, "33381049": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "33381050": {"name": "No label", "uuid": "jYQkJots68", "__type": "Var"}, "33381051": {"name": "any", "__type": "AnyType"}, "33381052": {"name": "Is disabled", "uuid": "ymxFvPSNXO", "__type": "Var"}, "33381053": {"name": "any", "__type": "AnyType"}, "33381054": {"name": "Is checked", "uuid": "5p1F1uKzuR", "__type": "Var"}, "33381055": {"name": "any", "__type": "AnyType"}, "33381056": {"name": "name", "uuid": "IPv5-rwxn-", "__type": "Var"}, "33381057": {"name": "text", "__type": "Text"}, "33381058": {"name": "value", "uuid": "9Giw1GBE8p", "__type": "Var"}, "33381059": {"name": "text", "__type": "Text"}, "33381060": {"name": "aria-label", "uuid": "lxIFeU-Ivd", "__type": "Var"}, "33381061": {"name": "text", "__type": "Text"}, "33381062": {"name": "aria-<PERSON>by", "uuid": "161HurOEeq", "__type": "Var"}, "33381063": {"name": "text", "__type": "Text"}, "33381064": {"name": "onChange", "uuid": "njOGC9elN0p", "__type": "Var"}, "33381066": {"tag": "div", "name": "toggle", "children": [{"__ref": "33381087"}, {"__ref": "33381088"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "mX2O6wSgvCY", "parent": {"__ref": "33381033"}, "locked": null, "vsettings": [{"__ref": "33381089"}, {"__ref": "33381090"}, {"__ref": "33381091"}, {"__ref": "33381092"}, {"__ref": "33381093"}, {"__ref": "33381094"}, {"__ref": "33381095"}], "__type": "TplTag"}, "33381067": {"tag": "div", "name": "labelContainer", "children": [{"__ref": "33381096"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QQ2PI4dSofV", "parent": {"__ref": "33381033"}, "locked": null, "vsettings": [{"__ref": "33381097"}, {"__ref": "33381098"}, {"__ref": "33381099"}], "__type": "TplTag"}, "33381068": {"variants": [{"__ref": "33381034"}], "args": [], "attrs": {"data-testid": {"__ref": "12613096"}}, "rs": {"__ref": "33381100"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381069": {"variants": [{"__ref": "33381076"}], "args": [], "attrs": {}, "rs": {"__ref": "33381101"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381070": {"variants": [{"__ref": "33381077"}], "args": [], "attrs": {}, "rs": {"__ref": "33381102"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381071": {"variants": [{"__ref": "33381036"}], "args": [], "attrs": {}, "rs": {"__ref": "33381103"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381072": {"variants": [{"__ref": "33381078"}], "args": [], "attrs": {}, "rs": {"__ref": "33381104"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381073": {"variants": [{"__ref": "33381078"}, {"__ref": "33381035"}], "args": [], "attrs": {}, "rs": {"__ref": "33381105"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381074": {"variants": [{"__ref": "33381035"}], "args": [], "attrs": {}, "rs": {"__ref": "33381106"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381075": {"variants": [{"__ref": "33381037"}], "args": [], "attrs": {}, "rs": {"__ref": "33381107"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381076": {"uuid": "BPHeXbpo0A_", "name": "No label", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33381039"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381077": {"uuid": "19EhrUUSpRy", "name": "Is disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33381040"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381078": {"uuid": "Nwpl3dnBsTf", "name": "Is checked", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33381041"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381079": {"cols": [{"__ref": "33381108"}, {"__ref": "33381109"}, {"__ref": "33381110"}, {"__ref": "33381111"}, {"__ref": "33381112"}], "rowKey": null, "__type": "ArenaFrameRow"}, "33381080": {"cols": [{"__ref": "33381113"}], "rowKey": {"__ref": "33381039"}, "__type": "ArenaFrameRow"}, "33381081": {"cols": [{"__ref": "33381114"}], "rowKey": {"__ref": "33381040"}, "__type": "ArenaFrameRow"}, "33381082": {"cols": [{"__ref": "33381115"}], "rowKey": {"__ref": "33381041"}, "__type": "ArenaFrameRow"}, "33381083": {"cols": [{"__ref": "33381116"}], "rowKey": null, "__type": "ArenaFrameRow"}, "33381084": {"param": {"__ref": "33381021"}, "expr": {"__ref": "33381304"}, "__type": "Arg"}, "33381085": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "33381087": {"tag": "div", "name": "track", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "EpZY_sq22nu", "parent": {"__ref": "33381066"}, "locked": null, "vsettings": [{"__ref": "33381121"}, {"__ref": "33381122"}, {"__ref": "33381123"}, {"__ref": "33381124"}, {"__ref": "33381125"}, {"__ref": "33381126"}, {"__ref": "33381127"}, {"__ref": "33381128"}], "__type": "TplTag"}, "33381088": {"tag": "div", "name": "thumb", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "a5Pv7FqUzAU", "parent": {"__ref": "33381066"}, "locked": null, "vsettings": [{"__ref": "33381129"}, {"__ref": "33381130"}, {"__ref": "33381131"}, {"__ref": "33381132"}, {"__ref": "33381133"}], "__type": "TplTag"}, "33381089": {"variants": [{"__ref": "33381034"}], "args": [], "attrs": {}, "rs": {"__ref": "33381134"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381090": {"variants": [{"__ref": "33381077"}], "args": [], "attrs": {}, "rs": {"__ref": "33381135"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381091": {"variants": [{"__ref": "33381078"}], "args": [], "attrs": {}, "rs": {"__ref": "33381136"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381092": {"variants": [{"__ref": "33381035"}], "args": [], "attrs": {}, "rs": {"__ref": "33381137"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381093": {"variants": [{"__ref": "33381036"}], "args": [], "attrs": {}, "rs": {"__ref": "33381138"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381094": {"variants": [{"__ref": "33381037"}], "args": [], "attrs": {}, "rs": {"__ref": "33381139"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381095": {"variants": [{"__ref": "33381078"}, {"__ref": "33381035"}], "args": [], "attrs": {}, "rs": {"__ref": "33381140"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381096": {"param": {"__ref": "33381021"}, "defaultContents": [{"__ref": "33381141"}], "uuid": "dD2PRpqjfw4", "parent": {"__ref": "33381067"}, "locked": null, "vsettings": [{"__ref": "33381142"}, {"__ref": "33381143"}, {"__ref": "33381144"}, {"__ref": "33381145"}], "__type": "TplSlot"}, "33381097": {"variants": [{"__ref": "33381034"}], "args": [], "attrs": {}, "rs": {"__ref": "33381146"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381098": {"variants": [{"__ref": "33381076"}], "args": [], "attrs": {}, "rs": {"__ref": "33381147"}, "dataCond": {"__ref": "33381148"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381099": {"variants": [{"__ref": "33381036"}], "args": [], "attrs": {}, "rs": {"__ref": "33381149"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381100": {"values": {"display": "flex", "flex-direction": "row", "position": "relative", "width": "wrap", "height": "wrap", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "33381101": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381102": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381103": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381104": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381105": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381106": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381107": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381108": {"frame": {"__ref": "33381156"}, "cellKey": {"__ref": "33381034"}, "__type": "ArenaFrameCell"}, "33381109": {"frame": {"__ref": "33381157"}, "cellKey": {"__ref": "33381035"}, "__type": "ArenaFrameCell"}, "33381110": {"frame": {"__ref": "33381158"}, "cellKey": {"__ref": "33381036"}, "__type": "ArenaFrameCell"}, "33381111": {"frame": {"__ref": "33381159"}, "cellKey": {"__ref": "33381037"}, "__type": "ArenaFrameCell"}, "33381112": {"frame": {"__ref": "33381160"}, "cellKey": {"__ref": "33381038"}, "__type": "ArenaFrameCell"}, "33381113": {"frame": {"__ref": "33381161"}, "cellKey": {"__ref": "33381076"}, "__type": "ArenaFrameCell"}, "33381114": {"frame": {"__ref": "33381162"}, "cellKey": {"__ref": "33381077"}, "__type": "ArenaFrameCell"}, "33381115": {"frame": {"__ref": "33381163"}, "cellKey": {"__ref": "33381078"}, "__type": "ArenaFrameCell"}, "33381116": {"frame": {"__ref": "33381164"}, "cellKey": [{"__ref": "33381078"}, {"__ref": "33381035"}], "__type": "ArenaFrameCell"}, "33381121": {"variants": [{"__ref": "33381034"}], "args": [], "attrs": {}, "rs": {"__ref": "33381166"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381122": {"variants": [{"__ref": "33381078"}], "args": [], "attrs": {}, "rs": {"__ref": "33381167"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381123": {"variants": [{"__ref": "33381035"}], "args": [], "attrs": {}, "rs": {"__ref": "33381168"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381124": {"variants": [{"__ref": "33381036"}], "args": [], "attrs": {}, "rs": {"__ref": "33381169"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381125": {"variants": [{"__ref": "33381037"}], "args": [], "attrs": {}, "rs": {"__ref": "33381170"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381126": {"variants": [{"__ref": "33381077"}], "args": [], "attrs": {}, "rs": {"__ref": "33381171"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381127": {"variants": [{"__ref": "33381078"}, {"__ref": "33381035"}], "args": [], "attrs": {}, "rs": {"__ref": "33381172"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381128": {"variants": [{"__ref": "33381038"}], "args": [], "attrs": {}, "rs": {"__ref": "33381173"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381129": {"variants": [{"__ref": "33381034"}], "args": [], "attrs": {}, "rs": {"__ref": "33381174"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381130": {"variants": [{"__ref": "33381078"}], "args": [], "attrs": {}, "rs": {"__ref": "33381175"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381131": {"variants": [{"__ref": "33381035"}], "args": [], "attrs": {}, "rs": {"__ref": "33381176"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381132": {"variants": [{"__ref": "33381036"}], "args": [], "attrs": {}, "rs": {"__ref": "33381177"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381133": {"variants": [{"__ref": "33381037"}], "args": [], "attrs": {}, "rs": {"__ref": "33381178"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381134": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "width": "40px", "height": "32px"}, "mixins": [], "__type": "RuleSet"}, "33381135": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381136": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381137": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381138": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381139": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381140": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381141": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "l7jOfVm7AGA", "parent": {"__ref": "33381096"}, "locked": null, "vsettings": [{"__ref": "33381186"}], "__type": "TplTag"}, "33381142": {"variants": [{"__ref": "33381034"}], "args": [], "attrs": {}, "rs": {"__ref": "33381187"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381143": {"variants": [{"__ref": "33381076"}], "args": [], "attrs": {}, "rs": {"__ref": "33381188"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381144": {"variants": [{"__ref": "33381036"}], "args": [], "attrs": {}, "rs": {"__ref": "33381189"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381145": {"variants": [{"__ref": "33381078"}], "args": [], "attrs": {}, "rs": {"__ref": "33381190"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381146": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "margin-left": "8px"}, "mixins": [], "__type": "RuleSet"}, "33381147": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381148": {"code": "false", "fallback": null, "__type": "CustomCode"}, "33381149": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381156": {"uuid": "9e8ELQIgGLQ", "width": 340, "height": 340, "container": {"__ref": "33381197"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381034"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381157": {"uuid": "d9THfFa7ccA", "width": 340, "height": 340, "container": {"__ref": "33381198"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381035"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381158": {"uuid": "r8soQCzswZ7", "width": 340, "height": 340, "container": {"__ref": "33381199"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381036"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381159": {"uuid": "f2j82Usxx09", "width": 340, "height": 340, "container": {"__ref": "33381200"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381037"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381160": {"uuid": "uuBeySOIP-x", "width": 340, "height": 340, "container": {"__ref": "33381201"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381038"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381161": {"uuid": "wcBkb6nhEQq", "width": 340, "height": 340, "container": {"__ref": "33381202"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381076"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381162": {"uuid": "xdzuwPxNWWU", "width": 340, "height": 340, "container": {"__ref": "33381203"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381077"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381163": {"uuid": "6Yd7lSKIlrD", "width": 340, "height": 340, "container": {"__ref": "33381204"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381078"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381164": {"uuid": "00xjjjVk8S8", "width": 340, "height": 340, "container": {"__ref": "33381205"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381078"}, {"__ref": "33381035"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381165": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Cy_W11Zru2t", "parent": {"__ref": "33381018"}, "locked": null, "vsettings": [{"__ref": "33381206"}], "__type": "TplTag"}, "33381166": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "width": "stretch", "height": "24px", "border-top-left-radius": "12px", "border-top-right-radius": "12px", "border-bottom-right-radius": "12px", "border-bottom-left-radius": "12px", "transition-property": "background", "transition-duration": "0.2s", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-color": "#FFFFFF00", "border-top-style": "solid", "border-right-color": "#FFFFFF00", "border-right-style": "solid", "border-bottom-color": "#FFFFFF00", "border-bottom-style": "solid", "border-left-color": "#FFFFFF00", "border-left-style": "solid", "background": "linear-gradient(#F3F3F2, #F3F3F2)"}, "mixins": [], "__type": "RuleSet"}, "33381167": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "33381168": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "33381169": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "33381170": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "33381171": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381172": {"values": {"background": "linear-gradient(#0081F1, #0081F1)"}, "mixins": [], "__type": "RuleSet"}, "33381173": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "33381174": {"values": {"display": "block", "position": "absolute", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "4px", "top": "8px", "width": "16px", "height": "16px", "border-top-left-radius": "100%", "border-top-right-radius": "100%", "border-bottom-right-radius": "100%", "border-bottom-left-radius": "100%", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-color": "#DBDBD7", "border-top-style": "solid", "border-right-color": "#DBDBD7", "border-right-style": "solid", "border-bottom-color": "#DBDBD7", "border-bottom-style": "solid", "border-left-color": "#DBDBD7", "border-left-style": "solid", "transition-property": "all", "transition-duration": "0.2s", "background": "linear-gradient(#ffffff, #ffffff)"}, "mixins": [], "__type": "RuleSet"}, "33381175": {"values": {"left": "20px", "right": "auto", "border-top-style": "none", "border-right-style": "none", "border-bottom-style": "none", "border-left-style": "none"}, "mixins": [], "__type": "RuleSet"}, "33381176": {"values": {"background": "linear-gradient(#F9F9F9, #F9F9F9)", "border-top-color": "#C8C7C1", "border-right-color": "#C8C7C1", "border-bottom-color": "#C8C7C1", "border-left-color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "33381177": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381178": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381186": {"variants": [{"__ref": "33381034"}], "args": [], "attrs": {}, "rs": {"__ref": "33381278"}, "dataCond": null, "dataRep": null, "text": {"__ref": "33381279"}, "columnsConfig": null, "__type": "VariantSetting"}, "33381187": {"values": {"white-space": "nowrap", "text-overflow": "ellipsis", "overflow": "hidden"}, "mixins": [], "__type": "RuleSet"}, "33381188": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381189": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381190": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381197": {"name": null, "component": {"__ref": "33381016"}, "uuid": "4Ne3kzqFRHT", "parent": null, "locked": null, "vsettings": [{"__ref": "33381283"}], "__type": "TplComponent"}, "33381198": {"name": null, "component": {"__ref": "33381016"}, "uuid": "KZKFipp9pFs", "parent": null, "locked": null, "vsettings": [{"__ref": "33381284"}], "__type": "TplComponent"}, "33381199": {"name": null, "component": {"__ref": "33381016"}, "uuid": "-sUNQQDYjld", "parent": null, "locked": null, "vsettings": [{"__ref": "33381285"}], "__type": "TplComponent"}, "33381200": {"name": null, "component": {"__ref": "33381016"}, "uuid": "ARX4I4eqpGN", "parent": null, "locked": null, "vsettings": [{"__ref": "33381286"}], "__type": "TplComponent"}, "33381201": {"name": null, "component": {"__ref": "33381016"}, "uuid": "A_AVHbn_MtH", "parent": null, "locked": null, "vsettings": [{"__ref": "33381287"}], "__type": "TplComponent"}, "33381202": {"name": null, "component": {"__ref": "33381016"}, "uuid": "E6GOD_AIpom", "parent": null, "locked": null, "vsettings": [{"__ref": "33381288"}], "__type": "TplComponent"}, "33381203": {"name": null, "component": {"__ref": "33381016"}, "uuid": "7GEVfunBz3j", "parent": null, "locked": null, "vsettings": [{"__ref": "33381289"}], "__type": "TplComponent"}, "33381204": {"name": null, "component": {"__ref": "33381016"}, "uuid": "kEBiOotjq6B", "parent": null, "locked": null, "vsettings": [{"__ref": "33381290"}], "__type": "TplComponent"}, "33381205": {"name": null, "component": {"__ref": "33381016"}, "uuid": "Ms3IxH_Hrfz", "parent": null, "locked": null, "vsettings": [{"__ref": "33381291"}], "__type": "TplComponent"}, "33381206": {"variants": [{"__ref": "23230004"}], "args": [], "attrs": {}, "rs": {"__ref": "33381292"}, "dataCond": null, "dataRep": null, "text": {"__ref": "33381303"}, "columnsConfig": null, "__type": "VariantSetting"}, "33381278": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381279": {"markers": [], "text": "Switch me", "__type": "RawText"}, "33381283": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381294"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381284": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381295"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381285": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381296"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381286": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381297"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381287": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381298"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381288": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381299"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381289": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381300"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381290": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381301"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381291": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381302"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381292": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381294": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381295": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381296": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381297": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381298": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381299": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381300": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381301": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381302": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381303": {"markers": [], "text": "Done", "__type": "RawText"}, "33381304": {"tpl": [{"__ref": "33381165"}], "__type": "RenderExpr"}, "33381305": {"param": {"__ref": "33381024"}, "expr": {"__ref": "33381307"}, "__type": "Arg"}, "33381307": {"path": ["currentItem", "done"], "fallback": null, "__type": "ObjectPath"}, "33381308": {"param": {"__ref": "33381029"}, "expr": {"__ref": "33381309"}, "__type": "Arg"}, "33381309": {"interactions": [{"__ref": "33381310"}], "__type": "EventHandler"}, "33381310": {"interactionName": "Set currentItem ▸ done", "actionName": "updateVariable", "args": [{"__ref": "33381318"}, {"__ref": "33381319"}], "condExpr": null, "conditionalMode": "always", "uuid": "hSELrIabP", "parent": {"__ref": "33381309"}, "__type": "Interaction"}, "33381317": {"path": ["currentItem", "done"], "fallback": null, "__type": "ObjectPath"}, "33381318": {"name": "variable", "expr": {"__ref": "33381317"}, "__type": "NameArg"}, "33381319": {"name": "operation", "expr": {"__ref": "33381320"}, "__type": "NameArg"}, "33381320": {"code": "4", "fallback": null, "__type": "CustomCode"}, "33381350": {"uuid": "khThQNmMTZB", "name": "search.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iPgogIDxwYXRoIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS41IiBkPSJNMTkuMjUgMTkuMjVMMTUuNSAxNS41TTQuNzUgMTFhNi4yNSA2LjI1IDAgMTExMi41IDAgNi4yNSA2LjI1IDAgMDEtMTIuNSAweiIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": null, "__type": "ImageAsset"}, "33381351": {"uuid": "1nGo_qaPSzF", "name": "TextInput", "params": [{"__ref": "33381356"}, {"__ref": "33381357"}, {"__ref": "33381358"}, {"__ref": "33381359"}, {"__ref": "33381360"}, {"__ref": "33381361"}, {"__ref": "33381362"}, {"__ref": "33381363"}, {"__ref": "33381364"}, {"__ref": "33381365"}, {"__ref": "33381366"}, {"__ref": "33381367"}, {"__ref": "33381368"}, {"__ref": "12613177"}, {"__ref": "64282063"}, {"__ref": "64282068"}, {"__ref": "64282073"}, {"__ref": "64282078"}], "states": [{"__ref": "33381369"}, {"__ref": "33381370"}, {"__ref": "33381371"}, {"__ref": "33381372"}, {"__ref": "33381373"}], "tplTree": {"__ref": "33381374"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "33381375"}, {"__ref": "33381376"}, {"__ref": "33381377"}, {"__ref": "33381378"}, {"__ref": "33381379"}, {"__ref": "33381380"}], "variantGroups": [{"__ref": "33381381"}, {"__ref": "33381382"}, {"__ref": "33381383"}, {"__ref": "33381384"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "33381385"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "33381352": {"component": {"__ref": "33381351"}, "matrix": {"__ref": "33381386"}, "customMatrix": {"__ref": "33381387"}, "__type": "ComponentArena"}, "33381353": {"name": "TextInput", "component": {"__ref": "33381351"}, "uuid": "4htis6-qUIX", "parent": {"__ref": "33380019"}, "locked": null, "vsettings": [{"__ref": "33381388"}], "__type": "TplComponent"}, "33381354": {"type": {"__ref": "33381390"}, "state": {"__ref": "33381355"}, "variable": {"__ref": "33381389"}, "uuid": "81ivUBrkuD8", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381355": {"param": {"__ref": "33381354"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "64282058"}, "tplNode": {"__ref": "33381353"}, "implicitState": {"__ref": "33381373"}, "__type": "State"}, "33381356": {"type": {"__ref": "33381392"}, "variable": {"__ref": "33381391"}, "uuid": "CLUplZtfEn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "33381393"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381357": {"type": {"__ref": "33381395"}, "tplSlot": {"__ref": "33381462"}, "variable": {"__ref": "33381394"}, "uuid": "oNIEyuKeVH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "33381358": {"type": {"__ref": "33381397"}, "tplSlot": {"__ref": "33381443"}, "variable": {"__ref": "33381396"}, "uuid": "JgEeirPVj5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "33381359": {"type": {"__ref": "33381399"}, "state": {"__ref": "33381369"}, "variable": {"__ref": "33381398"}, "uuid": "VN4YlsyKqS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381360": {"type": {"__ref": "33381401"}, "state": {"__ref": "33381370"}, "variable": {"__ref": "33381400"}, "uuid": "HUYd13vfPl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381361": {"type": {"__ref": "33381403"}, "state": {"__ref": "33381371"}, "variable": {"__ref": "33381402"}, "uuid": "blGkiPq7je", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381362": {"type": {"__ref": "33381405"}, "state": {"__ref": "33381372"}, "variable": {"__ref": "33381404"}, "uuid": "_jLMCxHcAm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381363": {"type": {"__ref": "33778002"}, "state": {"__ref": "33381373"}, "variable": {"__ref": "33381406"}, "uuid": "CyoqTXdmEw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381364": {"type": {"__ref": "33381409"}, "variable": {"__ref": "33381408"}, "uuid": "5yUgAyayAsC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381365": {"type": {"__ref": "33381411"}, "variable": {"__ref": "33381410"}, "uuid": "R1n8Lwqsqe1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381366": {"type": {"__ref": "33381413"}, "variable": {"__ref": "33381412"}, "uuid": "z7txPUKK0GZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381367": {"type": {"__ref": "33381415"}, "variable": {"__ref": "33381414"}, "uuid": "-Ydmw51D_5G", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381368": {"type": {"__ref": "33778003"}, "variable": {"__ref": "33381416"}, "uuid": "0RvEIpqYQrm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "33381369": {"variantGroup": {"__ref": "33381381"}, "param": {"__ref": "33381359"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282063"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33381370": {"variantGroup": {"__ref": "33381382"}, "param": {"__ref": "33381360"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282068"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33381371": {"variantGroup": {"__ref": "33381383"}, "param": {"__ref": "33381361"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282073"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33381372": {"variantGroup": {"__ref": "33381384"}, "param": {"__ref": "33381362"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "64282078"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "33381373": {"param": {"__ref": "33381363"}, "accessType": "writable", "variableType": "text", "onChangeParam": {"__ref": "33381368"}, "tplNode": null, "implicitState": null, "__type": "State"}, "33381374": {"tag": "div", "name": null, "children": [{"__ref": "33381418"}, {"__ref": "33381419"}, {"__ref": "33381420"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "jEsTWYAngeT", "parent": null, "locked": null, "vsettings": [{"__ref": "33381421"}, {"__ref": "33381422"}, {"__ref": "33381423"}, {"__ref": "33381424"}, {"__ref": "33381425"}, {"__ref": "33381426"}, {"__ref": "33381427"}], "__type": "TplTag"}, "33381375": {"uuid": "iMIvY6DzWWF", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381376": {"uuid": "sKY-QNq7S60", "name": "", "selectors": [":focus"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "33381419"}, "__type": "<PERSON><PERSON><PERSON>"}, "33381377": {"uuid": "3ffYQydg3VU", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381378": {"uuid": "-3bI-lJ59mq", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381379": {"uuid": "Twj9vna-rWM", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381380": {"uuid": "_K6a3oEdGV5", "name": "", "selectors": ["::placeholder"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "33381419"}, "__type": "<PERSON><PERSON><PERSON>"}, "33381381": {"type": "component", "param": {"__ref": "33381359"}, "linkedState": {"__ref": "33381369"}, "uuid": "vqliEAm5AuC", "variants": [{"__ref": "33381428"}], "multi": false, "__type": "ComponentVariantGroup"}, "33381382": {"type": "component", "param": {"__ref": "33381360"}, "linkedState": {"__ref": "33381370"}, "uuid": "K5e3xEuksQA", "variants": [{"__ref": "33381429"}], "multi": false, "__type": "ComponentVariantGroup"}, "33381383": {"type": "component", "param": {"__ref": "33381361"}, "linkedState": {"__ref": "33381371"}, "uuid": "zFbsgrDgKf6", "variants": [{"__ref": "33381430"}], "multi": false, "__type": "ComponentVariantGroup"}, "33381384": {"type": "component", "param": {"__ref": "33381362"}, "linkedState": {"__ref": "33381372"}, "uuid": "H_1j8P-FruO", "variants": [{"__ref": "33381431"}], "multi": false, "__type": "ComponentVariantGroup"}, "33381385": {"type": "text-input", "__type": "PlumeInfo"}, "33381386": {"rows": [{"__ref": "33381432"}, {"__ref": "33381433"}, {"__ref": "33381434"}, {"__ref": "33381435"}, {"__ref": "33381436"}], "__type": "ArenaFrameGrid"}, "33381387": {"rows": [{"__ref": "33381437"}], "__type": "ArenaFrameGrid"}, "33381388": {"variants": [{"__ref": "23230004"}], "args": [{"__ref": "33381438"}, {"__ref": "33381439"}, {"__ref": "33381440"}, {"__ref": "33381711"}, {"__ref": "12613181"}], "attrs": {"onBlur": {"__ref": "33381725"}, "onKeyDown": {"__ref": "33381750"}}, "rs": {"__ref": "33381441"}, "dataCond": {"__ref": "64282001"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381389": {"name": "TextInput value", "uuid": "-eQ05YQqDHW", "__type": "Var"}, "33381390": {"name": "text", "__type": "Text"}, "33381391": {"name": "placeholder", "uuid": "scuQbp0SB", "__type": "Var"}, "33381392": {"name": "text", "__type": "Text"}, "33381393": {"code": "\"Enter something…\"", "fallback": null, "__type": "CustomCode"}, "33381394": {"name": "end icon", "uuid": "GEiT7jqwF3", "__type": "Var"}, "33381395": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "33381396": {"name": "start icon", "uuid": "mu9FAZ8hWx", "__type": "Var"}, "33381397": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "33381398": {"name": "Show Start Icon", "uuid": "TxX2CbXxE_", "__type": "Var"}, "33381399": {"name": "any", "__type": "AnyType"}, "33381400": {"name": "Show End Icon", "uuid": "USdgq6GZMk", "__type": "Var"}, "33381401": {"name": "any", "__type": "AnyType"}, "33381402": {"name": "Is Disabled", "uuid": "krLto31Poj", "__type": "Var"}, "33381403": {"name": "any", "__type": "AnyType"}, "33381404": {"name": "Color", "uuid": "zQ2nhoRp0m", "__type": "Var"}, "33381405": {"name": "any", "__type": "AnyType"}, "33381406": {"name": "value", "uuid": "7gyfFDB72_", "__type": "Var"}, "33381408": {"name": "name", "uuid": "jgFQRyG3Ow5", "__type": "Var"}, "33381409": {"name": "text", "__type": "Text"}, "33381410": {"name": "required", "uuid": "L_-8FYYCYEs", "__type": "Var"}, "33381411": {"name": "bool", "__type": "BoolType"}, "33381412": {"name": "aria-label", "uuid": "kK2qhxC0HJI", "__type": "Var"}, "33381413": {"name": "text", "__type": "Text"}, "33381414": {"name": "aria-<PERSON>by", "uuid": "d77_Pq7C1VN", "__type": "Var"}, "33381415": {"name": "text", "__type": "Text"}, "33381416": {"name": "onChange", "uuid": "pZCn43ygVw-", "__type": "Var"}, "33381418": {"tag": "div", "name": "start icon container", "children": [{"__ref": "33381443"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "817JKzPofl8", "parent": {"__ref": "33381374"}, "locked": null, "vsettings": [{"__ref": "33381444"}, {"__ref": "33381445"}, {"__ref": "33381446"}, {"__ref": "33381447"}, {"__ref": "33381448"}, {"__ref": "33381449"}], "__type": "TplTag"}, "33381419": {"tag": "input", "name": "input", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "JqCBuiOHk8L", "parent": {"__ref": "33381374"}, "locked": null, "vsettings": [{"__ref": "33381450"}, {"__ref": "33381451"}, {"__ref": "33381452"}, {"__ref": "33381453"}, {"__ref": "33381454"}, {"__ref": "33381455"}, {"__ref": "33381456"}, {"__ref": "33381457"}, {"__ref": "33381458"}, {"__ref": "33381459"}, {"__ref": "33381460"}, {"__ref": "33381461"}], "__type": "TplTag"}, "33381420": {"tag": "div", "name": "end icon container", "children": [{"__ref": "33381462"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "-mMDpiT1FeK", "parent": {"__ref": "33381374"}, "locked": null, "vsettings": [{"__ref": "33381463"}, {"__ref": "33381464"}, {"__ref": "33381465"}], "__type": "TplTag"}, "33381421": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {}, "rs": {"__ref": "33381466"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381422": {"variants": [{"__ref": "33381377"}], "args": [], "attrs": {}, "rs": {"__ref": "33381467"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381423": {"variants": [{"__ref": "33381378"}], "args": [], "attrs": {}, "rs": {"__ref": "33381468"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381424": {"variants": [{"__ref": "33381379"}], "args": [], "attrs": {}, "rs": {"__ref": "33381469"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381425": {"variants": [{"__ref": "33381430"}], "args": [], "attrs": {}, "rs": {"__ref": "33381470"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381426": {"variants": [{"__ref": "33381428"}], "args": [], "attrs": {}, "rs": {"__ref": "33381471"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381427": {"variants": [{"__ref": "33381431"}], "args": [], "attrs": {}, "rs": {"__ref": "33381472"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381428": {"uuid": "kXbPits3866", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33381381"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381429": {"uuid": "veRaGJk82M3", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33381382"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381430": {"uuid": "Pv2kyCwrvUK", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33381383"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381431": {"uuid": "5Sr1mOYWPW6", "name": "Dark", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "33381384"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "33381432": {"cols": [{"__ref": "33381473"}, {"__ref": "33381474"}, {"__ref": "33381475"}, {"__ref": "33381476"}], "rowKey": null, "__type": "ArenaFrameRow"}, "33381433": {"cols": [{"__ref": "33381477"}], "rowKey": {"__ref": "33381381"}, "__type": "ArenaFrameRow"}, "33381434": {"cols": [{"__ref": "33381478"}], "rowKey": {"__ref": "33381382"}, "__type": "ArenaFrameRow"}, "33381435": {"cols": [{"__ref": "33381479"}], "rowKey": {"__ref": "33381383"}, "__type": "ArenaFrameRow"}, "33381436": {"cols": [{"__ref": "33381480"}], "rowKey": {"__ref": "33381384"}, "__type": "ArenaFrameRow"}, "33381437": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "33381438": {"param": {"__ref": "33381363"}, "expr": {"__ref": "33381748"}, "__type": "Arg"}, "33381439": {"param": {"__ref": "33381358"}, "expr": {"__ref": "12613129"}, "__type": "Arg"}, "33381440": {"param": {"__ref": "33381357"}, "expr": {"__ref": "12613130"}, "__type": "Arg"}, "33381441": {"values": {"max-width": "100%", "position": "relative", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "33381443": {"param": {"__ref": "33381358"}, "defaultContents": [{"__ref": "33381487"}], "uuid": "PJtvcRC7ZNg", "parent": {"__ref": "33381418"}, "locked": null, "vsettings": [{"__ref": "33381488"}, {"__ref": "33381489"}, {"__ref": "33381490"}], "__type": "TplSlot"}, "33381444": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {}, "rs": {"__ref": "33381491"}, "dataCond": {"__ref": "33381492"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381445": {"variants": [{"__ref": "33381379"}], "args": [], "attrs": {}, "rs": {"__ref": "33381493"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381446": {"variants": [{"__ref": "33381428"}], "args": [], "attrs": {}, "rs": {"__ref": "33381494"}, "dataCond": {"__ref": "33381495"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381447": {"variants": [{"__ref": "33381377"}], "args": [], "attrs": {}, "rs": {"__ref": "33381496"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381448": {"variants": [{"__ref": "33381430"}], "args": [], "attrs": {}, "rs": {"__ref": "33381497"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381449": {"variants": [{"__ref": "33381431"}], "args": [], "attrs": {}, "rs": {"__ref": "33381498"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381450": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {"type": {"__ref": "33381499"}, "placeholder": {"__ref": "33381500"}, "value": {"__ref": "33381501"}, "name": {"__ref": "33381502"}, "aria-label": {"__ref": "33381503"}, "aria-labelledby": {"__ref": "33381504"}, "required": {"__ref": "33381505"}, "data-testid": {"__ref": "12613186"}}, "rs": {"__ref": "33381506"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381451": {"variants": [{"__ref": "33381376"}], "args": [], "attrs": {}, "rs": {"__ref": "33381507"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381452": {"variants": [{"__ref": "33381430"}], "args": [], "attrs": {"disabled": {"__ref": "33381508"}}, "rs": {"__ref": "33381509"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381453": {"variants": [{"__ref": "33381380"}], "args": [], "attrs": {}, "rs": {"__ref": "33381510"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381454": {"variants": [{"__ref": "33381428"}], "args": [], "attrs": {}, "rs": {"__ref": "33381511"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381455": {"variants": [{"__ref": "33381377"}], "args": [], "attrs": {}, "rs": {"__ref": "33381512"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381456": {"variants": [{"__ref": "33381379"}], "args": [], "attrs": {}, "rs": {"__ref": "33381513"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381457": {"variants": [{"__ref": "33381378"}], "args": [], "attrs": {}, "rs": {"__ref": "33381514"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381458": {"variants": [{"__ref": "33381378"}, {"__ref": "33381376"}], "args": [], "attrs": {}, "rs": {"__ref": "33381515"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381459": {"variants": [{"__ref": "33381378"}, {"__ref": "33381380"}], "args": [], "attrs": {}, "rs": {"__ref": "33381516"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381460": {"variants": [{"__ref": "33381431"}], "args": [], "attrs": {}, "rs": {"__ref": "33381517"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381461": {"variants": [{"__ref": "33381431"}, {"__ref": "33381380"}], "args": [], "attrs": {}, "rs": {"__ref": "33381518"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381462": {"param": {"__ref": "33381357"}, "defaultContents": [{"__ref": "33381519"}], "uuid": "wBhFllchWCs", "parent": {"__ref": "33381420"}, "locked": null, "vsettings": [{"__ref": "33381520"}, {"__ref": "33381521"}, {"__ref": "33381522"}], "__type": "TplSlot"}, "33381463": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {}, "rs": {"__ref": "33381523"}, "dataCond": {"__ref": "33381524"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381464": {"variants": [{"__ref": "33381429"}], "args": [], "attrs": {}, "rs": {"__ref": "33381525"}, "dataCond": {"__ref": "33381526"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381465": {"variants": [{"__ref": "33381431"}], "args": [], "attrs": {}, "rs": {"__ref": "33381527"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381466": {"values": {"display": "flex", "flex-direction": "row", "width": "stretch", "height": "wrap", "align-items": "center", "justify-content": "flex-start", "border-top-color": "#DBDBD7", "border-right-color": "#DBDBD7", "border-bottom-color": "#DBDBD7", "border-left-color": "#DBDBD7", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "background": "linear-gradient(#FFFFFF, #FFFFFF)", "position": "sticky", "padding-top": "7px", "padding-right": "11px", "padding-bottom": "7px", "padding-left": "11px"}, "mixins": [], "__type": "RuleSet"}, "33381467": {"values": {"border-top-color": "#C8C7C1", "border-right-color": "#C8C7C1", "border-bottom-color": "#C8C7C1", "border-left-color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "33381468": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "33381469": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "33381470": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381471": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381472": {"values": {"background": "linear-gradient(#232320, #232320)", "border-top-color": "#717069", "border-right-color": "#717069", "border-bottom-color": "#717069", "border-left-color": "#717069"}, "mixins": [], "__type": "RuleSet"}, "33381473": {"frame": {"__ref": "33381575"}, "cellKey": {"__ref": "33381375"}, "__type": "ArenaFrameCell"}, "33381474": {"frame": {"__ref": "33381576"}, "cellKey": {"__ref": "33381377"}, "__type": "ArenaFrameCell"}, "33381475": {"frame": {"__ref": "33381577"}, "cellKey": {"__ref": "33381378"}, "__type": "ArenaFrameCell"}, "33381476": {"frame": {"__ref": "33381578"}, "cellKey": {"__ref": "33381379"}, "__type": "ArenaFrameCell"}, "33381477": {"frame": {"__ref": "33381579"}, "cellKey": {"__ref": "33381428"}, "__type": "ArenaFrameCell"}, "33381478": {"frame": {"__ref": "33381580"}, "cellKey": {"__ref": "33381429"}, "__type": "ArenaFrameCell"}, "33381479": {"frame": {"__ref": "33381581"}, "cellKey": {"__ref": "33381430"}, "__type": "ArenaFrameCell"}, "33381480": {"frame": {"__ref": "33381582"}, "cellKey": {"__ref": "33381431"}, "__type": "ArenaFrameCell"}, "33381487": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Sohk2g1ABm_", "parent": {"__ref": "33381443"}, "locked": null, "vsettings": [{"__ref": "33381585"}], "__type": "TplTag"}, "33381488": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {}, "rs": {"__ref": "33381586"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381489": {"variants": [{"__ref": "33381428"}], "args": [], "attrs": {}, "rs": {"__ref": "33381587"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381490": {"variants": [{"__ref": "33381431"}], "args": [], "attrs": {}, "rs": {"__ref": "33381588"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381491": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-right": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "33381492": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33381493": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381494": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "33381495": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33381496": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381497": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381498": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381499": {"code": "\"text\"", "fallback": null, "__type": "CustomCode"}, "33381500": {"variable": {"__ref": "33381391"}, "__type": "VarRef"}, "33381501": {"variable": {"__ref": "33381406"}, "__type": "VarRef"}, "33381502": {"variable": {"__ref": "33381408"}, "__type": "VarRef"}, "33381503": {"variable": {"__ref": "33381412"}, "__type": "VarRef"}, "33381504": {"variable": {"__ref": "33381414"}, "__type": "VarRef"}, "33381505": {"variable": {"__ref": "33381410"}, "__type": "VarRef"}, "33381506": {"values": {"width": "stretch", "left": "auto", "top": "auto", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "33381507": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381508": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33381509": {"values": {"cursor": "not-allowed"}, "mixins": [], "__type": "RuleSet"}, "33381510": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "33381511": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381512": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381513": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381514": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381515": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381516": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381517": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "33381518": {"values": {"color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "33381519": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Ic8Iqz17Yss", "parent": {"__ref": "33381462"}, "locked": null, "vsettings": [{"__ref": "33381615"}], "__type": "TplTag"}, "33381520": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {}, "rs": {"__ref": "33381616"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381521": {"variants": [{"__ref": "33381429"}], "args": [], "attrs": {}, "rs": {"__ref": "33381617"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381522": {"variants": [{"__ref": "33381431"}], "args": [], "attrs": {}, "rs": {"__ref": "33381618"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381523": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-left": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "33381524": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33381525": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "33381526": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33381527": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381575": {"uuid": "AH5fzk8To8M", "width": 340, "height": 340, "container": {"__ref": "33381629"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381375"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381576": {"uuid": "aYzymWIZEcy", "width": 340, "height": 340, "container": {"__ref": "33381630"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381377"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381577": {"uuid": "URoZyxXAydv", "width": 340, "height": 340, "container": {"__ref": "33381631"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381378"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381578": {"uuid": "G22Uv0M0mT1", "width": 340, "height": 340, "container": {"__ref": "33381632"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381379"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381579": {"uuid": "pIBQLSV84Vx", "width": 340, "height": 340, "container": {"__ref": "33381633"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381428"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381580": {"uuid": "I6nxFgJM8A4", "width": 340, "height": 340, "container": {"__ref": "33381634"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381429"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381581": {"uuid": "hyPBvxrQUOA", "width": 340, "height": 340, "container": {"__ref": "33381635"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381430"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381582": {"uuid": "7Tyxikjs91c", "width": 340, "height": 340, "container": {"__ref": "33381636"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "33381431"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "33381585": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {"outerHTML": {"__ref": "33381639"}}, "rs": {"__ref": "33381640"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381586": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381587": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "33381588": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "33381615": {"variants": [{"__ref": "33381375"}], "args": [], "attrs": {"outerHTML": {"__ref": "33381643"}}, "rs": {"__ref": "33381644"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381616": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381617": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "33381618": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "33381629": {"name": null, "component": {"__ref": "33381351"}, "uuid": "okJh9CbehV2", "parent": null, "locked": null, "vsettings": [{"__ref": "33381647"}], "__type": "TplComponent"}, "33381630": {"name": null, "component": {"__ref": "33381351"}, "uuid": "4HxDDQInEpX", "parent": null, "locked": null, "vsettings": [{"__ref": "33381648"}], "__type": "TplComponent"}, "33381631": {"name": null, "component": {"__ref": "33381351"}, "uuid": "b8CTWNHonbl", "parent": null, "locked": null, "vsettings": [{"__ref": "33381649"}], "__type": "TplComponent"}, "33381632": {"name": null, "component": {"__ref": "33381351"}, "uuid": "UQA_SLHF8Nc", "parent": null, "locked": null, "vsettings": [{"__ref": "33381650"}], "__type": "TplComponent"}, "33381633": {"name": null, "component": {"__ref": "33381351"}, "uuid": "v12AVetebxi", "parent": null, "locked": null, "vsettings": [{"__ref": "33381651"}], "__type": "TplComponent"}, "33381634": {"name": null, "component": {"__ref": "33381351"}, "uuid": "igvs7UsF4Ui", "parent": null, "locked": null, "vsettings": [{"__ref": "33381652"}], "__type": "TplComponent"}, "33381635": {"name": null, "component": {"__ref": "33381351"}, "uuid": "XykYlCCh-v2", "parent": null, "locked": null, "vsettings": [{"__ref": "33381653"}], "__type": "TplComponent"}, "33381636": {"name": null, "component": {"__ref": "33381351"}, "uuid": "2FD7nHFsT6P", "parent": null, "locked": null, "vsettings": [{"__ref": "33381654"}], "__type": "TplComponent"}, "33381639": {"asset": {"__ref": "33381350"}, "__type": "ImageAssetRef"}, "33381640": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "33381643": {"asset": {"__ref": "33380064"}, "__type": "ImageAssetRef"}, "33381644": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "33381647": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381667"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381648": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381668"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381649": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381669"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381650": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381670"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381651": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381671"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381652": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381672"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381653": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381673"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381654": {"variants": [{"__ref": "17636007"}], "args": [], "attrs": {}, "rs": {"__ref": "33381674"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "33381667": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381668": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381669": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381670": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381671": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381672": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381673": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381674": {"values": {}, "mixins": [], "__type": "RuleSet"}, "33381685": {"param": {"__ref": "33381686"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "64282083"}, "tplNode": null, "implicitState": null, "__type": "State"}, "33381686": {"type": {"__ref": "33381689"}, "state": {"__ref": "33381685"}, "variable": {"__ref": "33381687"}, "uuid": "AcuZ_OBsnD", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "33381687": {"name": "editingIndex", "uuid": "Fp7Da--w9", "__type": "Var"}, "33381689": {"name": "num", "__type": "<PERSON><PERSON>"}, "33381692": {"code": "($state.editingIndex !== currentIndex)", "fallback": {"__ref": "33381693"}, "__type": "CustomCode"}, "33381693": {"code": "true", "fallback": null, "__type": "CustomCode"}, "33381698": {"interactions": [{"__ref": "33381699"}], "__type": "EventHandler"}, "33381699": {"interactionName": "Set editingIndex", "actionName": "updateVariable", "args": [{"__ref": "33381707"}, {"__ref": "33381708"}, {"__ref": "33381709"}], "condExpr": {"__ref": "12613037"}, "conditionalMode": "expression", "uuid": "EQ4JwAyk3", "parent": {"__ref": "33381698"}, "__type": "Interaction"}, "33381703": {"code": "0", "fallback": null, "__type": "CustomCode"}, "33381706": {"path": ["$state", "editingIndex"], "fallback": null, "__type": "ObjectPath"}, "33381707": {"name": "variable", "expr": {"__ref": "33381706"}, "__type": "NameArg"}, "33381708": {"name": "operation", "expr": {"__ref": "33381703"}, "__type": "NameArg"}, "33381709": {"name": "value", "expr": {"__ref": "33381710"}, "__type": "NameArg"}, "33381710": {"code": "(currentIndex)", "fallback": null, "__type": "CustomCode"}, "33381711": {"param": {"__ref": "33381368"}, "expr": {"__ref": "33381712"}, "__type": "Arg"}, "33381712": {"interactions": [{"__ref": "33381713"}], "__type": "EventHandler"}, "33381713": {"interactionName": "Set currentItem ▸ title", "actionName": "updateVariable", "args": [{"__ref": "12613025"}, {"__ref": "12613026"}, {"__ref": "12613027"}], "condExpr": null, "conditionalMode": "always", "uuid": "pmfXQOwKa", "parent": {"__ref": "33381712"}, "__type": "Interaction"}, "33381717": {"code": "0", "fallback": null, "__type": "CustomCode"}, "33381720": {"path": ["currentItem", "title"], "fallback": null, "__type": "ObjectPath"}, "33381725": {"interactions": [{"__ref": "33381726"}], "__type": "EventHandler"}, "33381726": {"interactionName": "Set editingIndex", "actionName": "updateVariable", "args": [{"__ref": "33381742"}, {"__ref": "33381743"}, {"__ref": "33381744"}], "condExpr": null, "conditionalMode": "always", "uuid": "g-FnFLVVP", "parent": {"__ref": "33381725"}, "__type": "Interaction"}, "33381730": {"code": "0", "fallback": null, "__type": "CustomCode"}, "33381741": {"path": ["$state", "editingIndex"], "fallback": null, "__type": "ObjectPath"}, "33381742": {"name": "variable", "expr": {"__ref": "33381741"}, "__type": "NameArg"}, "33381743": {"name": "operation", "expr": {"__ref": "33381730"}, "__type": "NameArg"}, "33381744": {"name": "value", "expr": {"__ref": "33381745"}, "__type": "NameArg"}, "33381745": {"code": "(undefined)", "fallback": null, "__type": "CustomCode"}, "33381748": {"path": ["currentItem", "title"], "fallback": {"__ref": "33381749"}, "__type": "ObjectPath"}, "33381749": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "33381750": {"interactions": [{"__ref": "12613029"}], "__type": "EventHandler"}, "33778001": {"name": "func", "params": [{"__ref": "33778004"}], "__type": "FunctionType"}, "33778002": {"name": "text", "__type": "Text"}, "33778003": {"name": "func", "params": [{"__ref": "33778005"}], "__type": "FunctionType"}, "33778004": {"name": "arg", "argName": "isChecked", "displayName": null, "type": {"__ref": "33778006"}, "__type": "ArgType"}, "33778005": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "33778007"}, "__type": "ArgType"}, "33778006": {"name": "bool", "__type": "BoolType"}, "33778007": {"name": "any", "__type": "AnyType"}, "33778008": {"name": "sadfasdfsadfasdfsadfasdfadsf", "type": "Spacing", "uuid": "m2IDurQB2", "value": "1px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "33778014": {"name": "qweqwe", "type": "Spacing", "uuid": "ZMZ_fRT07", "value": "1px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "50519001": {"name": "abcabcabc", "type": "Spacing", "uuid": "EG105wS3o", "value": "1px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "64282001": {"code": "($state.editingIndex === currentIndex)", "fallback": {"__ref": "64282002"}, "__type": "CustomCode"}, "64282002": {"code": "true", "fallback": null, "__type": "CustomCode"}, "64282003": {"type": {"__ref": "64282004"}, "state": {"__ref": "12613038"}, "variable": {"__ref": "64282007"}, "uuid": "O0mJY_84pBl", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282004": {"name": "func", "params": [{"__ref": "64282005"}], "__type": "FunctionType"}, "64282005": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282006"}, "__type": "ArgType"}, "64282006": {"name": "num", "__type": "<PERSON><PERSON>"}, "64282007": {"name": "On uid change", "uuid": "vA-pgd1trVb", "__type": "Var"}, "64282008": {"type": {"__ref": "64282009"}, "state": {"__ref": "23230027"}, "variable": {"__ref": "64282012"}, "uuid": "xA2s_NYOTek", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282009": {"name": "func", "params": [{"__ref": "64282010"}], "__type": "FunctionType"}, "64282010": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282011"}, "__type": "ArgType"}, "64282011": {"name": "any", "__type": "AnyType"}, "64282012": {"name": "On tasks change", "uuid": "mh4hlDeyTZd", "__type": "Var"}, "64282013": {"type": {"__ref": "64282014"}, "state": {"__ref": "33380079"}, "variable": {"__ref": "64282017"}, "uuid": "jcVtwl7nkvX", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282014": {"name": "func", "params": [{"__ref": "64282015"}], "__type": "FunctionType"}, "64282015": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282016"}, "__type": "ArgType"}, "64282016": {"name": "any", "__type": "AnyType"}, "64282017": {"name": "On Show Start Icon change", "uuid": "64Mw0nqVwWQ", "__type": "Var"}, "64282018": {"type": {"__ref": "64282019"}, "state": {"__ref": "33380080"}, "variable": {"__ref": "64282022"}, "uuid": "bN48a_seERz", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282019": {"name": "func", "params": [{"__ref": "64282020"}], "__type": "FunctionType"}, "64282020": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282021"}, "__type": "ArgType"}, "64282021": {"name": "any", "__type": "AnyType"}, "64282022": {"name": "On Show End Icon change", "uuid": "ZVkZkjPeSdS", "__type": "Var"}, "64282023": {"type": {"__ref": "64282024"}, "state": {"__ref": "33380081"}, "variable": {"__ref": "64282027"}, "uuid": "LK0rhpnYi_L", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282024": {"name": "func", "params": [{"__ref": "64282025"}], "__type": "FunctionType"}, "64282025": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282026"}, "__type": "ArgType"}, "64282026": {"name": "any", "__type": "AnyType"}, "64282027": {"name": "On Is Disabled change", "uuid": "CLJ_MY2Pihu", "__type": "Var"}, "64282028": {"type": {"__ref": "64282029"}, "state": {"__ref": "33380082"}, "variable": {"__ref": "64282032"}, "uuid": "5D6ZyNlB-Yq", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282029": {"name": "func", "params": [{"__ref": "64282030"}], "__type": "FunctionType"}, "64282030": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282031"}, "__type": "ArgType"}, "64282031": {"name": "any", "__type": "AnyType"}, "64282032": {"name": "On Shape change", "uuid": "d3fzBKEwRLG", "__type": "Var"}, "64282033": {"type": {"__ref": "64282034"}, "state": {"__ref": "33380083"}, "variable": {"__ref": "64282037"}, "uuid": "gj7CdiY-Z0G", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282034": {"name": "func", "params": [{"__ref": "64282035"}], "__type": "FunctionType"}, "64282035": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282036"}, "__type": "ArgType"}, "64282036": {"name": "any", "__type": "AnyType"}, "64282037": {"name": "On Size change", "uuid": "DUaAO0Osczz", "__type": "Var"}, "64282038": {"type": {"__ref": "64282039"}, "state": {"__ref": "33380084"}, "variable": {"__ref": "64282042"}, "uuid": "Zb_Qh4mGVON", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282039": {"name": "func", "params": [{"__ref": "64282040"}], "__type": "FunctionType"}, "64282040": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282041"}, "__type": "ArgType"}, "64282041": {"name": "any", "__type": "AnyType"}, "64282042": {"name": "On Color change", "uuid": "xv_O7rSkuFB", "__type": "Var"}, "64282043": {"type": {"__ref": "64282044"}, "state": {"__ref": "33381020"}, "variable": {"__ref": "64282047"}, "uuid": "lJNvjgqMw1v", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282044": {"name": "func", "params": [{"__ref": "64282045"}], "__type": "FunctionType"}, "64282045": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282046"}, "__type": "ArgType"}, "64282046": {"name": "bool", "__type": "BoolType"}, "64282047": {"name": "On Switch Is checked change", "uuid": "OtfaPSGB13H", "__type": "Var"}, "64282048": {"type": {"__ref": "64282049"}, "state": {"__ref": "33381030"}, "variable": {"__ref": "64282052"}, "uuid": "sX8pJX5aXVn", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282049": {"name": "func", "params": [{"__ref": "64282050"}], "__type": "FunctionType"}, "64282050": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282051"}, "__type": "ArgType"}, "64282051": {"name": "any", "__type": "AnyType"}, "64282052": {"name": "On No label change", "uuid": "ihqQD3EHPfS", "__type": "Var"}, "64282053": {"type": {"__ref": "64282054"}, "state": {"__ref": "33381031"}, "variable": {"__ref": "64282057"}, "uuid": "YSvpVQ-16Ip", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282054": {"name": "func", "params": [{"__ref": "64282055"}], "__type": "FunctionType"}, "64282055": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282056"}, "__type": "ArgType"}, "64282056": {"name": "any", "__type": "AnyType"}, "64282057": {"name": "On Is disabled change", "uuid": "heX0PN66bO3", "__type": "Var"}, "64282058": {"type": {"__ref": "64282059"}, "state": {"__ref": "33381355"}, "variable": {"__ref": "64282062"}, "uuid": "fohNCyovvGK", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282059": {"name": "func", "params": [{"__ref": "64282060"}], "__type": "FunctionType"}, "64282060": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282061"}, "__type": "ArgType"}, "64282061": {"name": "text", "__type": "Text"}, "64282062": {"name": "On TextInput value change", "uuid": "0MwGEPGhXqQ", "__type": "Var"}, "64282063": {"type": {"__ref": "64282064"}, "state": {"__ref": "33381369"}, "variable": {"__ref": "64282067"}, "uuid": "o54iH11H9Tu", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282064": {"name": "func", "params": [{"__ref": "64282065"}], "__type": "FunctionType"}, "64282065": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282066"}, "__type": "ArgType"}, "64282066": {"name": "any", "__type": "AnyType"}, "64282067": {"name": "On Show Start Icon change", "uuid": "MdWvQgoUO4a", "__type": "Var"}, "64282068": {"type": {"__ref": "64282069"}, "state": {"__ref": "33381370"}, "variable": {"__ref": "64282072"}, "uuid": "xwDDUaRybn1", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282069": {"name": "func", "params": [{"__ref": "64282070"}], "__type": "FunctionType"}, "64282070": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282071"}, "__type": "ArgType"}, "64282071": {"name": "any", "__type": "AnyType"}, "64282072": {"name": "On Show End Icon change", "uuid": "ZHXkYwHeZb6", "__type": "Var"}, "64282073": {"type": {"__ref": "64282074"}, "state": {"__ref": "33381371"}, "variable": {"__ref": "64282077"}, "uuid": "ZzxuWExBKJZ", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282074": {"name": "func", "params": [{"__ref": "64282075"}], "__type": "FunctionType"}, "64282075": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282076"}, "__type": "ArgType"}, "64282076": {"name": "any", "__type": "AnyType"}, "64282077": {"name": "On Is Disabled change", "uuid": "Jkn-dGaCsjn", "__type": "Var"}, "64282078": {"type": {"__ref": "64282079"}, "state": {"__ref": "33381372"}, "variable": {"__ref": "64282082"}, "uuid": "x-gpZ-CvYK9", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282079": {"name": "func", "params": [{"__ref": "64282080"}], "__type": "FunctionType"}, "64282080": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282081"}, "__type": "ArgType"}, "64282081": {"name": "any", "__type": "AnyType"}, "64282082": {"name": "On Color change", "uuid": "e2RFJr6U526", "__type": "Var"}, "64282083": {"type": {"__ref": "64282084"}, "state": {"__ref": "33381685"}, "variable": {"__ref": "64282087"}, "uuid": "Ibyq2xiUGlW", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "64282084": {"name": "func", "params": [{"__ref": "64282085"}], "__type": "FunctionType"}, "64282085": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "64282086"}, "__type": "ArgType"}, "64282086": {"name": "num", "__type": "<PERSON><PERSON>"}, "64282087": {"name": "On editingIndex change", "uuid": "HqK91ZqXbEx", "__type": "Var"}}, "deps": [], "version": "246-add-component-updated-at"}]]