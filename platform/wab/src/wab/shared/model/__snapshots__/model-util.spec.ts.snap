// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`model-util works 1`] = `
{
  "__iid": "1",
  "__type": "Site",
  "activeScreenVariantGroup": {
    "__iidRef": "893",
    "__uuid": undefined,
  },
  "activeTheme": {
    "__iidRef": "914",
    "__uuid": undefined,
  },
  "arenas": [
    {
      "__iid": "845",
      "__type": "Arena",
      "children": [
        {
          "__iid": "846",
          "__type": "ArenaFrame",
          "bgColor": null,
          "container": {
            "__iid": "847",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "2",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "oYsjdq3ssG-",
            "vsettings": [
              {
                "__iid": "848",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "851",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 862,
          "lang": "English",
          "left": 0,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [],
          "targetVariants": [
            {
              "__iidRef": "9",
              "__uuid": undefined,
            },
          ],
          "top": 0,
          "uuid": "90rMdBC74L",
          "viewMode": "stretch",
          "width": 666,
        },
        {
          "__iid": "852",
          "__type": "ArenaFrame",
          "bgColor": null,
          "container": {
            "__iid": "853",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "159",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "4uussBQGgI9",
            "vsettings": [
              {
                "__iid": "854",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "855",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 155,
          "lang": "English",
          "left": 1289.8034,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {
            "sJAdt3n86Ze": true,
          },
          "targetGlobalVariants": [],
          "targetVariants": [
            {
              "__iidRef": "171",
              "__uuid": undefined,
            },
          ],
          "top": 388.8889,
          "uuid": "iDjkGvcr2J",
          "viewMode": "centered",
          "width": 437,
        },
        {
          "__iid": "856",
          "__type": "ArenaFrame",
          "bgColor": null,
          "container": {
            "__iid": "857",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "159",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "olJuBWZyS0t",
            "vsettings": [
              {
                "__iid": "858",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "859",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 155,
          "lang": "English",
          "left": 750.2051,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {
            "BiyRgR0UtVP": true,
            "sJAdt3n86Ze": true,
          },
          "targetGlobalVariants": [],
          "targetVariants": [
            {
              "__iidRef": "171",
              "__uuid": undefined,
            },
          ],
          "top": 398.3333,
          "uuid": "crw5-iASPC",
          "viewMode": "centered",
          "width": 437,
        },
        {
          "__iid": "860",
          "__type": "ArenaFrame",
          "bgColor": null,
          "container": {
            "__iid": "861",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "478",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "42VdC8ve5_5",
            "vsettings": [
              {
                "__iid": "862",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "863",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 100,
          "lang": "English",
          "left": 752.1993,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [],
          "targetVariants": [
            {
              "__iidRef": "490",
              "__uuid": undefined,
            },
          ],
          "top": 589.8385,
          "uuid": "2majt56Igq",
          "viewMode": "centered",
          "width": 528,
        },
        {
          "__iid": "864",
          "__type": "ArenaFrame",
          "bgColor": null,
          "container": {
            "__iid": "865",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "542",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "1YBAuq0DQhV",
            "vsettings": [
              {
                "__iid": "866",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "867",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 105,
          "lang": "English",
          "left": 753.5282,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {
            "Y3U-2j28S-V": true,
          },
          "targetGlobalVariants": [],
          "targetVariants": [
            {
              "__iidRef": "565",
              "__uuid": undefined,
            },
          ],
          "top": 728.8309,
          "uuid": "OFde0UZTPo",
          "viewMode": "centered",
          "width": 127,
        },
        {
          "__iid": "868",
          "__type": "ArenaFrame",
          "bgColor": null,
          "container": {
            "__iid": "869",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "34",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "J7nhOKIykC9",
            "vsettings": [
              {
                "__iid": "870",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "871",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 151,
          "lang": "English",
          "left": 768.8718,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [],
          "targetVariants": [
            {
              "__iidRef": "58",
              "__uuid": undefined,
            },
          ],
          "top": 15.6838,
          "uuid": "LVk6uksUV7",
          "viewMode": "centered",
          "width": 438,
        },
      ],
      "name": "Light Mode",
    },
    {
      "__iid": "872",
      "__type": "Arena",
      "children": [
        {
          "__iid": "873",
          "__type": "ArenaFrame",
          "bgColor": null,
          "container": {
            "__iid": "874",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "2",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "VhU9TxGky5e",
            "vsettings": [
              {
                "__iid": "875",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "876",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 789,
          "lang": "English",
          "left": 0,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [
            {
              "__iidRef": "23",
              "__uuid": undefined,
            },
          ],
          "targetVariants": [
            {
              "__iidRef": "9",
              "__uuid": undefined,
            },
          ],
          "top": 0,
          "uuid": "SxFc90Ydke",
          "viewMode": "centered",
          "width": 649,
        },
        {
          "__iid": "877",
          "__type": "ArenaFrame",
          "bgColor": "rgb(34,34,34)",
          "container": {
            "__iid": "878",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "159",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "U_lszZ2vdrG",
            "vsettings": [
              {
                "__iid": "879",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "880",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 129,
          "lang": "English",
          "left": 729,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [
            {
              "__iidRef": "23",
              "__uuid": undefined,
            },
          ],
          "targetVariants": [
            {
              "__iidRef": "171",
              "__uuid": undefined,
            },
          ],
          "top": 0,
          "uuid": "fAdFTXkJcIb",
          "viewMode": "centered",
          "width": 430,
        },
        {
          "__iid": "881",
          "__type": "ArenaFrame",
          "bgColor": "rgb(34,34,34)",
          "container": {
            "__iid": "882",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "159",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "gN3TQB9QvBy",
            "vsettings": [
              {
                "__iid": "883",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "884",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 129,
          "lang": "English",
          "left": 728.8361,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [
            {
              "__iidRef": "23",
              "__uuid": undefined,
            },
          ],
          "targetVariants": [
            {
              "__iidRef": "183",
              "__uuid": undefined,
            },
          ],
          "top": 176.3934,
          "uuid": "QbchpRc7yVa",
          "viewMode": "centered",
          "width": 430,
        },
        {
          "__iid": "885",
          "__type": "ArenaFrame",
          "bgColor": "rgb(34,34,34)",
          "container": {
            "__iid": "886",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "159",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "KUD4bzeSQHX",
            "vsettings": [
              {
                "__iid": "887",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "888",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 129,
          "lang": "English",
          "left": 730.8033,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [
            {
              "__iidRef": "23",
              "__uuid": undefined,
            },
          ],
          "targetVariants": [
            {
              "__iidRef": "186",
              "__uuid": undefined,
            },
          ],
          "top": 359.3443,
          "uuid": "lvATWZmmQZB",
          "viewMode": "centered",
          "width": 430,
        },
        {
          "__iid": "889",
          "__type": "ArenaFrame",
          "bgColor": "rgb(34,34,34)",
          "container": {
            "__iid": "890",
            "__type": "TplComponent",
            "component": {
              "__iidRef": "34",
              "__uuid": undefined,
            },
            "locked": null,
            "name": null,
            "parent": null,
            "uuid": "rp6Fyxmp45w",
            "vsettings": [
              {
                "__iid": "891",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "892",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "849",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          "height": 214,
          "lang": "English",
          "left": 1240.8033,
          "name": "",
          "pinnedGlobalVariants": {},
          "pinnedVariants": {},
          "targetGlobalVariants": [
            {
              "__iidRef": "23",
              "__uuid": undefined,
            },
          ],
          "targetVariants": [
            {
              "__iidRef": "42",
              "__uuid": undefined,
            },
          ],
          "top": 359.3443,
          "uuid": "-DlqUxYhmcZ",
          "viewMode": "centered",
          "width": 478,
        },
      ],
      "name": "Dark Mode",
    },
  ],
  "codeLibraries": [],
  "componentArenas": [],
  "components": [
    {
      "__iid": "2",
      "__type": "Component",
      "alwaysAutoName": false,
      "codeComponentMeta": null,
      "dataQueries": [],
      "editableByContentEditor": true,
      "figmaMappings": [],
      "hiddenFromContentEditor": false,
      "metadata": {},
      "name": "TodoApp",
      "pageMeta": null,
      "params": [
        {
          "__iid": "3",
          "__type": "StateParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "state": {
            "__iidRef": "30107001",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656001",
            "__type": "AnyType",
            "name": "any",
          },
          "uuid": "5gOL4qCIl",
          "variable": {
            "__iid": "4",
            "__type": "Var",
            "name": "State",
            "uuid": "YVGK80-gwb",
          },
        },
        {
          "__iid": "56656006",
          "__type": "StateChangeHandlerParam",
          "about": null,
          "defaultExpr": null,
          "description": "EventHandler",
          "displayName": null,
          "enumValues": [],
          "exportType": "ToolsOnly",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": false,
          "state": {
            "__iidRef": "30107001",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656007",
            "__type": "FunctionType",
            "name": "func",
            "params": [
              {
                "__iid": "56656008",
                "__type": "ArgType",
                "argName": "val",
                "displayName": null,
                "name": "arg",
                "type": {
                  "__iid": "56656009",
                  "__type": "AnyType",
                  "name": "any",
                },
              },
            ],
          },
          "uuid": "FkjGa9MrptK",
          "variable": {
            "__iid": "56656010",
            "__type": "Var",
            "name": "On State change",
            "uuid": "L55XrOZCK-K",
          },
        },
      ],
      "plumeInfo": null,
      "serverQueries": [],
      "states": [
        {
          "__iid": "30107001",
          "__type": "VariantGroupState",
          "accessType": "private",
          "implicitState": null,
          "onChangeParam": {
            "__iidRef": "56656006",
            "__uuid": undefined,
          },
          "param": {
            "__iidRef": "3",
            "__uuid": undefined,
          },
          "tplNode": null,
          "variableType": "variant",
          "variantGroup": {
            "__iidRef": "149",
            "__uuid": undefined,
          },
        },
      ],
      "subComps": [],
      "superComp": null,
      "templateInfo": null,
      "tplTree": {
        "__iid": "6",
        "__type": "TplTag",
        "children": [
          {
            "__iid": "7",
            "__type": "TplTag",
            "children": [],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": "app title",
            "parent": {
              "__iidRef": "6",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "text",
            "uuid": "ko15Ce3VTA",
            "vsettings": [
              {
                "__iid": "8",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "11",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "align-self": "auto",
                    "color": "var(--token-2TqFcBopNcN)",
                    "flex-basis": "auto",
                    "flex-grow": "0",
                    "flex-shrink": "1",
                    "font-size": "100px",
                    "height": "auto",
                    "position": "relative",
                    "width": "auto",
                  },
                },
                "text": {
                  "__iid": "21",
                  "__type": "RawText",
                  "markers": [],
                  "text": "todos",
                },
                "variants": [
                  {
                    "__iidRef": "9",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "22",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "29",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "color": "var(--token-a-AKFYtMSR5)",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "23",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          {
            "__iid": "31",
            "__type": "TplTag",
            "children": [
              {
                "__iid": "32",
                "__type": "TplTag",
                "children": [
                  {
                    "__iid": "33",
                    "__type": "TplComponent",
                    "component": {
                      "__iidRef": "34",
                      "__uuid": undefined,
                    },
                    "locked": null,
                    "name": null,
                    "parent": {
                      "__iidRef": "32",
                      "__uuid": undefined,
                    },
                    "uuid": "l2f9VSiGXR",
                    "vsettings": [
                      {
                        "__iid": "137",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "138",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "align-self": "stretch",
                            "flex-basis": "auto",
                            "flex-grow": "0",
                            "flex-shrink": "1",
                            "height": "auto",
                            "position": "relative",
                            "width": "auto",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "9",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "146",
                        "__type": "VariantSetting",
                        "args": [
                          {
                            "__iid": "150",
                            "__type": "Arg",
                            "expr": {
                              "__iid": "22347001",
                              "__type": "VariantsRef",
                              "variants": [
                                {
                                  "__iidRef": "55",
                                  "__uuid": undefined,
                                },
                              ],
                            },
                            "param": {
                              "__iidRef": "35",
                              "__uuid": undefined,
                            },
                          },
                        ],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "152",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "align-self": "stretch",
                            "width": "auto",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "147",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "155",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "156",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "23",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                  {
                    "__iid": "157",
                    "__type": "TplTag",
                    "children": [
                      {
                        "__iid": "158",
                        "__type": "TplComponent",
                        "component": {
                          "__iidRef": "159",
                          "__uuid": undefined,
                        },
                        "locked": null,
                        "name": null,
                        "parent": {
                          "__iidRef": "157",
                          "__uuid": undefined,
                        },
                        "uuid": "LSTd5ab9T8",
                        "vsettings": [
                          {
                            "__iid": "414",
                            "__type": "VariantSetting",
                            "args": [
                              {
                                "__iid": "415",
                                "__type": "Arg",
                                "expr": {
                                  "__iid": "416",
                                  "__type": "RenderExpr",
                                  "tpl": [
                                    {
                                      "__iid": "417",
                                      "__type": "TplTag",
                                      "children": [],
                                      "codeGenType": "auto",
                                      "columnsSetting": null,
                                      "locked": null,
                                      "name": null,
                                      "parent": {
                                        "__iidRef": "158",
                                        "__uuid": undefined,
                                      },
                                      "tag": "div",
                                      "type": "text",
                                      "uuid": "Jdst7RXIjk",
                                      "vsettings": [
                                        {
                                          "__iid": "418",
                                          "__type": "VariantSetting",
                                          "args": [],
                                          "attrs": {},
                                          "columnsConfig": null,
                                          "dataCond": null,
                                          "dataRep": null,
                                          "rs": {
                                            "__iid": "419",
                                            "__type": "RuleSet",
                                            "mixins": [],
                                            "values": {},
                                          },
                                          "text": {
                                            "__iid": "420",
                                            "__type": "RawText",
                                            "markers": [],
                                            "text": "Some kind of text here",
                                          },
                                          "variants": [
                                            {
                                              "__iidRef": "9",
                                              "__uuid": undefined,
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                  ],
                                },
                                "param": {
                                  "__iidRef": "163",
                                  "__uuid": undefined,
                                },
                              },
                            ],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "421",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {
                                "align-self": "stretch",
                                "flex-basis": "auto",
                              },
                            },
                            "text": null,
                            "variants": [
                              {
                                "__iidRef": "9",
                                "__uuid": undefined,
                              },
                            ],
                          },
                          {
                            "__iid": "424",
                            "__type": "VariantSetting",
                            "args": [],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "425",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {},
                            },
                            "text": null,
                            "variants": [
                              {
                                "__iidRef": "147",
                                "__uuid": undefined,
                              },
                            ],
                          },
                          {
                            "__iid": "426",
                            "__type": "VariantSetting",
                            "args": [],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "427",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {},
                            },
                            "text": null,
                            "variants": [
                              {
                                "__iidRef": "23",
                                "__uuid": undefined,
                              },
                            ],
                          },
                        ],
                      },
                      {
                        "__iid": "428",
                        "__type": "TplComponent",
                        "component": {
                          "__iidRef": "159",
                          "__uuid": undefined,
                        },
                        "locked": null,
                        "name": null,
                        "parent": {
                          "__iidRef": "157",
                          "__uuid": undefined,
                        },
                        "uuid": "A8lPnars2T",
                        "vsettings": [
                          {
                            "__iid": "429",
                            "__type": "VariantSetting",
                            "args": [
                              {
                                "__iid": "430",
                                "__type": "Arg",
                                "expr": {
                                  "__iid": "22347002",
                                  "__type": "VariantsRef",
                                  "variants": [
                                    {
                                      "__iidRef": "186",
                                      "__uuid": undefined,
                                    },
                                  ],
                                },
                                "param": {
                                  "__iidRef": "160",
                                  "__uuid": undefined,
                                },
                              },
                              {
                                "__iid": "432",
                                "__type": "Arg",
                                "expr": {
                                  "__iid": "433",
                                  "__type": "RenderExpr",
                                  "tpl": [
                                    {
                                      "__iid": "434",
                                      "__type": "TplTag",
                                      "children": [],
                                      "codeGenType": "auto",
                                      "columnsSetting": null,
                                      "locked": null,
                                      "name": null,
                                      "parent": {
                                        "__iidRef": "428",
                                        "__uuid": undefined,
                                      },
                                      "tag": "div",
                                      "type": "text",
                                      "uuid": "t3B3aHhswh",
                                      "vsettings": [
                                        {
                                          "__iid": "435",
                                          "__type": "VariantSetting",
                                          "args": [],
                                          "attrs": {},
                                          "columnsConfig": null,
                                          "dataCond": null,
                                          "dataRep": null,
                                          "rs": {
                                            "__iid": "436",
                                            "__type": "RuleSet",
                                            "mixins": [],
                                            "values": {},
                                          },
                                          "text": {
                                            "__iid": "437",
                                            "__type": "RawText",
                                            "markers": [],
                                            "text": "Some kind of text here",
                                          },
                                          "variants": [
                                            {
                                              "__iidRef": "9",
                                              "__uuid": undefined,
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                  ],
                                },
                                "param": {
                                  "__iidRef": "163",
                                  "__uuid": undefined,
                                },
                              },
                            ],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "438",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {
                                "align-self": "stretch",
                                "flex-basis": "auto",
                              },
                            },
                            "text": null,
                            "variants": [
                              {
                                "__iidRef": "9",
                                "__uuid": undefined,
                              },
                            ],
                          },
                          {
                            "__iid": "441",
                            "__type": "VariantSetting",
                            "args": [],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "442",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {},
                            },
                            "text": null,
                            "variants": [
                              {
                                "__iidRef": "23",
                                "__uuid": undefined,
                              },
                            ],
                          },
                        ],
                      },
                      {
                        "__iid": "443",
                        "__type": "TplComponent",
                        "component": {
                          "__iidRef": "159",
                          "__uuid": undefined,
                        },
                        "locked": null,
                        "name": null,
                        "parent": {
                          "__iidRef": "157",
                          "__uuid": undefined,
                        },
                        "uuid": "1Utsddh06p",
                        "vsettings": [
                          {
                            "__iid": "444",
                            "__type": "VariantSetting",
                            "args": [
                              {
                                "__iid": "445",
                                "__type": "Arg",
                                "expr": {
                                  "__iid": "22347003",
                                  "__type": "VariantsRef",
                                  "variants": [
                                    {
                                      "__iidRef": "183",
                                      "__uuid": undefined,
                                    },
                                  ],
                                },
                                "param": {
                                  "__iidRef": "160",
                                  "__uuid": undefined,
                                },
                              },
                              {
                                "__iid": "447",
                                "__type": "Arg",
                                "expr": {
                                  "__iid": "448",
                                  "__type": "RenderExpr",
                                  "tpl": [
                                    {
                                      "__iid": "449",
                                      "__type": "TplTag",
                                      "children": [],
                                      "codeGenType": "auto",
                                      "columnsSetting": null,
                                      "locked": null,
                                      "name": null,
                                      "parent": {
                                        "__iidRef": "443",
                                        "__uuid": undefined,
                                      },
                                      "tag": "div",
                                      "type": "text",
                                      "uuid": "kACxauAF28",
                                      "vsettings": [
                                        {
                                          "__iid": "450",
                                          "__type": "VariantSetting",
                                          "args": [],
                                          "attrs": {},
                                          "columnsConfig": null,
                                          "dataCond": null,
                                          "dataRep": null,
                                          "rs": {
                                            "__iid": "451",
                                            "__type": "RuleSet",
                                            "mixins": [],
                                            "values": {},
                                          },
                                          "text": {
                                            "__iid": "452",
                                            "__type": "RawText",
                                            "markers": [],
                                            "text": "Some kind of text here",
                                          },
                                          "variants": [
                                            {
                                              "__iidRef": "9",
                                              "__uuid": undefined,
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                  ],
                                },
                                "param": {
                                  "__iidRef": "163",
                                  "__uuid": undefined,
                                },
                              },
                            ],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "453",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {
                                "align-self": "stretch",
                                "flex-basis": "auto",
                              },
                            },
                            "text": null,
                            "variants": [
                              {
                                "__iidRef": "9",
                                "__uuid": undefined,
                              },
                            ],
                          },
                        ],
                      },
                      {
                        "__iid": "456",
                        "__type": "TplComponent",
                        "component": {
                          "__iidRef": "159",
                          "__uuid": undefined,
                        },
                        "locked": null,
                        "name": null,
                        "parent": {
                          "__iidRef": "157",
                          "__uuid": undefined,
                        },
                        "uuid": "yPvkKMxbaph",
                        "vsettings": [
                          {
                            "__iid": "457",
                            "__type": "VariantSetting",
                            "args": [
                              {
                                "__iid": "458",
                                "__type": "Arg",
                                "expr": {
                                  "__iid": "459",
                                  "__type": "RenderExpr",
                                  "tpl": [
                                    {
                                      "__iid": "460",
                                      "__type": "TplTag",
                                      "children": [],
                                      "codeGenType": "auto",
                                      "columnsSetting": null,
                                      "locked": null,
                                      "name": null,
                                      "parent": {
                                        "__iidRef": "456",
                                        "__uuid": undefined,
                                      },
                                      "tag": "div",
                                      "type": "text",
                                      "uuid": "jUEUuX-s1i8",
                                      "vsettings": [
                                        {
                                          "__iid": "461",
                                          "__type": "VariantSetting",
                                          "args": [],
                                          "attrs": {},
                                          "columnsConfig": null,
                                          "dataCond": null,
                                          "dataRep": null,
                                          "rs": {
                                            "__iid": "462",
                                            "__type": "RuleSet",
                                            "mixins": [],
                                            "values": {},
                                          },
                                          "text": {
                                            "__iid": "463",
                                            "__type": "RawText",
                                            "markers": [],
                                            "text": "I have a task to do something that takes a long time",
                                          },
                                          "variants": [
                                            {
                                              "__iidRef": "9",
                                              "__uuid": undefined,
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                  ],
                                },
                                "param": {
                                  "__iidRef": "163",
                                  "__uuid": undefined,
                                },
                              },
                            ],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "464",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {
                                "align-self": "stretch",
                                "flex-basis": "auto",
                              },
                            },
                            "text": null,
                            "variants": [
                              {
                                "__iidRef": "9",
                                "__uuid": undefined,
                              },
                            ],
                          },
                        ],
                      },
                    ],
                    "codeGenType": "auto",
                    "columnsSetting": null,
                    "locked": null,
                    "name": "tasks-container",
                    "parent": {
                      "__iidRef": "32",
                      "__uuid": undefined,
                    },
                    "tag": "div",
                    "type": "other",
                    "uuid": "7jZiEumcCy",
                    "vsettings": [
                      {
                        "__iid": "467",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "468",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "align-items": "stretch",
                            "display": "flex",
                            "flex-direction": "column",
                            "justify-content": "flex-start",
                            "position": "relative",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "9",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "474",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": {
                          "__iid": "476",
                          "__type": "CustomCode",
                          "code": "false",
                          "fallback": null,
                        },
                        "dataRep": null,
                        "rs": {
                          "__iid": "475",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "147",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                  {
                    "__iid": "477",
                    "__type": "TplComponent",
                    "component": {
                      "__iidRef": "478",
                      "__uuid": undefined,
                    },
                    "locked": null,
                    "name": null,
                    "parent": {
                      "__iidRef": "32",
                      "__uuid": undefined,
                    },
                    "uuid": "Ldy8SYLWzce",
                    "vsettings": [
                      {
                        "__iid": "723",
                        "__type": "VariantSetting",
                        "args": [
                          {
                            "__iid": "724",
                            "__type": "Arg",
                            "expr": {
                              "__iid": "22347005",
                              "__type": "VariantsRef",
                              "variants": [
                                {
                                  "__iidRef": "498",
                                  "__uuid": undefined,
                                },
                              ],
                            },
                            "param": {
                              "__iidRef": "479",
                              "__uuid": undefined,
                            },
                          },
                          {
                            "__iid": "726",
                            "__type": "Arg",
                            "expr": {
                              "__iid": "727",
                              "__type": "RenderExpr",
                              "tpl": [
                                {
                                  "__iid": "728",
                                  "__type": "TplTag",
                                  "children": [],
                                  "codeGenType": "auto",
                                  "columnsSetting": null,
                                  "locked": null,
                                  "name": null,
                                  "parent": {
                                    "__iidRef": "477",
                                    "__uuid": undefined,
                                  },
                                  "tag": "div",
                                  "type": "text",
                                  "uuid": "b1prBkC_Q21",
                                  "vsettings": [
                                    {
                                      "__iid": "729",
                                      "__type": "VariantSetting",
                                      "args": [],
                                      "attrs": {},
                                      "columnsConfig": null,
                                      "dataCond": null,
                                      "dataRep": null,
                                      "rs": {
                                        "__iid": "730",
                                        "__type": "RuleSet",
                                        "mixins": [],
                                        "values": {},
                                      },
                                      "text": {
                                        "__iid": "731",
                                        "__type": "RawText",
                                        "markers": [],
                                        "text": "2",
                                      },
                                      "variants": [
                                        {
                                          "__iidRef": "9",
                                          "__uuid": undefined,
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                            "param": {
                              "__iidRef": "482",
                              "__uuid": undefined,
                            },
                          },
                        ],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "732",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "align-self": "stretch",
                            "flex-basis": "auto",
                            "flex-grow": "0",
                            "flex-shrink": "1",
                            "height": "auto",
                            "position": "relative",
                            "width": "auto",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "9",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "740",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": {
                          "__iid": "742",
                          "__type": "CustomCode",
                          "code": "false",
                          "fallback": null,
                        },
                        "dataRep": null,
                        "rs": {
                          "__iid": "741",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "147",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                ],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": "app-body",
                "parent": {
                  "__iidRef": "31",
                  "__uuid": undefined,
                },
                "tag": "div",
                "type": "other",
                "uuid": "gsd0Aam3qZ",
                "vsettings": [
                  {
                    "__iid": "743",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "744",
                      "__type": "RuleSet",
                      "mixins": [
                        {
                          "__iidRef": "757",
                          "__uuid": undefined,
                        },
                      ],
                      "values": {
                        "align-items": "stretch",
                        "background": "linear-gradient(var(--token-vkNNUxlF69T), var(--token-vkNNUxlF69T))",
                        "display": "flex",
                        "flex-basis": "auto",
                        "flex-direction": "column",
                        "flex-grow": "0",
                        "flex-shrink": "1",
                        "height": "auto",
                        "justify-content": "space-evenly",
                        "position": "relative",
                        "width": "550px",
                        "z-index": "1",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "9",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "760",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "761",
                      "__type": "RuleSet",
                      "mixins": [
                        {
                          "__iidRef": "763",
                          "__uuid": undefined,
                        },
                      ],
                      "values": {
                        "background": "linear-gradient(var(--token--OrU7rp_dHe), var(--token--OrU7rp_dHe))",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "23",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
              {
                "__iid": "766",
                "__type": "TplTag",
                "children": [
                  {
                    "__iid": "767",
                    "__type": "TplTag",
                    "children": [],
                    "codeGenType": "auto",
                    "columnsSetting": null,
                    "locked": null,
                    "name": null,
                    "parent": {
                      "__iidRef": "766",
                      "__uuid": undefined,
                    },
                    "tag": "div",
                    "type": "other",
                    "uuid": "d3b6NKJp-3h",
                    "vsettings": [
                      {
                        "__iid": "768",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "769",
                          "__type": "RuleSet",
                          "mixins": [
                            {
                              "__iidRef": "757",
                              "__uuid": undefined,
                            },
                          ],
                          "values": {
                            "background": "linear-gradient(var(--token-vkNNUxlF69T), var(--token-vkNNUxlF69T))",
                            "display": "block",
                            "height": "10px",
                            "left": "10px",
                            "position": "absolute",
                            "right": "10px",
                            "top": "6px",
                            "width": "auto",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "9",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "778",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "779",
                          "__type": "RuleSet",
                          "mixins": [
                            {
                              "__iidRef": "763",
                              "__uuid": undefined,
                            },
                          ],
                          "values": {
                            "background": "linear-gradient(var(--token--OrU7rp_dHe), var(--token--OrU7rp_dHe))",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "23",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                  {
                    "__iid": "781",
                    "__type": "TplTag",
                    "children": [],
                    "codeGenType": "auto",
                    "columnsSetting": null,
                    "locked": null,
                    "name": null,
                    "parent": {
                      "__iidRef": "766",
                      "__uuid": undefined,
                    },
                    "tag": "div",
                    "type": "other",
                    "uuid": "Vm2NxalekbI",
                    "vsettings": [
                      {
                        "__iid": "782",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "783",
                          "__type": "RuleSet",
                          "mixins": [
                            {
                              "__iidRef": "757",
                              "__uuid": undefined,
                            },
                          ],
                          "values": {
                            "background": "linear-gradient(var(--token-vkNNUxlF69T), var(--token-vkNNUxlF69T))",
                            "display": "block",
                            "height": "10px",
                            "left": "5px",
                            "position": "absolute",
                            "right": "5px",
                            "top": "0px",
                            "width": "auto",
                            "z-index": "-1px",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "9",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "793",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "794",
                          "__type": "RuleSet",
                          "mixins": [
                            {
                              "__iidRef": "763",
                              "__uuid": undefined,
                            },
                          ],
                          "values": {
                            "background": "linear-gradient(var(--token--OrU7rp_dHe), var(--token--OrU7rp_dHe))",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "23",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                ],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": "fake-stack",
                "parent": {
                  "__iidRef": "31",
                  "__uuid": undefined,
                },
                "tag": "div",
                "type": "other",
                "uuid": "H8DLO-Q9H2O",
                "vsettings": [
                  {
                    "__iid": "796",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "797",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-self": "auto",
                        "background": "linear-gradient(rgba(68,192,255,0), rgba(68,192,255,0))",
                        "display": "block",
                        "flex-basis": "auto",
                        "flex-grow": "0",
                        "flex-shrink": "1",
                        "height": "auto",
                        "margin-top": "-4px",
                        "position": "relative",
                        "width": "100%",
                        "z-index": "-1px",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "9",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "809",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "811",
                      "__type": "CustomCode",
                      "code": "false",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "810",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "147",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
            ],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "6",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "other",
            "uuid": "AnFxn5iXQ8",
            "vsettings": [
              {
                "__iid": "812",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "813",
                  "__type": "RuleSet",
                  "mixins": [
                    {
                      "__iidRef": "822",
                      "__uuid": undefined,
                    },
                  ],
                  "values": {
                    "align-items": "stretch",
                    "align-self": "auto",
                    "display": "flex",
                    "flex-direction": "column",
                    "height": "auto",
                    "justify-content": "flex-start",
                    "position": "relative",
                    "width": "550px",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "9",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "825",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "826",
                  "__type": "RuleSet",
                  "mixins": [
                    {
                      "__iidRef": "827",
                      "__uuid": undefined,
                    },
                  ],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "23",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
        ],
        "codeGenType": "auto",
        "columnsSetting": null,
        "locked": null,
        "name": null,
        "parent": null,
        "tag": "div",
        "type": "other",
        "uuid": "edOatPMXpc",
        "vsettings": [
          {
            "__iid": "830",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "831",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "align-items": "center",
                "background": "linear-gradient(var(--token-8QpUfX_gwS2), var(--token-8QpUfX_gwS2))",
                "display": "flex",
                "flex-direction": "column",
                "height": "stretch",
                "justify-content": "flex-start",
                "position": "relative",
                "width": "stretch",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "9",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "840",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "841",
              "__type": "RuleSet",
              "mixins": [],
              "values": {},
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "147",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "842",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "843",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "background": "linear-gradient(var(--token-WuIxxo7bvas), var(--token-WuIxxo7bvas))",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "23",
                "__uuid": undefined,
              },
            ],
          },
        ],
      },
      "trapsFocus": false,
      "type": "plain",
      "updatedAt": null,
      "uuid": "BunrB8jR5E5",
      "variantGroups": [
        {
          "__iid": "149",
          "__type": "ComponentVariantGroup",
          "linkedState": {
            "__iidRef": "30107001",
            "__uuid": undefined,
          },
          "multi": false,
          "param": {
            "__iidRef": "3",
            "__uuid": undefined,
          },
          "type": "component",
          "uuid": "ADYTK8rKze",
          "variants": [
            {
              "__iid": "147",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "empty",
              "parent": {
                "__iidRef": "149",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "Mz19VU8rwb",
            },
          ],
        },
      ],
      "variants": [
        {
          "__iid": "9",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "base",
          "parent": null,
          "selectors": null,
          "uuid": "fOjGWsJuT",
        },
      ],
    },
    {
      "__iid": "34",
      "__type": "Component",
      "alwaysAutoName": false,
      "codeComponentMeta": null,
      "dataQueries": [],
      "editableByContentEditor": true,
      "figmaMappings": [],
      "hiddenFromContentEditor": false,
      "metadata": {},
      "name": "Header",
      "pageMeta": null,
      "params": [
        {
          "__iid": "35",
          "__type": "StateParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "state": {
            "__iidRef": "30107002",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656002",
            "__type": "AnyType",
            "name": "any",
          },
          "uuid": "38bQC-OYBc",
          "variable": {
            "__iid": "36",
            "__type": "Var",
            "name": "State",
            "uuid": "p-gX4dbKnRm",
          },
        },
        {
          "__iid": "56656011",
          "__type": "StateChangeHandlerParam",
          "about": null,
          "defaultExpr": null,
          "description": "EventHandler",
          "displayName": null,
          "enumValues": [],
          "exportType": "ToolsOnly",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": false,
          "state": {
            "__iidRef": "30107002",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656012",
            "__type": "FunctionType",
            "name": "func",
            "params": [
              {
                "__iid": "56656013",
                "__type": "ArgType",
                "argName": "val",
                "displayName": null,
                "name": "arg",
                "type": {
                  "__iid": "56656014",
                  "__type": "AnyType",
                  "name": "any",
                },
              },
            ],
          },
          "uuid": "fzlXEDAzS5f",
          "variable": {
            "__iid": "56656015",
            "__type": "Var",
            "name": "On State change",
            "uuid": "oNpntvj3YQL",
          },
        },
      ],
      "plumeInfo": null,
      "serverQueries": [],
      "states": [
        {
          "__iid": "30107002",
          "__type": "VariantGroupState",
          "accessType": "private",
          "implicitState": null,
          "onChangeParam": {
            "__iidRef": "56656011",
            "__uuid": undefined,
          },
          "param": {
            "__iidRef": "35",
            "__uuid": undefined,
          },
          "tplNode": null,
          "variableType": "variant",
          "variantGroup": {
            "__iidRef": "57",
            "__uuid": undefined,
          },
        },
      ],
      "subComps": [],
      "superComp": null,
      "templateInfo": null,
      "tplTree": {
        "__iid": "38",
        "__type": "TplTag",
        "children": [
          {
            "__iid": "39",
            "__type": "TplTag",
            "children": [
              {
                "__iid": "40",
                "__type": "TplTag",
                "children": [],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "39",
                  "__uuid": undefined,
                },
                "tag": "img",
                "type": "image",
                "uuid": "CxCtTZvyKWd",
                "vsettings": [
                  {
                    "__iid": "41",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {
                      "src": {
                        "__iid": "44",
                        "__type": "CustomCode",
                        "code": ""data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAxMiA2IiBoZWlnaHQ9IjYiIHdpZHRoPSIxMiI+CjxwYXRoIGZpbGw9ImJsYWNrIiBkPSJNMC42MTIgMC4xMTJMNS44NDQgMy4yMkwxMS4wNzYgMC4xMTJWMi41TDUuODQ0IDUuNTI0TDAuNjEyIDIuNDg4VjAuMTEyWiIvPgo8L3N2Zz4K"",
                        "fallback": null,
                      },
                    },
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "45",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-self": "auto",
                        "display": "flex",
                        "flex-basis": "auto",
                        "flex-grow": "0",
                        "flex-shrink": "0",
                        "height": "30px",
                        "opacity": "0.1",
                        "width": "30px",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "42",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "54",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "61",
                      "__type": "CustomCode",
                      "code": "false",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "60",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "55",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "62",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "63",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "opacity": "0.5",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "58",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "65",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {
                      "src": {
                        "__iid": "66",
                        "__type": "CustomCode",
                        "code": ""data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAxMiA2IiBoZWlnaHQ9IjYiIHdpZHRoPSIxMiIgc3R5bGU9ImZpbGw6IHJnYigyMTMsIDIwNiwgMjA2KTsiPgo8cGF0aCBmaWxsPSJibGFjayIgZD0iTTAuNjEyIDAuMTEyTDUuODQ0IDMuMjJMMTEuMDc2IDAuMTEyVjIuNUw1Ljg0NCA1LjUyNEwwLjYxMiAyLjQ4OFYwLjExMloiLz4KPC9zdmc+"",
                        "fallback": null,
                      },
                    },
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "67",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "23",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
            ],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "38",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "other",
            "uuid": "UHmtTZpH-JE",
            "vsettings": [
              {
                "__iid": "68",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "69",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "align-items": "center",
                    "display": "flex",
                    "flex-basis": "auto",
                    "flex-direction": "row",
                    "flex-grow": "0",
                    "flex-shrink": "0",
                    "justify-content": "space-evenly",
                    "position": "relative",
                    "width": "45px",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "42",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "79",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "80",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "55",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          {
            "__iid": "81",
            "__type": "TplTag",
            "children": [],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "38",
              "__uuid": undefined,
            },
            "tag": "input",
            "type": "other",
            "uuid": "i9zTy8unM5S",
            "vsettings": [
              {
                "__iid": "82",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {
                  "placeholder": {
                    "__iid": "84",
                    "__type": "CustomCode",
                    "code": ""What needs to be done?"",
                    "fallback": null,
                  },
                  "type": {
                    "__iid": "83",
                    "__type": "CustomCode",
                    "code": ""text"",
                    "fallback": null,
                  },
                },
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "85",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "background": "linear-gradient(rgba(255,255,255,0), rgba(255,255,255,0))",
                    "border-bottom-width": "0px",
                    "border-left-width": "0px",
                    "border-right-width": "0px",
                    "border-top-width": "0px",
                    "display": "flex",
                    "flex-basis": "100%",
                    "flex-grow": "1",
                    "flex-shrink": "1",
                    "font-size": "24px",
                    "padding-bottom": "15px",
                    "padding-left": "15px",
                    "padding-right": "15px",
                    "padding-top": "15px",
                    "width": "auto",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "42",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "107",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "108",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "55",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "109",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "110",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "58",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "115",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {
                  "value": {
                    "__iid": "116",
                    "__type": "CustomCode",
                    "code": ""My Task"",
                    "fallback": null,
                  },
                },
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "117",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "color": "var(--token-ZFzJeGwT7yS)",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "23",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
        ],
        "codeGenType": "auto",
        "columnsSetting": null,
        "locked": null,
        "name": "header-container",
        "parent": null,
        "tag": "div",
        "type": "other",
        "uuid": "FlC9glvK4O3",
        "vsettings": [
          {
            "__iid": "122",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "123",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "align-items": "center",
                "box-shadow": "inset 0px -2px 1px 0px rgba(0,0,0,0.03)",
                "display": "flex",
                "flex-direction": "row",
                "justify-content": "space-evenly",
                "position": "relative",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "42",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "130",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "131",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "width": "100%",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "55",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "133",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "134",
              "__type": "RuleSet",
              "mixins": [],
              "values": {},
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "58",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "135",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "136",
              "__type": "RuleSet",
              "mixins": [],
              "values": {},
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "23",
                "__uuid": undefined,
              },
            ],
          },
        ],
      },
      "trapsFocus": false,
      "type": "plain",
      "updatedAt": null,
      "uuid": "KE8Bnx1C5NF",
      "variantGroups": [
        {
          "__iid": "57",
          "__type": "ComponentVariantGroup",
          "linkedState": {
            "__iidRef": "30107002",
            "__uuid": undefined,
          },
          "multi": false,
          "param": {
            "__iidRef": "35",
            "__uuid": undefined,
          },
          "type": "component",
          "uuid": "hYGLJmD_WMr",
          "variants": [
            {
              "__iid": "55",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "empty",
              "parent": {
                "__iidRef": "57",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "u1DOkEANbuQ",
            },
            {
              "__iid": "58",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "all-checked",
              "parent": {
                "__iidRef": "57",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "qnGDYG2-r2N",
            },
          ],
        },
      ],
      "variants": [
        {
          "__iid": "42",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "base",
          "parent": null,
          "selectors": null,
          "uuid": "qoPKnfB22H3",
        },
      ],
    },
    {
      "__iid": "159",
      "__type": "Component",
      "alwaysAutoName": false,
      "codeComponentMeta": null,
      "dataQueries": [],
      "editableByContentEditor": true,
      "figmaMappings": [],
      "hiddenFromContentEditor": false,
      "metadata": {},
      "name": "Task",
      "pageMeta": null,
      "params": [
        {
          "__iid": "160",
          "__type": "StateParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "state": {
            "__iidRef": "30107003",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656003",
            "__type": "AnyType",
            "name": "any",
          },
          "uuid": "ADWmU4H3oP",
          "variable": {
            "__iid": "161",
            "__type": "Var",
            "name": "State",
            "uuid": "LuHi-mNRn08",
          },
        },
        {
          "__iid": "163",
          "__type": "SlotParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "tplSlot": {
            "__iidRef": "257",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "165",
            "__type": "RenderableType",
            "allowRootWrapper": null,
            "name": "renderable",
            "params": [],
          },
          "uuid": "A-z2p1fg9M",
          "variable": {
            "__iid": "164",
            "__type": "Var",
            "name": "children",
            "uuid": "6xnTLxHUUCl",
          },
        },
        {
          "__iid": "56656016",
          "__type": "StateChangeHandlerParam",
          "about": null,
          "defaultExpr": null,
          "description": "EventHandler",
          "displayName": null,
          "enumValues": [],
          "exportType": "ToolsOnly",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": false,
          "state": {
            "__iidRef": "30107003",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656017",
            "__type": "FunctionType",
            "name": "func",
            "params": [
              {
                "__iid": "56656018",
                "__type": "ArgType",
                "argName": "val",
                "displayName": null,
                "name": "arg",
                "type": {
                  "__iid": "56656019",
                  "__type": "AnyType",
                  "name": "any",
                },
              },
            ],
          },
          "uuid": "BFz98d9uCzh",
          "variable": {
            "__iid": "56656020",
            "__type": "Var",
            "name": "On State change",
            "uuid": "P8LKSo-ZxD6",
          },
        },
      ],
      "plumeInfo": null,
      "serverQueries": [],
      "states": [
        {
          "__iid": "30107003",
          "__type": "VariantGroupState",
          "accessType": "private",
          "implicitState": null,
          "onChangeParam": {
            "__iidRef": "56656016",
            "__uuid": undefined,
          },
          "param": {
            "__iidRef": "160",
            "__uuid": undefined,
          },
          "tplNode": null,
          "variableType": "variant",
          "variantGroup": {
            "__iidRef": "185",
            "__uuid": undefined,
          },
        },
      ],
      "subComps": [],
      "superComp": null,
      "templateInfo": null,
      "tplTree": {
        "__iid": "166",
        "__type": "TplTag",
        "children": [
          {
            "__iid": "167",
            "__type": "TplTag",
            "children": [
              {
                "__iid": "168",
                "__type": "TplTag",
                "children": [
                  {
                    "__iid": "169",
                    "__type": "TplTag",
                    "children": [],
                    "codeGenType": "auto",
                    "columnsSetting": null,
                    "locked": null,
                    "name": null,
                    "parent": {
                      "__iidRef": "168",
                      "__uuid": undefined,
                    },
                    "tag": "img",
                    "type": "image",
                    "uuid": "EF0IIZmE0VZ",
                    "vsettings": [
                      {
                        "__iid": "170",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {
                          "src": {
                            "__iid": "173",
                            "__type": "CustomCode",
                            "code": ""data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxZW0iIGhlaWdodD0iMWVtIiB2aWV3Qm94PSIwIDAgNTEyIDUxMiIgc3Ryb2tlLXdpZHRoPSIwIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZT0iY3VycmVudENvbG9yIj48cGF0aCBkPSJNMTczLjg5OCA0MzkuNDA0bC0xNjYuNC0xNjYuNGMtOS45OTctOS45OTctOS45OTctMjYuMjA2IDAtMzYuMjA0bDM2LjIwMy0zNi4yMDRjOS45OTctOS45OTggMjYuMjA3LTkuOTk4IDM2LjIwNCAwTDE5MiAzMTIuNjkgNDMyLjA5NSA3Mi41OTZjOS45OTctOS45OTcgMjYuMjA3LTkuOTk3IDM2LjIwNCAwbDM2LjIwMyAzNi4yMDRjOS45OTcgOS45OTcgOS45OTcgMjYuMjA2IDAgMzYuMjA0bC0yOTQuNCAyOTQuNDAxYy05Ljk5OCA5Ljk5Ny0yNi4yMDcgOS45OTctMzYuMjA0LS4wMDF6Ii8+PC9zdmc+"",
                            "fallback": null,
                          },
                        },
                        "columnsConfig": null,
                        "dataCond": {
                          "__iid": "181",
                          "__type": "CustomCode",
                          "code": "false",
                          "fallback": null,
                        },
                        "dataRep": null,
                        "rs": {
                          "__iid": "174",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "display": "flex",
                            "height": "16px",
                            "left": "11.5px",
                            "position": "absolute",
                            "top": "12px",
                            "width": "16px",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "171",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "182",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {
                          "src": {
                            "__iid": "188",
                            "__type": "CustomCode",
                            "code": ""data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxZW0iIGhlaWdodD0iMWVtIiB2aWV3Qm94PSIwIDAgNTEyIDUxMiIgc3Ryb2tlLXdpZHRoPSIwIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHlsZT0iZmlsbDogcmdiKDQsIDE0MywgNjQpOyI+PHBhdGggZD0iTTE3My44OTggNDM5LjQwNGwtMTY2LjQtMTY2LjRjLTkuOTk3LTkuOTk3LTkuOTk3LTI2LjIwNiAwLTM2LjIwNGwzNi4yMDMtMzYuMjA0YzkuOTk3LTkuOTk4IDI2LjIwNy05Ljk5OCAzNi4yMDQgMEwxOTIgMzEyLjY5IDQzMi4wOTUgNzIuNTk2YzkuOTk3LTkuOTk3IDI2LjIwNy05Ljk5NyAzNi4yMDQgMGwzNi4yMDMgMzYuMjA0YzkuOTk3IDkuOTk3IDkuOTk3IDI2LjIwNiAwIDM2LjIwNGwtMjk0LjQgMjk0LjQwMWMtOS45OTggOS45OTctMjYuMjA3IDkuOTk3LTM2LjIwNC0uMDAxeiIvPjwvc3ZnPg=="",
                            "fallback": null,
                          },
                        },
                        "columnsConfig": null,
                        "dataCond": {
                          "__iid": "194",
                          "__type": "CustomCode",
                          "code": "true",
                          "fallback": null,
                        },
                        "dataRep": null,
                        "rs": {
                          "__iid": "189",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "height": "23px",
                            "left": "3px",
                            "top": "4px",
                            "width": "23px",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "183",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "195",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "198",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "196",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "199",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "200",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "186",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "201",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "202",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "183",
                            "__uuid": undefined,
                          },
                          {
                            "__iidRef": "23",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                ],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "167",
                  "__uuid": undefined,
                },
                "tag": "div",
                "type": "other",
                "uuid": "r596oqBuEOe",
                "vsettings": [
                  {
                    "__iid": "203",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "204",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-self": "auto",
                        "border-bottom-color": "var(--token-B25IVWS9LHD)",
                        "border-bottom-left-radius": "100px",
                        "border-bottom-right-radius": "100px",
                        "border-bottom-style": "solid",
                        "border-bottom-width": "1px",
                        "border-left-color": "var(--token-B25IVWS9LHD)",
                        "border-left-style": "solid",
                        "border-left-width": "1px",
                        "border-right-color": "var(--token-B25IVWS9LHD)",
                        "border-right-style": "solid",
                        "border-right-width": "1px",
                        "border-top-color": "var(--token-B25IVWS9LHD)",
                        "border-top-left-radius": "100px",
                        "border-top-right-radius": "100px",
                        "border-top-style": "solid",
                        "border-top-width": "1px",
                        "display": "block",
                        "flex-basis": "auto",
                        "flex-grow": "0",
                        "flex-shrink": "0",
                        "height": "30px",
                        "position": "relative",
                        "width": "30px",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "171",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "229",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "230",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "183",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "231",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "233",
                      "__type": "CustomCode",
                      "code": "false",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "232",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "186",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "234",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "235",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "border-bottom-color": "var(--token-Xqg3BSZm-z7)",
                        "border-left-color": "var(--token-Xqg3BSZm-z7)",
                        "border-right-color": "var(--token-Xqg3BSZm-z7)",
                        "border-top-color": "var(--token-Xqg3BSZm-z7)",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "183",
                        "__uuid": undefined,
                      },
                      {
                        "__iidRef": "23",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
            ],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "166",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "other",
            "uuid": "veOz84qVFMj",
            "vsettings": [
              {
                "__iid": "240",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "241",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "align-items": "center",
                    "display": "flex",
                    "flex-basis": "auto",
                    "flex-direction": "row",
                    "flex-grow": "0",
                    "flex-shrink": "0",
                    "justify-content": "center",
                    "position": "relative",
                    "width": "45px",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "171",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "251",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "252",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "186",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "253",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "254",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "186",
                    "__uuid": undefined,
                  },
                  {
                    "__iidRef": "23",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          {
            "__iid": "255",
            "__type": "TplTag",
            "children": [
              {
                "__iid": "256",
                "__type": "TplTag",
                "children": [
                  {
                    "__iid": "257",
                    "__type": "TplSlot",
                    "defaultContents": [
                      {
                        "__iid": "258",
                        "__type": "TplTag",
                        "children": [],
                        "codeGenType": "auto",
                        "columnsSetting": null,
                        "locked": null,
                        "name": null,
                        "parent": {
                          "__iidRef": "257",
                          "__uuid": undefined,
                        },
                        "tag": "div",
                        "type": "text",
                        "uuid": "VHgukjCJqAq",
                        "vsettings": [
                          {
                            "__iid": "259",
                            "__type": "VariantSetting",
                            "args": [],
                            "attrs": {},
                            "columnsConfig": null,
                            "dataCond": null,
                            "dataRep": null,
                            "rs": {
                              "__iid": "260",
                              "__type": "RuleSet",
                              "mixins": [],
                              "values": {},
                            },
                            "text": {
                              "__iid": "261",
                              "__type": "RawText",
                              "markers": [],
                              "text": "Some kind of text here",
                            },
                            "variants": [
                              {
                                "__iidRef": "171",
                                "__uuid": undefined,
                              },
                            ],
                          },
                        ],
                      },
                    ],
                    "locked": null,
                    "param": {
                      "__iidRef": "163",
                      "__uuid": undefined,
                    },
                    "parent": {
                      "__iidRef": "256",
                      "__uuid": undefined,
                    },
                    "uuid": "z6poyDcA6v7",
                    "vsettings": [
                      {
                        "__iid": "262",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "263",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "color": "var(--token-l6yLXpF-AHV)",
                            "font-size": "24px",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "171",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "266",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "267",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "color": "var(--token-UFX-Eo5zTMR)",
                            "text-decoration-line": "line-through",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "183",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "270",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "271",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "186",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "272",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "273",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "196",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "274",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "275",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "color": "var(--token-Xqg3BSZm-z7)",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "183",
                            "__uuid": undefined,
                          },
                          {
                            "__iidRef": "23",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "277",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "278",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "color": "var(--token-ZFzJeGwT7yS)",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "23",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                ],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "255",
                  "__uuid": undefined,
                },
                "tag": "div",
                "type": "other",
                "uuid": "UrTS556Q7hR",
                "vsettings": [
                  {
                    "__iid": "280",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "281",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-items": "center",
                        "display": "flex",
                        "flex-basis": "100%",
                        "flex-direction": "row",
                        "flex-grow": "1",
                        "flex-shrink": "1",
                        "justify-content": "flex-start",
                        "position": "relative",
                        "width": "auto",
                      },
                    },
                    "text": {
                      "__iid": "291",
                      "__type": "RawText",
                      "markers": [],
                      "text": "Some kind of text here",
                    },
                    "variants": [
                      {
                        "__iidRef": "171",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "292",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "293",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "183",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "294",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "296",
                      "__type": "CustomCode",
                      "code": "false",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "295",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "186",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "297",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "298",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "196",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
              {
                "__iid": "299",
                "__type": "TplTag",
                "children": [],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "255",
                  "__uuid": undefined,
                },
                "tag": "input",
                "type": "other",
                "uuid": "HNNA0bcjtWj",
                "vsettings": [
                  {
                    "__iid": "300",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {
                      "placeholder": {
                        "__iid": "302",
                        "__type": "CustomCode",
                        "code": ""Some placeholder text"",
                        "fallback": null,
                      },
                      "type": {
                        "__iid": "301",
                        "__type": "CustomCode",
                        "code": ""text"",
                        "fallback": null,
                      },
                    },
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "305",
                      "__type": "CustomCode",
                      "code": "false",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "303",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "display": "flex",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "171",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "306",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "322",
                      "__type": "CustomCode",
                      "code": "true",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "307",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "background": "linear-gradient(rgba(255,255,255,0), rgba(255,255,255,0))",
                        "border-bottom-color": "var(--token-aU3V66K_Sbv)",
                        "border-left-color": "var(--token-aU3V66K_Sbv)",
                        "border-right-color": "var(--token-aU3V66K_Sbv)",
                        "border-top-color": "var(--token-aU3V66K_Sbv)",
                        "box-shadow": "inset 0px 0px 4px 2px rgba(0,0,0,0.3)",
                        "flex-basis": "100%",
                        "flex-grow": "1",
                        "flex-shrink": "1",
                        "font-size": "24px",
                        "padding-bottom": "15px",
                        "padding-left": "15px",
                        "padding-top": "15px",
                        "width": "auto",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "186",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "323",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "324",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "box-shadow": "inset 0px 0px 4px 2px rgba(235,245,16,0.46)",
                        "color": "var(--token-ZFzJeGwT7yS)",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "186",
                        "__uuid": undefined,
                      },
                      {
                        "__iidRef": "23",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
              {
                "__iid": "338",
                "__type": "TplTag",
                "children": [
                  {
                    "__iid": "339",
                    "__type": "TplTag",
                    "children": [],
                    "codeGenType": "auto",
                    "columnsSetting": null,
                    "locked": null,
                    "name": null,
                    "parent": {
                      "__iidRef": "338",
                      "__uuid": undefined,
                    },
                    "tag": "button",
                    "type": "text",
                    "uuid": "CVEbQd2kprJ",
                    "vsettings": [
                      {
                        "__iid": "340",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "341",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "font-size": "30px",
                            "line-height": "1",
                            "opacity": "0",
                            "padding-left": "10px",
                            "padding-right": "10px",
                            "position": "relative",
                          },
                        },
                        "text": {
                          "__iid": "348",
                          "__type": "RawText",
                          "markers": [],
                          "text": "×",
                        },
                        "variants": [
                          {
                            "__iidRef": "171",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "349",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "350",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {
                            "border-bottom-width": "0px",
                            "border-left-width": "0px",
                            "border-right-width": "0px",
                            "border-top-width": "0px",
                            "cursor": "pointer",
                            "opacity": "0.5",
                          },
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "196",
                            "__uuid": undefined,
                          },
                        ],
                      },
                      {
                        "__iid": "357",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "358",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": null,
                        "variants": [
                          {
                            "__iidRef": "186",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                ],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "255",
                  "__uuid": undefined,
                },
                "tag": "div",
                "type": "other",
                "uuid": "ERmkDCrGS8K",
                "vsettings": [
                  {
                    "__iid": "359",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "360",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-items": "center",
                        "display": "flex",
                        "flex-direction": "row",
                        "justify-content": "space-evenly",
                        "position": "relative",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "171",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "366",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "368",
                      "__type": "CustomCode",
                      "code": "false",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "367",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "186",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
            ],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "166",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "other",
            "uuid": "IF_ev41YPrg",
            "vsettings": [
              {
                "__iid": "369",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "370",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "align-items": "center",
                    "display": "flex",
                    "flex-basis": "100%",
                    "flex-direction": "row",
                    "flex-grow": "1",
                    "flex-shrink": "1",
                    "justify-content": "space-evenly",
                    "padding-bottom": "15px",
                    "padding-left": "15px",
                    "padding-top": "15px",
                    "position": "relative",
                    "width": "auto",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "171",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "383",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "384",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "padding-bottom": "0px",
                    "padding-left": "0px",
                    "padding-top": "0px",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "186",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "388",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "389",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "23",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "390",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "391",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "186",
                    "__uuid": undefined,
                  },
                  {
                    "__iidRef": "23",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
        ],
        "codeGenType": "auto",
        "columnsSetting": null,
        "locked": null,
        "name": null,
        "parent": null,
        "tag": "div",
        "type": "other",
        "uuid": "jrz5s_XOxby",
        "vsettings": [
          {
            "__iid": "392",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "393",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "align-items": "center",
                "border-bottom-color": "var(--token-X2mEJhQLrGT)",
                "border-bottom-style": "solid",
                "border-bottom-width": "1px",
                "display": "flex",
                "flex-direction": "row",
                "justify-content": "space-evenly",
                "position": "relative",
                "width": "100%",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "171",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "403",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "404",
              "__type": "RuleSet",
              "mixins": [],
              "values": {},
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "183",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "405",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "406",
              "__type": "RuleSet",
              "mixins": [],
              "values": {},
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "186",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "407",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "408",
              "__type": "RuleSet",
              "mixins": [],
              "values": {},
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "196",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "409",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "410",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "border-bottom-color": "var(--token-k9QJ1TOiLxs)",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "23",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "412",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "413",
              "__type": "RuleSet",
              "mixins": [],
              "values": {},
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "186",
                "__uuid": undefined,
              },
              {
                "__iidRef": "23",
                "__uuid": undefined,
              },
            ],
          },
        ],
      },
      "trapsFocus": false,
      "type": "plain",
      "updatedAt": null,
      "uuid": "kFp_WuvhFM1",
      "variantGroups": [
        {
          "__iid": "185",
          "__type": "ComponentVariantGroup",
          "linkedState": {
            "__iidRef": "30107003",
            "__uuid": undefined,
          },
          "multi": false,
          "param": {
            "__iidRef": "160",
            "__uuid": undefined,
          },
          "type": "component",
          "uuid": "43RtZE6nT34",
          "variants": [
            {
              "__iid": "183",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "checked",
              "parent": {
                "__iidRef": "185",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "MJGFkao2MgA",
            },
            {
              "__iid": "186",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "editing",
              "parent": {
                "__iidRef": "185",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "BiyRgR0UtVP",
            },
          ],
        },
      ],
      "variants": [
        {
          "__iid": "171",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "base",
          "parent": null,
          "selectors": null,
          "uuid": "sJAdt3n86Ze",
        },
        {
          "__iid": "196",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "",
          "parent": null,
          "selectors": [
            ":hover",
          ],
          "uuid": "CeGgGNdBKL5",
        },
      ],
    },
    {
      "__iid": "478",
      "__type": "Component",
      "alwaysAutoName": false,
      "codeComponentMeta": null,
      "dataQueries": [],
      "editableByContentEditor": true,
      "figmaMappings": [],
      "hiddenFromContentEditor": false,
      "metadata": {},
      "name": "Footer",
      "pageMeta": null,
      "params": [
        {
          "__iid": "479",
          "__type": "StateParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "state": {
            "__iidRef": "30107004",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656004",
            "__type": "AnyType",
            "name": "any",
          },
          "uuid": "Ptq-L7x6kb",
          "variable": {
            "__iid": "480",
            "__type": "Var",
            "name": "State",
            "uuid": "t8wkCEUJjBt",
          },
        },
        {
          "__iid": "482",
          "__type": "SlotParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "tplSlot": {
            "__iidRef": "487",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "484",
            "__type": "RenderableType",
            "allowRootWrapper": null,
            "name": "renderable",
            "params": [],
          },
          "uuid": "3SdTQtCVgV",
          "variable": {
            "__iid": "483",
            "__type": "Var",
            "name": "count",
            "uuid": "y5lYRsgtHlJ",
          },
        },
        {
          "__iid": "56656021",
          "__type": "StateChangeHandlerParam",
          "about": null,
          "defaultExpr": null,
          "description": "EventHandler",
          "displayName": null,
          "enumValues": [],
          "exportType": "ToolsOnly",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": false,
          "state": {
            "__iidRef": "30107004",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656022",
            "__type": "FunctionType",
            "name": "func",
            "params": [
              {
                "__iid": "56656023",
                "__type": "ArgType",
                "argName": "val",
                "displayName": null,
                "name": "arg",
                "type": {
                  "__iid": "56656024",
                  "__type": "AnyType",
                  "name": "any",
                },
              },
            ],
          },
          "uuid": "gKetoRhytZn",
          "variable": {
            "__iid": "56656025",
            "__type": "Var",
            "name": "On State change",
            "uuid": "_O6SFfFzrFW",
          },
        },
      ],
      "plumeInfo": null,
      "serverQueries": [],
      "states": [
        {
          "__iid": "30107004",
          "__type": "VariantGroupState",
          "accessType": "private",
          "implicitState": null,
          "onChangeParam": {
            "__iidRef": "56656021",
            "__uuid": undefined,
          },
          "param": {
            "__iidRef": "479",
            "__uuid": undefined,
          },
          "tplNode": null,
          "variableType": "variant",
          "variantGroup": {
            "__iidRef": "500",
            "__uuid": undefined,
          },
        },
      ],
      "subComps": [],
      "superComp": null,
      "templateInfo": null,
      "tplTree": {
        "__iid": "485",
        "__type": "TplTag",
        "children": [
          {
            "__iid": "486",
            "__type": "TplTag",
            "children": [
              {
                "__iid": "487",
                "__type": "TplSlot",
                "defaultContents": [
                  {
                    "__iid": "488",
                    "__type": "TplTag",
                    "children": [],
                    "codeGenType": "auto",
                    "columnsSetting": null,
                    "locked": null,
                    "name": null,
                    "parent": {
                      "__iidRef": "487",
                      "__uuid": undefined,
                    },
                    "tag": "div",
                    "type": "text",
                    "uuid": "DuJDDLsWcbf",
                    "vsettings": [
                      {
                        "__iid": "489",
                        "__type": "VariantSetting",
                        "args": [],
                        "attrs": {},
                        "columnsConfig": null,
                        "dataCond": null,
                        "dataRep": null,
                        "rs": {
                          "__iid": "492",
                          "__type": "RuleSet",
                          "mixins": [],
                          "values": {},
                        },
                        "text": {
                          "__iid": "493",
                          "__type": "RawText",
                          "markers": [],
                          "text": "2",
                        },
                        "variants": [
                          {
                            "__iidRef": "490",
                            "__uuid": undefined,
                          },
                        ],
                      },
                    ],
                  },
                ],
                "locked": null,
                "param": {
                  "__iidRef": "482",
                  "__uuid": undefined,
                },
                "parent": {
                  "__iidRef": "486",
                  "__uuid": undefined,
                },
                "uuid": "kH0VP4Hd4dG",
                "vsettings": [
                  {
                    "__iid": "494",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "495",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "color": "var(--token-aU3V66K_Sbv)",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "490",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "497",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "505",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "498",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "506",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "507",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "501",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
              {
                "__iid": "508",
                "__type": "TplTag",
                "children": [],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "486",
                  "__uuid": undefined,
                },
                "tag": "div",
                "type": "text",
                "uuid": "spqAW_jfE9Y",
                "vsettings": [
                  {
                    "__iid": "509",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "510",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "color": "var(--token-aU3V66K_Sbv)",
                      },
                    },
                    "text": {
                      "__iid": "512",
                      "__type": "RawText",
                      "markers": [],
                      "text": " items left",
                    },
                    "variants": [
                      {
                        "__iidRef": "490",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "513",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "514",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "498",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "515",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": {
                      "__iid": "517",
                      "__type": "CustomCode",
                      "code": "true",
                      "fallback": null,
                    },
                    "dataRep": null,
                    "rs": {
                      "__iid": "516",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": {
                      "__iid": "518",
                      "__type": "RawText",
                      "markers": [],
                      "text": " item left",
                    },
                    "variants": [
                      {
                        "__iidRef": "501",
                        "__uuid": undefined,
                      },
                    ],
                  },
                  {
                    "__iid": "519",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "520",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "503",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
            ],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "485",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "other",
            "uuid": "mi9Hzs8juRb",
            "vsettings": [
              {
                "__iid": "521",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "522",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "align-items": "stretch",
                    "display": "flex",
                    "flex-direction": "row",
                    "justify-content": "flex-start",
                    "left": "15px",
                    "position": "absolute",
                    "top": "14px",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "490",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "530",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": {
                  "__iid": "533",
                  "__type": "CustomCode",
                  "code": "true",
                  "fallback": null,
                },
                "dataRep": null,
                "rs": {
                  "__iid": "531",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "left": "15px",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "501",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "534",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": {
                  "__iid": "536",
                  "__type": "CustomCode",
                  "code": "true",
                  "fallback": null,
                },
                "dataRep": null,
                "rs": {
                  "__iid": "535",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "498",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "537",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": {
                  "__iid": "539",
                  "__type": "CustomCode",
                  "code": "false",
                  "fallback": null,
                },
                "dataRep": null,
                "rs": {
                  "__iid": "538",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "503",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          {
            "__iid": "540",
            "__type": "TplTag",
            "children": [
              {
                "__iid": "541",
                "__type": "TplComponent",
                "component": {
                  "__iidRef": "542",
                  "__uuid": undefined,
                },
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "540",
                  "__uuid": undefined,
                },
                "uuid": "LJuZ-zq_rTb",
                "vsettings": [
                  {
                    "__iid": "634",
                    "__type": "VariantSetting",
                    "args": [
                      {
                        "__iid": "635",
                        "__type": "Arg",
                        "expr": {
                          "__iid": "22347004",
                          "__type": "VariantsRef",
                          "variants": [
                            {
                              "__iidRef": "565",
                              "__uuid": undefined,
                            },
                          ],
                        },
                        "param": {
                          "__iidRef": "543",
                          "__uuid": undefined,
                        },
                      },
                      {
                        "__iid": "637",
                        "__type": "Arg",
                        "expr": {
                          "__iid": "638",
                          "__type": "RenderExpr",
                          "tpl": [
                            {
                              "__iid": "639",
                              "__type": "TplTag",
                              "children": [],
                              "codeGenType": "auto",
                              "columnsSetting": null,
                              "locked": null,
                              "name": null,
                              "parent": {
                                "__iidRef": "541",
                                "__uuid": undefined,
                              },
                              "tag": "div",
                              "type": "text",
                              "uuid": "P8yHhYBtB8z",
                              "vsettings": [
                                {
                                  "__iid": "640",
                                  "__type": "VariantSetting",
                                  "args": [],
                                  "attrs": {},
                                  "columnsConfig": null,
                                  "dataCond": null,
                                  "dataRep": null,
                                  "rs": {
                                    "__iid": "641",
                                    "__type": "RuleSet",
                                    "mixins": [],
                                    "values": {},
                                  },
                                  "text": {
                                    "__iid": "642",
                                    "__type": "RawText",
                                    "markers": [],
                                    "text": "All",
                                  },
                                  "variants": [
                                    {
                                      "__iidRef": "490",
                                      "__uuid": undefined,
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        "param": {
                          "__iidRef": "546",
                          "__uuid": undefined,
                        },
                      },
                    ],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "643",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-self": "flex-start",
                        "flex-basis": "auto",
                        "flex-grow": "0",
                        "flex-shrink": "1",
                        "height": "auto",
                        "position": "relative",
                        "width": "auto",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "490",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
              {
                "__iid": "651",
                "__type": "TplComponent",
                "component": {
                  "__iidRef": "542",
                  "__uuid": undefined,
                },
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "540",
                  "__uuid": undefined,
                },
                "uuid": "mwuOQe3rMc8",
                "vsettings": [
                  {
                    "__iid": "652",
                    "__type": "VariantSetting",
                    "args": [
                      {
                        "__iid": "653",
                        "__type": "Arg",
                        "expr": {
                          "__iid": "654",
                          "__type": "RenderExpr",
                          "tpl": [
                            {
                              "__iid": "655",
                              "__type": "TplTag",
                              "children": [],
                              "codeGenType": "auto",
                              "columnsSetting": null,
                              "locked": null,
                              "name": null,
                              "parent": {
                                "__iidRef": "651",
                                "__uuid": undefined,
                              },
                              "tag": "div",
                              "type": "text",
                              "uuid": "qMLCRAAwpPu",
                              "vsettings": [
                                {
                                  "__iid": "656",
                                  "__type": "VariantSetting",
                                  "args": [],
                                  "attrs": {},
                                  "columnsConfig": null,
                                  "dataCond": null,
                                  "dataRep": null,
                                  "rs": {
                                    "__iid": "657",
                                    "__type": "RuleSet",
                                    "mixins": [],
                                    "values": {},
                                  },
                                  "text": {
                                    "__iid": "658",
                                    "__type": "RawText",
                                    "markers": [],
                                    "text": "Completed",
                                  },
                                  "variants": [
                                    {
                                      "__iidRef": "490",
                                      "__uuid": undefined,
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        "param": {
                          "__iidRef": "546",
                          "__uuid": undefined,
                        },
                      },
                    ],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "659",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-self": "flex-start",
                        "flex-basis": "auto",
                        "flex-grow": "0",
                        "flex-shrink": "1",
                        "height": "auto",
                        "position": "relative",
                        "width": "auto",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "490",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
              {
                "__iid": "667",
                "__type": "TplComponent",
                "component": {
                  "__iidRef": "542",
                  "__uuid": undefined,
                },
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "540",
                  "__uuid": undefined,
                },
                "uuid": "HAgAzNVNb6Q",
                "vsettings": [
                  {
                    "__iid": "668",
                    "__type": "VariantSetting",
                    "args": [
                      {
                        "__iid": "669",
                        "__type": "Arg",
                        "expr": {
                          "__iid": "670",
                          "__type": "RenderExpr",
                          "tpl": [
                            {
                              "__iid": "671",
                              "__type": "TplTag",
                              "children": [],
                              "codeGenType": "auto",
                              "columnsSetting": null,
                              "locked": null,
                              "name": null,
                              "parent": {
                                "__iidRef": "667",
                                "__uuid": undefined,
                              },
                              "tag": "div",
                              "type": "text",
                              "uuid": "LgCcZuptvGg",
                              "vsettings": [
                                {
                                  "__iid": "672",
                                  "__type": "VariantSetting",
                                  "args": [],
                                  "attrs": {},
                                  "columnsConfig": null,
                                  "dataCond": null,
                                  "dataRep": null,
                                  "rs": {
                                    "__iid": "673",
                                    "__type": "RuleSet",
                                    "mixins": [],
                                    "values": {},
                                  },
                                  "text": {
                                    "__iid": "674",
                                    "__type": "RawText",
                                    "markers": [],
                                    "text": "Active",
                                  },
                                  "variants": [
                                    {
                                      "__iidRef": "490",
                                      "__uuid": undefined,
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        "param": {
                          "__iidRef": "546",
                          "__uuid": undefined,
                        },
                      },
                    ],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "675",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {
                        "align-self": "flex-start",
                        "flex-basis": "auto",
                        "flex-grow": "0",
                        "flex-shrink": "1",
                        "height": "auto",
                        "position": "relative",
                        "width": "auto",
                      },
                    },
                    "text": null,
                    "variants": [
                      {
                        "__iidRef": "490",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
            ],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "485",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "other",
            "uuid": "dZA6WObfTPw",
            "vsettings": [
              {
                "__iid": "683",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "684",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "align-items": "stretch",
                    "display": "flex",
                    "flex-column-gap": "20px",
                    "flex-direction": "row",
                    "justify-content": "center",
                    "position": "relative",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "490",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
          {
            "__iid": "691",
            "__type": "TplTag",
            "children": [],
            "codeGenType": "auto",
            "columnsSetting": null,
            "locked": null,
            "name": null,
            "parent": {
              "__iidRef": "485",
              "__uuid": undefined,
            },
            "tag": "div",
            "type": "text",
            "uuid": "8HLf3WcLEk6",
            "vsettings": [
              {
                "__iid": "692",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": {
                  "__iid": "699",
                  "__type": "CustomCode",
                  "code": "false",
                  "fallback": null,
                },
                "dataRep": null,
                "rs": {
                  "__iid": "693",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "color": "var(--token-aU3V66K_Sbv)",
                    "cursor": "pointer",
                    "left": "371px",
                    "position": "absolute",
                    "top": "14px",
                  },
                },
                "text": {
                  "__iid": "700",
                  "__type": "RawText",
                  "markers": [],
                  "text": "Clear completed",
                },
                "variants": [
                  {
                    "__iidRef": "490",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "706",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": {
                  "__iid": "710",
                  "__type": "CustomCode",
                  "code": "true",
                  "fallback": null,
                },
                "dataRep": null,
                "rs": {
                  "__iid": "707",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "left": "auto",
                    "right": "15px",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "498",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
        ],
        "codeGenType": "auto",
        "columnsSetting": null,
        "locked": null,
        "name": "footer-container",
        "parent": null,
        "tag": "div",
        "type": "other",
        "uuid": "QP-xZzYMsjS",
        "vsettings": [
          {
            "__iid": "711",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "712",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "align-items": "center",
                "display": "flex",
                "flex-direction": "row",
                "justify-content": "center",
                "padding-bottom": "10px",
                "padding-left": "15px",
                "padding-right": "15px",
                "padding-top": "10px",
                "position": "relative",
                "width": "100%",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "490",
                "__uuid": undefined,
              },
            ],
          },
        ],
      },
      "trapsFocus": false,
      "type": "plain",
      "updatedAt": null,
      "uuid": "CNLtgAlMgEz",
      "variantGroups": [
        {
          "__iid": "500",
          "__type": "ComponentVariantGroup",
          "linkedState": {
            "__iidRef": "30107004",
            "__uuid": undefined,
          },
          "multi": true,
          "param": {
            "__iidRef": "479",
            "__uuid": undefined,
          },
          "type": "component",
          "uuid": "LdFdeR2ZRwp",
          "variants": [
            {
              "__iid": "498",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "HasCompleted",
              "parent": {
                "__iidRef": "500",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "AR2Ovy94u8A",
            },
            {
              "__iid": "501",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "singular-left",
              "parent": {
                "__iidRef": "500",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "g8t-4xvcYUw",
            },
            {
              "__iid": "503",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "empty",
              "parent": {
                "__iidRef": "500",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "rQSLhLe97YB",
            },
          ],
        },
      ],
      "variants": [
        {
          "__iid": "490",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "base",
          "parent": null,
          "selectors": null,
          "uuid": "q2QvVpF6eCe",
        },
      ],
    },
    {
      "__iid": "542",
      "__type": "Component",
      "alwaysAutoName": false,
      "codeComponentMeta": null,
      "dataQueries": [],
      "editableByContentEditor": true,
      "figmaMappings": [],
      "hiddenFromContentEditor": false,
      "metadata": {},
      "name": "ToggleButton",
      "pageMeta": null,
      "params": [
        {
          "__iid": "543",
          "__type": "StateParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "state": {
            "__iidRef": "30107005",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656005",
            "__type": "AnyType",
            "name": "any",
          },
          "uuid": "FxL5uJm7Mf",
          "variable": {
            "__iid": "544",
            "__type": "Var",
            "name": "State",
            "uuid": "Y5N9NGu_22J",
          },
        },
        {
          "__iid": "546",
          "__type": "SlotParam",
          "about": null,
          "defaultExpr": null,
          "description": "",
          "displayName": null,
          "enumValues": [],
          "exportType": "External",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": true,
          "tplSlot": {
            "__iidRef": "550",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "548",
            "__type": "RenderableType",
            "allowRootWrapper": null,
            "name": "renderable",
            "params": [],
          },
          "uuid": "7FXuA7BTAD",
          "variable": {
            "__iid": "547",
            "__type": "Var",
            "name": "children",
            "uuid": "EMMOSAZ59Jj",
          },
        },
        {
          "__iid": "56656026",
          "__type": "StateChangeHandlerParam",
          "about": null,
          "defaultExpr": null,
          "description": "EventHandler",
          "displayName": null,
          "enumValues": [],
          "exportType": "ToolsOnly",
          "isLocalizable": false,
          "isMainContentSlot": false,
          "isRepeated": null,
          "mergeWithParent": false,
          "origin": null,
          "previewExpr": null,
          "propEffect": null,
          "required": false,
          "state": {
            "__iidRef": "30107005",
            "__uuid": undefined,
          },
          "type": {
            "__iid": "56656027",
            "__type": "FunctionType",
            "name": "func",
            "params": [
              {
                "__iid": "56656028",
                "__type": "ArgType",
                "argName": "val",
                "displayName": null,
                "name": "arg",
                "type": {
                  "__iid": "56656029",
                  "__type": "AnyType",
                  "name": "any",
                },
              },
            ],
          },
          "uuid": "ghFD69VoGgR",
          "variable": {
            "__iid": "56656030",
            "__type": "Var",
            "name": "On State change",
            "uuid": "BbjKDaoNnID",
          },
        },
      ],
      "plumeInfo": null,
      "serverQueries": [],
      "states": [
        {
          "__iid": "30107005",
          "__type": "VariantGroupState",
          "accessType": "private",
          "implicitState": null,
          "onChangeParam": {
            "__iidRef": "56656026",
            "__uuid": undefined,
          },
          "param": {
            "__iidRef": "543",
            "__uuid": undefined,
          },
          "tplNode": null,
          "variableType": "variant",
          "variantGroup": {
            "__iidRef": "567",
            "__uuid": undefined,
          },
        },
      ],
      "subComps": [],
      "superComp": null,
      "templateInfo": null,
      "tplTree": {
        "__iid": "549",
        "__type": "TplTag",
        "children": [
          {
            "__iid": "550",
            "__type": "TplSlot",
            "defaultContents": [
              {
                "__iid": "551",
                "__type": "TplTag",
                "children": [],
                "codeGenType": "auto",
                "columnsSetting": null,
                "locked": null,
                "name": null,
                "parent": {
                  "__iidRef": "550",
                  "__uuid": undefined,
                },
                "tag": "div",
                "type": "text",
                "uuid": "B5DVJy0AQRj",
                "vsettings": [
                  {
                    "__iid": "552",
                    "__type": "VariantSetting",
                    "args": [],
                    "attrs": {},
                    "columnsConfig": null,
                    "dataCond": null,
                    "dataRep": null,
                    "rs": {
                      "__iid": "555",
                      "__type": "RuleSet",
                      "mixins": [],
                      "values": {},
                    },
                    "text": {
                      "__iid": "556",
                      "__type": "RawText",
                      "markers": [],
                      "text": "All",
                    },
                    "variants": [
                      {
                        "__iidRef": "553",
                        "__uuid": undefined,
                      },
                    ],
                  },
                ],
              },
            ],
            "locked": null,
            "param": {
              "__iidRef": "546",
              "__uuid": undefined,
            },
            "parent": {
              "__iidRef": "549",
              "__uuid": undefined,
            },
            "uuid": "Z8LgC6FF-mu",
            "vsettings": [
              {
                "__iid": "557",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "558",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {
                    "color": "var(--token-aU3V66K_Sbv)",
                  },
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "553",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "560",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "563",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "561",
                    "__uuid": undefined,
                  },
                ],
              },
              {
                "__iid": "564",
                "__type": "VariantSetting",
                "args": [],
                "attrs": {},
                "columnsConfig": null,
                "dataCond": null,
                "dataRep": null,
                "rs": {
                  "__iid": "568",
                  "__type": "RuleSet",
                  "mixins": [],
                  "values": {},
                },
                "text": null,
                "variants": [
                  {
                    "__iidRef": "565",
                    "__uuid": undefined,
                  },
                ],
              },
            ],
          },
        ],
        "codeGenType": "auto",
        "columnsSetting": null,
        "locked": null,
        "name": null,
        "parent": null,
        "tag": "div",
        "type": "other",
        "uuid": "hOhdvw4rRcv",
        "vsettings": [
          {
            "__iid": "569",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "570",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "align-items": "center",
                "border-bottom-color": "rgba(0,0,0,0)",
                "border-bottom-left-radius": "3px",
                "border-bottom-right-radius": "3px",
                "border-bottom-style": "solid",
                "border-bottom-width": "1px",
                "border-left-color": "rgba(0,0,0,0)",
                "border-left-style": "solid",
                "border-left-width": "1px",
                "border-right-color": "rgba(0,0,0,0)",
                "border-right-style": "solid",
                "border-right-width": "1px",
                "border-top-color": "rgba(0,0,0,0)",
                "border-top-left-radius": "3px",
                "border-top-right-radius": "3px",
                "border-top-style": "solid",
                "border-top-width": "1px",
                "display": "flex",
                "flex-direction": "row",
                "justify-content": "space-around",
                "padding-bottom": "3px",
                "padding-left": "7px",
                "padding-right": "7px",
                "padding-top": "3px",
                "position": "relative",
              },
            },
            "text": {
              "__iid": "596",
              "__type": "RawText",
              "markers": [],
              "text": "All",
            },
            "variants": [
              {
                "__iidRef": "553",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "597",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "598",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "border-bottom-color": "var(--token-Qs14Urh8AOG)",
                "border-bottom-left-radius": "3px",
                "border-bottom-right-radius": "3px",
                "border-bottom-style": "solid",
                "border-bottom-width": "1px",
                "border-left-color": "var(--token-Qs14Urh8AOG)",
                "border-left-style": "solid",
                "border-left-width": "1px",
                "border-right-color": "var(--token-Qs14Urh8AOG)",
                "border-right-style": "solid",
                "border-right-width": "1px",
                "border-top-color": "var(--token-Qs14Urh8AOG)",
                "border-top-left-radius": "3px",
                "border-top-right-radius": "3px",
                "border-top-style": "solid",
                "border-top-width": "1px",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "561",
                "__uuid": undefined,
              },
            ],
          },
          {
            "__iid": "615",
            "__type": "VariantSetting",
            "args": [],
            "attrs": {},
            "columnsConfig": null,
            "dataCond": null,
            "dataRep": null,
            "rs": {
              "__iid": "616",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "border-bottom-color": "rgba(175,47,47,0.2)",
                "border-bottom-left-radius": "3px",
                "border-bottom-right-radius": "3px",
                "border-bottom-style": "solid",
                "border-bottom-width": "1px",
                "border-left-color": "rgba(175,47,47,0.2)",
                "border-left-style": "solid",
                "border-left-width": "1px",
                "border-right-color": "rgba(175,47,47,0.2)",
                "border-right-style": "solid",
                "border-right-width": "1px",
                "border-top-color": "rgba(175,47,47,0.2)",
                "border-top-left-radius": "3px",
                "border-top-right-radius": "3px",
                "border-top-style": "solid",
                "border-top-width": "1px",
                "cursor": "pointer",
              },
            },
            "text": null,
            "variants": [
              {
                "__iidRef": "565",
                "__uuid": undefined,
              },
            ],
          },
        ],
      },
      "trapsFocus": false,
      "type": "plain",
      "updatedAt": null,
      "uuid": "rt5mF9Fs8S0",
      "variantGroups": [
        {
          "__iid": "567",
          "__type": "ComponentVariantGroup",
          "linkedState": {
            "__iidRef": "30107005",
            "__uuid": undefined,
          },
          "multi": false,
          "param": {
            "__iidRef": "543",
            "__uuid": undefined,
          },
          "type": "component",
          "uuid": "pWWQHvHip6D",
          "variants": [
            {
              "__iid": "565",
              "__type": "Variant",
              "codeComponentName": null,
              "codeComponentVariantKeys": null,
              "description": null,
              "forTpl": null,
              "mediaQuery": null,
              "name": "Selected",
              "parent": {
                "__iidRef": "567",
                "__uuid": undefined,
              },
              "selectors": null,
              "uuid": "egUZysHrviX",
            },
          ],
        },
      ],
      "variants": [
        {
          "__iid": "553",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "base",
          "parent": null,
          "selectors": null,
          "uuid": "Y3U-2j28S-V",
        },
        {
          "__iid": "561",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "",
          "parent": null,
          "selectors": [
            ":hover",
          ],
          "uuid": "9_a2YxXirvS",
        },
      ],
    },
  ],
  "customFunctions": [],
  "defaultComponents": {},
  "defaultPageRoleId": null,
  "flags": {
    "usePlasmicImg": true,
  },
  "globalContexts": [],
  "globalVariant": {
    "__iid": "849",
    "__type": "Variant",
    "codeComponentName": null,
    "codeComponentVariantKeys": null,
    "description": null,
    "forTpl": null,
    "mediaQuery": null,
    "name": "base",
    "parent": null,
    "selectors": null,
    "uuid": "Qoau-29-b3J",
  },
  "globalVariantGroups": [
    {
      "__iid": "893",
      "__type": "GlobalVariantGroup",
      "multi": false,
      "param": {
        "__iid": "894",
        "__type": "GlobalVariantGroupParam",
        "about": null,
        "defaultExpr": null,
        "description": "",
        "displayName": null,
        "enumValues": [],
        "exportType": "External",
        "isLocalizable": false,
        "isMainContentSlot": false,
        "isRepeated": null,
        "mergeWithParent": false,
        "origin": null,
        "previewExpr": null,
        "propEffect": null,
        "required": true,
        "type": {
          "__iid": "896",
          "__type": "Text",
          "name": "text",
        },
        "uuid": "mrtVeNXn0R1",
        "variable": {
          "__iid": "895",
          "__type": "Var",
          "name": "Screen",
          "uuid": "dNYpWY07NcY",
        },
      },
      "type": "global-screen",
      "uuid": "jGMCdJLMpLs",
      "variants": [],
    },
    {
      "__iid": "25",
      "__type": "GlobalVariantGroup",
      "multi": false,
      "param": {
        "__iid": "26",
        "__type": "GlobalVariantGroupParam",
        "about": null,
        "defaultExpr": null,
        "description": "",
        "displayName": null,
        "enumValues": [],
        "exportType": "External",
        "isLocalizable": false,
        "isMainContentSlot": false,
        "isRepeated": null,
        "mergeWithParent": false,
        "origin": null,
        "previewExpr": null,
        "propEffect": null,
        "required": true,
        "type": {
          "__iid": "28",
          "__type": "Text",
          "name": "text",
        },
        "uuid": "9ZPq7uE_0u",
        "variable": {
          "__iid": "27",
          "__type": "Var",
          "name": "Theme",
          "uuid": "d8VLFdO3qSu",
        },
      },
      "type": "global-user-defined",
      "uuid": "GNzB0X5kuIv",
      "variants": [
        {
          "__iid": "23",
          "__type": "Variant",
          "codeComponentName": null,
          "codeComponentVariantKeys": null,
          "description": null,
          "forTpl": null,
          "mediaQuery": null,
          "name": "Dark",
          "parent": {
            "__iidRef": "25",
            "__uuid": undefined,
          },
          "selectors": null,
          "uuid": "nrFPZhhb9z9",
        },
      ],
    },
  ],
  "hostLessPackageInfo": null,
  "imageAssets": [],
  "mixins": [
    {
      "__iid": "757",
      "__type": "Mixin",
      "forTheme": false,
      "name": "card-shadow",
      "preview": null,
      "rs": {
        "__iid": "758",
        "__type": "RuleSet",
        "mixins": [],
        "values": {
          "box-shadow": "0px 2px 4px 0px rgba(0,0,0,0.2)",
        },
      },
      "uuid": "K-vdUj0Mdxv",
      "variantedRs": [],
    },
    {
      "__iid": "822",
      "__type": "Mixin",
      "forTheme": false,
      "name": "app-shadow",
      "preview": null,
      "rs": {
        "__iid": "823",
        "__type": "RuleSet",
        "mixins": [],
        "values": {
          "box-shadow": "0px 25px 50px 0px rgba(0,0,0,0.1)",
        },
      },
      "uuid": "G1Dy6tHCdEg",
      "variantedRs": [],
    },
    {
      "__iid": "763",
      "__type": "Mixin",
      "forTheme": false,
      "name": "card-shadow-dark",
      "preview": null,
      "rs": {
        "__iid": "764",
        "__type": "RuleSet",
        "mixins": [],
        "values": {
          "box-shadow": "0px 0px 2px 0px rgba(255,255,255,0.5)",
        },
      },
      "uuid": "NvPGmleWyZY",
      "variantedRs": [],
    },
    {
      "__iid": "827",
      "__type": "Mixin",
      "forTheme": false,
      "name": "app-shadow-dark",
      "preview": null,
      "rs": {
        "__iid": "828",
        "__type": "RuleSet",
        "mixins": [],
        "values": {
          "box-shadow": "0px 25px 50px 0px rgba(255,255,255,0.3)",
        },
      },
      "uuid": "5CKSyk9uoTa",
      "variantedRs": [],
    },
  ],
  "pageArenas": [],
  "pageWrapper": null,
  "projectDependencies": [],
  "splits": [],
  "styleTokens": [
    {
      "__iid": "897",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "header",
      "regKey": null,
      "type": "Color",
      "uuid": "2TqFcBopNcN",
      "value": "rgba(175,47,47,0.15)",
      "variantedValues": [],
    },
    {
      "__iid": "898",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "page bg",
      "regKey": null,
      "type": "Color",
      "uuid": "8QpUfX_gwS2",
      "value": "rgb(245,245,245)",
      "variantedValues": [],
    },
    {
      "__iid": "899",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "app bg",
      "regKey": null,
      "type": "Color",
      "uuid": "vkNNUxlF69T",
      "value": "#ffffff",
      "variantedValues": [],
    },
    {
      "__iid": "900",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "unselected chevron",
      "regKey": null,
      "type": "Color",
      "uuid": "B25IVWS9LHD",
      "value": "rgb(230,230,230)",
      "variantedValues": [],
    },
    {
      "__iid": "901",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "input placeholder",
      "regKey": null,
      "type": "Color",
      "uuid": "Qs14Urh8AOG",
      "value": "rgb(230,230,230)",
      "variantedValues": [],
    },
    {
      "__iid": "902",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "footer-text",
      "regKey": null,
      "type": "Color",
      "uuid": "aU3V66K_Sbv",
      "value": "rgb(119,119,119)",
      "variantedValues": [],
    },
    {
      "__iid": "903",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "app bg dark",
      "regKey": null,
      "type": "Color",
      "uuid": "-OrU7rp_dHe",
      "value": "rgb(34,34,34)",
      "variantedValues": [],
    },
    {
      "__iid": "904",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "page bg dark",
      "regKey": null,
      "type": "Color",
      "uuid": "WuIxxo7bvas",
      "value": "rgb(0,0,0)",
      "variantedValues": [],
    },
    {
      "__iid": "905",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "header dark",
      "regKey": null,
      "type": "Color",
      "uuid": "a-AKFYtMSR5",
      "value": "rgb(113,101,101)",
      "variantedValues": [],
    },
    {
      "__iid": "906",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "border gray light",
      "regKey": null,
      "type": "Color",
      "uuid": "X2mEJhQLrGT",
      "value": "rgb(230,230,230)",
      "variantedValues": [],
    },
    {
      "__iid": "907",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "border gray light dark",
      "regKey": null,
      "type": "Color",
      "uuid": "k9QJ1TOiLxs",
      "value": "rgb(48,48,48)",
      "variantedValues": [],
    },
    {
      "__iid": "908",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "task-text",
      "regKey": null,
      "type": "Color",
      "uuid": "l6yLXpF-AHV",
      "value": "rgb(83,83,83)",
      "variantedValues": [],
    },
    {
      "__iid": "909",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "task-text-dark",
      "regKey": null,
      "type": "Color",
      "uuid": "ZFzJeGwT7yS",
      "value": "rgb(234,234,234)",
      "variantedValues": [],
    },
    {
      "__iid": "910",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "task-text-dim",
      "regKey": null,
      "type": "Color",
      "uuid": "UFX-Eo5zTMR",
      "value": "rgb(230,230,230)",
      "variantedValues": [],
    },
    {
      "__iid": "911",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "task-text-dim-dark",
      "regKey": null,
      "type": "Color",
      "uuid": "Xqg3BSZm-z7",
      "value": "rgb(79,79,79)",
      "variantedValues": [],
    },
    {
      "__iid": "912",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "placeholder",
      "regKey": null,
      "type": "Color",
      "uuid": "dCYtinDJZQZ",
      "value": "rgb(230,230,230)",
      "variantedValues": [],
    },
    {
      "__iid": "913",
      "__type": "StyleToken",
      "isRegistered": false,
      "name": "placeholder-dark",
      "regKey": null,
      "type": "Color",
      "uuid": "8V6hzsRwIFr",
      "value": "rgb(79,79,79)",
      "variantedValues": [],
    },
  ],
  "themes": [
    {
      "__iid": "914",
      "__type": "Theme",
      "active": true,
      "addItemPrefs": {},
      "defaultStyle": {
        "__iid": "915",
        "__type": "Mixin",
        "forTheme": true,
        "name": "Default Theme",
        "preview": null,
        "rs": {
          "__iid": "916",
          "__type": "RuleSet",
          "mixins": [],
          "values": {
            "color": "#535353",
            "font-family": "Roboto",
            "font-size": "14px",
            "font-style": "normal",
            "font-weight": "normal",
            "letter-spacing": "normal",
            "line-height": "1.5",
            "text-align": "left",
            "text-transform": "none",
            "white-space": "pre-wrap",
          },
        },
        "uuid": "gLNZz3x1FgI",
        "variantedRs": [],
      },
      "layout": null,
      "styles": [
        {
          "__iid": "18255001",
          "__type": "ThemeStyle",
          "selector": "h1",
          "style": {
            "__iid": "18255002",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "h1"",
            "preview": null,
            "rs": {
              "__iid": "18255003",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#000000",
                "font-family": "Inter",
                "font-size": "72px",
                "font-weight": "900",
                "letter-spacing": "-4px",
                "line-height": "1",
              },
            },
            "uuid": "59N1pK-Akc8",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255010",
          "__type": "ThemeStyle",
          "selector": "h2",
          "style": {
            "__iid": "18255011",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "h2"",
            "preview": null,
            "rs": {
              "__iid": "18255012",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#000000",
                "font-family": "Inter",
                "font-size": "48px",
                "font-weight": "700",
                "letter-spacing": "-1px",
                "line-height": "1.1",
              },
            },
            "uuid": "HlHgyrN3gD-",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255019",
          "__type": "ThemeStyle",
          "selector": "h3",
          "style": {
            "__iid": "18255020",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "h3"",
            "preview": null,
            "rs": {
              "__iid": "18255021",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#000000",
                "font-family": "Inter",
                "font-size": "32px",
                "font-weight": "600",
                "letter-spacing": "-0.8px",
                "line-height": "1.2",
              },
            },
            "uuid": "mbwJg7D75W1",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255028",
          "__type": "ThemeStyle",
          "selector": "h4",
          "style": {
            "__iid": "18255029",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "h4"",
            "preview": null,
            "rs": {
              "__iid": "18255030",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#000000",
                "font-family": "Inter",
                "font-size": "24px",
                "font-weight": "600",
                "letter-spacing": "-0.5px",
                "line-height": "1.3",
              },
            },
            "uuid": "DeQ5miPPwO5",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255037",
          "__type": "ThemeStyle",
          "selector": "h5",
          "style": {
            "__iid": "18255038",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "h5"",
            "preview": null,
            "rs": {
              "__iid": "18255039",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#000000",
                "font-family": "Inter",
                "font-size": "20px",
                "font-weight": "600",
                "letter-spacing": "-0.3px",
                "line-height": "1.5",
              },
            },
            "uuid": "sUU36yQ91Mf",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255046",
          "__type": "ThemeStyle",
          "selector": "h6",
          "style": {
            "__iid": "18255047",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "h6"",
            "preview": null,
            "rs": {
              "__iid": "18255048",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#000000",
                "font-family": "Inter",
                "font-size": "16px",
                "font-weight": "600",
                "line-height": "1.5",
              },
            },
            "uuid": "wA592URYpVF",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255054",
          "__type": "ThemeStyle",
          "selector": "a",
          "style": {
            "__iid": "18255055",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "a"",
            "preview": null,
            "rs": {
              "__iid": "18255056",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#0070f3",
              },
            },
            "uuid": "3HZbnnmOOSy",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255058",
          "__type": "ThemeStyle",
          "selector": "a:hover",
          "style": {
            "__iid": "18255059",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "a:hover"",
            "preview": null,
            "rs": {
              "__iid": "18255060",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "color": "#3291ff",
              },
            },
            "uuid": "1j-8LkTQgJz",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255062",
          "__type": "ThemeStyle",
          "selector": "blockquote",
          "style": {
            "__iid": "18255063",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "blockquote"",
            "preview": null,
            "rs": {
              "__iid": "18255064",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "border-left-color": "#dddddd",
                "border-left-style": "solid",
                "border-left-width": "3px",
                "color": "#888888",
                "padding-left": "10px",
              },
            },
            "uuid": "MdPaa9w9P-R",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255070",
          "__type": "ThemeStyle",
          "selector": "code",
          "style": {
            "__iid": "18255071",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "code"",
            "preview": null,
            "rs": {
              "__iid": "18255072",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "background": "linear-gradient(#f8f8f8, #f8f8f8)",
                "border-bottom-color": "#dddddd",
                "border-bottom-left-radius": "3px",
                "border-bottom-right-radius": "3px",
                "border-bottom-style": "solid",
                "border-bottom-width": "1px",
                "border-left-color": "#dddddd",
                "border-left-style": "solid",
                "border-left-width": "1px",
                "border-right-color": "#dddddd",
                "border-right-style": "solid",
                "border-right-width": "1px",
                "border-top-color": "#dddddd",
                "border-top-left-radius": "3px",
                "border-top-right-radius": "3px",
                "border-top-style": "solid",
                "border-top-width": "1px",
                "font-family": "Inconsolata",
                "padding-bottom": "1px",
                "padding-left": "4px",
                "padding-right": "4px",
                "padding-top": "1px",
              },
            },
            "uuid": "gZp2naIqA-a",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255095",
          "__type": "ThemeStyle",
          "selector": "pre",
          "style": {
            "__iid": "18255096",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "pre"",
            "preview": null,
            "rs": {
              "__iid": "18255097",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "background": "linear-gradient(#f8f8f8, #f8f8f8)",
                "border-bottom-color": "#dddddd",
                "border-bottom-left-radius": "3px",
                "border-bottom-right-radius": "3px",
                "border-bottom-style": "solid",
                "border-bottom-width": "1px",
                "border-left-color": "#dddddd",
                "border-left-style": "solid",
                "border-left-width": "1px",
                "border-right-color": "#dddddd",
                "border-right-style": "solid",
                "border-right-width": "1px",
                "border-top-color": "#dddddd",
                "border-top-left-radius": "3px",
                "border-top-right-radius": "3px",
                "border-top-style": "solid",
                "border-top-width": "1px",
                "font-family": "Inconsolata",
                "padding-bottom": "3px",
                "padding-left": "6px",
                "padding-right": "6px",
                "padding-top": "3px",
              },
            },
            "uuid": "5geycbvZH6n",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255120",
          "__type": "ThemeStyle",
          "selector": "ol",
          "style": {
            "__iid": "18255121",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "ol"",
            "preview": null,
            "rs": {
              "__iid": "18255122",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "align-items": "stretch",
                "display": "flex",
                "flex-direction": "column",
                "justify-content": "flex-start",
                "list-style-position": "outside",
                "list-style-type": "decimal",
                "padding-left": "40px",
                "position": "relative",
              },
            },
            "uuid": "QcmsTySwk-z",
            "variantedRs": [],
          },
        },
        {
          "__iid": "18255131",
          "__type": "ThemeStyle",
          "selector": "ul",
          "style": {
            "__iid": "18255132",
            "__type": "Mixin",
            "forTheme": true,
            "name": "Default "ul"",
            "preview": null,
            "rs": {
              "__iid": "18255133",
              "__type": "RuleSet",
              "mixins": [],
              "values": {
                "align-items": "stretch",
                "display": "flex",
                "flex-direction": "column",
                "justify-content": "flex-start",
                "list-style-position": "outside",
                "list-style-type": "disc",
                "padding-left": "40px",
                "position": "relative",
              },
            },
            "uuid": "QRHC1gEaDj_",
            "variantedRs": [],
          },
        },
      ],
    },
  ],
  "userManagedFonts": [],
}
`;
