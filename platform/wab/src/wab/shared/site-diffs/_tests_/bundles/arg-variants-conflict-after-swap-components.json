{"branches": [{"id": "8AbpysoczKDnbRUNtbdTUr", "name": "branch-01"}], "pkgVersions": [{"id": "e59019e7-b245-4e0c-b862-91a12b98b3c5", "data": {"root": "N1ERkITzhyF3", "map": {"7PHEmfJGxHjS": {"name": "title", "uuid": "QaQn0jIUO6kS", "__type": "Var"}, "32lom0eugKee": {"name": "text", "__type": "Text"}, "hEm6HdNYoD-4": {"type": {"__ref": "32lom0eugKee"}, "variable": {"__ref": "7PHEmfJGxHjS"}, "uuid": "ygpxNwi_aJQQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "HlyK0h3svzuJ": {"name": "description", "uuid": "c4ZC4yrjFkJh", "__type": "Var"}, "AvM3xPQ18vTA": {"name": "text", "__type": "Text"}, "aYY2nGeprec4": {"type": {"__ref": "AvM3xPQ18vTA"}, "variable": {"__ref": "HlyK0h3svzuJ"}, "uuid": "y385-0cot9rz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "QnDibX19w1nR": {"name": "image", "uuid": "IT6Il7XL3CDk", "__type": "Var"}, "RMNoqQrMnaap": {"name": "img", "__type": "Img"}, "YSki8zrzW8zi": {"type": {"__ref": "RMNoqQrMnaap"}, "variable": {"__ref": "QnDibX19w1nR"}, "uuid": "FFst2abo5nqv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "p6BeJdEcH3Uv": {"name": "canonical", "uuid": "qmIN47KQ1oGL", "__type": "Var"}, "NfqE8BGWPpmV": {"name": "text", "__type": "Text"}, "iVzEOhuIP-tT": {"type": {"__ref": "NfqE8BGWPpmV"}, "variable": {"__ref": "p6BeJdEcH3Uv"}, "uuid": "rIGjdljpN-Ur", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "U5F1Y_hc34FA": {"uuid": "IONqSxknC8VD", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "UnwhoyYV4pzN": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "REIhFQGA-LOM": {"variants": [{"__ref": "U5F1Y_hc34FA"}], "args": [], "attrs": {}, "rs": {"__ref": "UnwhoyYV4pzN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HpqOG9J0YLGu": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "JwKYzzbFJQsD", "parent": null, "locked": null, "vsettings": [{"__ref": "REIhFQGA-LOM"}], "__type": "TplTag"}, "JbqX6A9STJxM": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "ZeTMh1wSNr1j": {"uuid": "FMl9MXW343mh", "name": "hostless-plasmic-head", "params": [{"__ref": "hEm6HdNYoD-4"}, {"__ref": "aYY2nGeprec4"}, {"__ref": "YSki8zrzW8zi"}, {"__ref": "iVzEOhuIP-tT"}], "states": [], "tplTree": {"__ref": "HpqOG9J0YLGu"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "U5F1Y_hc34FA"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "JbqX6A9STJxM"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "2yFT2Ecm7sYf": {"name": "dataOp", "uuid": "FG8v4W4LOvDj", "__type": "Var"}, "-25zd0csoh0x": {"name": "any", "__type": "AnyType"}, "rrpjQDEUGbko": {"type": {"__ref": "-25zd0csoh0x"}, "variable": {"__ref": "2yFT2Ecm7sYf"}, "uuid": "hRRDrICMq_qH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "PVxxZcETlGDo": {"name": "name", "uuid": "0WFUcr9-hssj", "__type": "Var"}, "k0SfW4gHI5Vd": {"name": "text", "__type": "Text"}, "maVckXb3fNVg": {"type": {"__ref": "k0SfW4gHI5Vd"}, "variable": {"__ref": "PVxxZcETlGDo"}, "uuid": "_xBlbY1mTfKR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "kT95uuqztLEJ": {"name": "children", "uuid": "Ybr7wlv1Od_u", "__type": "Var"}, "PxgicnRYw5Ua": {"name": "any", "__type": "AnyType"}, "IyGw61VQxOG1": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "PxgicnRYw5Ua"}, "__type": "ArgType"}, "VMukqNXSaD-5": {"name": "renderFunc", "params": [{"__ref": "IyGw61VQxOG1"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "_ydazMpnwC2s": {"type": {"__ref": "VMukqNXSaD-5"}, "tplSlot": {"__ref": "wxoWTlZmi9Sj"}, "variable": {"__ref": "kT95uuqztLEJ"}, "uuid": "wVrOUxw7gSxW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "-YzMmdoYyT33": {"name": "pageSize", "uuid": "-ii5FqGHqggy", "__type": "Var"}, "hkpvWmkACLfN": {"name": "num", "__type": "<PERSON><PERSON>"}, "LR42WRZXv0af": {"type": {"__ref": "hkpvWmkACLfN"}, "variable": {"__ref": "-YzMmdoYyT33"}, "uuid": "o5N5NGyz_IdX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "yeP9IDZQTgGg": {"name": "pageIndex", "uuid": "34SE7tDgsM97", "__type": "Var"}, "s95aG043gB52": {"name": "num", "__type": "<PERSON><PERSON>"}, "-rm0bvP3JDR6": {"type": {"__ref": "s95aG043gB52"}, "variable": {"__ref": "yeP9IDZQTgGg"}, "uuid": "SeBvyDWJHu8u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "jLl_ZNCAJro7": {"uuid": "27sBioZ13lio", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "CTXGi5dBJcYP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NYAeJ8KJwVzP": {"variants": [{"__ref": "jLl_ZNCAJro7"}], "args": [], "attrs": {}, "rs": {"__ref": "CTXGi5dBJcYP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wxoWTlZmi9Sj": {"param": {"__ref": "_ydazMpnwC2s"}, "defaultContents": [], "uuid": "EvH9AkIpfvpz", "parent": {"__ref": "uURfef4do3f5"}, "locked": null, "vsettings": [{"__ref": "NYAeJ8KJwVzP"}], "__type": "TplSlot"}, "XlMfs_JVflOS": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "EC3crTp8FE_m": {"variants": [{"__ref": "jLl_ZNCAJro7"}], "args": [], "attrs": {}, "rs": {"__ref": "XlMfs_JVflOS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uURfef4do3f5": {"tag": "div", "name": null, "children": [{"__ref": "wxoWTlZmi9Sj"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3jKKmIENkxkI", "parent": null, "locked": null, "vsettings": [{"__ref": "EC3crTp8FE_m"}], "__type": "TplTag"}, "OwCsLkbl6sEz": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "0GJqiU6xYXkO": {"uuid": "m_SwUqOMMuwB", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "rrpjQDEUGbko"}, {"__ref": "maVckXb3fNVg"}, {"__ref": "_ydazMpnwC2s"}, {"__ref": "LR42WRZXv0af"}, {"__ref": "-rm0bvP3JDR6"}], "states": [], "tplTree": {"__ref": "uURfef4do3f5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "jLl_ZNCAJro7"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "OwCsLkbl6sEz"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "Vm0vIzIM0Zd4": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "AWlJjU969Uj3": {"name": "Default Typography", "rs": {"__ref": "Vm0vIzIM0Zd4"}, "preview": null, "uuid": "rDkNRZPp0PCN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "d7SIn4kbRG1f": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "uEbL0oP0y1W0": {"name": "Default \"h1\"", "rs": {"__ref": "d7SIn4kbRG1f"}, "preview": null, "uuid": "LVAM1PbzMhpr", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "tUSTgf8cbcG3": {"selector": "h1", "style": {"__ref": "uEbL0oP0y1W0"}, "__type": "ThemeStyle"}, "cQL6wOLnhI2J": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "Ij4adfmPQqZq": {"name": "Default \"h2\"", "rs": {"__ref": "cQL6wOLnhI2J"}, "preview": null, "uuid": "o6hPzH4YZudd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "YP9uysYA21fD": {"selector": "h2", "style": {"__ref": "Ij4adfmPQqZq"}, "__type": "ThemeStyle"}, "zCU2HsP8olD_": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "pJ2xShm0meOe": {"name": "Default \"a\"", "rs": {"__ref": "zCU2HsP8olD_"}, "preview": null, "uuid": "YH7_sbyaLK6n", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "UMRpTwCQD6cB": {"selector": "a", "style": {"__ref": "pJ2xShm0meOe"}, "__type": "ThemeStyle"}, "sWycs_s_CbWM": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "3fjxww1mpcaH": {"name": "Default \"h3\"", "rs": {"__ref": "sWycs_s_CbWM"}, "preview": null, "uuid": "F_-XbgJy4usC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Zvxd-zaIv9bT": {"selector": "h3", "style": {"__ref": "3fjxww1mpcaH"}, "__type": "ThemeStyle"}, "H0tJvY5os1js": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "pWlGmwiiVyrK": {"name": "Default \"h4\"", "rs": {"__ref": "H0tJvY5os1js"}, "preview": null, "uuid": "i_x_W6NnxDoe", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ll0qYAuxfpQK": {"selector": "h4", "style": {"__ref": "pWlGmwiiVyrK"}, "__type": "ThemeStyle"}, "zStySgp5bao8": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "pzVV4j6xIgYU": {"name": "Default \"code\"", "rs": {"__ref": "zStySgp5bao8"}, "preview": null, "uuid": "QRnI88y7jc4T", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "M4oDe05wnHB6": {"selector": "code", "style": {"__ref": "pzVV4j6xIgYU"}, "__type": "ThemeStyle"}, "Zu2hdUZ1TiiA": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "WKYmTtPklI83": {"name": "Default \"blockquote\"", "rs": {"__ref": "Zu2hdUZ1TiiA"}, "preview": null, "uuid": "6r3iG3H0NzAE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_z3UzJWBBvUz": {"selector": "blockquote", "style": {"__ref": "WKYmTtPklI83"}, "__type": "ThemeStyle"}, "D7mYEvG-jOn7": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "0QRmBmr5VubE": {"name": "Default \"pre\"", "rs": {"__ref": "D7mYEvG-jOn7"}, "preview": null, "uuid": "LHsd_VYjrQRq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "uQDMjECtcrM_": {"selector": "pre", "style": {"__ref": "0QRmBmr5VubE"}, "__type": "ThemeStyle"}, "2Tq79masESEw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "ySMOA2cx8LeG": {"name": "Default \"ul\"", "rs": {"__ref": "2Tq79masESEw"}, "preview": null, "uuid": "z-uNDGj_VqX5", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "J877YFzivJ_s": {"selector": "ul", "style": {"__ref": "ySMOA2cx8LeG"}, "__type": "ThemeStyle"}, "GVRzFdyGDWlw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "nbz3IgHBSJVs": {"name": "Default \"ol\"", "rs": {"__ref": "GVRzFdyGDWlw"}, "preview": null, "uuid": "LIkD8Sd4XLpv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "OCKqa7fwxOmD": {"selector": "ol", "style": {"__ref": "nbz3IgHBSJVs"}, "__type": "ThemeStyle"}, "4aIHY2pSaaai": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8V_paFA0NHLY": {"name": "Default \"h5\"", "rs": {"__ref": "4aIHY2pSaaai"}, "preview": null, "uuid": "c51XDH4Ff3jG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HmToX_hbZSd4": {"selector": "h5", "style": {"__ref": "8V_paFA0NHLY"}, "__type": "ThemeStyle"}, "N_GGwifZlR3l": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "EJFWKh1jQmMs": {"name": "Default \"h6\"", "rs": {"__ref": "N_GGwifZlR3l"}, "preview": null, "uuid": "OlQ4L5SDAoAD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nA2cSg55mXOh": {"selector": "h6", "style": {"__ref": "EJFWKh1jQmMs"}, "__type": "ThemeStyle"}, "2wRhvBQOTxpG": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "9bc97ig-StTW": {"name": "Default \"a:hover\"", "rs": {"__ref": "2wRhvBQOTxpG"}, "preview": null, "uuid": "edk1uZQcMn3Q", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "aWl-01RS9pXO": {"selector": "a:hover", "style": {"__ref": "9bc97ig-StTW"}, "__type": "ThemeStyle"}, "3yDXIrfATE4h": {"values": {}, "mixins": [], "__type": "RuleSet"}, "It8y_9mCAo2d": {"name": "Default \"li\"", "rs": {"__ref": "3yDXIrfATE4h"}, "preview": null, "uuid": "jDivs2o7TFmu", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "mHf9JjBpLedi": {"selector": "li", "style": {"__ref": "It8y_9mCAo2d"}, "__type": "ThemeStyle"}, "lIfaCrNlEiAl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pm_wCgGpBMaW": {"name": "Default \"p\"", "rs": {"__ref": "lIfaCrNlEiAl"}, "preview": null, "uuid": "kaP7wvdDUgx9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7FFkGefJd-aR": {"selector": "p", "style": {"__ref": "pm_wCgGpBMaW"}, "__type": "ThemeStyle"}, "-5nNiOx1pT5H": {"defaultStyle": {"__ref": "AWlJjU969Uj3"}, "styles": [{"__ref": "tUSTgf8cbcG3"}, {"__ref": "YP9uysYA21fD"}, {"__ref": "UMRpTwCQD6cB"}, {"__ref": "Zvxd-zaIv9bT"}, {"__ref": "Ll0qYAuxfpQK"}, {"__ref": "M4oDe05wnHB6"}, {"__ref": "_z3UzJWBBvUz"}, {"__ref": "uQDMjECtcrM_"}, {"__ref": "J877YFzivJ_s"}, {"__ref": "OCKqa7fwxOmD"}, {"__ref": "HmToX_hbZSd4"}, {"__ref": "nA2cSg55mXOh"}, {"__ref": "aWl-01RS9pXO"}, {"__ref": "mHf9JjBpLedi"}, {"__ref": "7FFkGefJd-aR"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "Vc-b2EEf6Msz": {"name": "Screen", "uuid": "ocJG6zqfCPXw", "__type": "Var"}, "TTnflj7emO9M": {"name": "text", "__type": "Text"}, "pykUTAmUp1QQ": {"type": {"__ref": "TTnflj7emO9M"}, "variable": {"__ref": "Vc-b2EEf6Msz"}, "uuid": "KN-t1AS0dUGa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "I20v_f2yXbce": {"uuid": "JYN8SozuKJGW", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "mlIaiXIH5-nH"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "mlIaiXIH5-nH": {"type": "global-screen", "param": {"__ref": "pykUTAmUp1QQ"}, "uuid": "63rGzUum7sjo", "variants": [{"__ref": "I20v_f2yXbce"}], "multi": true, "__type": "GlobalVariantGroup"}, "DFezqTM7r2fD": {"uuid": "iHHjoocfITo0", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "klGPLiElBirW": {"components": [{"__ref": "ZeTMh1wSNr1j"}, {"__ref": "0GJqiU6xYXkO"}, {"__ref": "e3R0Sbu2LGxH"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "kj4hGQddKynK"}], "globalVariantGroups": [{"__ref": "mlIaiXIH5-nH"}], "userManagedFonts": [], "globalVariant": {"__ref": "DFezqTM7r2fD"}, "styleTokens": [], "mixins": [{"__ref": "k8K-NBeNWgnx"}], "themes": [{"__ref": "-5nNiOx1pT5H"}], "activeTheme": {"__ref": "-5nNiOx1pT5H"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "mlIaiXIH5-nH"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "k8K-NBeNWgnx": {"name": "Unnamed Style preset", "rs": {"__ref": "WqcudSe3EHsC"}, "preview": null, "uuid": "rnzOzhqFvved", "forTheme": false, "variantedRs": [], "__type": "Mixin"}, "WqcudSe3EHsC": {"values": {"font-style": "italic"}, "mixins": [], "__type": "RuleSet"}, "e3R0Sbu2LGxH": {"uuid": "306yiYFbtx1Q", "name": "TestComponent New", "params": [{"__ref": "HuwDQw5kObPc"}, {"__ref": "JruW22hhds64"}], "states": [{"__ref": "IxkUaI9H5rE3"}], "tplTree": {"__ref": "NuNhpbLhfB_a"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "AgbdwfKZfkz5"}, {"__ref": "T_cMvuRpLv4c"}], "variantGroups": [{"__ref": "70COgJcHb6aa"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "kj4hGQddKynK": {"component": {"__ref": "e3R0Sbu2LGxH"}, "matrix": {"__ref": "H_tn0bZB5mFb"}, "customMatrix": {"__ref": "6PktfE_qsUq5"}, "__type": "ComponentArena"}, "HuwDQw5kObPc": {"type": {"__ref": "St0QyFxDcFB7"}, "state": {"__ref": "IxkUaI9H5rE3"}, "variable": {"__ref": "5YoVRQnbZ4yU"}, "uuid": "gJJToqrGomSO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "JruW22hhds64": {"type": {"__ref": "85GCg_A0oPsT"}, "state": {"__ref": "IxkUaI9H5rE3"}, "variable": {"__ref": "dpRX5rT_MrvY"}, "uuid": "5bM1PlaZxXfA", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "IxkUaI9H5rE3": {"variantGroup": {"__ref": "70COgJcHb6aa"}, "param": {"__ref": "HuwDQw5kObPc"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "JruW22hhds64"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "NuNhpbLhfB_a": {"tag": "div", "name": null, "children": [{"__ref": "La1cYjy5lCBA"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "EahFuFLOxCV7", "parent": null, "locked": null, "vsettings": [{"__ref": "ezVDEydbSv-X"}, {"__ref": "NBLrNs_zF4qX"}, {"__ref": "kkdRO2zDTpeh"}], "__type": "TplTag"}, "AgbdwfKZfkz5": {"uuid": "sIGorW4K24zI", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "T_cMvuRpLv4c": {"uuid": "7b51U4k9gpYE", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "70COgJcHb6aa": {"type": "component", "param": {"__ref": "HuwDQw5kObPc"}, "linkedState": {"__ref": "IxkUaI9H5rE3"}, "uuid": "RWaKcbF7-MEX", "variants": [{"__ref": "Xo2l6544zG2a"}], "multi": false, "__type": "ComponentVariantGroup"}, "H_tn0bZB5mFb": {"rows": [{"__ref": "pGFPF0-jodaW"}, {"__ref": "-1tPN5A8QNJn"}], "__type": "ArenaFrameGrid"}, "6PktfE_qsUq5": {"rows": [{"__ref": "IjwlInPQcKfq"}], "__type": "ArenaFrameGrid"}, "St0QyFxDcFB7": {"name": "text", "__type": "Text"}, "5YoVRQnbZ4yU": {"name": "isActive", "uuid": "3zT4hXa0Zkgv", "__type": "Var"}, "85GCg_A0oPsT": {"name": "func", "params": [{"__ref": "x9qRABXs_eMI"}], "__type": "FunctionType"}, "dpRX5rT_MrvY": {"name": "On isActive change", "uuid": "nBET7O8wnRGo", "__type": "Var"}, "La1cYjy5lCBA": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "aF9rOonplscq", "parent": {"__ref": "NuNhpbLhfB_a"}, "locked": null, "vsettings": [{"__ref": "vl5lleDvqxNq"}, {"__ref": "ZtJlgXKRccoS"}], "__type": "TplTag"}, "ezVDEydbSv-X": {"variants": [{"__ref": "AgbdwfKZfkz5"}], "args": [], "attrs": {}, "rs": {"__ref": "IvdAwV2kfeBP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NBLrNs_zF4qX": {"variants": [{"__ref": "Xo2l6544zG2a"}], "args": [], "attrs": {}, "rs": {"__ref": "ubMYuutnr2ia"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kkdRO2zDTpeh": {"variants": [{"__ref": "T_cMvuRpLv4c"}], "args": [], "attrs": {}, "rs": {"__ref": "aPmtsb2-r0Em"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Xo2l6544zG2a": {"uuid": "WCTksEgcfBU8", "name": "isActive", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "70COgJcHb6aa"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "pGFPF0-jodaW": {"cols": [{"__ref": "KRld5yOEIFaO"}, {"__ref": "5ZKresTSiL7A"}], "rowKey": null, "__type": "ArenaFrameRow"}, "-1tPN5A8QNJn": {"cols": [{"__ref": "voJizqGc5hpt"}], "rowKey": {"__ref": "70COgJcHb6aa"}, "__type": "ArenaFrameRow"}, "IjwlInPQcKfq": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "x9qRABXs_eMI": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "0IEoeNlvs7BB"}, "__type": "ArgType"}, "vl5lleDvqxNq": {"variants": [{"__ref": "AgbdwfKZfkz5"}], "args": [], "attrs": {}, "rs": {"__ref": "vyY3HI9dVBdH"}, "dataCond": null, "dataRep": null, "text": {"__ref": "QdLaEB10WIO1"}, "columnsConfig": null, "__type": "VariantSetting"}, "ZtJlgXKRccoS": {"variants": [{"__ref": "T_cMvuRpLv4c"}], "args": [], "attrs": {}, "rs": {"__ref": "A_f40G5L_EVC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IvdAwV2kfeBP": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "center", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "ubMYuutnr2ia": {"values": {"background": "linear-gradient(#FFEEDD, #FFEEDD)"}, "mixins": [], "__type": "RuleSet"}, "aPmtsb2-r0Em": {"values": {}, "mixins": [], "__type": "RuleSet"}, "KRld5yOEIFaO": {"frame": {"__ref": "DIpISIWgdllj"}, "cellKey": {"__ref": "AgbdwfKZfkz5"}, "__type": "ArenaFrameCell"}, "5ZKresTSiL7A": {"frame": {"__ref": "81L_Ysy9eVJH"}, "cellKey": {"__ref": "T_cMvuRpLv4c"}, "__type": "ArenaFrameCell"}, "voJizqGc5hpt": {"frame": {"__ref": "v8cyILfq_FLE"}, "cellKey": {"__ref": "Xo2l6544zG2a"}, "__type": "ArenaFrameCell"}, "0IEoeNlvs7BB": {"name": "any", "__type": "AnyType"}, "vyY3HI9dVBdH": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "QdLaEB10WIO1": {"markers": [], "text": "Sample text", "__type": "RawText"}, "A_f40G5L_EVC": {"values": {"color": "#0005FF"}, "mixins": [], "__type": "RuleSet"}, "DIpISIWgdllj": {"uuid": "9Exg65y1YLHw", "width": 1180, "height": 540, "container": {"__ref": "O0ezb9YwfOtG"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "AgbdwfKZfkz5"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "81L_Ysy9eVJH": {"uuid": "24cUQFSQGpUG", "width": 1180, "height": 540, "container": {"__ref": "oKJH5H1AWW4W"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "T_cMvuRpLv4c"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "v8cyILfq_FLE": {"uuid": "Be_XgveiJQ4n", "width": 1180, "height": 540, "container": {"__ref": "nPhmUhTuB98Z"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Xo2l6544zG2a"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "O0ezb9YwfOtG": {"name": null, "component": {"__ref": "e3R0Sbu2LGxH"}, "uuid": "rrP9JQMKuJXi", "parent": null, "locked": null, "vsettings": [{"__ref": "7JHvaWBWbyZw"}], "__type": "TplComponent"}, "oKJH5H1AWW4W": {"name": null, "component": {"__ref": "e3R0Sbu2LGxH"}, "uuid": "M-IxT7GPPMSC", "parent": null, "locked": null, "vsettings": [{"__ref": "Bpcu63JWFePv"}], "__type": "TplComponent"}, "nPhmUhTuB98Z": {"name": null, "component": {"__ref": "e3R0Sbu2LGxH"}, "uuid": "xPf8skEtVnF4", "parent": null, "locked": null, "vsettings": [{"__ref": "mzEI-MvrFiCf"}], "__type": "TplComponent"}, "7JHvaWBWbyZw": {"variants": [{"__ref": "DFezqTM7r2fD"}], "args": [], "attrs": {}, "rs": {"__ref": "WvkZUkHBw5ON"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Bpcu63JWFePv": {"variants": [{"__ref": "DFezqTM7r2fD"}], "args": [], "attrs": {}, "rs": {"__ref": "dt94uliF64JA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mzEI-MvrFiCf": {"variants": [{"__ref": "DFezqTM7r2fD"}], "args": [], "attrs": {}, "rs": {"__ref": "p1M2ToTUaqSW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "WvkZUkHBw5ON": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dt94uliF64JA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "p1M2ToTUaqSW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "N1ERkITzhyF3": {"uuid": "dnheIQXnGVEj", "pkgId": "4095423e-e27c-4538-aa8d-f6e14bc8dab0", "projectId": "wHtfg2nLs5zqybWknqMXbm", "version": "6.0.0", "name": "Parent Project", "site": {"__ref": "klGPLiElBirW"}, "__type": "ProjectDependency"}}, "deps": [], "version": "246-add-component-updated-at"}, "projectId": "wHtfg2nLs5zqybWknqMXbm", "version": "6.0.0", "branchId": "main"}, {"id": "a1a67f8a-ec8a-402a-87da-eb8b7ebf18e8", "data": {"root": "8KVzgECeyObU", "map": {"7PHEmfJGxHjS": {"name": "title", "uuid": "QaQn0jIUO6kS", "__type": "Var"}, "32lom0eugKee": {"name": "text", "__type": "Text"}, "hEm6HdNYoD-4": {"type": {"__ref": "32lom0eugKee"}, "variable": {"__ref": "7PHEmfJGxHjS"}, "uuid": "ygpxNwi_aJQQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "HlyK0h3svzuJ": {"name": "description", "uuid": "c4ZC4yrjFkJh", "__type": "Var"}, "AvM3xPQ18vTA": {"name": "text", "__type": "Text"}, "aYY2nGeprec4": {"type": {"__ref": "AvM3xPQ18vTA"}, "variable": {"__ref": "HlyK0h3svzuJ"}, "uuid": "y385-0cot9rz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "QnDibX19w1nR": {"name": "image", "uuid": "IT6Il7XL3CDk", "__type": "Var"}, "RMNoqQrMnaap": {"name": "img", "__type": "Img"}, "YSki8zrzW8zi": {"type": {"__ref": "RMNoqQrMnaap"}, "variable": {"__ref": "QnDibX19w1nR"}, "uuid": "FFst2abo5nqv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "p6BeJdEcH3Uv": {"name": "canonical", "uuid": "qmIN47KQ1oGL", "__type": "Var"}, "NfqE8BGWPpmV": {"name": "text", "__type": "Text"}, "iVzEOhuIP-tT": {"type": {"__ref": "NfqE8BGWPpmV"}, "variable": {"__ref": "p6BeJdEcH3Uv"}, "uuid": "rIGjdljpN-Ur", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "U5F1Y_hc34FA": {"uuid": "IONqSxknC8VD", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "UnwhoyYV4pzN": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "REIhFQGA-LOM": {"variants": [{"__ref": "U5F1Y_hc34FA"}], "args": [], "attrs": {}, "rs": {"__ref": "UnwhoyYV4pzN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HpqOG9J0YLGu": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "JwKYzzbFJQsD", "parent": null, "locked": null, "vsettings": [{"__ref": "REIhFQGA-LOM"}], "__type": "TplTag"}, "JbqX6A9STJxM": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "ZeTMh1wSNr1j": {"uuid": "FMl9MXW343mh", "name": "hostless-plasmic-head", "params": [{"__ref": "hEm6HdNYoD-4"}, {"__ref": "aYY2nGeprec4"}, {"__ref": "YSki8zrzW8zi"}, {"__ref": "iVzEOhuIP-tT"}], "states": [], "tplTree": {"__ref": "HpqOG9J0YLGu"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "U5F1Y_hc34FA"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "JbqX6A9STJxM"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "2yFT2Ecm7sYf": {"name": "dataOp", "uuid": "FG8v4W4LOvDj", "__type": "Var"}, "-25zd0csoh0x": {"name": "any", "__type": "AnyType"}, "rrpjQDEUGbko": {"type": {"__ref": "-25zd0csoh0x"}, "variable": {"__ref": "2yFT2Ecm7sYf"}, "uuid": "hRRDrICMq_qH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "PVxxZcETlGDo": {"name": "name", "uuid": "0WFUcr9-hssj", "__type": "Var"}, "k0SfW4gHI5Vd": {"name": "text", "__type": "Text"}, "maVckXb3fNVg": {"type": {"__ref": "k0SfW4gHI5Vd"}, "variable": {"__ref": "PVxxZcETlGDo"}, "uuid": "_xBlbY1mTfKR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "kT95uuqztLEJ": {"name": "children", "uuid": "Ybr7wlv1Od_u", "__type": "Var"}, "PxgicnRYw5Ua": {"name": "any", "__type": "AnyType"}, "IyGw61VQxOG1": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "PxgicnRYw5Ua"}, "__type": "ArgType"}, "VMukqNXSaD-5": {"name": "renderFunc", "params": [{"__ref": "IyGw61VQxOG1"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "_ydazMpnwC2s": {"type": {"__ref": "VMukqNXSaD-5"}, "tplSlot": {"__ref": "wxoWTlZmi9Sj"}, "variable": {"__ref": "kT95uuqztLEJ"}, "uuid": "wVrOUxw7gSxW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "-YzMmdoYyT33": {"name": "pageSize", "uuid": "-ii5FqGHqggy", "__type": "Var"}, "hkpvWmkACLfN": {"name": "num", "__type": "<PERSON><PERSON>"}, "LR42WRZXv0af": {"type": {"__ref": "hkpvWmkACLfN"}, "variable": {"__ref": "-YzMmdoYyT33"}, "uuid": "o5N5NGyz_IdX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "yeP9IDZQTgGg": {"name": "pageIndex", "uuid": "34SE7tDgsM97", "__type": "Var"}, "s95aG043gB52": {"name": "num", "__type": "<PERSON><PERSON>"}, "-rm0bvP3JDR6": {"type": {"__ref": "s95aG043gB52"}, "variable": {"__ref": "yeP9IDZQTgGg"}, "uuid": "SeBvyDWJHu8u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "jLl_ZNCAJro7": {"uuid": "27sBioZ13lio", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "CTXGi5dBJcYP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NYAeJ8KJwVzP": {"variants": [{"__ref": "jLl_ZNCAJro7"}], "args": [], "attrs": {}, "rs": {"__ref": "CTXGi5dBJcYP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wxoWTlZmi9Sj": {"param": {"__ref": "_ydazMpnwC2s"}, "defaultContents": [], "uuid": "EvH9AkIpfvpz", "parent": {"__ref": "uURfef4do3f5"}, "locked": null, "vsettings": [{"__ref": "NYAeJ8KJwVzP"}], "__type": "TplSlot"}, "XlMfs_JVflOS": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "EC3crTp8FE_m": {"variants": [{"__ref": "jLl_ZNCAJro7"}], "args": [], "attrs": {}, "rs": {"__ref": "XlMfs_JVflOS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uURfef4do3f5": {"tag": "div", "name": null, "children": [{"__ref": "wxoWTlZmi9Sj"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3jKKmIENkxkI", "parent": null, "locked": null, "vsettings": [{"__ref": "EC3crTp8FE_m"}], "__type": "TplTag"}, "OwCsLkbl6sEz": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "0GJqiU6xYXkO": {"uuid": "m_SwUqOMMuwB", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "rrpjQDEUGbko"}, {"__ref": "maVckXb3fNVg"}, {"__ref": "_ydazMpnwC2s"}, {"__ref": "LR42WRZXv0af"}, {"__ref": "-rm0bvP3JDR6"}], "states": [], "tplTree": {"__ref": "uURfef4do3f5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "jLl_ZNCAJro7"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "OwCsLkbl6sEz"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "Vm0vIzIM0Zd4": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "AWlJjU969Uj3": {"name": "Default Typography", "rs": {"__ref": "Vm0vIzIM0Zd4"}, "preview": null, "uuid": "rDkNRZPp0PCN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "d7SIn4kbRG1f": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "uEbL0oP0y1W0": {"name": "Default \"h1\"", "rs": {"__ref": "d7SIn4kbRG1f"}, "preview": null, "uuid": "LVAM1PbzMhpr", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "tUSTgf8cbcG3": {"selector": "h1", "style": {"__ref": "uEbL0oP0y1W0"}, "__type": "ThemeStyle"}, "cQL6wOLnhI2J": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "Ij4adfmPQqZq": {"name": "Default \"h2\"", "rs": {"__ref": "cQL6wOLnhI2J"}, "preview": null, "uuid": "o6hPzH4YZudd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "YP9uysYA21fD": {"selector": "h2", "style": {"__ref": "Ij4adfmPQqZq"}, "__type": "ThemeStyle"}, "zCU2HsP8olD_": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "pJ2xShm0meOe": {"name": "Default \"a\"", "rs": {"__ref": "zCU2HsP8olD_"}, "preview": null, "uuid": "YH7_sbyaLK6n", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "UMRpTwCQD6cB": {"selector": "a", "style": {"__ref": "pJ2xShm0meOe"}, "__type": "ThemeStyle"}, "sWycs_s_CbWM": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "3fjxww1mpcaH": {"name": "Default \"h3\"", "rs": {"__ref": "sWycs_s_CbWM"}, "preview": null, "uuid": "F_-XbgJy4usC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Zvxd-zaIv9bT": {"selector": "h3", "style": {"__ref": "3fjxww1mpcaH"}, "__type": "ThemeStyle"}, "H0tJvY5os1js": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "pWlGmwiiVyrK": {"name": "Default \"h4\"", "rs": {"__ref": "H0tJvY5os1js"}, "preview": null, "uuid": "i_x_W6NnxDoe", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ll0qYAuxfpQK": {"selector": "h4", "style": {"__ref": "pWlGmwiiVyrK"}, "__type": "ThemeStyle"}, "zStySgp5bao8": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "pzVV4j6xIgYU": {"name": "Default \"code\"", "rs": {"__ref": "zStySgp5bao8"}, "preview": null, "uuid": "QRnI88y7jc4T", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "M4oDe05wnHB6": {"selector": "code", "style": {"__ref": "pzVV4j6xIgYU"}, "__type": "ThemeStyle"}, "Zu2hdUZ1TiiA": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "WKYmTtPklI83": {"name": "Default \"blockquote\"", "rs": {"__ref": "Zu2hdUZ1TiiA"}, "preview": null, "uuid": "6r3iG3H0NzAE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_z3UzJWBBvUz": {"selector": "blockquote", "style": {"__ref": "WKYmTtPklI83"}, "__type": "ThemeStyle"}, "D7mYEvG-jOn7": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "0QRmBmr5VubE": {"name": "Default \"pre\"", "rs": {"__ref": "D7mYEvG-jOn7"}, "preview": null, "uuid": "LHsd_VYjrQRq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "uQDMjECtcrM_": {"selector": "pre", "style": {"__ref": "0QRmBmr5VubE"}, "__type": "ThemeStyle"}, "2Tq79masESEw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "ySMOA2cx8LeG": {"name": "Default \"ul\"", "rs": {"__ref": "2Tq79masESEw"}, "preview": null, "uuid": "z-uNDGj_VqX5", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "J877YFzivJ_s": {"selector": "ul", "style": {"__ref": "ySMOA2cx8LeG"}, "__type": "ThemeStyle"}, "GVRzFdyGDWlw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "nbz3IgHBSJVs": {"name": "Default \"ol\"", "rs": {"__ref": "GVRzFdyGDWlw"}, "preview": null, "uuid": "LIkD8Sd4XLpv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "OCKqa7fwxOmD": {"selector": "ol", "style": {"__ref": "nbz3IgHBSJVs"}, "__type": "ThemeStyle"}, "4aIHY2pSaaai": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8V_paFA0NHLY": {"name": "Default \"h5\"", "rs": {"__ref": "4aIHY2pSaaai"}, "preview": null, "uuid": "c51XDH4Ff3jG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HmToX_hbZSd4": {"selector": "h5", "style": {"__ref": "8V_paFA0NHLY"}, "__type": "ThemeStyle"}, "N_GGwifZlR3l": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "EJFWKh1jQmMs": {"name": "Default \"h6\"", "rs": {"__ref": "N_GGwifZlR3l"}, "preview": null, "uuid": "OlQ4L5SDAoAD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nA2cSg55mXOh": {"selector": "h6", "style": {"__ref": "EJFWKh1jQmMs"}, "__type": "ThemeStyle"}, "2wRhvBQOTxpG": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "9bc97ig-StTW": {"name": "Default \"a:hover\"", "rs": {"__ref": "2wRhvBQOTxpG"}, "preview": null, "uuid": "edk1uZQcMn3Q", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "aWl-01RS9pXO": {"selector": "a:hover", "style": {"__ref": "9bc97ig-StTW"}, "__type": "ThemeStyle"}, "3yDXIrfATE4h": {"values": {}, "mixins": [], "__type": "RuleSet"}, "It8y_9mCAo2d": {"name": "Default \"li\"", "rs": {"__ref": "3yDXIrfATE4h"}, "preview": null, "uuid": "jDivs2o7TFmu", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "mHf9JjBpLedi": {"selector": "li", "style": {"__ref": "It8y_9mCAo2d"}, "__type": "ThemeStyle"}, "lIfaCrNlEiAl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pm_wCgGpBMaW": {"name": "Default \"p\"", "rs": {"__ref": "lIfaCrNlEiAl"}, "preview": null, "uuid": "kaP7wvdDUgx9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7FFkGefJd-aR": {"selector": "p", "style": {"__ref": "pm_wCgGpBMaW"}, "__type": "ThemeStyle"}, "-5nNiOx1pT5H": {"defaultStyle": {"__ref": "AWlJjU969Uj3"}, "styles": [{"__ref": "tUSTgf8cbcG3"}, {"__ref": "YP9uysYA21fD"}, {"__ref": "UMRpTwCQD6cB"}, {"__ref": "Zvxd-zaIv9bT"}, {"__ref": "Ll0qYAuxfpQK"}, {"__ref": "M4oDe05wnHB6"}, {"__ref": "_z3UzJWBBvUz"}, {"__ref": "uQDMjECtcrM_"}, {"__ref": "J877YFzivJ_s"}, {"__ref": "OCKqa7fwxOmD"}, {"__ref": "HmToX_hbZSd4"}, {"__ref": "nA2cSg55mXOh"}, {"__ref": "aWl-01RS9pXO"}, {"__ref": "mHf9JjBpLedi"}, {"__ref": "7FFkGefJd-aR"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "Vc-b2EEf6Msz": {"name": "Screen", "uuid": "ocJG6zqfCPXw", "__type": "Var"}, "TTnflj7emO9M": {"name": "text", "__type": "Text"}, "pykUTAmUp1QQ": {"type": {"__ref": "TTnflj7emO9M"}, "variable": {"__ref": "Vc-b2EEf6Msz"}, "uuid": "KN-t1AS0dUGa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "I20v_f2yXbce": {"uuid": "JYN8SozuKJGW", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "mlIaiXIH5-nH"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "mlIaiXIH5-nH": {"type": "global-screen", "param": {"__ref": "pykUTAmUp1QQ"}, "uuid": "63rGzUum7sjo", "variants": [{"__ref": "I20v_f2yXbce"}], "multi": true, "__type": "GlobalVariantGroup"}, "DFezqTM7r2fD": {"uuid": "iHHjoocfITo0", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "klGPLiElBirW": {"components": [{"__ref": "ZeTMh1wSNr1j"}, {"__ref": "0GJqiU6xYXkO"}, {"__ref": "51lCyCxpdxE_"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "pxgg9KqyMKOZ"}], "globalVariantGroups": [{"__ref": "mlIaiXIH5-nH"}], "userManagedFonts": [], "globalVariant": {"__ref": "DFezqTM7r2fD"}, "styleTokens": [], "mixins": [{"__ref": "k8K-NBeNWgnx"}], "themes": [{"__ref": "-5nNiOx1pT5H"}], "activeTheme": {"__ref": "-5nNiOx1pT5H"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "mlIaiXIH5-nH"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "k8K-NBeNWgnx": {"name": "Unnamed Style preset", "rs": {"__ref": "WqcudSe3EHsC"}, "preview": null, "uuid": "rnzOzhqFvved", "forTheme": false, "variantedRs": [], "__type": "Mixin"}, "WqcudSe3EHsC": {"values": {"font-style": "italic"}, "mixins": [], "__type": "RuleSet"}, "51lCyCxpdxE_": {"uuid": "Lu4s06O9r5JT", "name": "TestComponent", "params": [{"__ref": "nYyJZV-GKCtF"}, {"__ref": "786ImMB6w31e"}], "states": [{"__ref": "8NQvbKTpO9aA"}], "tplTree": {"__ref": "ZxgVNHD7tTd6"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "MjK9HsEhz44u"}, {"__ref": "phHtglJ3y5tC"}], "variantGroups": [{"__ref": "25KZiDx5-peC"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "pxgg9KqyMKOZ": {"component": {"__ref": "51lCyCxpdxE_"}, "matrix": {"__ref": "PcHsaD2QasS3"}, "customMatrix": {"__ref": "Q2PERJoi6wlZ"}, "__type": "ComponentArena"}, "ZxgVNHD7tTd6": {"tag": "div", "name": null, "children": [{"__ref": "2JJXehWW3_s4"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "S5Rqk8dQdcBs", "parent": null, "locked": null, "vsettings": [{"__ref": "nvcEJZdsFdvp"}, {"__ref": "_kbkdjmHnQdt"}, {"__ref": "7FyWalX2CRf6"}], "__type": "TplTag"}, "MjK9HsEhz44u": {"uuid": "IKHlss52-iqo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "PcHsaD2QasS3": {"rows": [{"__ref": "KHTTWTEz0p_n"}, {"__ref": "uAuRBDYblLST"}], "__type": "ArenaFrameGrid"}, "Q2PERJoi6wlZ": {"rows": [{"__ref": "6wVY8MZzbnrQ"}], "__type": "ArenaFrameGrid"}, "nvcEJZdsFdvp": {"variants": [{"__ref": "MjK9HsEhz44u"}], "args": [], "attrs": {}, "rs": {"__ref": "l6CqFgbkAHnF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KHTTWTEz0p_n": {"cols": [{"__ref": "qSbkM6AsMD_S"}, {"__ref": "s2VMqpY_dXPk"}], "rowKey": null, "__type": "ArenaFrameRow"}, "6wVY8MZzbnrQ": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "l6CqFgbkAHnF": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "center", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "qSbkM6AsMD_S": {"frame": {"__ref": "Q8jTl0X8Wfl_"}, "cellKey": {"__ref": "MjK9HsEhz44u"}, "__type": "ArenaFrameCell"}, "Q8jTl0X8Wfl_": {"uuid": "I3ts7yT3vuYH", "width": 1180, "height": 540, "container": {"__ref": "bHwGwW9-C0FX"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "MjK9HsEhz44u"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "bHwGwW9-C0FX": {"name": null, "component": {"__ref": "51lCyCxpdxE_"}, "uuid": "2e8LwqWT_Zxk", "parent": null, "locked": null, "vsettings": [{"__ref": "WKLrSg-aFc15"}], "__type": "TplComponent"}, "WKLrSg-aFc15": {"variants": [{"__ref": "DFezqTM7r2fD"}], "args": [], "attrs": {}, "rs": {"__ref": "KhRgJ0IL0cat"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KhRgJ0IL0cat": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2JJXehWW3_s4": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "WIPrHoT8HN07", "parent": {"__ref": "ZxgVNHD7tTd6"}, "locked": null, "vsettings": [{"__ref": "yn8cj0W13iUS"}, {"__ref": "xZtANp3SHCYX"}], "__type": "TplTag"}, "yn8cj0W13iUS": {"variants": [{"__ref": "MjK9HsEhz44u"}], "args": [], "attrs": {}, "rs": {"__ref": "Pv2X-afAj-aB"}, "dataCond": null, "dataRep": null, "text": {"__ref": "5HwkDUdi9s9O"}, "columnsConfig": null, "__type": "VariantSetting"}, "Pv2X-afAj-aB": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "8NQvbKTpO9aA": {"variantGroup": {"__ref": "25KZiDx5-peC"}, "param": {"__ref": "nYyJZV-GKCtF"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "786ImMB6w31e"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "25KZiDx5-peC": {"type": "component", "param": {"__ref": "nYyJZV-GKCtF"}, "linkedState": {"__ref": "8NQvbKTpO9aA"}, "uuid": "3CzddAykZ2xg", "variants": [{"__ref": "zFrcc-zWrEi8"}], "multi": false, "__type": "ComponentVariantGroup"}, "nYyJZV-GKCtF": {"type": {"__ref": "nEKgeAafPanx"}, "state": {"__ref": "8NQvbKTpO9aA"}, "variable": {"__ref": "PMONRmfDnIw_"}, "uuid": "EH2TNzvoKigi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "786ImMB6w31e": {"type": {"__ref": "JqNxfn5v7FI5"}, "state": {"__ref": "8NQvbKTpO9aA"}, "variable": {"__ref": "tJSxkgXFRG3G"}, "uuid": "y6hoDcmg5WYV", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "uAuRBDYblLST": {"cols": [{"__ref": "0Cp26wYSfeLE"}], "rowKey": {"__ref": "25KZiDx5-peC"}, "__type": "ArenaFrameRow"}, "_kbkdjmHnQdt": {"variants": [{"__ref": "zFrcc-zWrEi8"}], "args": [], "attrs": {}, "rs": {"__ref": "JZsfWN_h6P1D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zFrcc-zWrEi8": {"uuid": "fEakjkQWuD8Z", "name": "isActive", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "25KZiDx5-peC"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "nEKgeAafPanx": {"name": "text", "__type": "Text"}, "PMONRmfDnIw_": {"name": "isActive", "uuid": "qmuO7WLxQhD5", "__type": "Var"}, "JqNxfn5v7FI5": {"name": "func", "params": [{"__ref": "ik9RGZ_1GU_z"}], "__type": "FunctionType"}, "tJSxkgXFRG3G": {"name": "On isActive change", "uuid": "hb43Jagp2iHj", "__type": "Var"}, "0Cp26wYSfeLE": {"frame": {"__ref": "RbSc5mkR2w8J"}, "cellKey": {"__ref": "zFrcc-zWrEi8"}, "__type": "ArenaFrameCell"}, "JZsfWN_h6P1D": {"values": {"background": "linear-gradient(#FFEEDD, #FFEEDD)"}, "mixins": [], "__type": "RuleSet"}, "ik9RGZ_1GU_z": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "K8PA1YmUCYzT"}, "__type": "ArgType"}, "RbSc5mkR2w8J": {"uuid": "47SgBFFyA-mN", "width": 1180, "height": 540, "container": {"__ref": "IFkdHmnaIhot"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "zFrcc-zWrEi8"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "K8PA1YmUCYzT": {"name": "any", "__type": "AnyType"}, "IFkdHmnaIhot": {"name": null, "component": {"__ref": "51lCyCxpdxE_"}, "uuid": "3ICFGR2ZoGgP", "parent": null, "locked": null, "vsettings": [{"__ref": "TDJfYjtp_Oo4"}], "__type": "TplComponent"}, "TDJfYjtp_Oo4": {"variants": [{"__ref": "DFezqTM7r2fD"}], "args": [], "attrs": {}, "rs": {"__ref": "MYqb9DE7QNNl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MYqb9DE7QNNl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5HwkDUdi9s9O": {"markers": [], "text": "Sample text", "__type": "RawText"}, "phHtglJ3y5tC": {"uuid": "8otmk_WAUtIR", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "s2VMqpY_dXPk": {"frame": {"__ref": "APROACGT2gbr"}, "cellKey": {"__ref": "phHtglJ3y5tC"}, "__type": "ArenaFrameCell"}, "7FyWalX2CRf6": {"variants": [{"__ref": "phHtglJ3y5tC"}], "args": [], "attrs": {}, "rs": {"__ref": "HOYFc_djf0NR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "APROACGT2gbr": {"uuid": "NkvZfYjiMKSq", "width": 1180, "height": 540, "container": {"__ref": "7Fcg6V8lY5P0"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "phHtglJ3y5tC"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "HOYFc_djf0NR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7Fcg6V8lY5P0": {"name": null, "component": {"__ref": "51lCyCxpdxE_"}, "uuid": "CxF3J6rKc5Sv", "parent": null, "locked": null, "vsettings": [{"__ref": "bp-ZuWSGQSB4"}], "__type": "TplComponent"}, "bp-ZuWSGQSB4": {"variants": [{"__ref": "DFezqTM7r2fD"}], "args": [], "attrs": {}, "rs": {"__ref": "ypcYzB554jCP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ypcYzB554jCP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xZtANp3SHCYX": {"variants": [{"__ref": "phHtglJ3y5tC"}], "args": [], "attrs": {}, "rs": {"__ref": "iAEw2tZlxMV-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iAEw2tZlxMV-": {"values": {"color": "#0005FF"}, "mixins": [], "__type": "RuleSet"}, "8KVzgECeyObU": {"uuid": "vuUk0yHVK5p7", "pkgId": "4095423e-e27c-4538-aa8d-f6e14bc8dab0", "projectId": "wHtfg2nLs5zqybWknqMXbm", "version": "4.1.0", "name": "Parent Project", "site": {"__ref": "klGPLiElBirW"}, "__type": "ProjectDependency"}}, "deps": [], "version": "246-add-component-updated-at"}, "projectId": "wHtfg2nLs5zqybWknqMXbm", "version": "4.1.0", "branchId": "main"}, {"id": "b690e5b2-0815-4429-ae04-c8f885763e08", "data": {"root": "KXbnLZOux20q", "map": {"BhdY42hR-Lxp": {"uuid": "VdcBCMHPMQlx", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Liea6UxaQwij": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "stretch", "height": "stretch", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "H_n_hCTG1w_q": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "Liea6UxaQwij"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PUieQAmiKo2S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BtyealwHhehP": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "PUieQAmiKo2S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZDPwdVBBrgYI": {"tag": "div", "name": null, "children": [{"__ref": "rz0W8-Im1Lcy"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "iiprFWcIs5p9", "parent": null, "locked": null, "vsettings": [{"__ref": "H_n_hCTG1w_q"}, {"__ref": "BtyealwHhehP"}], "__type": "TplTag"}, "1TSmsL3E3KVw": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "cLsMrVaCAsM_": {"uuid": "V_IlQKz1I1ss", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "ZDPwdVBBrgYI"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BhdY42hR-Lxp"}], "variantGroups": [], "pageMeta": {"__ref": "1TSmsL3E3KVw"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "agdNyxwUb5LL": {"name": "title", "uuid": "pbWtBHXt7QzT", "__type": "Var"}, "tl8IqtfC00W0": {"name": "text", "__type": "Text"}, "vIku7dYn2lOk": {"type": {"__ref": "tl8IqtfC00W0"}, "variable": {"__ref": "agdNyxwUb5LL"}, "uuid": "lX0fSYLoAuTi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wA9spTLub2Jx": {"name": "description", "uuid": "fsfDmxOurgZ7", "__type": "Var"}, "R_kYr-icqfH5": {"name": "text", "__type": "Text"}, "ajfByMtDR3SD": {"type": {"__ref": "R_kYr-icqfH5"}, "variable": {"__ref": "wA9spTLub2Jx"}, "uuid": "5q5CzxIwtCyj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hRXycIUSLkF0": {"name": "image", "uuid": "iq0x3v5oNjO6", "__type": "Var"}, "1d2qvAHmLYTX": {"name": "img", "__type": "Img"}, "4J8hp6CarOdy": {"type": {"__ref": "1d2qvAHmLYTX"}, "variable": {"__ref": "hRXycIUSLkF0"}, "uuid": "bCDdnuOSQLZM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "P27wGUv-Qgx9": {"name": "canonical", "uuid": "Jdgf8PrJBGfp", "__type": "Var"}, "pOM2DYQvazEW": {"name": "text", "__type": "Text"}, "Tdo3ZL-cW2hP": {"type": {"__ref": "pOM2DYQvazEW"}, "variable": {"__ref": "P27wGUv-Qgx9"}, "uuid": "DILE13O2n51m", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "LU8FftYLeIfh": {"uuid": "Sf2JPX0o5bC9", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "TBXkCcEW95C6": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "89sUQoymcgJU": {"variants": [{"__ref": "LU8FftYLeIfh"}], "args": [], "attrs": {}, "rs": {"__ref": "TBXkCcEW95C6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oBh8b7--oQKJ": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "UYjOwa6Pijgu", "parent": null, "locked": null, "vsettings": [{"__ref": "89sUQoymcgJU"}], "__type": "TplTag"}, "eTWlajcHbsXS": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "iVgviOD2y313": {"uuid": "7dNVhxXEfmzt", "name": "hostless-plasmic-head", "params": [{"__ref": "vIku7dYn2lOk"}, {"__ref": "ajfByMtDR3SD"}, {"__ref": "4J8hp6CarOdy"}, {"__ref": "Tdo3ZL-cW2hP"}], "states": [], "tplTree": {"__ref": "oBh8b7--oQKJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "LU8FftYLeIfh"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "eTWlajcHbsXS"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "mn-i0wyTX_8d": {"name": "dataOp", "uuid": "aw3To6-iRxiT", "__type": "Var"}, "1gZ8WLiSqinW": {"name": "any", "__type": "AnyType"}, "XbCO6KZXdTOI": {"type": {"__ref": "1gZ8WLiSqinW"}, "variable": {"__ref": "mn-i0wyTX_8d"}, "uuid": "kDGPzXajGZuF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4TJiowzsgSA6": {"name": "name", "uuid": "qkFDzgOGuun7", "__type": "Var"}, "gqsxkw4HNnT6": {"name": "text", "__type": "Text"}, "wKY8XDohrk5E": {"type": {"__ref": "gqsxkw4HNnT6"}, "variable": {"__ref": "4TJiowzsgSA6"}, "uuid": "MSEh5mmXKnEv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "e1AHNXBi-gbX": {"name": "children", "uuid": "hjv8itvMaejl", "__type": "Var"}, "0s4Xm5sA3bgk": {"name": "any", "__type": "AnyType"}, "yFoizy9jwCH3": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "0s4Xm5sA3bgk"}, "__type": "ArgType"}, "mkhxIrcCKe0I": {"name": "renderFunc", "params": [{"__ref": "yFoizy9jwCH3"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "4w6nDpMQsoDl": {"type": {"__ref": "mkhxIrcCKe0I"}, "tplSlot": {"__ref": "EQOoFeB1jJWg"}, "variable": {"__ref": "e1AHNXBi-gbX"}, "uuid": "pDF7Mkhr0sj3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "hABaEvXCIzy7": {"name": "pageSize", "uuid": "4iBDc_66nKg4", "__type": "Var"}, "NJDmOFISnXrV": {"name": "num", "__type": "<PERSON><PERSON>"}, "YJdR63-NsHsj": {"type": {"__ref": "NJDmOFISnXrV"}, "variable": {"__ref": "hABaEvXCIzy7"}, "uuid": "dd4DrRA-g_tA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "8BvioTbKdGPX": {"name": "pageIndex", "uuid": "HF4o8INzirSv", "__type": "Var"}, "5CvNPBoS8Eej": {"name": "num", "__type": "<PERSON><PERSON>"}, "fFblb4jAPw46": {"type": {"__ref": "5CvNPBoS8Eej"}, "variable": {"__ref": "8BvioTbKdGPX"}, "uuid": "UHhciNCazNro", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "JK1qk-LNA9xF": {"uuid": "12arXxMPC7fK", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "6gmMTOrciMXi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mURWtnYSURxx": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "6gmMTOrciMXi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EQOoFeB1jJWg": {"param": {"__ref": "4w6nDpMQsoDl"}, "defaultContents": [], "uuid": "pjoD2AVLM97A", "parent": {"__ref": "b852meGjAbbJ"}, "locked": null, "vsettings": [{"__ref": "mURWtnYSURxx"}], "__type": "TplSlot"}, "y6b8w_eJA3hG": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "oKYA3Ni4Izig": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "y6b8w_eJA3hG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "b852meGjAbbJ": {"tag": "div", "name": null, "children": [{"__ref": "EQOoFeB1jJWg"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "o1VCnil8mlLe", "parent": null, "locked": null, "vsettings": [{"__ref": "oKYA3Ni4Izig"}], "__type": "TplTag"}, "YUR9v-cVROMw": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "CGTKnrhUwnGJ": {"uuid": "EgSmz5VbPf14", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "XbCO6KZXdTOI"}, {"__ref": "wKY8XDohrk5E"}, {"__ref": "4w6nDpMQsoDl"}, {"__ref": "YJdR63-NsHsj"}, {"__ref": "fFblb4jAPw46"}], "states": [], "tplTree": {"__ref": "b852meGjAbbJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "JK1qk-LNA9xF"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "YUR9v-cVROMw"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "O6D5BizBFFPl": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "9rWFEsD51ijs": {"name": "Default Typography", "rs": {"__ref": "O6D5BizBFFPl"}, "preview": null, "uuid": "65YsmemuB5Gk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "B16VruBHaw4V": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "JuYFCtRAZj1L": {"name": "Default \"h1\"", "rs": {"__ref": "B16VruBHaw4V"}, "preview": null, "uuid": "N5whCaIyI5yq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "FW8XAtNSjtu6": {"selector": "h1", "style": {"__ref": "JuYFCtRAZj1L"}, "__type": "ThemeStyle"}, "_2Bcb1Mu36uY": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "p1iCCLwkpDoy": {"name": "Default \"h2\"", "rs": {"__ref": "_2Bcb1Mu36uY"}, "preview": null, "uuid": "591o8eV6APJY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EXyTxUa4ABV-": {"selector": "h2", "style": {"__ref": "p1iCCLwkpDoy"}, "__type": "ThemeStyle"}, "coKpJBdyxM1B": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "yyzKh6WaoGU3": {"name": "Default \"a\"", "rs": {"__ref": "coKpJBdyxM1B"}, "preview": null, "uuid": "OzvmmY5ljIKh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "q9zp2dIwMR4H": {"selector": "a", "style": {"__ref": "yyzKh6WaoGU3"}, "__type": "ThemeStyle"}, "5VOKWTPo9qQ-": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "aQGs2EzpDklN": {"name": "Default \"h3\"", "rs": {"__ref": "5VOKWTPo9qQ-"}, "preview": null, "uuid": "NnRLc2qrbHrp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ySMS8lwIR8e4": {"selector": "h3", "style": {"__ref": "aQGs2EzpDklN"}, "__type": "ThemeStyle"}, "1Eo6Elb-mOXu": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "11M5AU1J79oZ": {"name": "Default \"h4\"", "rs": {"__ref": "1Eo6Elb-mOXu"}, "preview": null, "uuid": "Fw2NyZXagP3_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TPT4LIw_FlmE": {"selector": "h4", "style": {"__ref": "11M5AU1J79oZ"}, "__type": "ThemeStyle"}, "Ap8tydcErz0o": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "jff-39xNBt4h": {"name": "Default \"code\"", "rs": {"__ref": "Ap8tydcErz0o"}, "preview": null, "uuid": "96ch0AkhaXOC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ml1ObT0h6WF0": {"selector": "code", "style": {"__ref": "jff-39xNBt4h"}, "__type": "ThemeStyle"}, "meGo-3SsDjxW": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "Rk4vU0kpCLqX": {"name": "Default \"blockquote\"", "rs": {"__ref": "meGo-3SsDjxW"}, "preview": null, "uuid": "gYG9UeAQLGHl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "uuwyZ8MaMwzC": {"selector": "blockquote", "style": {"__ref": "Rk4vU0kpCLqX"}, "__type": "ThemeStyle"}, "Xm8vVOguc99Z": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "pNJ3JD644f1B": {"name": "Default \"pre\"", "rs": {"__ref": "Xm8vVOguc99Z"}, "preview": null, "uuid": "lWS_bqGRCyQI", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xADr-YcMKj-A": {"selector": "pre", "style": {"__ref": "pNJ3JD644f1B"}, "__type": "ThemeStyle"}, "sb9QqZPTqrMD": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "oG1vMCxc_rAf": {"name": "Default \"ul\"", "rs": {"__ref": "sb9QqZPTqrMD"}, "preview": null, "uuid": "4fBIMOGTQv9T", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vWcy13o2wF2w": {"selector": "ul", "style": {"__ref": "oG1vMCxc_rAf"}, "__type": "ThemeStyle"}, "OJMyIbBCwzBE": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "WIkY9opOarFu": {"name": "Default \"ol\"", "rs": {"__ref": "OJMyIbBCwzBE"}, "preview": null, "uuid": "5JPbmKB_mLIY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ebOB-q1gpIYe": {"selector": "ol", "style": {"__ref": "WIkY9opOarFu"}, "__type": "ThemeStyle"}, "LJZHTjVy8-KX": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "q_up9_dZGmRp": {"name": "Default \"h5\"", "rs": {"__ref": "LJZHTjVy8-KX"}, "preview": null, "uuid": "HFTrV2sHI6_v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HpQe3u3igwNK": {"selector": "h5", "style": {"__ref": "q_up9_dZGmRp"}, "__type": "ThemeStyle"}, "36QFg90QpNzF": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "2qvlcfRG1Yqf": {"name": "Default \"h6\"", "rs": {"__ref": "36QFg90QpNzF"}, "preview": null, "uuid": "7ne0HsGQbiX-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "AHK6bdjMUFCZ": {"selector": "h6", "style": {"__ref": "2qvlcfRG1Yqf"}, "__type": "ThemeStyle"}, "VTuC2NSDgvTw": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "kocbDeNmD10M": {"name": "Default \"a:hover\"", "rs": {"__ref": "VTuC2NSDgvTw"}, "preview": null, "uuid": "pv_rGaCsBcE7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TLsnPhLuZijX": {"selector": "a:hover", "style": {"__ref": "kocbDeNmD10M"}, "__type": "ThemeStyle"}, "8NKHRGPgmO_7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2R5gsgHL1SEg": {"name": "Default \"li\"", "rs": {"__ref": "8NKHRGPgmO_7"}, "preview": null, "uuid": "SGgFYYxtVP9N", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "-HxiJ-vruiDF": {"selector": "li", "style": {"__ref": "2R5gsgHL1SEg"}, "__type": "ThemeStyle"}, "400JXoUXp8-f": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NUhspy26QvGl": {"name": "Default \"p\"", "rs": {"__ref": "400JXoUXp8-f"}, "preview": null, "uuid": "YsYyl_vkV4o4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "u1Jb5Ps_CziS": {"selector": "p", "style": {"__ref": "NUhspy26QvGl"}, "__type": "ThemeStyle"}, "2BVmOrsFr2nd": {"defaultStyle": {"__ref": "9rWFEsD51ijs"}, "styles": [{"__ref": "FW8XAtNSjtu6"}, {"__ref": "EXyTxUa4ABV-"}, {"__ref": "q9zp2dIwMR4H"}, {"__ref": "ySMS8lwIR8e4"}, {"__ref": "TPT4LIw_FlmE"}, {"__ref": "ml1ObT0h6WF0"}, {"__ref": "uuwyZ8MaMwzC"}, {"__ref": "xADr-YcMKj-A"}, {"__ref": "vWcy13o2wF2w"}, {"__ref": "ebOB-q1gpIYe"}, {"__ref": "HpQe3u3igwNK"}, {"__ref": "AHK6bdjMUFCZ"}, {"__ref": "TLsnPhLuZijX"}, {"__ref": "-HxiJ-vruiDF"}, {"__ref": "u1Jb5Ps_CziS"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "ePPLz0oUoM9g": {"name": "Screen", "uuid": "lc-5gWiYQiUw", "__type": "Var"}, "2WZaQOllH-qH": {"name": "text", "__type": "Text"}, "fTkyo-D50byd": {"type": {"__ref": "2WZaQOllH-qH"}, "variable": {"__ref": "ePPLz0oUoM9g"}, "uuid": "pBmgCu3yQB9T", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "ZWPc7EzDIVMW": {"uuid": "C5gJ7OmaUrOL", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "rO6y12j3WBms"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rO6y12j3WBms": {"type": "global-screen", "param": {"__ref": "fTkyo-D50byd"}, "uuid": "JHdfnMT9T35R", "variants": [{"__ref": "ZWPc7EzDIVMW"}], "multi": true, "__type": "GlobalVariantGroup"}, "752Z_tQ_x_fR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Rbqm34gRqzJz": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "752Z_tQ_x_fR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eZb3xelRiEfN": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9yvTWSyqzEZQ", "parent": null, "locked": null, "vsettings": [{"__ref": "Rbqm34gRqzJz"}], "__type": "TplComponent"}, "4jOaAgi9Lh-B": {"uuid": "s0dGF-mxGILF", "width": 1440, "height": 1024, "container": {"__ref": "eZb3xelRiEfN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "BhdY42hR-Lxp"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "17QEwua0drdO": {"frame": {"__ref": "4jOaAgi9Lh-B"}, "cellKey": null, "__type": "ArenaFrameCell"}, "7-_NchqVyaKS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aHX9dM82Ufr4": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "7-_NchqVyaKS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3bZjULCuzoCU": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9CS1eJrJD4S8", "parent": null, "locked": null, "vsettings": [{"__ref": "aHX9dM82Ufr4"}], "__type": "TplComponent"}, "rVj4nSXnpwV4": {"uuid": "tgpZSeTC7Yp7", "width": 414, "height": 736, "container": {"__ref": "3bZjULCuzoCU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"C5gJ7OmaUrOL": true}, "targetGlobalVariants": [{"__ref": "ZWPc7EzDIVMW"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "UhAjO5P4kFFM": {"frame": {"__ref": "rVj4nSXnpwV4"}, "cellKey": null, "__type": "ArenaFrameCell"}, "h-_-qgw0akdP": {"cols": [{"__ref": "17QEwua0drdO"}, {"__ref": "UhAjO5P4kFFM"}], "rowKey": {"__ref": "BhdY42hR-Lxp"}, "__type": "ArenaFrameRow"}, "fyh3Qla9RLSG": {"rows": [{"__ref": "h-_-qgw0akdP"}], "__type": "ArenaFrameGrid"}, "Ej9GYeyLIBLC": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3ti82_n-06ko": {"rows": [{"__ref": "Ej9GYeyLIBLC"}], "__type": "ArenaFrameGrid"}, "ZfWiEZ1-0hE6": {"component": {"__ref": "cLsMrVaCAsM_"}, "matrix": {"__ref": "fyh3Qla9RLSG"}, "customMatrix": {"__ref": "3ti82_n-06ko"}, "__type": "PageArena"}, "bNQ0GDteuhaj": {"uuid": "nhuuHLIvqj5r", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WvF3aLJv68QX": {"components": [{"__ref": "cLsMrVaCAsM_"}, {"__ref": "iVgviOD2y313"}, {"__ref": "CGTKnrhUwnGJ"}], "arenas": [], "pageArenas": [{"__ref": "ZfWiEZ1-0hE6"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "rO6y12j3WBms"}], "userManagedFonts": [], "globalVariant": {"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "2BVmOrsFr2nd"}], "activeTheme": {"__ref": "2BVmOrsFr2nd"}, "imageAssets": [], "projectDependencies": [{"__xref": {"uuid": "a1a67f8a-ec8a-402a-87da-eb8b7ebf18e8", "iid": "8KVzgECeyObU"}}], "activeScreenVariantGroup": {"__ref": "rO6y12j3WBms"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "rz0W8-Im1Lcy": {"tag": "section", "name": null, "children": [{"__ref": "rZl1SkoFms2Z"}, {"__ref": "Z6GoBtYOptqo"}, {"__ref": "beIF61l89WIC"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DllKIXAyh5UP", "parent": {"__ref": "ZDPwdVBBrgYI"}, "locked": null, "vsettings": [{"__ref": "OQEWENIfpi5-"}, {"__ref": "83E6u4NeABM3"}], "__type": "TplTag"}, "rZl1SkoFms2Z": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "spYOtJYdUTLD", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "73B_3r6ina-x"}], "__type": "TplTag"}, "Z6GoBtYOptqo": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "DlTH_k504aot", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "wz3FW2Cj5NXL"}], "__type": "TplTag"}, "OQEWENIfpi5-": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "66HY2Ju8B857"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "83E6u4NeABM3": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "hI_W_vtrTB4m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "73B_3r6ina-x": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "LleI-9WZGPYs"}, "dataCond": null, "dataRep": null, "text": {"__ref": "365AkSlNTyTj"}, "columnsConfig": null, "__type": "VariantSetting"}, "wz3FW2Cj5NXL": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "ntU0nqHKbTpo"}, "dataCond": null, "dataRep": null, "text": {"__ref": "wzfdLfQcOKVW"}, "columnsConfig": null, "__type": "VariantSetting"}, "66HY2Ju8B857": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "plasmic-layout-full-bleed", "height": "wrap", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "96px", "padding-top": "96px", "grid-row-gap": "16px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "hI_W_vtrTB4m": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LleI-9WZGPYs": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "margin-bottom": "32px"}, "mixins": [], "__type": "RuleSet"}, "365AkSlNTyTj": {"markers": [], "text": "Welcome to your first page.", "__type": "RawText"}, "ntU0nqHKbTpo": {"values": {"position": "relative", "width": "stretch", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "wzfdLfQcOKVW": {"markers": [{"__ref": "h29L2P0ZFn5U"}], "text": "If you haven't already done so, go back and learn the basics by going through the Plasmic Levels tutorial.\n\nIt's always easier to start from examples! Add a new page using a template—do this from the list of pages in the top toolbar.\n\nOr press the big blue + button to start inserting items into this page.\n\nIntegrate this project into your codebase—press the Code button in the top right and follow the quickstart instructions.\n\nJoin our Slack community (icon in bottom left) for help any time.", "__type": "RawText"}, "h29L2P0ZFn5U": {"rs": {"__ref": "JbwI01KxVj_0"}, "position": 360, "length": 4, "__type": "<PERSON><PERSON>arker"}, "JbwI01KxVj_0": {"values": {"font-weight": "700"}, "mixins": [], "__type": "RuleSet"}, "beIF61l89WIC": {"name": null, "component": {"__xref": {"uuid": "a1a67f8a-ec8a-402a-87da-eb8b7ebf18e8", "iid": "51lCyCxpdxE_"}}, "uuid": "OFEUtqimoqvW", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "cL_KvYPtoOCq"}], "__type": "TplComponent"}, "cL_KvYPtoOCq": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [{"__ref": "jbh1_pCe3_XH"}], "attrs": {}, "rs": {"__ref": "-PzhyiMMqlQe"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-PzhyiMMqlQe": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "jbh1_pCe3_XH": {"param": {"__xref": {"uuid": "a1a67f8a-ec8a-402a-87da-eb8b7ebf18e8", "iid": "nYyJZV-GKCtF"}}, "expr": {"__ref": "aBI1euXi0cR-"}, "__type": "Arg"}, "aBI1euXi0cR-": {"variants": [{"__xref": {"uuid": "a1a67f8a-ec8a-402a-87da-eb8b7ebf18e8", "iid": "zFrcc-zWrEi8"}}], "__type": "VariantsRef"}, "KXbnLZOux20q": {"uuid": "1YDbREnQXwZJ", "pkgId": "b77fce8e-3031-4bd5-8c4f-74f8a93824fe", "projectId": "uGrWS46cRJ2qquYfzAsaTw", "version": "6.1.0", "name": "Child", "site": {"__ref": "WvF3aLJv68QX"}, "__type": "ProjectDependency"}}, "deps": ["a1a67f8a-ec8a-402a-87da-eb8b7ebf18e8"], "version": "246-add-component-updated-at"}, "projectId": "uGrWS46cRJ2qquYfzAsaTw", "version": "6.1.0", "branchId": "main"}, {"id": "b4ddfa96-f81c-4a01-a933-59f75371ef36", "data": {"root": "N7JO55l7AaSP", "map": {"BhdY42hR-Lxp": {"uuid": "VdcBCMHPMQlx", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Liea6UxaQwij": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "stretch", "height": "stretch", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "H_n_hCTG1w_q": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "Liea6UxaQwij"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PUieQAmiKo2S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BtyealwHhehP": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "PUieQAmiKo2S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZDPwdVBBrgYI": {"tag": "div", "name": null, "children": [{"__ref": "rz0W8-Im1Lcy"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "iiprFWcIs5p9", "parent": null, "locked": null, "vsettings": [{"__ref": "H_n_hCTG1w_q"}, {"__ref": "BtyealwHhehP"}], "__type": "TplTag"}, "1TSmsL3E3KVw": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "cLsMrVaCAsM_": {"uuid": "V_IlQKz1I1ss", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "ZDPwdVBBrgYI"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BhdY42hR-Lxp"}], "variantGroups": [], "pageMeta": {"__ref": "1TSmsL3E3KVw"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "agdNyxwUb5LL": {"name": "title", "uuid": "pbWtBHXt7QzT", "__type": "Var"}, "tl8IqtfC00W0": {"name": "text", "__type": "Text"}, "vIku7dYn2lOk": {"type": {"__ref": "tl8IqtfC00W0"}, "variable": {"__ref": "agdNyxwUb5LL"}, "uuid": "lX0fSYLoAuTi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wA9spTLub2Jx": {"name": "description", "uuid": "fsfDmxOurgZ7", "__type": "Var"}, "R_kYr-icqfH5": {"name": "text", "__type": "Text"}, "ajfByMtDR3SD": {"type": {"__ref": "R_kYr-icqfH5"}, "variable": {"__ref": "wA9spTLub2Jx"}, "uuid": "5q5CzxIwtCyj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hRXycIUSLkF0": {"name": "image", "uuid": "iq0x3v5oNjO6", "__type": "Var"}, "1d2qvAHmLYTX": {"name": "img", "__type": "Img"}, "4J8hp6CarOdy": {"type": {"__ref": "1d2qvAHmLYTX"}, "variable": {"__ref": "hRXycIUSLkF0"}, "uuid": "bCDdnuOSQLZM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "P27wGUv-Qgx9": {"name": "canonical", "uuid": "Jdgf8PrJBGfp", "__type": "Var"}, "pOM2DYQvazEW": {"name": "text", "__type": "Text"}, "Tdo3ZL-cW2hP": {"type": {"__ref": "pOM2DYQvazEW"}, "variable": {"__ref": "P27wGUv-Qgx9"}, "uuid": "DILE13O2n51m", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "LU8FftYLeIfh": {"uuid": "Sf2JPX0o5bC9", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "TBXkCcEW95C6": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "89sUQoymcgJU": {"variants": [{"__ref": "LU8FftYLeIfh"}], "args": [], "attrs": {}, "rs": {"__ref": "TBXkCcEW95C6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oBh8b7--oQKJ": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "UYjOwa6Pijgu", "parent": null, "locked": null, "vsettings": [{"__ref": "89sUQoymcgJU"}], "__type": "TplTag"}, "eTWlajcHbsXS": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "iVgviOD2y313": {"uuid": "7dNVhxXEfmzt", "name": "hostless-plasmic-head", "params": [{"__ref": "vIku7dYn2lOk"}, {"__ref": "ajfByMtDR3SD"}, {"__ref": "4J8hp6CarOdy"}, {"__ref": "Tdo3ZL-cW2hP"}], "states": [], "tplTree": {"__ref": "oBh8b7--oQKJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "LU8FftYLeIfh"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "eTWlajcHbsXS"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "mn-i0wyTX_8d": {"name": "dataOp", "uuid": "aw3To6-iRxiT", "__type": "Var"}, "1gZ8WLiSqinW": {"name": "any", "__type": "AnyType"}, "XbCO6KZXdTOI": {"type": {"__ref": "1gZ8WLiSqinW"}, "variable": {"__ref": "mn-i0wyTX_8d"}, "uuid": "kDGPzXajGZuF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4TJiowzsgSA6": {"name": "name", "uuid": "qkFDzgOGuun7", "__type": "Var"}, "gqsxkw4HNnT6": {"name": "text", "__type": "Text"}, "wKY8XDohrk5E": {"type": {"__ref": "gqsxkw4HNnT6"}, "variable": {"__ref": "4TJiowzsgSA6"}, "uuid": "MSEh5mmXKnEv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "e1AHNXBi-gbX": {"name": "children", "uuid": "hjv8itvMaejl", "__type": "Var"}, "0s4Xm5sA3bgk": {"name": "any", "__type": "AnyType"}, "yFoizy9jwCH3": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "0s4Xm5sA3bgk"}, "__type": "ArgType"}, "mkhxIrcCKe0I": {"name": "renderFunc", "params": [{"__ref": "yFoizy9jwCH3"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "4w6nDpMQsoDl": {"type": {"__ref": "mkhxIrcCKe0I"}, "tplSlot": {"__ref": "EQOoFeB1jJWg"}, "variable": {"__ref": "e1AHNXBi-gbX"}, "uuid": "pDF7Mkhr0sj3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "hABaEvXCIzy7": {"name": "pageSize", "uuid": "4iBDc_66nKg4", "__type": "Var"}, "NJDmOFISnXrV": {"name": "num", "__type": "<PERSON><PERSON>"}, "YJdR63-NsHsj": {"type": {"__ref": "NJDmOFISnXrV"}, "variable": {"__ref": "hABaEvXCIzy7"}, "uuid": "dd4DrRA-g_tA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "8BvioTbKdGPX": {"name": "pageIndex", "uuid": "HF4o8INzirSv", "__type": "Var"}, "5CvNPBoS8Eej": {"name": "num", "__type": "<PERSON><PERSON>"}, "fFblb4jAPw46": {"type": {"__ref": "5CvNPBoS8Eej"}, "variable": {"__ref": "8BvioTbKdGPX"}, "uuid": "UHhciNCazNro", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "JK1qk-LNA9xF": {"uuid": "12arXxMPC7fK", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "6gmMTOrciMXi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mURWtnYSURxx": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "6gmMTOrciMXi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EQOoFeB1jJWg": {"param": {"__ref": "4w6nDpMQsoDl"}, "defaultContents": [], "uuid": "pjoD2AVLM97A", "parent": {"__ref": "b852meGjAbbJ"}, "locked": null, "vsettings": [{"__ref": "mURWtnYSURxx"}], "__type": "TplSlot"}, "y6b8w_eJA3hG": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "oKYA3Ni4Izig": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "y6b8w_eJA3hG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "b852meGjAbbJ": {"tag": "div", "name": null, "children": [{"__ref": "EQOoFeB1jJWg"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "o1VCnil8mlLe", "parent": null, "locked": null, "vsettings": [{"__ref": "oKYA3Ni4Izig"}], "__type": "TplTag"}, "YUR9v-cVROMw": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "CGTKnrhUwnGJ": {"uuid": "EgSmz5VbPf14", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "XbCO6KZXdTOI"}, {"__ref": "wKY8XDohrk5E"}, {"__ref": "4w6nDpMQsoDl"}, {"__ref": "YJdR63-NsHsj"}, {"__ref": "fFblb4jAPw46"}], "states": [], "tplTree": {"__ref": "b852meGjAbbJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "JK1qk-LNA9xF"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "YUR9v-cVROMw"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "O6D5BizBFFPl": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "9rWFEsD51ijs": {"name": "Default Typography", "rs": {"__ref": "O6D5BizBFFPl"}, "preview": null, "uuid": "65YsmemuB5Gk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "B16VruBHaw4V": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "JuYFCtRAZj1L": {"name": "Default \"h1\"", "rs": {"__ref": "B16VruBHaw4V"}, "preview": null, "uuid": "N5whCaIyI5yq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "FW8XAtNSjtu6": {"selector": "h1", "style": {"__ref": "JuYFCtRAZj1L"}, "__type": "ThemeStyle"}, "_2Bcb1Mu36uY": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "p1iCCLwkpDoy": {"name": "Default \"h2\"", "rs": {"__ref": "_2Bcb1Mu36uY"}, "preview": null, "uuid": "591o8eV6APJY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EXyTxUa4ABV-": {"selector": "h2", "style": {"__ref": "p1iCCLwkpDoy"}, "__type": "ThemeStyle"}, "coKpJBdyxM1B": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "yyzKh6WaoGU3": {"name": "Default \"a\"", "rs": {"__ref": "coKpJBdyxM1B"}, "preview": null, "uuid": "OzvmmY5ljIKh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "q9zp2dIwMR4H": {"selector": "a", "style": {"__ref": "yyzKh6WaoGU3"}, "__type": "ThemeStyle"}, "5VOKWTPo9qQ-": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "aQGs2EzpDklN": {"name": "Default \"h3\"", "rs": {"__ref": "5VOKWTPo9qQ-"}, "preview": null, "uuid": "NnRLc2qrbHrp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ySMS8lwIR8e4": {"selector": "h3", "style": {"__ref": "aQGs2EzpDklN"}, "__type": "ThemeStyle"}, "1Eo6Elb-mOXu": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "11M5AU1J79oZ": {"name": "Default \"h4\"", "rs": {"__ref": "1Eo6Elb-mOXu"}, "preview": null, "uuid": "Fw2NyZXagP3_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TPT4LIw_FlmE": {"selector": "h4", "style": {"__ref": "11M5AU1J79oZ"}, "__type": "ThemeStyle"}, "Ap8tydcErz0o": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "jff-39xNBt4h": {"name": "Default \"code\"", "rs": {"__ref": "Ap8tydcErz0o"}, "preview": null, "uuid": "96ch0AkhaXOC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ml1ObT0h6WF0": {"selector": "code", "style": {"__ref": "jff-39xNBt4h"}, "__type": "ThemeStyle"}, "meGo-3SsDjxW": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "Rk4vU0kpCLqX": {"name": "Default \"blockquote\"", "rs": {"__ref": "meGo-3SsDjxW"}, "preview": null, "uuid": "gYG9UeAQLGHl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "uuwyZ8MaMwzC": {"selector": "blockquote", "style": {"__ref": "Rk4vU0kpCLqX"}, "__type": "ThemeStyle"}, "Xm8vVOguc99Z": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "pNJ3JD644f1B": {"name": "Default \"pre\"", "rs": {"__ref": "Xm8vVOguc99Z"}, "preview": null, "uuid": "lWS_bqGRCyQI", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xADr-YcMKj-A": {"selector": "pre", "style": {"__ref": "pNJ3JD644f1B"}, "__type": "ThemeStyle"}, "sb9QqZPTqrMD": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "oG1vMCxc_rAf": {"name": "Default \"ul\"", "rs": {"__ref": "sb9QqZPTqrMD"}, "preview": null, "uuid": "4fBIMOGTQv9T", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vWcy13o2wF2w": {"selector": "ul", "style": {"__ref": "oG1vMCxc_rAf"}, "__type": "ThemeStyle"}, "OJMyIbBCwzBE": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "WIkY9opOarFu": {"name": "Default \"ol\"", "rs": {"__ref": "OJMyIbBCwzBE"}, "preview": null, "uuid": "5JPbmKB_mLIY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ebOB-q1gpIYe": {"selector": "ol", "style": {"__ref": "WIkY9opOarFu"}, "__type": "ThemeStyle"}, "LJZHTjVy8-KX": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "q_up9_dZGmRp": {"name": "Default \"h5\"", "rs": {"__ref": "LJZHTjVy8-KX"}, "preview": null, "uuid": "HFTrV2sHI6_v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HpQe3u3igwNK": {"selector": "h5", "style": {"__ref": "q_up9_dZGmRp"}, "__type": "ThemeStyle"}, "36QFg90QpNzF": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "2qvlcfRG1Yqf": {"name": "Default \"h6\"", "rs": {"__ref": "36QFg90QpNzF"}, "preview": null, "uuid": "7ne0HsGQbiX-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "AHK6bdjMUFCZ": {"selector": "h6", "style": {"__ref": "2qvlcfRG1Yqf"}, "__type": "ThemeStyle"}, "VTuC2NSDgvTw": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "kocbDeNmD10M": {"name": "Default \"a:hover\"", "rs": {"__ref": "VTuC2NSDgvTw"}, "preview": null, "uuid": "pv_rGaCsBcE7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TLsnPhLuZijX": {"selector": "a:hover", "style": {"__ref": "kocbDeNmD10M"}, "__type": "ThemeStyle"}, "8NKHRGPgmO_7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2R5gsgHL1SEg": {"name": "Default \"li\"", "rs": {"__ref": "8NKHRGPgmO_7"}, "preview": null, "uuid": "SGgFYYxtVP9N", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "-HxiJ-vruiDF": {"selector": "li", "style": {"__ref": "2R5gsgHL1SEg"}, "__type": "ThemeStyle"}, "400JXoUXp8-f": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NUhspy26QvGl": {"name": "Default \"p\"", "rs": {"__ref": "400JXoUXp8-f"}, "preview": null, "uuid": "YsYyl_vkV4o4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "u1Jb5Ps_CziS": {"selector": "p", "style": {"__ref": "NUhspy26QvGl"}, "__type": "ThemeStyle"}, "2BVmOrsFr2nd": {"defaultStyle": {"__ref": "9rWFEsD51ijs"}, "styles": [{"__ref": "FW8XAtNSjtu6"}, {"__ref": "EXyTxUa4ABV-"}, {"__ref": "q9zp2dIwMR4H"}, {"__ref": "ySMS8lwIR8e4"}, {"__ref": "TPT4LIw_FlmE"}, {"__ref": "ml1ObT0h6WF0"}, {"__ref": "uuwyZ8MaMwzC"}, {"__ref": "xADr-YcMKj-A"}, {"__ref": "vWcy13o2wF2w"}, {"__ref": "ebOB-q1gpIYe"}, {"__ref": "HpQe3u3igwNK"}, {"__ref": "AHK6bdjMUFCZ"}, {"__ref": "TLsnPhLuZijX"}, {"__ref": "-HxiJ-vruiDF"}, {"__ref": "u1Jb5Ps_CziS"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "ePPLz0oUoM9g": {"name": "Screen", "uuid": "lc-5gWiYQiUw", "__type": "Var"}, "2WZaQOllH-qH": {"name": "text", "__type": "Text"}, "fTkyo-D50byd": {"type": {"__ref": "2WZaQOllH-qH"}, "variable": {"__ref": "ePPLz0oUoM9g"}, "uuid": "pBmgCu3yQB9T", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "ZWPc7EzDIVMW": {"uuid": "C5gJ7OmaUrOL", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "rO6y12j3WBms"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rO6y12j3WBms": {"type": "global-screen", "param": {"__ref": "fTkyo-D50byd"}, "uuid": "JHdfnMT9T35R", "variants": [{"__ref": "ZWPc7EzDIVMW"}], "multi": true, "__type": "GlobalVariantGroup"}, "752Z_tQ_x_fR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Rbqm34gRqzJz": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "752Z_tQ_x_fR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eZb3xelRiEfN": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9yvTWSyqzEZQ", "parent": null, "locked": null, "vsettings": [{"__ref": "Rbqm34gRqzJz"}], "__type": "TplComponent"}, "4jOaAgi9Lh-B": {"uuid": "s0dGF-mxGILF", "width": 1440, "height": 1024, "container": {"__ref": "eZb3xelRiEfN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "BhdY42hR-Lxp"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "17QEwua0drdO": {"frame": {"__ref": "4jOaAgi9Lh-B"}, "cellKey": null, "__type": "ArenaFrameCell"}, "7-_NchqVyaKS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aHX9dM82Ufr4": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "7-_NchqVyaKS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3bZjULCuzoCU": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9CS1eJrJD4S8", "parent": null, "locked": null, "vsettings": [{"__ref": "aHX9dM82Ufr4"}], "__type": "TplComponent"}, "rVj4nSXnpwV4": {"uuid": "tgpZSeTC7Yp7", "width": 414, "height": 736, "container": {"__ref": "3bZjULCuzoCU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"C5gJ7OmaUrOL": true}, "targetGlobalVariants": [{"__ref": "ZWPc7EzDIVMW"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "UhAjO5P4kFFM": {"frame": {"__ref": "rVj4nSXnpwV4"}, "cellKey": null, "__type": "ArenaFrameCell"}, "h-_-qgw0akdP": {"cols": [{"__ref": "17QEwua0drdO"}, {"__ref": "UhAjO5P4kFFM"}], "rowKey": {"__ref": "BhdY42hR-Lxp"}, "__type": "ArenaFrameRow"}, "fyh3Qla9RLSG": {"rows": [{"__ref": "h-_-qgw0akdP"}], "__type": "ArenaFrameGrid"}, "Ej9GYeyLIBLC": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3ti82_n-06ko": {"rows": [{"__ref": "Ej9GYeyLIBLC"}], "__type": "ArenaFrameGrid"}, "ZfWiEZ1-0hE6": {"component": {"__ref": "cLsMrVaCAsM_"}, "matrix": {"__ref": "fyh3Qla9RLSG"}, "customMatrix": {"__ref": "3ti82_n-06ko"}, "__type": "PageArena"}, "bNQ0GDteuhaj": {"uuid": "nhuuHLIvqj5r", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WvF3aLJv68QX": {"components": [{"__ref": "cLsMrVaCAsM_"}, {"__ref": "iVgviOD2y313"}, {"__ref": "CGTKnrhUwnGJ"}, {"__ref": "wZETMFZPlnHe"}], "arenas": [], "pageArenas": [{"__ref": "ZfWiEZ1-0hE6"}], "componentArenas": [{"__ref": "c4NxC8A_Qtrg"}], "globalVariantGroups": [{"__ref": "rO6y12j3WBms"}], "userManagedFonts": [], "globalVariant": {"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "2BVmOrsFr2nd"}], "activeTheme": {"__ref": "2BVmOrsFr2nd"}, "imageAssets": [], "projectDependencies": [{"__xref": {"uuid": "e59019e7-b245-4e0c-b862-91a12b98b3c5", "iid": "N1ERkITzhyF3"}}], "activeScreenVariantGroup": {"__ref": "rO6y12j3WBms"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "rz0W8-Im1Lcy": {"tag": "section", "name": null, "children": [{"__ref": "rZl1SkoFms2Z"}, {"__ref": "Z6GoBtYOptqo"}, {"__ref": "beIF61l89WIC"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DllKIXAyh5UP", "parent": {"__ref": "ZDPwdVBBrgYI"}, "locked": null, "vsettings": [{"__ref": "OQEWENIfpi5-"}, {"__ref": "83E6u4NeABM3"}], "__type": "TplTag"}, "rZl1SkoFms2Z": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "spYOtJYdUTLD", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "73B_3r6ina-x"}], "__type": "TplTag"}, "Z6GoBtYOptqo": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "DlTH_k504aot", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "wz3FW2Cj5NXL"}], "__type": "TplTag"}, "OQEWENIfpi5-": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "66HY2Ju8B857"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "83E6u4NeABM3": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "hI_W_vtrTB4m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "73B_3r6ina-x": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "LleI-9WZGPYs"}, "dataCond": null, "dataRep": null, "text": {"__ref": "365AkSlNTyTj"}, "columnsConfig": null, "__type": "VariantSetting"}, "wz3FW2Cj5NXL": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "ntU0nqHKbTpo"}, "dataCond": null, "dataRep": null, "text": {"__ref": "wzfdLfQcOKVW"}, "columnsConfig": null, "__type": "VariantSetting"}, "66HY2Ju8B857": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "plasmic-layout-full-bleed", "height": "wrap", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "96px", "padding-top": "96px", "grid-row-gap": "16px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "hI_W_vtrTB4m": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LleI-9WZGPYs": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "margin-bottom": "32px"}, "mixins": [], "__type": "RuleSet"}, "365AkSlNTyTj": {"markers": [], "text": "Welcome to your first page.", "__type": "RawText"}, "ntU0nqHKbTpo": {"values": {"position": "relative", "width": "stretch", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "wzfdLfQcOKVW": {"markers": [{"__ref": "h29L2P0ZFn5U"}], "text": "If you haven't already done so, go back and learn the basics by going through the Plasmic Levels tutorial.\n\nIt's always easier to start from examples! Add a new page using a template—do this from the list of pages in the top toolbar.\n\nOr press the big blue + button to start inserting items into this page.\n\nIntegrate this project into your codebase—press the Code button in the top right and follow the quickstart instructions.\n\nJoin our Slack community (icon in bottom left) for help any time.", "__type": "RawText"}, "h29L2P0ZFn5U": {"rs": {"__ref": "JbwI01KxVj_0"}, "position": 360, "length": 4, "__type": "<PERSON><PERSON>arker"}, "JbwI01KxVj_0": {"values": {"font-weight": "700"}, "mixins": [], "__type": "RuleSet"}, "beIF61l89WIC": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "OFEUtqimoqvW", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "cL_KvYPtoOCq"}], "__type": "TplComponent"}, "cL_KvYPtoOCq": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [{"__ref": "jbh1_pCe3_XH"}], "attrs": {}, "rs": {"__ref": "-PzhyiMMqlQe"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-PzhyiMMqlQe": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "jbh1_pCe3_XH": {"param": {"__ref": "_o8wXWGE6gDQ"}, "expr": {"__ref": "aBI1euXi0cR-"}, "__type": "Arg"}, "aBI1euXi0cR-": {"variants": [{"__ref": "OEZZxLh2cAyc"}], "__type": "VariantsRef"}, "wZETMFZPlnHe": {"uuid": "M3Uenm6rdtPj", "name": "TestComponent", "params": [{"__ref": "_o8wXWGE6gDQ"}, {"__ref": "qo7j9NNEldQ9"}], "states": [{"__ref": "8TbzWoBssL6F"}], "tplTree": {"__ref": "EE3goD6hVeQl"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "1WRxQ1nbx7W6"}, {"__ref": "OTMLuzywK3hM"}], "variantGroups": [{"__ref": "pe_KJm0dXuQN"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "c4NxC8A_Qtrg": {"component": {"__ref": "wZETMFZPlnHe"}, "matrix": {"__ref": "wyH1otRQlS4E"}, "customMatrix": {"__ref": "TsVEXHVpb1CF"}, "__type": "ComponentArena"}, "_o8wXWGE6gDQ": {"type": {"__ref": "uvOamMNdNJFW"}, "state": {"__ref": "8TbzWoBssL6F"}, "variable": {"__ref": "xpW7gYzudfX2"}, "uuid": "nFaH5O0pX74T", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "qo7j9NNEldQ9": {"type": {"__ref": "CkB0xXeAO43U"}, "state": {"__ref": "8TbzWoBssL6F"}, "variable": {"__ref": "sjP2m6FJ_pmW"}, "uuid": "ktT_Ya6tU3_b", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "8TbzWoBssL6F": {"variantGroup": {"__ref": "pe_KJm0dXuQN"}, "param": {"__ref": "_o8wXWGE6gDQ"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "qo7j9NNEldQ9"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "EE3goD6hVeQl": {"tag": "div", "name": null, "children": [{"__ref": "NaKB4Ld0Rrnj"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "pd-Ka3JNbGbx", "parent": null, "locked": null, "vsettings": [{"__ref": "SYqfT68AvosY"}, {"__ref": "waVF0UjmUWuL"}, {"__ref": "6T0XSZluj82j"}], "__type": "TplTag"}, "1WRxQ1nbx7W6": {"uuid": "f-o2Nu8Hw1q4", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "OTMLuzywK3hM": {"uuid": "o_bNLQkUKCcy", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "pe_KJm0dXuQN": {"type": "component", "param": {"__ref": "_o8wXWGE6gDQ"}, "linkedState": {"__ref": "8TbzWoBssL6F"}, "uuid": "6s4NZZKGgUpb", "variants": [{"__ref": "OEZZxLh2cAyc"}], "multi": false, "__type": "ComponentVariantGroup"}, "wyH1otRQlS4E": {"rows": [{"__ref": "yXKxJeiGaMMn"}, {"__ref": "PKqnQXM_8H_-"}], "__type": "ArenaFrameGrid"}, "TsVEXHVpb1CF": {"rows": [{"__ref": "n1ZUc5fUwylM"}], "__type": "ArenaFrameGrid"}, "uvOamMNdNJFW": {"name": "text", "__type": "Text"}, "xpW7gYzudfX2": {"name": "isActive", "uuid": "FDMNwKBf3OZv", "__type": "Var"}, "CkB0xXeAO43U": {"name": "func", "params": [{"__ref": "9ifHfgwY_ZiL"}], "__type": "FunctionType"}, "sjP2m6FJ_pmW": {"name": "On isActive change", "uuid": "0KSAlBv2NEFe", "__type": "Var"}, "NaKB4Ld0Rrnj": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "OG_ALlyDYvIa", "parent": {"__ref": "EE3goD6hVeQl"}, "locked": null, "vsettings": [{"__ref": "9LN6mtB-M76w"}, {"__ref": "bIQXVeP0l7yG"}], "__type": "TplTag"}, "SYqfT68AvosY": {"variants": [{"__ref": "1WRxQ1nbx7W6"}], "args": [], "attrs": {}, "rs": {"__ref": "RdM5HVM6dqlD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "waVF0UjmUWuL": {"variants": [{"__ref": "OEZZxLh2cAyc"}], "args": [], "attrs": {}, "rs": {"__ref": "9ht2PyWgJieR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6T0XSZluj82j": {"variants": [{"__ref": "OTMLuzywK3hM"}], "args": [], "attrs": {}, "rs": {"__ref": "Z-Xuuw4dyb4s"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OEZZxLh2cAyc": {"uuid": "FaBBhkkhvYho", "name": "isActive", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "pe_KJm0dXuQN"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "yXKxJeiGaMMn": {"cols": [{"__ref": "a7Fdx64w88_P"}, {"__ref": "6xL2uN0ttyXv"}], "rowKey": null, "__type": "ArenaFrameRow"}, "PKqnQXM_8H_-": {"cols": [{"__ref": "gxw4YlZoxz8P"}], "rowKey": {"__ref": "pe_KJm0dXuQN"}, "__type": "ArenaFrameRow"}, "n1ZUc5fUwylM": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "9ifHfgwY_ZiL": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "fM1UfccswL1t"}, "__type": "ArgType"}, "9LN6mtB-M76w": {"variants": [{"__ref": "1WRxQ1nbx7W6"}], "args": [], "attrs": {}, "rs": {"__ref": "RFa2uP6JnWHs"}, "dataCond": null, "dataRep": null, "text": {"__ref": "SGvoBEM00T5U"}, "columnsConfig": null, "__type": "VariantSetting"}, "bIQXVeP0l7yG": {"variants": [{"__ref": "OTMLuzywK3hM"}], "args": [], "attrs": {}, "rs": {"__ref": "MIYbFLNhI-Ke"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RdM5HVM6dqlD": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "center", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "9ht2PyWgJieR": {"values": {"background": "linear-gradient(#FFEEDD, #FFEEDD)"}, "mixins": [], "__type": "RuleSet"}, "Z-Xuuw4dyb4s": {"values": {}, "mixins": [], "__type": "RuleSet"}, "a7Fdx64w88_P": {"frame": {"__ref": "scWdBJMwghV0"}, "cellKey": {"__ref": "1WRxQ1nbx7W6"}, "__type": "ArenaFrameCell"}, "6xL2uN0ttyXv": {"frame": {"__ref": "CzqAoR5rckhE"}, "cellKey": {"__ref": "OTMLuzywK3hM"}, "__type": "ArenaFrameCell"}, "gxw4YlZoxz8P": {"frame": {"__ref": "ggozz9bmtiNF"}, "cellKey": {"__ref": "OEZZxLh2cAyc"}, "__type": "ArenaFrameCell"}, "fM1UfccswL1t": {"name": "any", "__type": "AnyType"}, "RFa2uP6JnWHs": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "SGvoBEM00T5U": {"markers": [], "text": "Sample text", "__type": "RawText"}, "MIYbFLNhI-Ke": {"values": {"color": "#0005FF"}, "mixins": [], "__type": "RuleSet"}, "scWdBJMwghV0": {"uuid": "1MYNXL9ID3uJ", "width": 1180, "height": 540, "container": {"__ref": "_2BZ1Nw8Xm6h"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1WRxQ1nbx7W6"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "CzqAoR5rckhE": {"uuid": "e3A23m1mB93x", "width": 1180, "height": 540, "container": {"__ref": "728wMF6MdNBp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "OTMLuzywK3hM"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ggozz9bmtiNF": {"uuid": "fr1x7PC8dwvO", "width": 1180, "height": 540, "container": {"__ref": "IokWQa7D91SG"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "OEZZxLh2cAyc"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "_2BZ1Nw8Xm6h": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "xUexvpqMYxFa", "parent": null, "locked": null, "vsettings": [{"__ref": "SIi8Z0nkPgVB"}], "__type": "TplComponent"}, "728wMF6MdNBp": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "AvuJDRt3Qv1i", "parent": null, "locked": null, "vsettings": [{"__ref": "gMJqdycZxFGC"}], "__type": "TplComponent"}, "IokWQa7D91SG": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "_Z56LzelGt3j", "parent": null, "locked": null, "vsettings": [{"__ref": "ly3wd8OExEb9"}], "__type": "TplComponent"}, "SIi8Z0nkPgVB": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "F8J5zhkKGg05"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gMJqdycZxFGC": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "zVOrpQPwGviE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ly3wd8OExEb9": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "FvZtdW8TCk1o"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F8J5zhkKGg05": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zVOrpQPwGviE": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FvZtdW8TCk1o": {"values": {}, "mixins": [], "__type": "RuleSet"}, "N7JO55l7AaSP": {"uuid": "Co-VuC4WaJcK", "pkgId": "b77fce8e-3031-4bd5-8c4f-74f8a93824fe", "projectId": "uGrWS46cRJ2qquYfzAsaTw", "version": "6.2.0", "name": "Child", "site": {"__ref": "WvF3aLJv68QX"}, "__type": "ProjectDependency"}}, "deps": ["e59019e7-b245-4e0c-b862-91a12b98b3c5"], "version": "246-add-component-updated-at"}, "projectId": "uGrWS46cRJ2qquYfzAsaTw", "version": "6.2.0", "branchId": "main"}], "project": {"id": "uGrWS46cRJ2qquYfzAsaTw", "name": "Child", "commitGraph": {"parents": {"089dc2a4-bf16-4102-a647-51991d533a50": ["3d1588b7-545e-41ae-ad2b-52a6b9535842", "1e166cc5-59d5-43e3-8b71-e5ca69625836"], "0b2a4374-aefe-44c6-9521-463699ae1de0": ["df00f58c-955d-43da-b44b-7cd3546d171c", "15882404-2863-46b7-9679-b013f376e887"], "0d2075ac-4a51-43aa-8c95-80640f7ce7e9": ["1e166cc5-59d5-43e3-8b71-e5ca69625836"], "15882404-2863-46b7-9679-b013f376e887": ["9316fc34-5087-4d86-ae01-45ea1586b93e"], "1b1471f3-40cd-4373-870b-d731f2321ded": ["3d1588b7-545e-41ae-ad2b-52a6b9535842", "1e166cc5-59d5-43e3-8b71-e5ca69625836"], "1e166cc5-59d5-43e3-8b71-e5ca69625836": ["395edb89-da95-41dd-a072-67b3b317934c"], "395edb89-da95-41dd-a072-67b3b317934c": ["921131f3-5b08-43a3-878d-59c91efe8b76"], "3d1588b7-545e-41ae-ad2b-52a6b9535842": ["921131f3-5b08-43a3-878d-59c91efe8b76"], "441e449d-83e0-4fe0-ae8e-79c6bcb51323": ["395edb89-da95-41dd-a072-67b3b317934c", "3d1588b7-545e-41ae-ad2b-52a6b9535842"], "46028c27-238b-45d4-9d6a-39927594a899": ["3d1588b7-545e-41ae-ad2b-52a6b9535842", "1e166cc5-59d5-43e3-8b71-e5ca69625836"], "641f3651-5ae5-4a79-90b4-9b59ab8152a7": ["3d1588b7-545e-41ae-ad2b-52a6b9535842", "1e166cc5-59d5-43e3-8b71-e5ca69625836"], "6919417b-8ae0-4404-bca3-cfcbe59b5a43": ["e4b38d74-61e3-40b5-9a49-2f8b039e0f40"], "862efb6b-906b-46f6-8564-f92cc22ebf48": ["3d1588b7-545e-41ae-ad2b-52a6b9535842", "1e166cc5-59d5-43e3-8b71-e5ca69625836"], "921131f3-5b08-43a3-878d-59c91efe8b76": ["0b2a4374-aefe-44c6-9521-463699ae1de0"], "9316fc34-5087-4d86-ae01-45ea1586b93e": ["9e8c4f16-b9b1-4130-a322-3fd7fd98c17c"], "9e8c4f16-b9b1-4130-a322-3fd7fd98c17c": ["a2ea45a4-e8c0-492d-8f0c-4dd2eb286f5c", "cf28e84e-6736-4e3d-9e76-4f25998a73e7"], "a2ea45a4-e8c0-492d-8f0c-4dd2eb286f5c": ["a43b9014-93ca-4f5b-b076-44b43dc22d42"], "a43b9014-93ca-4f5b-b076-44b43dc22d42": [], "b4ddfa96-f81c-4a01-a933-59f75371ef36": ["b690e5b2-0815-4429-ae04-c8f885763e08"], "b690e5b2-0815-4429-ae04-c8f885763e08": ["6919417b-8ae0-4404-bca3-cfcbe59b5a43"], "bc4c764d-1f92-4aad-b154-d5da115913b6": ["0d2075ac-4a51-43aa-8c95-80640f7ce7e9"], "cf28e84e-6736-4e3d-9e76-4f25998a73e7": ["a43b9014-93ca-4f5b-b076-44b43dc22d42"], "d8f1de46-744f-489a-928c-89f512233c28": ["0d2075ac-4a51-43aa-8c95-80640f7ce7e9"], "df00f58c-955d-43da-b44b-7cd3546d171c": ["9316fc34-5087-4d86-ae01-45ea1586b93e"], "e0669591-47e0-43cc-b5e0-23b6381e76c0": ["3d1588b7-545e-41ae-ad2b-52a6b9535842", "1e166cc5-59d5-43e3-8b71-e5ca69625836"], "e4b38d74-61e3-40b5-9a49-2f8b039e0f40": ["bc4c764d-1f92-4aad-b154-d5da115913b6", "d8f1de46-744f-489a-928c-89f512233c28"]}, "branches": {"main": "b4ddfa96-f81c-4a01-a933-59f75371ef36", "4dTbw1F4vUk8EqorAFen7A": "cf28e84e-6736-4e3d-9e76-4f25998a73e7", "8AbpysoczKDnbRUNtbdTUr": "b690e5b2-0815-4429-ae04-c8f885763e08", "cBQa6bMZkY5DrJW126akEt": "d8f1de46-744f-489a-928c-89f512233c28", "id9Q59ggm7y5f18A6AtHV4": "3d1588b7-545e-41ae-ad2b-52a6b9535842", "sM4MH983JWA8gZko5UH2Nm": "15882404-2863-46b7-9679-b013f376e887"}}}, "revisions": [{"branchId": "8AbpysoczKDnbRUNtbdTUr", "data": {"root": "WvF3aLJv68QX", "map": {"BhdY42hR-Lxp": {"uuid": "VdcBCMHPMQlx", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Liea6UxaQwij": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "stretch", "height": "stretch", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "H_n_hCTG1w_q": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "Liea6UxaQwij"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PUieQAmiKo2S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BtyealwHhehP": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "PUieQAmiKo2S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZDPwdVBBrgYI": {"tag": "div", "name": null, "children": [{"__ref": "rz0W8-Im1Lcy"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "iiprFWcIs5p9", "parent": null, "locked": null, "vsettings": [{"__ref": "H_n_hCTG1w_q"}, {"__ref": "BtyealwHhehP"}], "__type": "TplTag"}, "1TSmsL3E3KVw": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "cLsMrVaCAsM_": {"uuid": "V_IlQKz1I1ss", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "ZDPwdVBBrgYI"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BhdY42hR-Lxp"}], "variantGroups": [], "pageMeta": {"__ref": "1TSmsL3E3KVw"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "agdNyxwUb5LL": {"name": "title", "uuid": "pbWtBHXt7QzT", "__type": "Var"}, "tl8IqtfC00W0": {"name": "text", "__type": "Text"}, "vIku7dYn2lOk": {"type": {"__ref": "tl8IqtfC00W0"}, "variable": {"__ref": "agdNyxwUb5LL"}, "uuid": "lX0fSYLoAuTi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wA9spTLub2Jx": {"name": "description", "uuid": "fsfDmxOurgZ7", "__type": "Var"}, "R_kYr-icqfH5": {"name": "text", "__type": "Text"}, "ajfByMtDR3SD": {"type": {"__ref": "R_kYr-icqfH5"}, "variable": {"__ref": "wA9spTLub2Jx"}, "uuid": "5q5CzxIwtCyj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hRXycIUSLkF0": {"name": "image", "uuid": "iq0x3v5oNjO6", "__type": "Var"}, "1d2qvAHmLYTX": {"name": "img", "__type": "Img"}, "4J8hp6CarOdy": {"type": {"__ref": "1d2qvAHmLYTX"}, "variable": {"__ref": "hRXycIUSLkF0"}, "uuid": "bCDdnuOSQLZM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "P27wGUv-Qgx9": {"name": "canonical", "uuid": "Jdgf8PrJBGfp", "__type": "Var"}, "pOM2DYQvazEW": {"name": "text", "__type": "Text"}, "Tdo3ZL-cW2hP": {"type": {"__ref": "pOM2DYQvazEW"}, "variable": {"__ref": "P27wGUv-Qgx9"}, "uuid": "DILE13O2n51m", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "LU8FftYLeIfh": {"uuid": "Sf2JPX0o5bC9", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "TBXkCcEW95C6": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "89sUQoymcgJU": {"variants": [{"__ref": "LU8FftYLeIfh"}], "args": [], "attrs": {}, "rs": {"__ref": "TBXkCcEW95C6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oBh8b7--oQKJ": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "UYjOwa6Pijgu", "parent": null, "locked": null, "vsettings": [{"__ref": "89sUQoymcgJU"}], "__type": "TplTag"}, "eTWlajcHbsXS": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "iVgviOD2y313": {"uuid": "7dNVhxXEfmzt", "name": "hostless-plasmic-head", "params": [{"__ref": "vIku7dYn2lOk"}, {"__ref": "ajfByMtDR3SD"}, {"__ref": "4J8hp6CarOdy"}, {"__ref": "Tdo3ZL-cW2hP"}], "states": [], "tplTree": {"__ref": "oBh8b7--oQKJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "LU8FftYLeIfh"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "eTWlajcHbsXS"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "mn-i0wyTX_8d": {"name": "dataOp", "uuid": "aw3To6-iRxiT", "__type": "Var"}, "1gZ8WLiSqinW": {"name": "any", "__type": "AnyType"}, "XbCO6KZXdTOI": {"type": {"__ref": "1gZ8WLiSqinW"}, "variable": {"__ref": "mn-i0wyTX_8d"}, "uuid": "kDGPzXajGZuF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4TJiowzsgSA6": {"name": "name", "uuid": "qkFDzgOGuun7", "__type": "Var"}, "gqsxkw4HNnT6": {"name": "text", "__type": "Text"}, "wKY8XDohrk5E": {"type": {"__ref": "gqsxkw4HNnT6"}, "variable": {"__ref": "4TJiowzsgSA6"}, "uuid": "MSEh5mmXKnEv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "e1AHNXBi-gbX": {"name": "children", "uuid": "hjv8itvMaejl", "__type": "Var"}, "0s4Xm5sA3bgk": {"name": "any", "__type": "AnyType"}, "yFoizy9jwCH3": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "0s4Xm5sA3bgk"}, "__type": "ArgType"}, "mkhxIrcCKe0I": {"name": "renderFunc", "params": [{"__ref": "yFoizy9jwCH3"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "4w6nDpMQsoDl": {"type": {"__ref": "mkhxIrcCKe0I"}, "tplSlot": {"__ref": "EQOoFeB1jJWg"}, "variable": {"__ref": "e1AHNXBi-gbX"}, "uuid": "pDF7Mkhr0sj3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "hABaEvXCIzy7": {"name": "pageSize", "uuid": "4iBDc_66nKg4", "__type": "Var"}, "NJDmOFISnXrV": {"name": "num", "__type": "<PERSON><PERSON>"}, "YJdR63-NsHsj": {"type": {"__ref": "NJDmOFISnXrV"}, "variable": {"__ref": "hABaEvXCIzy7"}, "uuid": "dd4DrRA-g_tA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "8BvioTbKdGPX": {"name": "pageIndex", "uuid": "HF4o8INzirSv", "__type": "Var"}, "5CvNPBoS8Eej": {"name": "num", "__type": "<PERSON><PERSON>"}, "fFblb4jAPw46": {"type": {"__ref": "5CvNPBoS8Eej"}, "variable": {"__ref": "8BvioTbKdGPX"}, "uuid": "UHhciNCazNro", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "JK1qk-LNA9xF": {"uuid": "12arXxMPC7fK", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "6gmMTOrciMXi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mURWtnYSURxx": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "6gmMTOrciMXi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EQOoFeB1jJWg": {"param": {"__ref": "4w6nDpMQsoDl"}, "defaultContents": [], "uuid": "pjoD2AVLM97A", "parent": {"__ref": "b852meGjAbbJ"}, "locked": null, "vsettings": [{"__ref": "mURWtnYSURxx"}], "__type": "TplSlot"}, "y6b8w_eJA3hG": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "oKYA3Ni4Izig": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "y6b8w_eJA3hG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "b852meGjAbbJ": {"tag": "div", "name": null, "children": [{"__ref": "EQOoFeB1jJWg"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "o1VCnil8mlLe", "parent": null, "locked": null, "vsettings": [{"__ref": "oKYA3Ni4Izig"}], "__type": "TplTag"}, "YUR9v-cVROMw": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "CGTKnrhUwnGJ": {"uuid": "EgSmz5VbPf14", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "XbCO6KZXdTOI"}, {"__ref": "wKY8XDohrk5E"}, {"__ref": "4w6nDpMQsoDl"}, {"__ref": "YJdR63-NsHsj"}, {"__ref": "fFblb4jAPw46"}], "states": [], "tplTree": {"__ref": "b852meGjAbbJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "JK1qk-LNA9xF"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "YUR9v-cVROMw"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "O6D5BizBFFPl": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "9rWFEsD51ijs": {"name": "Default Typography", "rs": {"__ref": "O6D5BizBFFPl"}, "preview": null, "uuid": "65YsmemuB5Gk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "B16VruBHaw4V": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "JuYFCtRAZj1L": {"name": "Default \"h1\"", "rs": {"__ref": "B16VruBHaw4V"}, "preview": null, "uuid": "N5whCaIyI5yq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "FW8XAtNSjtu6": {"selector": "h1", "style": {"__ref": "JuYFCtRAZj1L"}, "__type": "ThemeStyle"}, "_2Bcb1Mu36uY": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "p1iCCLwkpDoy": {"name": "Default \"h2\"", "rs": {"__ref": "_2Bcb1Mu36uY"}, "preview": null, "uuid": "591o8eV6APJY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EXyTxUa4ABV-": {"selector": "h2", "style": {"__ref": "p1iCCLwkpDoy"}, "__type": "ThemeStyle"}, "coKpJBdyxM1B": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "yyzKh6WaoGU3": {"name": "Default \"a\"", "rs": {"__ref": "coKpJBdyxM1B"}, "preview": null, "uuid": "OzvmmY5ljIKh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "q9zp2dIwMR4H": {"selector": "a", "style": {"__ref": "yyzKh6WaoGU3"}, "__type": "ThemeStyle"}, "5VOKWTPo9qQ-": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "aQGs2EzpDklN": {"name": "Default \"h3\"", "rs": {"__ref": "5VOKWTPo9qQ-"}, "preview": null, "uuid": "NnRLc2qrbHrp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ySMS8lwIR8e4": {"selector": "h3", "style": {"__ref": "aQGs2EzpDklN"}, "__type": "ThemeStyle"}, "1Eo6Elb-mOXu": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "11M5AU1J79oZ": {"name": "Default \"h4\"", "rs": {"__ref": "1Eo6Elb-mOXu"}, "preview": null, "uuid": "Fw2NyZXagP3_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TPT4LIw_FlmE": {"selector": "h4", "style": {"__ref": "11M5AU1J79oZ"}, "__type": "ThemeStyle"}, "Ap8tydcErz0o": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "jff-39xNBt4h": {"name": "Default \"code\"", "rs": {"__ref": "Ap8tydcErz0o"}, "preview": null, "uuid": "96ch0AkhaXOC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ml1ObT0h6WF0": {"selector": "code", "style": {"__ref": "jff-39xNBt4h"}, "__type": "ThemeStyle"}, "meGo-3SsDjxW": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "Rk4vU0kpCLqX": {"name": "Default \"blockquote\"", "rs": {"__ref": "meGo-3SsDjxW"}, "preview": null, "uuid": "gYG9UeAQLGHl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "uuwyZ8MaMwzC": {"selector": "blockquote", "style": {"__ref": "Rk4vU0kpCLqX"}, "__type": "ThemeStyle"}, "Xm8vVOguc99Z": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "pNJ3JD644f1B": {"name": "Default \"pre\"", "rs": {"__ref": "Xm8vVOguc99Z"}, "preview": null, "uuid": "lWS_bqGRCyQI", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xADr-YcMKj-A": {"selector": "pre", "style": {"__ref": "pNJ3JD644f1B"}, "__type": "ThemeStyle"}, "sb9QqZPTqrMD": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "oG1vMCxc_rAf": {"name": "Default \"ul\"", "rs": {"__ref": "sb9QqZPTqrMD"}, "preview": null, "uuid": "4fBIMOGTQv9T", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vWcy13o2wF2w": {"selector": "ul", "style": {"__ref": "oG1vMCxc_rAf"}, "__type": "ThemeStyle"}, "OJMyIbBCwzBE": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "WIkY9opOarFu": {"name": "Default \"ol\"", "rs": {"__ref": "OJMyIbBCwzBE"}, "preview": null, "uuid": "5JPbmKB_mLIY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ebOB-q1gpIYe": {"selector": "ol", "style": {"__ref": "WIkY9opOarFu"}, "__type": "ThemeStyle"}, "LJZHTjVy8-KX": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "q_up9_dZGmRp": {"name": "Default \"h5\"", "rs": {"__ref": "LJZHTjVy8-KX"}, "preview": null, "uuid": "HFTrV2sHI6_v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HpQe3u3igwNK": {"selector": "h5", "style": {"__ref": "q_up9_dZGmRp"}, "__type": "ThemeStyle"}, "36QFg90QpNzF": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "2qvlcfRG1Yqf": {"name": "Default \"h6\"", "rs": {"__ref": "36QFg90QpNzF"}, "preview": null, "uuid": "7ne0HsGQbiX-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "AHK6bdjMUFCZ": {"selector": "h6", "style": {"__ref": "2qvlcfRG1Yqf"}, "__type": "ThemeStyle"}, "VTuC2NSDgvTw": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "kocbDeNmD10M": {"name": "Default \"a:hover\"", "rs": {"__ref": "VTuC2NSDgvTw"}, "preview": null, "uuid": "pv_rGaCsBcE7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TLsnPhLuZijX": {"selector": "a:hover", "style": {"__ref": "kocbDeNmD10M"}, "__type": "ThemeStyle"}, "8NKHRGPgmO_7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2R5gsgHL1SEg": {"name": "Default \"li\"", "rs": {"__ref": "8NKHRGPgmO_7"}, "preview": null, "uuid": "SGgFYYxtVP9N", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "-HxiJ-vruiDF": {"selector": "li", "style": {"__ref": "2R5gsgHL1SEg"}, "__type": "ThemeStyle"}, "400JXoUXp8-f": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NUhspy26QvGl": {"name": "Default \"p\"", "rs": {"__ref": "400JXoUXp8-f"}, "preview": null, "uuid": "YsYyl_vkV4o4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "u1Jb5Ps_CziS": {"selector": "p", "style": {"__ref": "NUhspy26QvGl"}, "__type": "ThemeStyle"}, "2BVmOrsFr2nd": {"defaultStyle": {"__ref": "9rWFEsD51ijs"}, "styles": [{"__ref": "FW8XAtNSjtu6"}, {"__ref": "EXyTxUa4ABV-"}, {"__ref": "q9zp2dIwMR4H"}, {"__ref": "ySMS8lwIR8e4"}, {"__ref": "TPT4LIw_FlmE"}, {"__ref": "ml1ObT0h6WF0"}, {"__ref": "uuwyZ8MaMwzC"}, {"__ref": "xADr-YcMKj-A"}, {"__ref": "vWcy13o2wF2w"}, {"__ref": "ebOB-q1gpIYe"}, {"__ref": "HpQe3u3igwNK"}, {"__ref": "AHK6bdjMUFCZ"}, {"__ref": "TLsnPhLuZijX"}, {"__ref": "-HxiJ-vruiDF"}, {"__ref": "u1Jb5Ps_CziS"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "ePPLz0oUoM9g": {"name": "Screen", "uuid": "lc-5gWiYQiUw", "__type": "Var"}, "2WZaQOllH-qH": {"name": "text", "__type": "Text"}, "fTkyo-D50byd": {"type": {"__ref": "2WZaQOllH-qH"}, "variable": {"__ref": "ePPLz0oUoM9g"}, "uuid": "pBmgCu3yQB9T", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "ZWPc7EzDIVMW": {"uuid": "C5gJ7OmaUrOL", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "rO6y12j3WBms"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rO6y12j3WBms": {"type": "global-screen", "param": {"__ref": "fTkyo-D50byd"}, "uuid": "JHdfnMT9T35R", "variants": [{"__ref": "ZWPc7EzDIVMW"}], "multi": true, "__type": "GlobalVariantGroup"}, "752Z_tQ_x_fR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Rbqm34gRqzJz": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "752Z_tQ_x_fR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eZb3xelRiEfN": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9yvTWSyqzEZQ", "parent": null, "locked": null, "vsettings": [{"__ref": "Rbqm34gRqzJz"}], "__type": "TplComponent"}, "4jOaAgi9Lh-B": {"uuid": "s0dGF-mxGILF", "width": 1440, "height": 1024, "container": {"__ref": "eZb3xelRiEfN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "BhdY42hR-Lxp"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "17QEwua0drdO": {"frame": {"__ref": "4jOaAgi9Lh-B"}, "cellKey": null, "__type": "ArenaFrameCell"}, "7-_NchqVyaKS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aHX9dM82Ufr4": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "7-_NchqVyaKS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3bZjULCuzoCU": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9CS1eJrJD4S8", "parent": null, "locked": null, "vsettings": [{"__ref": "aHX9dM82Ufr4"}], "__type": "TplComponent"}, "rVj4nSXnpwV4": {"uuid": "tgpZSeTC7Yp7", "width": 414, "height": 736, "container": {"__ref": "3bZjULCuzoCU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"C5gJ7OmaUrOL": true}, "targetGlobalVariants": [{"__ref": "ZWPc7EzDIVMW"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "UhAjO5P4kFFM": {"frame": {"__ref": "rVj4nSXnpwV4"}, "cellKey": null, "__type": "ArenaFrameCell"}, "h-_-qgw0akdP": {"cols": [{"__ref": "17QEwua0drdO"}, {"__ref": "UhAjO5P4kFFM"}], "rowKey": {"__ref": "BhdY42hR-Lxp"}, "__type": "ArenaFrameRow"}, "fyh3Qla9RLSG": {"rows": [{"__ref": "h-_-qgw0akdP"}], "__type": "ArenaFrameGrid"}, "Ej9GYeyLIBLC": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3ti82_n-06ko": {"rows": [{"__ref": "Ej9GYeyLIBLC"}], "__type": "ArenaFrameGrid"}, "ZfWiEZ1-0hE6": {"component": {"__ref": "cLsMrVaCAsM_"}, "matrix": {"__ref": "fyh3Qla9RLSG"}, "customMatrix": {"__ref": "3ti82_n-06ko"}, "__type": "PageArena"}, "bNQ0GDteuhaj": {"uuid": "nhuuHLIvqj5r", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WvF3aLJv68QX": {"components": [{"__ref": "cLsMrVaCAsM_"}, {"__ref": "iVgviOD2y313"}, {"__ref": "CGTKnrhUwnGJ"}, {"__ref": "292dX-QIv1S3"}], "arenas": [], "pageArenas": [{"__ref": "ZfWiEZ1-0hE6"}], "componentArenas": [{"__ref": "SWwKh67xg6uK"}], "globalVariantGroups": [{"__ref": "rO6y12j3WBms"}], "userManagedFonts": [], "globalVariant": {"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "2BVmOrsFr2nd"}], "activeTheme": {"__ref": "2BVmOrsFr2nd"}, "imageAssets": [], "projectDependencies": [{"__xref": {"uuid": "e59019e7-b245-4e0c-b862-91a12b98b3c5", "iid": "N1ERkITzhyF3"}}], "activeScreenVariantGroup": {"__ref": "rO6y12j3WBms"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "rz0W8-Im1Lcy": {"tag": "section", "name": null, "children": [{"__ref": "rZl1SkoFms2Z"}, {"__ref": "Z6GoBtYOptqo"}, {"__ref": "beIF61l89WIC"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DllKIXAyh5UP", "parent": {"__ref": "ZDPwdVBBrgYI"}, "locked": null, "vsettings": [{"__ref": "OQEWENIfpi5-"}, {"__ref": "83E6u4NeABM3"}], "__type": "TplTag"}, "rZl1SkoFms2Z": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "spYOtJYdUTLD", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "73B_3r6ina-x"}], "__type": "TplTag"}, "Z6GoBtYOptqo": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "DlTH_k504aot", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "wz3FW2Cj5NXL"}], "__type": "TplTag"}, "OQEWENIfpi5-": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "66HY2Ju8B857"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "83E6u4NeABM3": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "hI_W_vtrTB4m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "73B_3r6ina-x": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "LleI-9WZGPYs"}, "dataCond": null, "dataRep": null, "text": {"__ref": "365AkSlNTyTj"}, "columnsConfig": null, "__type": "VariantSetting"}, "wz3FW2Cj5NXL": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "ntU0nqHKbTpo"}, "dataCond": null, "dataRep": null, "text": {"__ref": "wzfdLfQcOKVW"}, "columnsConfig": null, "__type": "VariantSetting"}, "66HY2Ju8B857": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "plasmic-layout-full-bleed", "height": "wrap", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "96px", "padding-top": "96px", "grid-row-gap": "16px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "hI_W_vtrTB4m": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LleI-9WZGPYs": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "margin-bottom": "32px"}, "mixins": [], "__type": "RuleSet"}, "365AkSlNTyTj": {"markers": [], "text": "Welcome to your first page.", "__type": "RawText"}, "ntU0nqHKbTpo": {"values": {"position": "relative", "width": "stretch", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "wzfdLfQcOKVW": {"markers": [{"__ref": "h29L2P0ZFn5U"}], "text": "If you haven't already done so, go back and learn the basics by going through the Plasmic Levels tutorial.\n\nIt's always easier to start from examples! Add a new page using a template—do this from the list of pages in the top toolbar.\n\nOr press the big blue + button to start inserting items into this page.\n\nIntegrate this project into your codebase—press the Code button in the top right and follow the quickstart instructions.\n\nJoin our Slack community (icon in bottom left) for help any time.", "__type": "RawText"}, "h29L2P0ZFn5U": {"rs": {"__ref": "JbwI01KxVj_0"}, "position": 360, "length": 4, "__type": "<PERSON><PERSON>arker"}, "JbwI01KxVj_0": {"values": {"font-weight": "700"}, "mixins": [], "__type": "RuleSet"}, "beIF61l89WIC": {"name": null, "component": {"__ref": "292dX-QIv1S3"}, "uuid": "OFEUtqimoqvW", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "cL_KvYPtoOCq"}], "__type": "TplComponent"}, "cL_KvYPtoOCq": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [{"__ref": "jbh1_pCe3_XH"}], "attrs": {}, "rs": {"__ref": "-PzhyiMMqlQe"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-PzhyiMMqlQe": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "jbh1_pCe3_XH": {"param": {"__ref": "Zt2EZd6FjfSt"}, "expr": {"__ref": "aBI1euXi0cR-"}, "__type": "Arg"}, "aBI1euXi0cR-": {"variants": [{"__ref": "LFI-093Y1b2T"}], "__type": "VariantsRef"}, "292dX-QIv1S3": {"uuid": "4xZdb0c9YV2c", "name": "TestComponent", "params": [{"__ref": "Zt2EZd6FjfSt"}, {"__ref": "Bo4A2laTcY1o"}], "states": [{"__ref": "JnEDyPExgtjB"}], "tplTree": {"__ref": "1WJGj3XktTeY"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "0dRW-y5VJm7g"}, {"__ref": "gZTc4ITiNU6F"}], "variantGroups": [{"__ref": "oqHe44DXnZzr"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "SWwKh67xg6uK": {"component": {"__ref": "292dX-QIv1S3"}, "matrix": {"__ref": "mf2nR9dsbhSQ"}, "customMatrix": {"__ref": "CPveuZ8RHm4h"}, "__type": "ComponentArena"}, "Zt2EZd6FjfSt": {"type": {"__ref": "mi3UbXJDU25H"}, "state": {"__ref": "JnEDyPExgtjB"}, "variable": {"__ref": "AR2kuCqo6R2S"}, "uuid": "I4Xi0bl-3ogb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "Bo4A2laTcY1o": {"type": {"__ref": "NPAEuxHyR_EW"}, "state": {"__ref": "JnEDyPExgtjB"}, "variable": {"__ref": "UHnRjXaoTqCs"}, "uuid": "caFy7z6HS7tw", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "JnEDyPExgtjB": {"variantGroup": {"__ref": "oqHe44DXnZzr"}, "param": {"__ref": "Zt2EZd6FjfSt"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "Bo4A2laTcY1o"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "1WJGj3XktTeY": {"tag": "div", "name": null, "children": [{"__ref": "iePUr2YZgIaa"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "mMODMgimAHUx", "parent": null, "locked": null, "vsettings": [{"__ref": "4ISWqrflz7CN"}, {"__ref": "796w40V2cIOu"}, {"__ref": "Yit4KlU2KnD2"}], "__type": "TplTag"}, "0dRW-y5VJm7g": {"uuid": "2k8IWLDYDATX", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gZTc4ITiNU6F": {"uuid": "evp0rfn646sT", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "oqHe44DXnZzr": {"type": "component", "param": {"__ref": "Zt2EZd6FjfSt"}, "linkedState": {"__ref": "JnEDyPExgtjB"}, "uuid": "D1c0j9z2Mz9e", "variants": [{"__ref": "LFI-093Y1b2T"}], "multi": false, "__type": "ComponentVariantGroup"}, "mf2nR9dsbhSQ": {"rows": [{"__ref": "IosqcuVp1o8M"}, {"__ref": "VZZ-s7GIziqN"}], "__type": "ArenaFrameGrid"}, "CPveuZ8RHm4h": {"rows": [{"__ref": "h8JPhgGs2Bvc"}], "__type": "ArenaFrameGrid"}, "mi3UbXJDU25H": {"name": "text", "__type": "Text"}, "AR2kuCqo6R2S": {"name": "isActive", "uuid": "vWcCuIBv72ip", "__type": "Var"}, "NPAEuxHyR_EW": {"name": "func", "params": [{"__ref": "8cLg0D2wKxte"}], "__type": "FunctionType"}, "UHnRjXaoTqCs": {"name": "On isActive change", "uuid": "FAPWOMTvNx5m", "__type": "Var"}, "iePUr2YZgIaa": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "33b3MfcB6Hro", "parent": {"__ref": "1WJGj3XktTeY"}, "locked": null, "vsettings": [{"__ref": "OMDRBPTOH-55"}, {"__ref": "FAgDKWBrTUlX"}], "__type": "TplTag"}, "4ISWqrflz7CN": {"variants": [{"__ref": "0dRW-y5VJm7g"}], "args": [], "attrs": {}, "rs": {"__ref": "ONufVYbyYnpA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "796w40V2cIOu": {"variants": [{"__ref": "LFI-093Y1b2T"}], "args": [], "attrs": {}, "rs": {"__ref": "JXvQQs50VgCE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Yit4KlU2KnD2": {"variants": [{"__ref": "gZTc4ITiNU6F"}], "args": [], "attrs": {}, "rs": {"__ref": "sAJUiF43hEm3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LFI-093Y1b2T": {"uuid": "wKcAR9o1gkRE", "name": "isActive", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "oqHe44DXnZzr"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "IosqcuVp1o8M": {"cols": [{"__ref": "iDTZUup7b67w"}, {"__ref": "3XNRxwUJyM-V"}], "rowKey": null, "__type": "ArenaFrameRow"}, "VZZ-s7GIziqN": {"cols": [{"__ref": "WjiAJEIO1fio"}], "rowKey": {"__ref": "oqHe44DXnZzr"}, "__type": "ArenaFrameRow"}, "h8JPhgGs2Bvc": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "8cLg0D2wKxte": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "iG5Y_1It8qz-"}, "__type": "ArgType"}, "OMDRBPTOH-55": {"variants": [{"__ref": "0dRW-y5VJm7g"}], "args": [], "attrs": {}, "rs": {"__ref": "XaGG40YqwiT7"}, "dataCond": null, "dataRep": null, "text": {"__ref": "XW_MJ3qPAF-y"}, "columnsConfig": null, "__type": "VariantSetting"}, "FAgDKWBrTUlX": {"variants": [{"__ref": "gZTc4ITiNU6F"}], "args": [], "attrs": {}, "rs": {"__ref": "PfKCRDqHpKf_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ONufVYbyYnpA": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "center", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "JXvQQs50VgCE": {"values": {"background": "linear-gradient(#FFEEDD, #FFEEDD)"}, "mixins": [], "__type": "RuleSet"}, "sAJUiF43hEm3": {"values": {}, "mixins": [], "__type": "RuleSet"}, "iDTZUup7b67w": {"frame": {"__ref": "brNKLi_n0hPp"}, "cellKey": {"__ref": "0dRW-y5VJm7g"}, "__type": "ArenaFrameCell"}, "3XNRxwUJyM-V": {"frame": {"__ref": "9cnFxUc7K-YP"}, "cellKey": {"__ref": "gZTc4ITiNU6F"}, "__type": "ArenaFrameCell"}, "WjiAJEIO1fio": {"frame": {"__ref": "RYMoLEYntr77"}, "cellKey": {"__ref": "LFI-093Y1b2T"}, "__type": "ArenaFrameCell"}, "iG5Y_1It8qz-": {"name": "any", "__type": "AnyType"}, "XaGG40YqwiT7": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "XW_MJ3qPAF-y": {"markers": [], "text": "Sample text", "__type": "RawText"}, "PfKCRDqHpKf_": {"values": {"color": "#0005FF"}, "mixins": [], "__type": "RuleSet"}, "brNKLi_n0hPp": {"uuid": "UH1q_9h0g7TH", "width": 1180, "height": 540, "container": {"__ref": "D-GxvbEaPGv_"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "0dRW-y5VJm7g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "9cnFxUc7K-YP": {"uuid": "iU0Yq98XVhfe", "width": 1180, "height": 540, "container": {"__ref": "jHXYRFVlz0Je"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "gZTc4ITiNU6F"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "RYMoLEYntr77": {"uuid": "af7HqYjrzf8w", "width": 1180, "height": 540, "container": {"__ref": "4ydONV4YdPvY"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "LFI-093Y1b2T"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "D-GxvbEaPGv_": {"name": null, "component": {"__ref": "292dX-QIv1S3"}, "uuid": "ARkFbxBJGV6D", "parent": null, "locked": null, "vsettings": [{"__ref": "2TuSf3wsWcHB"}], "__type": "TplComponent"}, "jHXYRFVlz0Je": {"name": null, "component": {"__ref": "292dX-QIv1S3"}, "uuid": "71V_s5pLv42T", "parent": null, "locked": null, "vsettings": [{"__ref": "9qNs8Tvx_c9U"}], "__type": "TplComponent"}, "4ydONV4YdPvY": {"name": null, "component": {"__ref": "292dX-QIv1S3"}, "uuid": "MSDrGVoCctnq", "parent": null, "locked": null, "vsettings": [{"__ref": "RHpPzP6GyWeH"}], "__type": "TplComponent"}, "2TuSf3wsWcHB": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "m0zhkJ2535Em"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9qNs8Tvx_c9U": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "WOUwUVPZZqVp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RHpPzP6GyWeH": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "HK8YGobPkdqG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "m0zhkJ2535Em": {"values": {}, "mixins": [], "__type": "RuleSet"}, "WOUwUVPZZqVp": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HK8YGobPkdqG": {"values": {}, "mixins": [], "__type": "RuleSet"}}, "deps": ["e59019e7-b245-4e0c-b862-91a12b98b3c5"], "version": "246-add-component-updated-at"}}, {"branchId": "main", "data": {"root": "WvF3aLJv68QX", "map": {"BhdY42hR-Lxp": {"uuid": "VdcBCMHPMQlx", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Liea6UxaQwij": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "stretch", "height": "stretch", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "H_n_hCTG1w_q": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "Liea6UxaQwij"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PUieQAmiKo2S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BtyealwHhehP": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "PUieQAmiKo2S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZDPwdVBBrgYI": {"tag": "div", "name": null, "children": [{"__ref": "rz0W8-Im1Lcy"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "iiprFWcIs5p9", "parent": null, "locked": null, "vsettings": [{"__ref": "H_n_hCTG1w_q"}, {"__ref": "BtyealwHhehP"}], "__type": "TplTag"}, "1TSmsL3E3KVw": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "cLsMrVaCAsM_": {"uuid": "V_IlQKz1I1ss", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "ZDPwdVBBrgYI"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BhdY42hR-Lxp"}], "variantGroups": [], "pageMeta": {"__ref": "1TSmsL3E3KVw"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "agdNyxwUb5LL": {"name": "title", "uuid": "pbWtBHXt7QzT", "__type": "Var"}, "tl8IqtfC00W0": {"name": "text", "__type": "Text"}, "vIku7dYn2lOk": {"type": {"__ref": "tl8IqtfC00W0"}, "variable": {"__ref": "agdNyxwUb5LL"}, "uuid": "lX0fSYLoAuTi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "wA9spTLub2Jx": {"name": "description", "uuid": "fsfDmxOurgZ7", "__type": "Var"}, "R_kYr-icqfH5": {"name": "text", "__type": "Text"}, "ajfByMtDR3SD": {"type": {"__ref": "R_kYr-icqfH5"}, "variable": {"__ref": "wA9spTLub2Jx"}, "uuid": "5q5CzxIwtCyj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hRXycIUSLkF0": {"name": "image", "uuid": "iq0x3v5oNjO6", "__type": "Var"}, "1d2qvAHmLYTX": {"name": "img", "__type": "Img"}, "4J8hp6CarOdy": {"type": {"__ref": "1d2qvAHmLYTX"}, "variable": {"__ref": "hRXycIUSLkF0"}, "uuid": "bCDdnuOSQLZM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "P27wGUv-Qgx9": {"name": "canonical", "uuid": "Jdgf8PrJBGfp", "__type": "Var"}, "pOM2DYQvazEW": {"name": "text", "__type": "Text"}, "Tdo3ZL-cW2hP": {"type": {"__ref": "pOM2DYQvazEW"}, "variable": {"__ref": "P27wGUv-Qgx9"}, "uuid": "DILE13O2n51m", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "LU8FftYLeIfh": {"uuid": "Sf2JPX0o5bC9", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "TBXkCcEW95C6": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "89sUQoymcgJU": {"variants": [{"__ref": "LU8FftYLeIfh"}], "args": [], "attrs": {}, "rs": {"__ref": "TBXkCcEW95C6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oBh8b7--oQKJ": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "UYjOwa6Pijgu", "parent": null, "locked": null, "vsettings": [{"__ref": "89sUQoymcgJU"}], "__type": "TplTag"}, "eTWlajcHbsXS": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "iVgviOD2y313": {"uuid": "7dNVhxXEfmzt", "name": "hostless-plasmic-head", "params": [{"__ref": "vIku7dYn2lOk"}, {"__ref": "ajfByMtDR3SD"}, {"__ref": "4J8hp6CarOdy"}, {"__ref": "Tdo3ZL-cW2hP"}], "states": [], "tplTree": {"__ref": "oBh8b7--oQKJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "LU8FftYLeIfh"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "eTWlajcHbsXS"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "mn-i0wyTX_8d": {"name": "dataOp", "uuid": "aw3To6-iRxiT", "__type": "Var"}, "1gZ8WLiSqinW": {"name": "any", "__type": "AnyType"}, "XbCO6KZXdTOI": {"type": {"__ref": "1gZ8WLiSqinW"}, "variable": {"__ref": "mn-i0wyTX_8d"}, "uuid": "kDGPzXajGZuF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4TJiowzsgSA6": {"name": "name", "uuid": "qkFDzgOGuun7", "__type": "Var"}, "gqsxkw4HNnT6": {"name": "text", "__type": "Text"}, "wKY8XDohrk5E": {"type": {"__ref": "gqsxkw4HNnT6"}, "variable": {"__ref": "4TJiowzsgSA6"}, "uuid": "MSEh5mmXKnEv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "e1AHNXBi-gbX": {"name": "children", "uuid": "hjv8itvMaejl", "__type": "Var"}, "0s4Xm5sA3bgk": {"name": "any", "__type": "AnyType"}, "yFoizy9jwCH3": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "0s4Xm5sA3bgk"}, "__type": "ArgType"}, "mkhxIrcCKe0I": {"name": "renderFunc", "params": [{"__ref": "yFoizy9jwCH3"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "4w6nDpMQsoDl": {"type": {"__ref": "mkhxIrcCKe0I"}, "tplSlot": {"__ref": "EQOoFeB1jJWg"}, "variable": {"__ref": "e1AHNXBi-gbX"}, "uuid": "pDF7Mkhr0sj3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "hABaEvXCIzy7": {"name": "pageSize", "uuid": "4iBDc_66nKg4", "__type": "Var"}, "NJDmOFISnXrV": {"name": "num", "__type": "<PERSON><PERSON>"}, "YJdR63-NsHsj": {"type": {"__ref": "NJDmOFISnXrV"}, "variable": {"__ref": "hABaEvXCIzy7"}, "uuid": "dd4DrRA-g_tA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "8BvioTbKdGPX": {"name": "pageIndex", "uuid": "HF4o8INzirSv", "__type": "Var"}, "5CvNPBoS8Eej": {"name": "num", "__type": "<PERSON><PERSON>"}, "fFblb4jAPw46": {"type": {"__ref": "5CvNPBoS8Eej"}, "variable": {"__ref": "8BvioTbKdGPX"}, "uuid": "UHhciNCazNro", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "JK1qk-LNA9xF": {"uuid": "12arXxMPC7fK", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "6gmMTOrciMXi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mURWtnYSURxx": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "6gmMTOrciMXi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EQOoFeB1jJWg": {"param": {"__ref": "4w6nDpMQsoDl"}, "defaultContents": [], "uuid": "pjoD2AVLM97A", "parent": {"__ref": "b852meGjAbbJ"}, "locked": null, "vsettings": [{"__ref": "mURWtnYSURxx"}], "__type": "TplSlot"}, "y6b8w_eJA3hG": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "oKYA3Ni4Izig": {"variants": [{"__ref": "JK1qk-LNA9xF"}], "args": [], "attrs": {}, "rs": {"__ref": "y6b8w_eJA3hG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "b852meGjAbbJ": {"tag": "div", "name": null, "children": [{"__ref": "EQOoFeB1jJWg"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "o1VCnil8mlLe", "parent": null, "locked": null, "vsettings": [{"__ref": "oKYA3Ni4Izig"}], "__type": "TplTag"}, "YUR9v-cVROMw": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "CGTKnrhUwnGJ": {"uuid": "EgSmz5VbPf14", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "XbCO6KZXdTOI"}, {"__ref": "wKY8XDohrk5E"}, {"__ref": "4w6nDpMQsoDl"}, {"__ref": "YJdR63-NsHsj"}, {"__ref": "fFblb4jAPw46"}], "states": [], "tplTree": {"__ref": "b852meGjAbbJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "JK1qk-LNA9xF"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "YUR9v-cVROMw"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "O6D5BizBFFPl": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "9rWFEsD51ijs": {"name": "Default Typography", "rs": {"__ref": "O6D5BizBFFPl"}, "preview": null, "uuid": "65YsmemuB5Gk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "B16VruBHaw4V": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "JuYFCtRAZj1L": {"name": "Default \"h1\"", "rs": {"__ref": "B16VruBHaw4V"}, "preview": null, "uuid": "N5whCaIyI5yq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "FW8XAtNSjtu6": {"selector": "h1", "style": {"__ref": "JuYFCtRAZj1L"}, "__type": "ThemeStyle"}, "_2Bcb1Mu36uY": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "p1iCCLwkpDoy": {"name": "Default \"h2\"", "rs": {"__ref": "_2Bcb1Mu36uY"}, "preview": null, "uuid": "591o8eV6APJY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EXyTxUa4ABV-": {"selector": "h2", "style": {"__ref": "p1iCCLwkpDoy"}, "__type": "ThemeStyle"}, "coKpJBdyxM1B": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "yyzKh6WaoGU3": {"name": "Default \"a\"", "rs": {"__ref": "coKpJBdyxM1B"}, "preview": null, "uuid": "OzvmmY5ljIKh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "q9zp2dIwMR4H": {"selector": "a", "style": {"__ref": "yyzKh6WaoGU3"}, "__type": "ThemeStyle"}, "5VOKWTPo9qQ-": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "aQGs2EzpDklN": {"name": "Default \"h3\"", "rs": {"__ref": "5VOKWTPo9qQ-"}, "preview": null, "uuid": "NnRLc2qrbHrp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ySMS8lwIR8e4": {"selector": "h3", "style": {"__ref": "aQGs2EzpDklN"}, "__type": "ThemeStyle"}, "1Eo6Elb-mOXu": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "11M5AU1J79oZ": {"name": "Default \"h4\"", "rs": {"__ref": "1Eo6Elb-mOXu"}, "preview": null, "uuid": "Fw2NyZXagP3_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TPT4LIw_FlmE": {"selector": "h4", "style": {"__ref": "11M5AU1J79oZ"}, "__type": "ThemeStyle"}, "Ap8tydcErz0o": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "jff-39xNBt4h": {"name": "Default \"code\"", "rs": {"__ref": "Ap8tydcErz0o"}, "preview": null, "uuid": "96ch0AkhaXOC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ml1ObT0h6WF0": {"selector": "code", "style": {"__ref": "jff-39xNBt4h"}, "__type": "ThemeStyle"}, "meGo-3SsDjxW": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "Rk4vU0kpCLqX": {"name": "Default \"blockquote\"", "rs": {"__ref": "meGo-3SsDjxW"}, "preview": null, "uuid": "gYG9UeAQLGHl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "uuwyZ8MaMwzC": {"selector": "blockquote", "style": {"__ref": "Rk4vU0kpCLqX"}, "__type": "ThemeStyle"}, "Xm8vVOguc99Z": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "pNJ3JD644f1B": {"name": "Default \"pre\"", "rs": {"__ref": "Xm8vVOguc99Z"}, "preview": null, "uuid": "lWS_bqGRCyQI", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xADr-YcMKj-A": {"selector": "pre", "style": {"__ref": "pNJ3JD644f1B"}, "__type": "ThemeStyle"}, "sb9QqZPTqrMD": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "oG1vMCxc_rAf": {"name": "Default \"ul\"", "rs": {"__ref": "sb9QqZPTqrMD"}, "preview": null, "uuid": "4fBIMOGTQv9T", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vWcy13o2wF2w": {"selector": "ul", "style": {"__ref": "oG1vMCxc_rAf"}, "__type": "ThemeStyle"}, "OJMyIbBCwzBE": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "WIkY9opOarFu": {"name": "Default \"ol\"", "rs": {"__ref": "OJMyIbBCwzBE"}, "preview": null, "uuid": "5JPbmKB_mLIY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ebOB-q1gpIYe": {"selector": "ol", "style": {"__ref": "WIkY9opOarFu"}, "__type": "ThemeStyle"}, "LJZHTjVy8-KX": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "q_up9_dZGmRp": {"name": "Default \"h5\"", "rs": {"__ref": "LJZHTjVy8-KX"}, "preview": null, "uuid": "HFTrV2sHI6_v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HpQe3u3igwNK": {"selector": "h5", "style": {"__ref": "q_up9_dZGmRp"}, "__type": "ThemeStyle"}, "36QFg90QpNzF": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "2qvlcfRG1Yqf": {"name": "Default \"h6\"", "rs": {"__ref": "36QFg90QpNzF"}, "preview": null, "uuid": "7ne0HsGQbiX-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "AHK6bdjMUFCZ": {"selector": "h6", "style": {"__ref": "2qvlcfRG1Yqf"}, "__type": "ThemeStyle"}, "VTuC2NSDgvTw": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "kocbDeNmD10M": {"name": "Default \"a:hover\"", "rs": {"__ref": "VTuC2NSDgvTw"}, "preview": null, "uuid": "pv_rGaCsBcE7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TLsnPhLuZijX": {"selector": "a:hover", "style": {"__ref": "kocbDeNmD10M"}, "__type": "ThemeStyle"}, "8NKHRGPgmO_7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2R5gsgHL1SEg": {"name": "Default \"li\"", "rs": {"__ref": "8NKHRGPgmO_7"}, "preview": null, "uuid": "SGgFYYxtVP9N", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "-HxiJ-vruiDF": {"selector": "li", "style": {"__ref": "2R5gsgHL1SEg"}, "__type": "ThemeStyle"}, "400JXoUXp8-f": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NUhspy26QvGl": {"name": "Default \"p\"", "rs": {"__ref": "400JXoUXp8-f"}, "preview": null, "uuid": "YsYyl_vkV4o4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "u1Jb5Ps_CziS": {"selector": "p", "style": {"__ref": "NUhspy26QvGl"}, "__type": "ThemeStyle"}, "2BVmOrsFr2nd": {"defaultStyle": {"__ref": "9rWFEsD51ijs"}, "styles": [{"__ref": "FW8XAtNSjtu6"}, {"__ref": "EXyTxUa4ABV-"}, {"__ref": "q9zp2dIwMR4H"}, {"__ref": "ySMS8lwIR8e4"}, {"__ref": "TPT4LIw_FlmE"}, {"__ref": "ml1ObT0h6WF0"}, {"__ref": "uuwyZ8MaMwzC"}, {"__ref": "xADr-YcMKj-A"}, {"__ref": "vWcy13o2wF2w"}, {"__ref": "ebOB-q1gpIYe"}, {"__ref": "HpQe3u3igwNK"}, {"__ref": "AHK6bdjMUFCZ"}, {"__ref": "TLsnPhLuZijX"}, {"__ref": "-HxiJ-vruiDF"}, {"__ref": "u1Jb5Ps_CziS"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "ePPLz0oUoM9g": {"name": "Screen", "uuid": "lc-5gWiYQiUw", "__type": "Var"}, "2WZaQOllH-qH": {"name": "text", "__type": "Text"}, "fTkyo-D50byd": {"type": {"__ref": "2WZaQOllH-qH"}, "variable": {"__ref": "ePPLz0oUoM9g"}, "uuid": "pBmgCu3yQB9T", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "ZWPc7EzDIVMW": {"uuid": "C5gJ7OmaUrOL", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "rO6y12j3WBms"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rO6y12j3WBms": {"type": "global-screen", "param": {"__ref": "fTkyo-D50byd"}, "uuid": "JHdfnMT9T35R", "variants": [{"__ref": "ZWPc7EzDIVMW"}], "multi": true, "__type": "GlobalVariantGroup"}, "752Z_tQ_x_fR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Rbqm34gRqzJz": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "752Z_tQ_x_fR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eZb3xelRiEfN": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9yvTWSyqzEZQ", "parent": null, "locked": null, "vsettings": [{"__ref": "Rbqm34gRqzJz"}], "__type": "TplComponent"}, "4jOaAgi9Lh-B": {"uuid": "s0dGF-mxGILF", "width": 1440, "height": 1024, "container": {"__ref": "eZb3xelRiEfN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "BhdY42hR-Lxp"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "17QEwua0drdO": {"frame": {"__ref": "4jOaAgi9Lh-B"}, "cellKey": null, "__type": "ArenaFrameCell"}, "7-_NchqVyaKS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aHX9dM82Ufr4": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "7-_NchqVyaKS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3bZjULCuzoCU": {"name": null, "component": {"__ref": "cLsMrVaCAsM_"}, "uuid": "9CS1eJrJD4S8", "parent": null, "locked": null, "vsettings": [{"__ref": "aHX9dM82Ufr4"}], "__type": "TplComponent"}, "rVj4nSXnpwV4": {"uuid": "tgpZSeTC7Yp7", "width": 414, "height": 736, "container": {"__ref": "3bZjULCuzoCU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"C5gJ7OmaUrOL": true}, "targetGlobalVariants": [{"__ref": "ZWPc7EzDIVMW"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "UhAjO5P4kFFM": {"frame": {"__ref": "rVj4nSXnpwV4"}, "cellKey": null, "__type": "ArenaFrameCell"}, "h-_-qgw0akdP": {"cols": [{"__ref": "17QEwua0drdO"}, {"__ref": "UhAjO5P4kFFM"}], "rowKey": {"__ref": "BhdY42hR-Lxp"}, "__type": "ArenaFrameRow"}, "fyh3Qla9RLSG": {"rows": [{"__ref": "h-_-qgw0akdP"}], "__type": "ArenaFrameGrid"}, "Ej9GYeyLIBLC": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3ti82_n-06ko": {"rows": [{"__ref": "Ej9GYeyLIBLC"}], "__type": "ArenaFrameGrid"}, "ZfWiEZ1-0hE6": {"component": {"__ref": "cLsMrVaCAsM_"}, "matrix": {"__ref": "fyh3Qla9RLSG"}, "customMatrix": {"__ref": "3ti82_n-06ko"}, "__type": "PageArena"}, "bNQ0GDteuhaj": {"uuid": "nhuuHLIvqj5r", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WvF3aLJv68QX": {"components": [{"__ref": "cLsMrVaCAsM_"}, {"__ref": "iVgviOD2y313"}, {"__ref": "CGTKnrhUwnGJ"}, {"__ref": "wZETMFZPlnHe"}], "arenas": [], "pageArenas": [{"__ref": "ZfWiEZ1-0hE6"}], "componentArenas": [{"__ref": "c4NxC8A_Qtrg"}], "globalVariantGroups": [{"__ref": "rO6y12j3WBms"}], "userManagedFonts": [], "globalVariant": {"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "2BVmOrsFr2nd"}], "activeTheme": {"__ref": "2BVmOrsFr2nd"}, "imageAssets": [], "projectDependencies": [{"__xref": {"uuid": "e59019e7-b245-4e0c-b862-91a12b98b3c5", "iid": "N1ERkITzhyF3"}}], "activeScreenVariantGroup": {"__ref": "rO6y12j3WBms"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "rz0W8-Im1Lcy": {"tag": "section", "name": null, "children": [{"__ref": "rZl1SkoFms2Z"}, {"__ref": "Z6GoBtYOptqo"}, {"__ref": "beIF61l89WIC"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DllKIXAyh5UP", "parent": {"__ref": "ZDPwdVBBrgYI"}, "locked": null, "vsettings": [{"__ref": "OQEWENIfpi5-"}, {"__ref": "83E6u4NeABM3"}], "__type": "TplTag"}, "rZl1SkoFms2Z": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "spYOtJYdUTLD", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "73B_3r6ina-x"}], "__type": "TplTag"}, "Z6GoBtYOptqo": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "DlTH_k504aot", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "wz3FW2Cj5NXL"}], "__type": "TplTag"}, "OQEWENIfpi5-": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "66HY2Ju8B857"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "83E6u4NeABM3": {"variants": [{"__ref": "ZWPc7EzDIVMW"}], "args": [], "attrs": {}, "rs": {"__ref": "hI_W_vtrTB4m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "73B_3r6ina-x": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "LleI-9WZGPYs"}, "dataCond": null, "dataRep": null, "text": {"__ref": "365AkSlNTyTj"}, "columnsConfig": null, "__type": "VariantSetting"}, "wz3FW2Cj5NXL": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [], "attrs": {}, "rs": {"__ref": "ntU0nqHKbTpo"}, "dataCond": null, "dataRep": null, "text": {"__ref": "wzfdLfQcOKVW"}, "columnsConfig": null, "__type": "VariantSetting"}, "66HY2Ju8B857": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "plasmic-layout-full-bleed", "height": "wrap", "padding-left": "0px", "padding-right": "0px", "padding-bottom": "96px", "padding-top": "96px", "grid-row-gap": "16px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "hI_W_vtrTB4m": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LleI-9WZGPYs": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "margin-bottom": "32px"}, "mixins": [], "__type": "RuleSet"}, "365AkSlNTyTj": {"markers": [], "text": "Welcome to your first page.", "__type": "RawText"}, "ntU0nqHKbTpo": {"values": {"position": "relative", "width": "stretch", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "wzfdLfQcOKVW": {"markers": [{"__ref": "h29L2P0ZFn5U"}], "text": "If you haven't already done so, go back and learn the basics by going through the Plasmic Levels tutorial.\n\nIt's always easier to start from examples! Add a new page using a template—do this from the list of pages in the top toolbar.\n\nOr press the big blue + button to start inserting items into this page.\n\nIntegrate this project into your codebase—press the Code button in the top right and follow the quickstart instructions.\n\nJoin our Slack community (icon in bottom left) for help any time.", "__type": "RawText"}, "h29L2P0ZFn5U": {"rs": {"__ref": "JbwI01KxVj_0"}, "position": 360, "length": 4, "__type": "<PERSON><PERSON>arker"}, "JbwI01KxVj_0": {"values": {"font-weight": "700"}, "mixins": [], "__type": "RuleSet"}, "beIF61l89WIC": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "OFEUtqimoqvW", "parent": {"__ref": "rz0W8-Im1Lcy"}, "locked": null, "vsettings": [{"__ref": "cL_KvYPtoOCq"}], "__type": "TplComponent"}, "cL_KvYPtoOCq": {"variants": [{"__ref": "BhdY42hR-Lxp"}], "args": [{"__ref": "jbh1_pCe3_XH"}], "attrs": {}, "rs": {"__ref": "-PzhyiMMqlQe"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-PzhyiMMqlQe": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "jbh1_pCe3_XH": {"param": {"__ref": "_o8wXWGE6gDQ"}, "expr": {"__ref": "aBI1euXi0cR-"}, "__type": "Arg"}, "aBI1euXi0cR-": {"variants": [{"__ref": "OEZZxLh2cAyc"}], "__type": "VariantsRef"}, "wZETMFZPlnHe": {"uuid": "M3Uenm6rdtPj", "name": "TestComponent", "params": [{"__ref": "_o8wXWGE6gDQ"}, {"__ref": "qo7j9NNEldQ9"}], "states": [{"__ref": "8TbzWoBssL6F"}], "tplTree": {"__ref": "EE3goD6hVeQl"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "1WRxQ1nbx7W6"}, {"__ref": "OTMLuzywK3hM"}], "variantGroups": [{"__ref": "pe_KJm0dXuQN"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "c4NxC8A_Qtrg": {"component": {"__ref": "wZETMFZPlnHe"}, "matrix": {"__ref": "wyH1otRQlS4E"}, "customMatrix": {"__ref": "TsVEXHVpb1CF"}, "__type": "ComponentArena"}, "_o8wXWGE6gDQ": {"type": {"__ref": "uvOamMNdNJFW"}, "state": {"__ref": "8TbzWoBssL6F"}, "variable": {"__ref": "xpW7gYzudfX2"}, "uuid": "nFaH5O0pX74T", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "qo7j9NNEldQ9": {"type": {"__ref": "CkB0xXeAO43U"}, "state": {"__ref": "8TbzWoBssL6F"}, "variable": {"__ref": "sjP2m6FJ_pmW"}, "uuid": "ktT_Ya6tU3_b", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "8TbzWoBssL6F": {"variantGroup": {"__ref": "pe_KJm0dXuQN"}, "param": {"__ref": "_o8wXWGE6gDQ"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "qo7j9NNEldQ9"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "EE3goD6hVeQl": {"tag": "div", "name": null, "children": [{"__ref": "NaKB4Ld0Rrnj"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "pd-Ka3JNbGbx", "parent": null, "locked": null, "vsettings": [{"__ref": "SYqfT68AvosY"}, {"__ref": "waVF0UjmUWuL"}, {"__ref": "6T0XSZluj82j"}], "__type": "TplTag"}, "1WRxQ1nbx7W6": {"uuid": "f-o2Nu8Hw1q4", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "OTMLuzywK3hM": {"uuid": "o_bNLQkUKCcy", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "pe_KJm0dXuQN": {"type": "component", "param": {"__ref": "_o8wXWGE6gDQ"}, "linkedState": {"__ref": "8TbzWoBssL6F"}, "uuid": "6s4NZZKGgUpb", "variants": [{"__ref": "OEZZxLh2cAyc"}], "multi": false, "__type": "ComponentVariantGroup"}, "wyH1otRQlS4E": {"rows": [{"__ref": "yXKxJeiGaMMn"}, {"__ref": "PKqnQXM_8H_-"}], "__type": "ArenaFrameGrid"}, "TsVEXHVpb1CF": {"rows": [{"__ref": "n1ZUc5fUwylM"}], "__type": "ArenaFrameGrid"}, "uvOamMNdNJFW": {"name": "text", "__type": "Text"}, "xpW7gYzudfX2": {"name": "isActive", "uuid": "FDMNwKBf3OZv", "__type": "Var"}, "CkB0xXeAO43U": {"name": "func", "params": [{"__ref": "9ifHfgwY_ZiL"}], "__type": "FunctionType"}, "sjP2m6FJ_pmW": {"name": "On isActive change", "uuid": "0KSAlBv2NEFe", "__type": "Var"}, "NaKB4Ld0Rrnj": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "OG_ALlyDYvIa", "parent": {"__ref": "EE3goD6hVeQl"}, "locked": null, "vsettings": [{"__ref": "9LN6mtB-M76w"}, {"__ref": "bIQXVeP0l7yG"}], "__type": "TplTag"}, "SYqfT68AvosY": {"variants": [{"__ref": "1WRxQ1nbx7W6"}], "args": [], "attrs": {}, "rs": {"__ref": "RdM5HVM6dqlD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "waVF0UjmUWuL": {"variants": [{"__ref": "OEZZxLh2cAyc"}], "args": [], "attrs": {}, "rs": {"__ref": "9ht2PyWgJieR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6T0XSZluj82j": {"variants": [{"__ref": "OTMLuzywK3hM"}], "args": [], "attrs": {}, "rs": {"__ref": "Z-Xuuw4dyb4s"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OEZZxLh2cAyc": {"uuid": "FaBBhkkhvYho", "name": "isActive", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "pe_KJm0dXuQN"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "yXKxJeiGaMMn": {"cols": [{"__ref": "a7Fdx64w88_P"}, {"__ref": "6xL2uN0ttyXv"}], "rowKey": null, "__type": "ArenaFrameRow"}, "PKqnQXM_8H_-": {"cols": [{"__ref": "gxw4YlZoxz8P"}], "rowKey": {"__ref": "pe_KJm0dXuQN"}, "__type": "ArenaFrameRow"}, "n1ZUc5fUwylM": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "9ifHfgwY_ZiL": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "fM1UfccswL1t"}, "__type": "ArgType"}, "9LN6mtB-M76w": {"variants": [{"__ref": "1WRxQ1nbx7W6"}], "args": [], "attrs": {}, "rs": {"__ref": "RFa2uP6JnWHs"}, "dataCond": null, "dataRep": null, "text": {"__ref": "SGvoBEM00T5U"}, "columnsConfig": null, "__type": "VariantSetting"}, "bIQXVeP0l7yG": {"variants": [{"__ref": "OTMLuzywK3hM"}], "args": [], "attrs": {}, "rs": {"__ref": "MIYbFLNhI-Ke"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RdM5HVM6dqlD": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "center", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "9ht2PyWgJieR": {"values": {"background": "linear-gradient(#FFEEDD, #FFEEDD)"}, "mixins": [], "__type": "RuleSet"}, "Z-Xuuw4dyb4s": {"values": {}, "mixins": [], "__type": "RuleSet"}, "a7Fdx64w88_P": {"frame": {"__ref": "scWdBJMwghV0"}, "cellKey": {"__ref": "1WRxQ1nbx7W6"}, "__type": "ArenaFrameCell"}, "6xL2uN0ttyXv": {"frame": {"__ref": "CzqAoR5rckhE"}, "cellKey": {"__ref": "OTMLuzywK3hM"}, "__type": "ArenaFrameCell"}, "gxw4YlZoxz8P": {"frame": {"__ref": "ggozz9bmtiNF"}, "cellKey": {"__ref": "OEZZxLh2cAyc"}, "__type": "ArenaFrameCell"}, "fM1UfccswL1t": {"name": "any", "__type": "AnyType"}, "RFa2uP6JnWHs": {"values": {"position": "relative", "width": "wrap", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "SGvoBEM00T5U": {"markers": [], "text": "Sample text", "__type": "RawText"}, "MIYbFLNhI-Ke": {"values": {"color": "#0005FF"}, "mixins": [], "__type": "RuleSet"}, "scWdBJMwghV0": {"uuid": "1MYNXL9ID3uJ", "width": 1180, "height": 540, "container": {"__ref": "_2BZ1Nw8Xm6h"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1WRxQ1nbx7W6"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "CzqAoR5rckhE": {"uuid": "e3A23m1mB93x", "width": 1180, "height": 540, "container": {"__ref": "728wMF6MdNBp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "OTMLuzywK3hM"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ggozz9bmtiNF": {"uuid": "fr1x7PC8dwvO", "width": 1180, "height": 540, "container": {"__ref": "IokWQa7D91SG"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "OEZZxLh2cAyc"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "_2BZ1Nw8Xm6h": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "xUexvpqMYxFa", "parent": null, "locked": null, "vsettings": [{"__ref": "SIi8Z0nkPgVB"}], "__type": "TplComponent"}, "728wMF6MdNBp": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "AvuJDRt3Qv1i", "parent": null, "locked": null, "vsettings": [{"__ref": "gMJqdycZxFGC"}], "__type": "TplComponent"}, "IokWQa7D91SG": {"name": null, "component": {"__ref": "wZETMFZPlnHe"}, "uuid": "_Z56LzelGt3j", "parent": null, "locked": null, "vsettings": [{"__ref": "ly3wd8OExEb9"}], "__type": "TplComponent"}, "SIi8Z0nkPgVB": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "F8J5zhkKGg05"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gMJqdycZxFGC": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "zVOrpQPwGviE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ly3wd8OExEb9": {"variants": [{"__ref": "bNQ0G<PERSON><PERSON><PERSON><PERSON>"}], "args": [], "attrs": {}, "rs": {"__ref": "FvZtdW8TCk1o"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F8J5zhkKGg05": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zVOrpQPwGviE": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FvZtdW8TCk1o": {"values": {}, "mixins": [], "__type": "RuleSet"}}, "deps": ["e59019e7-b245-4e0c-b862-91a12b98b3c5"], "version": "246-add-component-updated-at"}}]}