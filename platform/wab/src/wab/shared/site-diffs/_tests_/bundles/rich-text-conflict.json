{"branches": [{"id": "aaPqWFv7zeedY7AAvgW7S5", "name": "test"}], "pkgVersions": [{"id": "e05f7e52-52e3-49f0-aa0d-1dcad69caaca", "data": {"root": "I1jfiYzWCipI", "map": {"9WNfdrlcUbJL": {"values": {"text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "udezTfppl5IJ": {"name": "Default Typography", "rs": {"__ref": "9WNfdrlcUbJL"}, "preview": null, "uuid": "LSy25RxZ-JJR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "tWydOPipcatV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lwH8-kJp-axv": {"rs": {"__ref": "tWydOPipcatV"}, "__type": "ThemeLayoutSettings"}, "pO1FsayrXtix": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sc2BSXQPltFz": {"name": "Default \"h1\"", "rs": {"__ref": "pO1FsayrXtix"}, "preview": null, "uuid": "JH0e_PTCj700", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_vJ3lDjlI1q7": {"selector": "h1", "style": {"__ref": "sc2BSXQPltFz"}, "__type": "ThemeStyle"}, "Bc8HPwp2Gsma": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZaY9vOB-vBlj": {"name": "Default \"h2\"", "rs": {"__ref": "Bc8HPwp2Gsma"}, "preview": null, "uuid": "ZGlHYT2dLjuy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_sJml96Ih9tV": {"selector": "h2", "style": {"__ref": "ZaY9vOB-vBlj"}, "__type": "ThemeStyle"}, "KBbO90OKNAGN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gACFPVkYWmcX": {"name": "Default \"h3\"", "rs": {"__ref": "KBbO90OKNAGN"}, "preview": null, "uuid": "AVstxsk0RJxl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Adt9kKi0IC1G": {"selector": "h3", "style": {"__ref": "gACFPVkYWmcX"}, "__type": "ThemeStyle"}, "x09gqiV7fUuS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "efF7ADqXToT9": {"name": "Default \"h4\"", "rs": {"__ref": "x09gqiV7fUuS"}, "preview": null, "uuid": "NORC2mn_ww8B", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ovIFUlykqJTC": {"selector": "h4", "style": {"__ref": "efF7ADqXToT9"}, "__type": "ThemeStyle"}, "0f05-0UyC3XI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BE3kgQxtMBgf": {"name": "Default \"h5\"", "rs": {"__ref": "0f05-0UyC3XI"}, "preview": null, "uuid": "wW_wHM9-ABXM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "qi5e1d2DVpSO": {"selector": "h5", "style": {"__ref": "BE3kgQxtMBgf"}, "__type": "ThemeStyle"}, "dNVN2XMl_LRX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zEHgaX72SiRH": {"name": "Default \"h6\"", "rs": {"__ref": "dNVN2XMl_LRX"}, "preview": null, "uuid": "7rR9dKEYCw7W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jrluhQZAiY_2": {"selector": "h6", "style": {"__ref": "zEHgaX72SiRH"}, "__type": "ThemeStyle"}, "-qTcSvsOVrZr": {"values": {"color": "#006DA8"}, "mixins": [], "__type": "RuleSet"}, "Vr4J3FGrMlOo": {"name": "Default \"a\"", "rs": {"__ref": "-qTcSvsOVrZr"}, "preview": null, "uuid": "y9fLM4HU6SRN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "rBLgJglzSbWM": {"selector": "a", "style": {"__ref": "Vr4J3FGrMlOo"}, "__type": "ThemeStyle"}, "0Truk2jLg0_p": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "oKOG99SaJDg5": {"name": "Default \"a:hover\"", "rs": {"__ref": "0Truk2jLg0_p"}, "preview": null, "uuid": "_4BVG-fRJ47f", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "W7pSgIkhovd2": {"selector": "a:hover", "style": {"__ref": "oKOG99SaJDg5"}, "__type": "ThemeStyle"}, "pRrev2C5szv7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6gR9M-A2iofi": {"name": "Default \"blockquote\"", "rs": {"__ref": "pRrev2C5szv7"}, "preview": null, "uuid": "bixPuFf1_YDZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "kH0aEfd43zQs": {"selector": "blockquote", "style": {"__ref": "6gR9M-A2iofi"}, "__type": "ThemeStyle"}, "31IkbWGqWXOq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7A0nHLDw-I0N": {"name": "Default \"code\"", "rs": {"__ref": "31IkbWGqWXOq"}, "preview": null, "uuid": "oe-5JQWW5Z8_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "iLk0MgFb9sPg": {"selector": "code", "style": {"__ref": "7A0nHLDw-I0N"}, "__type": "ThemeStyle"}, "Jk8TTpyZsBjV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OgiKXHBV-7TM": {"name": "Default \"pre\"", "rs": {"__ref": "Jk8TTpyZsBjV"}, "preview": null, "uuid": "lf5-rq8Hplam", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HDRu2Ix98Zy9": {"selector": "pre", "style": {"__ref": "OgiKXHBV-7TM"}, "__type": "ThemeStyle"}, "xi3QGLGGM0er": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "ncCcUC3I0Yfh": {"name": "Default \"ol\"", "rs": {"__ref": "xi3QGLGGM0er"}, "preview": null, "uuid": "Jn-zpnJ6RLDN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "g-9DndIUWH_q": {"selector": "ol", "style": {"__ref": "ncCcUC3I0Yfh"}, "__type": "ThemeStyle"}, "4tXUPnDvzWD9": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "SdUJPC9k0xPf": {"name": "Default \"ul\"", "rs": {"__ref": "4tXUPnDvzWD9"}, "preview": null, "uuid": "4nnQVC3q5_qc", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "4OIL_7CKiEfh": {"selector": "ul", "style": {"__ref": "SdUJPC9k0xPf"}, "__type": "ThemeStyle"}, "FLGRG6efCm8O": {"defaultStyle": {"__ref": "udezTfppl5IJ"}, "styles": [{"__ref": "_vJ3lDjlI1q7"}, {"__ref": "_sJml96Ih9tV"}, {"__ref": "Adt9kKi0IC1G"}, {"__ref": "ovIFUlykqJTC"}, {"__ref": "qi5e1d2DVpSO"}, {"__ref": "jrluhQZAiY_2"}, {"__ref": "rBLgJglzSbWM"}, {"__ref": "W7pSgIkhovd2"}, {"__ref": "kH0aEfd43zQs"}, {"__ref": "iLk0MgFb9sPg"}, {"__ref": "HDRu2Ix98Zy9"}, {"__ref": "g-9DndIUWH_q"}, {"__ref": "4OIL_7CKiEfh"}, {"__ref": "qPIzRUdjofNl"}, {"__ref": "o8yKgflgQqtN"}], "layout": {"__ref": "lwH8-kJp-axv"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "fj0g1VpssYav": {"name": "text", "__type": "Text"}, "TOklrar-2H0D": {"name": "Screen", "uuid": "i1A3KHy_Tr8I", "__type": "Var"}, "tBAnY7I_kyH_": {"type": {"__ref": "fj0g1VpssYav"}, "variable": {"__ref": "TOklrar-2H0D"}, "uuid": "OYCthaJQdf0U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "rvFeT_cKsSkP": {"type": "global-screen", "param": {"__ref": "tBAnY7I_kyH_"}, "uuid": "Hj_46pp6BDr3", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "eXSNY1F9gpj7": {"uuid": "wQDrG9WWSQCJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "a07B4Ee6Rdtk": {"components": [{"__ref": "NOCK1w5qpoma"}, {"__ref": "hK7WNZSao4hc"}, {"__ref": "qYKN2lAvzJGV"}], "arenas": [{"__ref": "ElQKxo6bB7Xd"}], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [{"__ref": "rvFeT_cKsSkP"}], "userManagedFonts": [], "globalVariant": {"__ref": "eXSNY1F9gpj7"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "FLGRG6efCm8O"}], "activeTheme": {"__ref": "FLGRG6efCm8O"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "rvFeT_cKsSkP"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "ElQKxo6bB7Xd": {"name": "Custom arena 1", "children": [{"__ref": "IpPBKmw1aD48"}], "__type": "Arena"}, "NOCK1w5qpoma": {"uuid": "OlQpScP5PCRU", "name": "hostless-plasmic-head", "params": [{"__ref": "53UCSMw4KXEg"}, {"__ref": "bvjCxF7LNJ3J"}, {"__ref": "he97zt90gJaa"}, {"__ref": "zOUpoYyzdj1y"}], "states": [], "tplTree": {"__ref": "sWES4KAIrKC5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "IgdjCHmZD_W5"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "rpidxz_QmQNn"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "hK7WNZSao4hc": {"uuid": "n_N_VuSc52iP", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "4f5QJDTElPWj"}, {"__ref": "4l2h1KOMMK8-"}, {"__ref": "7QkfVnNC9_W4"}, {"__ref": "QR_vsDcAp8M6"}, {"__ref": "YD2sON48hCeK"}], "states": [], "tplTree": {"__ref": "BSPzYt7V3aTr"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "bWtOttVjJeHc"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "E_UWKgXAwu8W"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53UCSMw4KXEg": {"type": {"__ref": "8cyCagXbmfTU"}, "variable": {"__ref": "o5emTqYfAe8x"}, "uuid": "uecZ2PSEIRQY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "bvjCxF7LNJ3J": {"type": {"__ref": "zRqgM3vdaD0T"}, "variable": {"__ref": "CIvcIG-SmjDB"}, "uuid": "LG1byQFgNTWY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "he97zt90gJaa": {"type": {"__ref": "Z4WxcU8om3VX"}, "variable": {"__ref": "50TIQGj-JnMa"}, "uuid": "bq8SISfq0-WS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "zOUpoYyzdj1y": {"type": {"__ref": "S5PFq-xgAKDQ"}, "variable": {"__ref": "TZfA1VPXa8DQ"}, "uuid": "ArqzBoqG8AIO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "sWES4KAIrKC5": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "D2G4DhnGc-Vy", "parent": null, "locked": null, "vsettings": [{"__ref": "EOil-EZtB9H1"}], "__type": "TplTag"}, "IgdjCHmZD_W5": {"uuid": "3TYp_4b-zxOG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rpidxz_QmQNn": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "4f5QJDTElPWj": {"type": {"__ref": "19-KO5WSIn9u"}, "variable": {"__ref": "YcFa6_h_dglQ"}, "uuid": "hjA3FGe9pZfX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4l2h1KOMMK8-": {"type": {"__ref": "pnp_WDuW6MpF"}, "variable": {"__ref": "3Mpjl7kE-_mZ"}, "uuid": "10W9p8OMQiEF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "7QkfVnNC9_W4": {"type": {"__ref": "kp9Yu_5W_nTL"}, "tplSlot": {"__ref": "ncDbMk445TZS"}, "variable": {"__ref": "3dqxWrMGaFXF"}, "uuid": "eZBWIYc-PQ3K", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "QR_vsDcAp8M6": {"type": {"__ref": "Wtz5IX0VB2ut"}, "variable": {"__ref": "_JoS2AqOKrUs"}, "uuid": "ECkyJkIZf7TA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YD2sON48hCeK": {"type": {"__ref": "pt_WkPAozKDH"}, "variable": {"__ref": "X0H7KX_PSQdT"}, "uuid": "WiEJIbzk-IKP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "BSPzYt7V3aTr": {"tag": "div", "name": null, "children": [{"__ref": "ncDbMk445TZS"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "yZ2IKndczJJU", "parent": null, "locked": null, "vsettings": [{"__ref": "YZq2e7oW5DEq"}], "__type": "TplTag"}, "bWtOttVjJeHc": {"uuid": "SxnyV-_9EqjN", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "E_UWKgXAwu8W": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "8cyCagXbmfTU": {"name": "text", "__type": "Text"}, "o5emTqYfAe8x": {"name": "title", "uuid": "WqEv7rOJGxRp", "__type": "Var"}, "zRqgM3vdaD0T": {"name": "text", "__type": "Text"}, "CIvcIG-SmjDB": {"name": "description", "uuid": "bm7R33QmJPoe", "__type": "Var"}, "Z4WxcU8om3VX": {"name": "img", "__type": "Img"}, "50TIQGj-JnMa": {"name": "image", "uuid": "PlCVrvkqcOEY", "__type": "Var"}, "S5PFq-xgAKDQ": {"name": "text", "__type": "Text"}, "TZfA1VPXa8DQ": {"name": "canonical", "uuid": "_mfgxbqVbbWC", "__type": "Var"}, "EOil-EZtB9H1": {"variants": [{"__ref": "IgdjCHmZD_W5"}], "args": [], "attrs": {}, "rs": {"__ref": "OMUIVHKptuGc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "19-KO5WSIn9u": {"name": "any", "__type": "AnyType"}, "YcFa6_h_dglQ": {"name": "dataOp", "uuid": "u5HdWaxsytdj", "__type": "Var"}, "pnp_WDuW6MpF": {"name": "text", "__type": "Text"}, "3Mpjl7kE-_mZ": {"name": "name", "uuid": "yiSPBrinXG8c", "__type": "Var"}, "kp9Yu_5W_nTL": {"name": "renderFunc", "params": [{"__ref": "aA60ZqW9wiOR"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "3dqxWrMGaFXF": {"name": "children", "uuid": "38dlx5P_zbCG", "__type": "Var"}, "Wtz5IX0VB2ut": {"name": "num", "__type": "<PERSON><PERSON>"}, "_JoS2AqOKrUs": {"name": "pageSize", "uuid": "w2sh4ImG3es7", "__type": "Var"}, "pt_WkPAozKDH": {"name": "num", "__type": "<PERSON><PERSON>"}, "X0H7KX_PSQdT": {"name": "pageIndex", "uuid": "P02WLVTSBnbm", "__type": "Var"}, "ncDbMk445TZS": {"param": {"__ref": "7QkfVnNC9_W4"}, "defaultContents": [], "uuid": "sFDpnoifSFbg", "parent": {"__ref": "BSPzYt7V3aTr"}, "locked": null, "vsettings": [{"__ref": "wu7QQId3TX6b"}], "__type": "TplSlot"}, "YZq2e7oW5DEq": {"variants": [{"__ref": "bWtOttVjJeHc"}], "args": [], "attrs": {}, "rs": {"__ref": "UVxexLP7_kVi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OMUIVHKptuGc": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "aA60ZqW9wiOR": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "n-VdSwku7UwH"}, "__type": "ArgType"}, "wu7QQId3TX6b": {"variants": [{"__ref": "bWtOttVjJeHc"}], "args": [], "attrs": {}, "rs": {"__ref": "E9sn-Z202NUV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UVxexLP7_kVi": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "n-VdSwku7UwH": {"name": "any", "__type": "AnyType"}, "E9sn-Z202NUV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "qYKN2lAvzJGV": {"uuid": "Q-lNeXDgFp94", "name": "", "params": [], "states": [], "tplTree": {"__ref": "noZWxrMHKgtp"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "KMEIkjIh_Awu"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "IpPBKmw1aD48": {"uuid": "bu3dBcyE0srJ", "width": 800, "height": 800, "container": {"__ref": "Wb1MBtMfn2uj"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "KMEIkjIh_Awu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": 62, "left": 581, "__type": "ArenaFrame"}, "noZWxrMHKgtp": {"tag": "div", "name": null, "children": [{"__ref": "ZrOvYTUnB2eD"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "M7iL5p6T-rzW", "parent": null, "locked": null, "vsettings": [{"__ref": "VCS3hD7RDPOW"}], "__type": "TplTag"}, "KMEIkjIh_Awu": {"uuid": "rKAEkNnRYspd", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Wb1MBtMfn2uj": {"name": null, "component": {"__ref": "qYKN2lAvzJGV"}, "uuid": "X2FrSJR86i1n", "parent": null, "locked": null, "vsettings": [{"__ref": "YQ1W2w-R9Xmo"}], "__type": "TplComponent"}, "VCS3hD7RDPOW": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {}, "rs": {"__ref": "0_jvhSsstyu7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YQ1W2w-R9Xmo": {"variants": [{"__ref": "eXSNY1F9gpj7"}], "args": [], "attrs": {}, "rs": {"__ref": "RXBTs8uhZci9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0_jvhSsstyu7": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "align-items": "center", "justify-content": "center"}, "mixins": [], "__type": "RuleSet"}, "RXBTs8uhZci9": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZrOvYTUnB2eD": {"tag": "div", "name": null, "children": [{"__ref": "F0gkXGfBK0Ki"}], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kB6ldIF3jPGI", "parent": {"__ref": "noZWxrMHKgtp"}, "locked": null, "vsettings": [{"__ref": "NMJNECWdKJFm"}], "__type": "TplTag"}, "NMJNECWdKJFm": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {}, "rs": {"__ref": "J7qF9Hch5abQ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "vxXMrdLVna1D"}, "columnsConfig": null, "__type": "VariantSetting"}, "J7qF9Hch5abQ": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "vxXMrdLVna1D": {"markers": [{"__ref": "mTDTgu7s2OFq"}], "text": "Test [child] child", "__type": "RawText"}, "F0gkXGfBK0Ki": {"tag": "a", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "FZRkXH1OhKHg", "parent": {"__ref": "ZrOvYTUnB2eD"}, "locked": null, "vsettings": [{"__ref": "iXx_0VJIXr1k"}], "__type": "TplTag"}, "mTDTgu7s2OFq": {"tpl": {"__ref": "F0gkXGfBK0Ki"}, "position": 5, "length": 7, "__type": "NodeMarker"}, "iXx_0VJIXr1k": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {"href": {"__ref": "_pp8sj_1IGM7"}}, "rs": {"__ref": "Zg4Gz2lRv3DT"}, "dataCond": null, "dataRep": null, "text": {"__ref": "Z-iclaORynyJ"}, "columnsConfig": null, "__type": "VariantSetting"}, "_pp8sj_1IGM7": {"code": "\"/\"", "fallback": null, "__type": "CustomCode"}, "Zg4Gz2lRv3DT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Z-iclaORynyJ": {"markers": [], "text": "with", "__type": "RawText"}, "qPIzRUdjofNl": {"selector": "li", "style": {"__ref": "nHXaUz8LLrxl"}, "__type": "ThemeStyle"}, "nHXaUz8LLrxl": {"name": "Default \"li\"", "rs": {"__ref": "7RGTdvU_w4xT"}, "preview": null, "uuid": "WMoJVB2G-A2g", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7RGTdvU_w4xT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "o8yKgflgQqtN": {"selector": "p", "style": {"__ref": "Zo_EPbOPrUnm"}, "__type": "ThemeStyle"}, "Zo_EPbOPrUnm": {"name": "Default \"p\"", "rs": {"__ref": "9GvQPDOnPjAI"}, "preview": null, "uuid": "FI6sBT7qsD_Z", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9GvQPDOnPjAI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "I1jfiYzWCipI": {"uuid": "jueyrBSCw_DU", "pkgId": "e88e0408-727f-48b3-890d-56ba4865fde7", "projectId": "aH1hFXMTo8YybdGp5ScZNk", "version": "0.0.1", "name": "Untitled Project", "site": {"__ref": "a07B4Ee6Rdtk"}, "__type": "ProjectDependency"}}, "deps": [], "version": "246-add-component-updated-at"}, "projectId": "aH1hFXMTo8YybdGp5ScZNk", "version": "0.0.1", "branchId": "main"}], "project": {"id": "aH1hFXMTo8YybdGp5ScZNk", "name": "Untitled Project", "commitGraph": {"parents": {"e05f7e52-52e3-49f0-aa0d-1dcad69caaca": []}, "branches": {"main": "e05f7e52-52e3-49f0-aa0d-1dcad69caaca", "aaPqWFv7zeedY7AAvgW7S5": "e05f7e52-52e3-49f0-aa0d-1dcad69caaca"}}}, "revisions": [{"branchId": "aaPqWFv7zeedY7AAvgW7S5", "data": {"root": "a07B4Ee6Rdtk", "map": {"9WNfdrlcUbJL": {"values": {"text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "udezTfppl5IJ": {"name": "Default Typography", "rs": {"__ref": "9WNfdrlcUbJL"}, "preview": null, "uuid": "LSy25RxZ-JJR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "tWydOPipcatV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lwH8-kJp-axv": {"rs": {"__ref": "tWydOPipcatV"}, "__type": "ThemeLayoutSettings"}, "pO1FsayrXtix": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sc2BSXQPltFz": {"name": "Default \"h1\"", "rs": {"__ref": "pO1FsayrXtix"}, "preview": null, "uuid": "JH0e_PTCj700", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_vJ3lDjlI1q7": {"selector": "h1", "style": {"__ref": "sc2BSXQPltFz"}, "__type": "ThemeStyle"}, "Bc8HPwp2Gsma": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZaY9vOB-vBlj": {"name": "Default \"h2\"", "rs": {"__ref": "Bc8HPwp2Gsma"}, "preview": null, "uuid": "ZGlHYT2dLjuy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_sJml96Ih9tV": {"selector": "h2", "style": {"__ref": "ZaY9vOB-vBlj"}, "__type": "ThemeStyle"}, "KBbO90OKNAGN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gACFPVkYWmcX": {"name": "Default \"h3\"", "rs": {"__ref": "KBbO90OKNAGN"}, "preview": null, "uuid": "AVstxsk0RJxl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Adt9kKi0IC1G": {"selector": "h3", "style": {"__ref": "gACFPVkYWmcX"}, "__type": "ThemeStyle"}, "x09gqiV7fUuS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "efF7ADqXToT9": {"name": "Default \"h4\"", "rs": {"__ref": "x09gqiV7fUuS"}, "preview": null, "uuid": "NORC2mn_ww8B", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ovIFUlykqJTC": {"selector": "h4", "style": {"__ref": "efF7ADqXToT9"}, "__type": "ThemeStyle"}, "0f05-0UyC3XI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BE3kgQxtMBgf": {"name": "Default \"h5\"", "rs": {"__ref": "0f05-0UyC3XI"}, "preview": null, "uuid": "wW_wHM9-ABXM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "qi5e1d2DVpSO": {"selector": "h5", "style": {"__ref": "BE3kgQxtMBgf"}, "__type": "ThemeStyle"}, "dNVN2XMl_LRX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zEHgaX72SiRH": {"name": "Default \"h6\"", "rs": {"__ref": "dNVN2XMl_LRX"}, "preview": null, "uuid": "7rR9dKEYCw7W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jrluhQZAiY_2": {"selector": "h6", "style": {"__ref": "zEHgaX72SiRH"}, "__type": "ThemeStyle"}, "-qTcSvsOVrZr": {"values": {"color": "#006DA8"}, "mixins": [], "__type": "RuleSet"}, "Vr4J3FGrMlOo": {"name": "Default \"a\"", "rs": {"__ref": "-qTcSvsOVrZr"}, "preview": null, "uuid": "y9fLM4HU6SRN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "rBLgJglzSbWM": {"selector": "a", "style": {"__ref": "Vr4J3FGrMlOo"}, "__type": "ThemeStyle"}, "0Truk2jLg0_p": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "oKOG99SaJDg5": {"name": "Default \"a:hover\"", "rs": {"__ref": "0Truk2jLg0_p"}, "preview": null, "uuid": "_4BVG-fRJ47f", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "W7pSgIkhovd2": {"selector": "a:hover", "style": {"__ref": "oKOG99SaJDg5"}, "__type": "ThemeStyle"}, "pRrev2C5szv7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6gR9M-A2iofi": {"name": "Default \"blockquote\"", "rs": {"__ref": "pRrev2C5szv7"}, "preview": null, "uuid": "bixPuFf1_YDZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "kH0aEfd43zQs": {"selector": "blockquote", "style": {"__ref": "6gR9M-A2iofi"}, "__type": "ThemeStyle"}, "31IkbWGqWXOq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7A0nHLDw-I0N": {"name": "Default \"code\"", "rs": {"__ref": "31IkbWGqWXOq"}, "preview": null, "uuid": "oe-5JQWW5Z8_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "iLk0MgFb9sPg": {"selector": "code", "style": {"__ref": "7A0nHLDw-I0N"}, "__type": "ThemeStyle"}, "Jk8TTpyZsBjV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OgiKXHBV-7TM": {"name": "Default \"pre\"", "rs": {"__ref": "Jk8TTpyZsBjV"}, "preview": null, "uuid": "lf5-rq8Hplam", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HDRu2Ix98Zy9": {"selector": "pre", "style": {"__ref": "OgiKXHBV-7TM"}, "__type": "ThemeStyle"}, "xi3QGLGGM0er": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "ncCcUC3I0Yfh": {"name": "Default \"ol\"", "rs": {"__ref": "xi3QGLGGM0er"}, "preview": null, "uuid": "Jn-zpnJ6RLDN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "g-9DndIUWH_q": {"selector": "ol", "style": {"__ref": "ncCcUC3I0Yfh"}, "__type": "ThemeStyle"}, "4tXUPnDvzWD9": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "SdUJPC9k0xPf": {"name": "Default \"ul\"", "rs": {"__ref": "4tXUPnDvzWD9"}, "preview": null, "uuid": "4nnQVC3q5_qc", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "4OIL_7CKiEfh": {"selector": "ul", "style": {"__ref": "SdUJPC9k0xPf"}, "__type": "ThemeStyle"}, "FLGRG6efCm8O": {"defaultStyle": {"__ref": "udezTfppl5IJ"}, "styles": [{"__ref": "_vJ3lDjlI1q7"}, {"__ref": "_sJml96Ih9tV"}, {"__ref": "Adt9kKi0IC1G"}, {"__ref": "ovIFUlykqJTC"}, {"__ref": "qi5e1d2DVpSO"}, {"__ref": "jrluhQZAiY_2"}, {"__ref": "rBLgJglzSbWM"}, {"__ref": "W7pSgIkhovd2"}, {"__ref": "kH0aEfd43zQs"}, {"__ref": "iLk0MgFb9sPg"}, {"__ref": "HDRu2Ix98Zy9"}, {"__ref": "g-9DndIUWH_q"}, {"__ref": "4OIL_7CKiEfh"}, {"__ref": "qPIzRUdjofNl"}, {"__ref": "o8yKgflgQqtN"}], "layout": {"__ref": "lwH8-kJp-axv"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "fj0g1VpssYav": {"name": "text", "__type": "Text"}, "TOklrar-2H0D": {"name": "Screen", "uuid": "i1A3KHy_Tr8I", "__type": "Var"}, "tBAnY7I_kyH_": {"type": {"__ref": "fj0g1VpssYav"}, "variable": {"__ref": "TOklrar-2H0D"}, "uuid": "OYCthaJQdf0U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "rvFeT_cKsSkP": {"type": "global-screen", "param": {"__ref": "tBAnY7I_kyH_"}, "uuid": "Hj_46pp6BDr3", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "eXSNY1F9gpj7": {"uuid": "wQDrG9WWSQCJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "a07B4Ee6Rdtk": {"components": [{"__ref": "NOCK1w5qpoma"}, {"__ref": "hK7WNZSao4hc"}, {"__ref": "qYKN2lAvzJGV"}], "arenas": [{"__ref": "ElQKxo6bB7Xd"}], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [{"__ref": "rvFeT_cKsSkP"}], "userManagedFonts": [], "globalVariant": {"__ref": "eXSNY1F9gpj7"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "FLGRG6efCm8O"}], "activeTheme": {"__ref": "FLGRG6efCm8O"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "rvFeT_cKsSkP"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "ElQKxo6bB7Xd": {"name": "Custom arena 1", "children": [{"__ref": "IpPBKmw1aD48"}], "__type": "Arena"}, "NOCK1w5qpoma": {"uuid": "OlQpScP5PCRU", "name": "hostless-plasmic-head", "params": [{"__ref": "53UCSMw4KXEg"}, {"__ref": "bvjCxF7LNJ3J"}, {"__ref": "he97zt90gJaa"}, {"__ref": "zOUpoYyzdj1y"}], "states": [], "tplTree": {"__ref": "sWES4KAIrKC5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "IgdjCHmZD_W5"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "rpidxz_QmQNn"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "hK7WNZSao4hc": {"uuid": "n_N_VuSc52iP", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "4f5QJDTElPWj"}, {"__ref": "4l2h1KOMMK8-"}, {"__ref": "7QkfVnNC9_W4"}, {"__ref": "QR_vsDcAp8M6"}, {"__ref": "YD2sON48hCeK"}], "states": [], "tplTree": {"__ref": "BSPzYt7V3aTr"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "bWtOttVjJeHc"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "E_UWKgXAwu8W"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53UCSMw4KXEg": {"type": {"__ref": "8cyCagXbmfTU"}, "variable": {"__ref": "o5emTqYfAe8x"}, "uuid": "uecZ2PSEIRQY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "bvjCxF7LNJ3J": {"type": {"__ref": "zRqgM3vdaD0T"}, "variable": {"__ref": "CIvcIG-SmjDB"}, "uuid": "LG1byQFgNTWY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "he97zt90gJaa": {"type": {"__ref": "Z4WxcU8om3VX"}, "variable": {"__ref": "50TIQGj-JnMa"}, "uuid": "bq8SISfq0-WS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "zOUpoYyzdj1y": {"type": {"__ref": "S5PFq-xgAKDQ"}, "variable": {"__ref": "TZfA1VPXa8DQ"}, "uuid": "ArqzBoqG8AIO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "sWES4KAIrKC5": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "D2G4DhnGc-Vy", "parent": null, "locked": null, "vsettings": [{"__ref": "EOil-EZtB9H1"}], "__type": "TplTag"}, "IgdjCHmZD_W5": {"uuid": "3TYp_4b-zxOG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rpidxz_QmQNn": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "4f5QJDTElPWj": {"type": {"__ref": "19-KO5WSIn9u"}, "variable": {"__ref": "YcFa6_h_dglQ"}, "uuid": "hjA3FGe9pZfX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4l2h1KOMMK8-": {"type": {"__ref": "pnp_WDuW6MpF"}, "variable": {"__ref": "3Mpjl7kE-_mZ"}, "uuid": "10W9p8OMQiEF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "7QkfVnNC9_W4": {"type": {"__ref": "kp9Yu_5W_nTL"}, "tplSlot": {"__ref": "ncDbMk445TZS"}, "variable": {"__ref": "3dqxWrMGaFXF"}, "uuid": "eZBWIYc-PQ3K", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "QR_vsDcAp8M6": {"type": {"__ref": "Wtz5IX0VB2ut"}, "variable": {"__ref": "_JoS2AqOKrUs"}, "uuid": "ECkyJkIZf7TA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YD2sON48hCeK": {"type": {"__ref": "pt_WkPAozKDH"}, "variable": {"__ref": "X0H7KX_PSQdT"}, "uuid": "WiEJIbzk-IKP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "BSPzYt7V3aTr": {"tag": "div", "name": null, "children": [{"__ref": "ncDbMk445TZS"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "yZ2IKndczJJU", "parent": null, "locked": null, "vsettings": [{"__ref": "YZq2e7oW5DEq"}], "__type": "TplTag"}, "bWtOttVjJeHc": {"uuid": "SxnyV-_9EqjN", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "E_UWKgXAwu8W": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "8cyCagXbmfTU": {"name": "text", "__type": "Text"}, "o5emTqYfAe8x": {"name": "title", "uuid": "WqEv7rOJGxRp", "__type": "Var"}, "zRqgM3vdaD0T": {"name": "text", "__type": "Text"}, "CIvcIG-SmjDB": {"name": "description", "uuid": "bm7R33QmJPoe", "__type": "Var"}, "Z4WxcU8om3VX": {"name": "img", "__type": "Img"}, "50TIQGj-JnMa": {"name": "image", "uuid": "PlCVrvkqcOEY", "__type": "Var"}, "S5PFq-xgAKDQ": {"name": "text", "__type": "Text"}, "TZfA1VPXa8DQ": {"name": "canonical", "uuid": "_mfgxbqVbbWC", "__type": "Var"}, "EOil-EZtB9H1": {"variants": [{"__ref": "IgdjCHmZD_W5"}], "args": [], "attrs": {}, "rs": {"__ref": "OMUIVHKptuGc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "19-KO5WSIn9u": {"name": "any", "__type": "AnyType"}, "YcFa6_h_dglQ": {"name": "dataOp", "uuid": "u5HdWaxsytdj", "__type": "Var"}, "pnp_WDuW6MpF": {"name": "text", "__type": "Text"}, "3Mpjl7kE-_mZ": {"name": "name", "uuid": "yiSPBrinXG8c", "__type": "Var"}, "kp9Yu_5W_nTL": {"name": "renderFunc", "params": [{"__ref": "aA60ZqW9wiOR"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "3dqxWrMGaFXF": {"name": "children", "uuid": "38dlx5P_zbCG", "__type": "Var"}, "Wtz5IX0VB2ut": {"name": "num", "__type": "<PERSON><PERSON>"}, "_JoS2AqOKrUs": {"name": "pageSize", "uuid": "w2sh4ImG3es7", "__type": "Var"}, "pt_WkPAozKDH": {"name": "num", "__type": "<PERSON><PERSON>"}, "X0H7KX_PSQdT": {"name": "pageIndex", "uuid": "P02WLVTSBnbm", "__type": "Var"}, "ncDbMk445TZS": {"param": {"__ref": "7QkfVnNC9_W4"}, "defaultContents": [], "uuid": "sFDpnoifSFbg", "parent": {"__ref": "BSPzYt7V3aTr"}, "locked": null, "vsettings": [{"__ref": "wu7QQId3TX6b"}], "__type": "TplSlot"}, "YZq2e7oW5DEq": {"variants": [{"__ref": "bWtOttVjJeHc"}], "args": [], "attrs": {}, "rs": {"__ref": "UVxexLP7_kVi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OMUIVHKptuGc": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "aA60ZqW9wiOR": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "n-VdSwku7UwH"}, "__type": "ArgType"}, "wu7QQId3TX6b": {"variants": [{"__ref": "bWtOttVjJeHc"}], "args": [], "attrs": {}, "rs": {"__ref": "E9sn-Z202NUV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UVxexLP7_kVi": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "n-VdSwku7UwH": {"name": "any", "__type": "AnyType"}, "E9sn-Z202NUV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "qYKN2lAvzJGV": {"uuid": "Q-lNeXDgFp94", "name": "", "params": [], "states": [], "tplTree": {"__ref": "noZWxrMHKgtp"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "KMEIkjIh_Awu"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "IpPBKmw1aD48": {"uuid": "bu3dBcyE0srJ", "width": 800, "height": 800, "container": {"__ref": "Wb1MBtMfn2uj"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "KMEIkjIh_Awu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": 62, "left": 581, "__type": "ArenaFrame"}, "noZWxrMHKgtp": {"tag": "div", "name": null, "children": [{"__ref": "ZrOvYTUnB2eD"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "M7iL5p6T-rzW", "parent": null, "locked": null, "vsettings": [{"__ref": "VCS3hD7RDPOW"}], "__type": "TplTag"}, "KMEIkjIh_Awu": {"uuid": "rKAEkNnRYspd", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Wb1MBtMfn2uj": {"name": null, "component": {"__ref": "qYKN2lAvzJGV"}, "uuid": "X2FrSJR86i1n", "parent": null, "locked": null, "vsettings": [{"__ref": "YQ1W2w-R9Xmo"}], "__type": "TplComponent"}, "VCS3hD7RDPOW": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {}, "rs": {"__ref": "0_jvhSsstyu7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YQ1W2w-R9Xmo": {"variants": [{"__ref": "eXSNY1F9gpj7"}], "args": [], "attrs": {}, "rs": {"__ref": "RXBTs8uhZci9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0_jvhSsstyu7": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "align-items": "center", "justify-content": "center"}, "mixins": [], "__type": "RuleSet"}, "RXBTs8uhZci9": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZrOvYTUnB2eD": {"tag": "div", "name": null, "children": [{"__ref": "F0gkXGfBK0Ki"}], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kB6ldIF3jPGI", "parent": {"__ref": "noZWxrMHKgtp"}, "locked": null, "vsettings": [{"__ref": "NMJNECWdKJFm"}], "__type": "TplTag"}, "NMJNECWdKJFm": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {}, "rs": {"__ref": "J7qF9Hch5abQ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "-ifRgdKcoygE"}, "columnsConfig": null, "__type": "VariantSetting"}, "J7qF9Hch5abQ": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "F0gkXGfBK0Ki": {"tag": "a", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "FZRkXH1OhKHg", "parent": {"__ref": "ZrOvYTUnB2eD"}, "locked": null, "vsettings": [{"__ref": "iXx_0VJIXr1k"}], "__type": "TplTag"}, "iXx_0VJIXr1k": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {"href": {"__ref": "_pp8sj_1IGM7"}}, "rs": {"__ref": "Zg4Gz2lRv3DT"}, "dataCond": null, "dataRep": null, "text": {"__ref": "RXFD2UD937z6"}, "columnsConfig": null, "__type": "VariantSetting"}, "_pp8sj_1IGM7": {"code": "\"/\"", "fallback": null, "__type": "CustomCode"}, "Zg4Gz2lRv3DT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "qPIzRUdjofNl": {"selector": "li", "style": {"__ref": "nHXaUz8LLrxl"}, "__type": "ThemeStyle"}, "nHXaUz8LLrxl": {"name": "Default \"li\"", "rs": {"__ref": "7RGTdvU_w4xT"}, "preview": null, "uuid": "WMoJVB2G-A2g", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7RGTdvU_w4xT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "o8yKgflgQqtN": {"selector": "p", "style": {"__ref": "Zo_EPbOPrUnm"}, "__type": "ThemeStyle"}, "Zo_EPbOPrUnm": {"name": "Default \"p\"", "rs": {"__ref": "9GvQPDOnPjAI"}, "preview": null, "uuid": "FI6sBT7qsD_Z", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9GvQPDOnPjAI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RXFD2UD937z6": {"markers": [], "text": "w/", "__type": "RawText"}, "-ifRgdKcoygE": {"markers": [{"__ref": "2RTLdSrfDP23"}], "text": "Test [child] child2", "__type": "RawText"}, "2RTLdSrfDP23": {"tpl": {"__ref": "F0gkXGfBK0Ki"}, "position": 5, "length": 7, "__type": "NodeMarker"}}, "deps": [], "version": "246-add-component-updated-at"}}, {"branchId": "main", "data": {"root": "a07B4Ee6Rdtk", "map": {"9WNfdrlcUbJL": {"values": {"text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "udezTfppl5IJ": {"name": "Default Typography", "rs": {"__ref": "9WNfdrlcUbJL"}, "preview": null, "uuid": "LSy25RxZ-JJR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "tWydOPipcatV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lwH8-kJp-axv": {"rs": {"__ref": "tWydOPipcatV"}, "__type": "ThemeLayoutSettings"}, "pO1FsayrXtix": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sc2BSXQPltFz": {"name": "Default \"h1\"", "rs": {"__ref": "pO1FsayrXtix"}, "preview": null, "uuid": "JH0e_PTCj700", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_vJ3lDjlI1q7": {"selector": "h1", "style": {"__ref": "sc2BSXQPltFz"}, "__type": "ThemeStyle"}, "Bc8HPwp2Gsma": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZaY9vOB-vBlj": {"name": "Default \"h2\"", "rs": {"__ref": "Bc8HPwp2Gsma"}, "preview": null, "uuid": "ZGlHYT2dLjuy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_sJml96Ih9tV": {"selector": "h2", "style": {"__ref": "ZaY9vOB-vBlj"}, "__type": "ThemeStyle"}, "KBbO90OKNAGN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gACFPVkYWmcX": {"name": "Default \"h3\"", "rs": {"__ref": "KBbO90OKNAGN"}, "preview": null, "uuid": "AVstxsk0RJxl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Adt9kKi0IC1G": {"selector": "h3", "style": {"__ref": "gACFPVkYWmcX"}, "__type": "ThemeStyle"}, "x09gqiV7fUuS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "efF7ADqXToT9": {"name": "Default \"h4\"", "rs": {"__ref": "x09gqiV7fUuS"}, "preview": null, "uuid": "NORC2mn_ww8B", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ovIFUlykqJTC": {"selector": "h4", "style": {"__ref": "efF7ADqXToT9"}, "__type": "ThemeStyle"}, "0f05-0UyC3XI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BE3kgQxtMBgf": {"name": "Default \"h5\"", "rs": {"__ref": "0f05-0UyC3XI"}, "preview": null, "uuid": "wW_wHM9-ABXM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "qi5e1d2DVpSO": {"selector": "h5", "style": {"__ref": "BE3kgQxtMBgf"}, "__type": "ThemeStyle"}, "dNVN2XMl_LRX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zEHgaX72SiRH": {"name": "Default \"h6\"", "rs": {"__ref": "dNVN2XMl_LRX"}, "preview": null, "uuid": "7rR9dKEYCw7W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jrluhQZAiY_2": {"selector": "h6", "style": {"__ref": "zEHgaX72SiRH"}, "__type": "ThemeStyle"}, "-qTcSvsOVrZr": {"values": {"color": "#006DA8"}, "mixins": [], "__type": "RuleSet"}, "Vr4J3FGrMlOo": {"name": "Default \"a\"", "rs": {"__ref": "-qTcSvsOVrZr"}, "preview": null, "uuid": "y9fLM4HU6SRN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "rBLgJglzSbWM": {"selector": "a", "style": {"__ref": "Vr4J3FGrMlOo"}, "__type": "ThemeStyle"}, "0Truk2jLg0_p": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "oKOG99SaJDg5": {"name": "Default \"a:hover\"", "rs": {"__ref": "0Truk2jLg0_p"}, "preview": null, "uuid": "_4BVG-fRJ47f", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "W7pSgIkhovd2": {"selector": "a:hover", "style": {"__ref": "oKOG99SaJDg5"}, "__type": "ThemeStyle"}, "pRrev2C5szv7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6gR9M-A2iofi": {"name": "Default \"blockquote\"", "rs": {"__ref": "pRrev2C5szv7"}, "preview": null, "uuid": "bixPuFf1_YDZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "kH0aEfd43zQs": {"selector": "blockquote", "style": {"__ref": "6gR9M-A2iofi"}, "__type": "ThemeStyle"}, "31IkbWGqWXOq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7A0nHLDw-I0N": {"name": "Default \"code\"", "rs": {"__ref": "31IkbWGqWXOq"}, "preview": null, "uuid": "oe-5JQWW5Z8_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "iLk0MgFb9sPg": {"selector": "code", "style": {"__ref": "7A0nHLDw-I0N"}, "__type": "ThemeStyle"}, "Jk8TTpyZsBjV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OgiKXHBV-7TM": {"name": "Default \"pre\"", "rs": {"__ref": "Jk8TTpyZsBjV"}, "preview": null, "uuid": "lf5-rq8Hplam", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HDRu2Ix98Zy9": {"selector": "pre", "style": {"__ref": "OgiKXHBV-7TM"}, "__type": "ThemeStyle"}, "xi3QGLGGM0er": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "ncCcUC3I0Yfh": {"name": "Default \"ol\"", "rs": {"__ref": "xi3QGLGGM0er"}, "preview": null, "uuid": "Jn-zpnJ6RLDN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "g-9DndIUWH_q": {"selector": "ol", "style": {"__ref": "ncCcUC3I0Yfh"}, "__type": "ThemeStyle"}, "4tXUPnDvzWD9": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "SdUJPC9k0xPf": {"name": "Default \"ul\"", "rs": {"__ref": "4tXUPnDvzWD9"}, "preview": null, "uuid": "4nnQVC3q5_qc", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "4OIL_7CKiEfh": {"selector": "ul", "style": {"__ref": "SdUJPC9k0xPf"}, "__type": "ThemeStyle"}, "FLGRG6efCm8O": {"defaultStyle": {"__ref": "udezTfppl5IJ"}, "styles": [{"__ref": "_vJ3lDjlI1q7"}, {"__ref": "_sJml96Ih9tV"}, {"__ref": "Adt9kKi0IC1G"}, {"__ref": "ovIFUlykqJTC"}, {"__ref": "qi5e1d2DVpSO"}, {"__ref": "jrluhQZAiY_2"}, {"__ref": "rBLgJglzSbWM"}, {"__ref": "W7pSgIkhovd2"}, {"__ref": "kH0aEfd43zQs"}, {"__ref": "iLk0MgFb9sPg"}, {"__ref": "HDRu2Ix98Zy9"}, {"__ref": "g-9DndIUWH_q"}, {"__ref": "4OIL_7CKiEfh"}, {"__ref": "qPIzRUdjofNl"}, {"__ref": "o8yKgflgQqtN"}], "layout": {"__ref": "lwH8-kJp-axv"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "fj0g1VpssYav": {"name": "text", "__type": "Text"}, "TOklrar-2H0D": {"name": "Screen", "uuid": "i1A3KHy_Tr8I", "__type": "Var"}, "tBAnY7I_kyH_": {"type": {"__ref": "fj0g1VpssYav"}, "variable": {"__ref": "TOklrar-2H0D"}, "uuid": "OYCthaJQdf0U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "rvFeT_cKsSkP": {"type": "global-screen", "param": {"__ref": "tBAnY7I_kyH_"}, "uuid": "Hj_46pp6BDr3", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "eXSNY1F9gpj7": {"uuid": "wQDrG9WWSQCJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "a07B4Ee6Rdtk": {"components": [{"__ref": "NOCK1w5qpoma"}, {"__ref": "hK7WNZSao4hc"}, {"__ref": "qYKN2lAvzJGV"}], "arenas": [{"__ref": "ElQKxo6bB7Xd"}], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [{"__ref": "rvFeT_cKsSkP"}], "userManagedFonts": [], "globalVariant": {"__ref": "eXSNY1F9gpj7"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "FLGRG6efCm8O"}], "activeTheme": {"__ref": "FLGRG6efCm8O"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "rvFeT_cKsSkP"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "ElQKxo6bB7Xd": {"name": "Custom arena 1", "children": [{"__ref": "IpPBKmw1aD48"}], "__type": "Arena"}, "NOCK1w5qpoma": {"uuid": "OlQpScP5PCRU", "name": "hostless-plasmic-head", "params": [{"__ref": "53UCSMw4KXEg"}, {"__ref": "bvjCxF7LNJ3J"}, {"__ref": "he97zt90gJaa"}, {"__ref": "zOUpoYyzdj1y"}], "states": [], "tplTree": {"__ref": "sWES4KAIrKC5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "IgdjCHmZD_W5"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "rpidxz_QmQNn"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "hK7WNZSao4hc": {"uuid": "n_N_VuSc52iP", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "4f5QJDTElPWj"}, {"__ref": "4l2h1KOMMK8-"}, {"__ref": "7QkfVnNC9_W4"}, {"__ref": "QR_vsDcAp8M6"}, {"__ref": "YD2sON48hCeK"}], "states": [], "tplTree": {"__ref": "BSPzYt7V3aTr"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "bWtOttVjJeHc"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "E_UWKgXAwu8W"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53UCSMw4KXEg": {"type": {"__ref": "8cyCagXbmfTU"}, "variable": {"__ref": "o5emTqYfAe8x"}, "uuid": "uecZ2PSEIRQY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "bvjCxF7LNJ3J": {"type": {"__ref": "zRqgM3vdaD0T"}, "variable": {"__ref": "CIvcIG-SmjDB"}, "uuid": "LG1byQFgNTWY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "he97zt90gJaa": {"type": {"__ref": "Z4WxcU8om3VX"}, "variable": {"__ref": "50TIQGj-JnMa"}, "uuid": "bq8SISfq0-WS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "zOUpoYyzdj1y": {"type": {"__ref": "S5PFq-xgAKDQ"}, "variable": {"__ref": "TZfA1VPXa8DQ"}, "uuid": "ArqzBoqG8AIO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "sWES4KAIrKC5": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "D2G4DhnGc-Vy", "parent": null, "locked": null, "vsettings": [{"__ref": "EOil-EZtB9H1"}], "__type": "TplTag"}, "IgdjCHmZD_W5": {"uuid": "3TYp_4b-zxOG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rpidxz_QmQNn": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "4f5QJDTElPWj": {"type": {"__ref": "19-KO5WSIn9u"}, "variable": {"__ref": "YcFa6_h_dglQ"}, "uuid": "hjA3FGe9pZfX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "4l2h1KOMMK8-": {"type": {"__ref": "pnp_WDuW6MpF"}, "variable": {"__ref": "3Mpjl7kE-_mZ"}, "uuid": "10W9p8OMQiEF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "7QkfVnNC9_W4": {"type": {"__ref": "kp9Yu_5W_nTL"}, "tplSlot": {"__ref": "ncDbMk445TZS"}, "variable": {"__ref": "3dqxWrMGaFXF"}, "uuid": "eZBWIYc-PQ3K", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "QR_vsDcAp8M6": {"type": {"__ref": "Wtz5IX0VB2ut"}, "variable": {"__ref": "_JoS2AqOKrUs"}, "uuid": "ECkyJkIZf7TA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YD2sON48hCeK": {"type": {"__ref": "pt_WkPAozKDH"}, "variable": {"__ref": "X0H7KX_PSQdT"}, "uuid": "WiEJIbzk-IKP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "BSPzYt7V3aTr": {"tag": "div", "name": null, "children": [{"__ref": "ncDbMk445TZS"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "yZ2IKndczJJU", "parent": null, "locked": null, "vsettings": [{"__ref": "YZq2e7oW5DEq"}], "__type": "TplTag"}, "bWtOttVjJeHc": {"uuid": "SxnyV-_9EqjN", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "E_UWKgXAwu8W": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "8cyCagXbmfTU": {"name": "text", "__type": "Text"}, "o5emTqYfAe8x": {"name": "title", "uuid": "WqEv7rOJGxRp", "__type": "Var"}, "zRqgM3vdaD0T": {"name": "text", "__type": "Text"}, "CIvcIG-SmjDB": {"name": "description", "uuid": "bm7R33QmJPoe", "__type": "Var"}, "Z4WxcU8om3VX": {"name": "img", "__type": "Img"}, "50TIQGj-JnMa": {"name": "image", "uuid": "PlCVrvkqcOEY", "__type": "Var"}, "S5PFq-xgAKDQ": {"name": "text", "__type": "Text"}, "TZfA1VPXa8DQ": {"name": "canonical", "uuid": "_mfgxbqVbbWC", "__type": "Var"}, "EOil-EZtB9H1": {"variants": [{"__ref": "IgdjCHmZD_W5"}], "args": [], "attrs": {}, "rs": {"__ref": "OMUIVHKptuGc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "19-KO5WSIn9u": {"name": "any", "__type": "AnyType"}, "YcFa6_h_dglQ": {"name": "dataOp", "uuid": "u5HdWaxsytdj", "__type": "Var"}, "pnp_WDuW6MpF": {"name": "text", "__type": "Text"}, "3Mpjl7kE-_mZ": {"name": "name", "uuid": "yiSPBrinXG8c", "__type": "Var"}, "kp9Yu_5W_nTL": {"name": "renderFunc", "params": [{"__ref": "aA60ZqW9wiOR"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "3dqxWrMGaFXF": {"name": "children", "uuid": "38dlx5P_zbCG", "__type": "Var"}, "Wtz5IX0VB2ut": {"name": "num", "__type": "<PERSON><PERSON>"}, "_JoS2AqOKrUs": {"name": "pageSize", "uuid": "w2sh4ImG3es7", "__type": "Var"}, "pt_WkPAozKDH": {"name": "num", "__type": "<PERSON><PERSON>"}, "X0H7KX_PSQdT": {"name": "pageIndex", "uuid": "P02WLVTSBnbm", "__type": "Var"}, "ncDbMk445TZS": {"param": {"__ref": "7QkfVnNC9_W4"}, "defaultContents": [], "uuid": "sFDpnoifSFbg", "parent": {"__ref": "BSPzYt7V3aTr"}, "locked": null, "vsettings": [{"__ref": "wu7QQId3TX6b"}], "__type": "TplSlot"}, "YZq2e7oW5DEq": {"variants": [{"__ref": "bWtOttVjJeHc"}], "args": [], "attrs": {}, "rs": {"__ref": "UVxexLP7_kVi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OMUIVHKptuGc": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "aA60ZqW9wiOR": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "n-VdSwku7UwH"}, "__type": "ArgType"}, "wu7QQId3TX6b": {"variants": [{"__ref": "bWtOttVjJeHc"}], "args": [], "attrs": {}, "rs": {"__ref": "E9sn-Z202NUV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UVxexLP7_kVi": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "n-VdSwku7UwH": {"name": "any", "__type": "AnyType"}, "E9sn-Z202NUV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "qYKN2lAvzJGV": {"uuid": "Q-lNeXDgFp94", "name": "", "params": [], "states": [], "tplTree": {"__ref": "noZWxrMHKgtp"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "KMEIkjIh_Awu"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "IpPBKmw1aD48": {"uuid": "bu3dBcyE0srJ", "width": 800, "height": 800, "container": {"__ref": "Wb1MBtMfn2uj"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "KMEIkjIh_Awu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": 62, "left": 581, "__type": "ArenaFrame"}, "noZWxrMHKgtp": {"tag": "div", "name": null, "children": [{"__ref": "ZrOvYTUnB2eD"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "M7iL5p6T-rzW", "parent": null, "locked": null, "vsettings": [{"__ref": "VCS3hD7RDPOW"}], "__type": "TplTag"}, "KMEIkjIh_Awu": {"uuid": "rKAEkNnRYspd", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Wb1MBtMfn2uj": {"name": null, "component": {"__ref": "qYKN2lAvzJGV"}, "uuid": "X2FrSJR86i1n", "parent": null, "locked": null, "vsettings": [{"__ref": "YQ1W2w-R9Xmo"}], "__type": "TplComponent"}, "VCS3hD7RDPOW": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {}, "rs": {"__ref": "0_jvhSsstyu7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YQ1W2w-R9Xmo": {"variants": [{"__ref": "eXSNY1F9gpj7"}], "args": [], "attrs": {}, "rs": {"__ref": "RXBTs8uhZci9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0_jvhSsstyu7": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "align-items": "center", "justify-content": "center"}, "mixins": [], "__type": "RuleSet"}, "RXBTs8uhZci9": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZrOvYTUnB2eD": {"tag": "div", "name": null, "children": [{"__ref": "F0gkXGfBK0Ki"}], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kB6ldIF3jPGI", "parent": {"__ref": "noZWxrMHKgtp"}, "locked": null, "vsettings": [{"__ref": "NMJNECWdKJFm"}], "__type": "TplTag"}, "NMJNECWdKJFm": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {}, "rs": {"__ref": "J7qF9Hch5abQ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "D_bgM-AciM5T"}, "columnsConfig": null, "__type": "VariantSetting"}, "J7qF9Hch5abQ": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "F0gkXGfBK0Ki": {"tag": "a", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "FZRkXH1OhKHg", "parent": {"__ref": "ZrOvYTUnB2eD"}, "locked": null, "vsettings": [{"__ref": "iXx_0VJIXr1k"}], "__type": "TplTag"}, "iXx_0VJIXr1k": {"variants": [{"__ref": "KMEIkjIh_Awu"}], "args": [], "attrs": {"href": {"__ref": "_pp8sj_1IGM7"}}, "rs": {"__ref": "Zg4Gz2lRv3DT"}, "dataCond": null, "dataRep": null, "text": {"__ref": "xT2Y8MQrlxcC"}, "columnsConfig": null, "__type": "VariantSetting"}, "_pp8sj_1IGM7": {"code": "\"/\"", "fallback": null, "__type": "CustomCode"}, "Zg4Gz2lRv3DT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "qPIzRUdjofNl": {"selector": "li", "style": {"__ref": "nHXaUz8LLrxl"}, "__type": "ThemeStyle"}, "nHXaUz8LLrxl": {"name": "Default \"li\"", "rs": {"__ref": "7RGTdvU_w4xT"}, "preview": null, "uuid": "WMoJVB2G-A2g", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7RGTdvU_w4xT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "o8yKgflgQqtN": {"selector": "p", "style": {"__ref": "Zo_EPbOPrUnm"}, "__type": "ThemeStyle"}, "Zo_EPbOPrUnm": {"name": "Default \"p\"", "rs": {"__ref": "9GvQPDOnPjAI"}, "preview": null, "uuid": "FI6sBT7qsD_Z", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9GvQPDOnPjAI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xT2Y8MQrlxcC": {"markers": [], "text": "w1th", "__type": "RawText"}, "D_bgM-AciM5T": {"markers": [{"__ref": "4B1JMddzQ6Iz"}], "text": "Test [child] child3", "__type": "RawText"}, "4B1JMddzQ6Iz": {"tpl": {"__ref": "F0gkXGfBK0Ki"}, "position": 5, "length": 7, "__type": "NodeMarker"}}, "deps": [], "version": "246-add-component-updated-at"}}]}