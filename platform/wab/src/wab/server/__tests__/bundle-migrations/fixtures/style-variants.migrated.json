{"root": "mZW4NX4TIgod", "map": {"eJhiDWmhLo-E": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "sqdCsA16BrHr": {"name": "Default Typography", "rs": {"__ref": "eJhiDWmhLo-E"}, "preview": null, "uuid": "ZEXfj-LTUDdA", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ZOQYKJkEmp2U": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OpAJV5T7yIpC": {"rs": {"__ref": "ZOQYKJkEmp2U"}, "__type": "ThemeLayoutSettings"}, "SCTh8JxqqfqJ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "lrJBYSy6B92M": {"name": "Default \"h1\"", "rs": {"__ref": "SCTh8JxqqfqJ"}, "preview": null, "uuid": "8cfXmPJirY4J", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "-usvq8jxuqeb": {"selector": "h1", "style": {"__ref": "lrJBYSy6B92M"}, "__type": "ThemeStyle"}, "DV_iGSvG4jxq": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "4BHXhuIF2qsn": {"name": "Default \"h2\"", "rs": {"__ref": "DV_iGSvG4jxq"}, "preview": null, "uuid": "broCV-fA1Xkp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "oJf2ynb4U8wz": {"selector": "h2", "style": {"__ref": "4BHXhuIF2qsn"}, "__type": "ThemeStyle"}, "8A1wCwsNs2SE": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "kIZCvjlf37RB": {"name": "Default \"h3\"", "rs": {"__ref": "8A1wCwsNs2SE"}, "preview": null, "uuid": "XsFapElC6dXc", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "eBfKperFlvhg": {"selector": "h3", "style": {"__ref": "kIZCvjlf37RB"}, "__type": "ThemeStyle"}, "6KqtdHoVt6mS": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "CYNIRTMtA7MF": {"name": "Default \"h4\"", "rs": {"__ref": "6KqtdHoVt6mS"}, "preview": null, "uuid": "KneN0P3biSFm", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "3EkdiMrlrHHJ": {"selector": "h4", "style": {"__ref": "CYNIRTMtA7MF"}, "__type": "ThemeStyle"}, "MYSWaKbUDiLv": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8XM0iv4z53b3": {"name": "Default \"h5\"", "rs": {"__ref": "MYSWaKbUDiLv"}, "preview": null, "uuid": "Vf1nlWWWxX7c", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "wvNXVzE7mKKL": {"selector": "h5", "style": {"__ref": "8XM0iv4z53b3"}, "__type": "ThemeStyle"}, "FFOFUn_OXW_k": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "WEGPQvHZEiYQ": {"name": "Default \"h6\"", "rs": {"__ref": "FFOFUn_OXW_k"}, "preview": null, "uuid": "sZVAtBbVPr6d", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "qXnGQAPyKj0A": {"selector": "h6", "style": {"__ref": "WEGPQvHZEiYQ"}, "__type": "ThemeStyle"}, "i03ao7CueOJI": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "o2pLQ9eOw0ax": {"name": "Default \"a\"", "rs": {"__ref": "i03ao7CueOJI"}, "preview": null, "uuid": "asR7R1q3FvuP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9afDX5d9kvnn": {"selector": "a", "style": {"__ref": "o2pLQ9eOw0ax"}, "__type": "ThemeStyle"}, "-_QdA-nMTIER": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "5jRDJwf4832w": {"name": "Default \"a:hover\"", "rs": {"__ref": "-_QdA-nMTIER"}, "preview": null, "uuid": "M07KQqZ5OdJE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "l00FAQwTcO4u": {"selector": "a:hover", "style": {"__ref": "5jRDJwf4832w"}, "__type": "ThemeStyle"}, "nA79TMfjZvYX": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "1zBjviDs_71D": {"name": "Default \"blockquote\"", "rs": {"__ref": "nA79TMfjZvYX"}, "preview": null, "uuid": "TPpuxCDSKMl1", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "--IiRc0AoPl_": {"selector": "blockquote", "style": {"__ref": "1zBjviDs_71D"}, "__type": "ThemeStyle"}, "4F2Wriw74VMJ": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "mbIxUpStXtgs": {"name": "Default \"code\"", "rs": {"__ref": "4F2Wriw74VMJ"}, "preview": null, "uuid": "dzAvAfVurKNN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "MMVzdfVIXzdb": {"selector": "code", "style": {"__ref": "mbIxUpStXtgs"}, "__type": "ThemeStyle"}, "XwLi79d5Jw6m": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "Q3wq6QM_WwMP": {"name": "Default \"pre\"", "rs": {"__ref": "XwLi79d5Jw6m"}, "preview": null, "uuid": "YY-yi88JErzW", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "YYyfI7XNRhVf": {"selector": "pre", "style": {"__ref": "Q3wq6QM_WwMP"}, "__type": "ThemeStyle"}, "eBUe1_5RVSo5": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "UwrDQxQPqjBG": {"name": "Default \"ol\"", "rs": {"__ref": "eBUe1_5RVSo5"}, "preview": null, "uuid": "Fz-mJ7N4OAUt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "pBzVgibt42B5": {"selector": "ol", "style": {"__ref": "UwrDQxQPqjBG"}, "__type": "ThemeStyle"}, "gLnChgN5Mlho": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "A4NhXhNbwPGa": {"name": "Default \"ul\"", "rs": {"__ref": "gLnChgN5Mlho"}, "preview": null, "uuid": "H2E-HsF_gwE5", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "-ENKfOqYiVhA": {"selector": "ul", "style": {"__ref": "A4NhXhNbwPGa"}, "__type": "ThemeStyle"}, "qo3qwTcxHCkC": {"defaultStyle": {"__ref": "sqdCsA16BrHr"}, "styles": [{"__ref": "-usvq8jxuqeb"}, {"__ref": "oJf2ynb4U8wz"}, {"__ref": "eBfKperFlvhg"}, {"__ref": "3EkdiMrlrHHJ"}, {"__ref": "wvNXVzE7mKKL"}, {"__ref": "qXnGQAPyKj0A"}, {"__ref": "9afDX5d9kvnn"}, {"__ref": "l00FAQwTcO4u"}, {"__ref": "--IiRc0AoPl_"}, {"__ref": "MMVzdfVIXzdb"}, {"__ref": "YYyfI7XNRhVf"}, {"__ref": "pBzVgibt42B5"}, {"__ref": "-ENKfOqYiVhA"}], "layout": {"__ref": "OpAJV5T7yIpC"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "gd4hiCPhrnur": {"name": "text", "__type": "Text"}, "Ht-YJfnY5Xli": {"name": "Screen", "uuid": "6UXfEWQq66EZ", "__type": "Var"}, "xl7eCHRClJUC": {"type": {"__ref": "gd4hiCPhrnur"}, "variable": {"__ref": "Ht-YJfnY5Xli"}, "uuid": "CpJIHlY_kk6x", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "wXo0ZYN2Lbpe": {"type": "global-screen", "param": {"__ref": "xl7eCHRClJUC"}, "uuid": "ql6fA1T7fIfO", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "mdpjNxKVMBQg": {"uuid": "Nh6IbNiVfrt3", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "mZW4NX4TIgod": {"components": [{"__ref": "qSTo-25VqnHJ"}, {"__ref": "wEKiWocCR6xm"}, {"__ref": "Gw3H3s8IkqRj"}, {"__ref": "UsMt9aWHCK5h"}, {"__ref": "mvwHRdv1InhQ"}, {"__ref": "MLdw7y6m_dlh"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "yU7yNPwz3-Vh"}, {"__ref": "ui6lVf41FvfI"}], "globalVariantGroups": [{"__ref": "wXo0ZYN2Lbpe"}], "userManagedFonts": [], "globalVariant": {"__ref": "mdpjNxKVMBQg"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "qo3qwTcxHCkC"}], "activeTheme": {"__ref": "qo3qwTcxHCkC"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "wXo0ZYN2Lbpe"}, "flags": {"usePlasmicImg": true, "useLoadingState": true, "defaultInsertable": "plexus"}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "qSTo-25VqnHJ": {"uuid": "JZuQ2i5KdOUr", "name": "hostless-plasmic-head", "params": [{"__ref": "Sa1eOk_qaeNs"}, {"__ref": "NU6Vazpv0h-L"}, {"__ref": "K7mRHMkn7tO_"}, {"__ref": "otLMzoZ2z3_F"}], "states": [], "tplTree": {"__ref": "zUcfryyKOI2v"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "LM_CO7-VIRM7"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "ZWCAlN3CcWdY"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "wEKiWocCR6xm": {"uuid": "xsIub4JZmzk2", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "GlEmF5V4BvG0"}, {"__ref": "BwqqnyEQaGZ7"}, {"__ref": "-h8YUSPlz4vt"}, {"__ref": "qUQpBjkNT25g"}, {"__ref": "_kv6G9zgjJWb"}], "states": [], "tplTree": {"__ref": "2d2mlhl1yigH"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "4nXP9u1wlvfN"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "gPE1sePLXEAq"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "Sa1eOk_qaeNs": {"type": {"__ref": "LXATOWxNSms0"}, "variable": {"__ref": "NctcmZiTGLCN"}, "uuid": "nekthGTNIB3d", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "NU6Vazpv0h-L": {"type": {"__ref": "QuH_bzLhi<PERSON>li"}, "variable": {"__ref": "IUO1V3Vafrt4"}, "uuid": "KZo7vAC_AcKI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "K7mRHMkn7tO_": {"type": {"__ref": "aD3mrCY8Pf0F"}, "variable": {"__ref": "_2GwnI9obhKH"}, "uuid": "iVqvGezmH-jX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "otLMzoZ2z3_F": {"type": {"__ref": "_TRapvcc4kek"}, "variable": {"__ref": "2ldo5yW5qUCl"}, "uuid": "IWusCTcncJE5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "zUcfryyKOI2v": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "eeW2lPlVRk3S", "parent": null, "locked": null, "vsettings": [{"__ref": "zwotsG5fMx56"}], "__type": "TplTag"}, "LM_CO7-VIRM7": {"uuid": "GCpOFr6YZrFS", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ZWCAlN3CcWdY": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "GlEmF5V4BvG0": {"type": {"__ref": "kLwfiribPrNR"}, "variable": {"__ref": "zjhlaDFb8mXR"}, "uuid": "HTb2qGW7rUxy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "BwqqnyEQaGZ7": {"type": {"__ref": "DsBQZxyKGxkF"}, "variable": {"__ref": "lV3tAjrEk9XX"}, "uuid": "8D0cE4oUFpmE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "-h8YUSPlz4vt": {"type": {"__ref": "8_LvkvRwVbAV"}, "tplSlot": {"__ref": "okrH0_JtJSw1"}, "variable": {"__ref": "zm1MCCzg-Y0K"}, "uuid": "rtZfleIhpTuT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "qUQpBjkNT25g": {"type": {"__ref": "SulhjUUKjkRn"}, "variable": {"__ref": "GfYrBaElydXd"}, "uuid": "X3oCbsL240_2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "_kv6G9zgjJWb": {"type": {"__ref": "0wBLysZgnth3"}, "variable": {"__ref": "uVHgZVz7REAw"}, "uuid": "XFRtFp4pFic1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "2d2mlhl1yigH": {"tag": "div", "name": null, "children": [{"__ref": "okrH0_JtJSw1"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "rqhNWb3hJET0", "parent": null, "locked": null, "vsettings": [{"__ref": "LvzcqNPxWOSO"}], "__type": "TplTag"}, "4nXP9u1wlvfN": {"uuid": "gPRGo6fIvYMV", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gPE1sePLXEAq": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "LXATOWxNSms0": {"name": "text", "__type": "Text"}, "NctcmZiTGLCN": {"name": "title", "uuid": "-X9TEGd3eJni", "__type": "Var"}, "QuH_bzLhiDli": {"name": "text", "__type": "Text"}, "IUO1V3Vafrt4": {"name": "description", "uuid": "UNf-J0wVegaP", "__type": "Var"}, "aD3mrCY8Pf0F": {"name": "img", "__type": "Img"}, "_2GwnI9obhKH": {"name": "image", "uuid": "ctYBHLMqJLpW", "__type": "Var"}, "_TRapvcc4kek": {"name": "text", "__type": "Text"}, "2ldo5yW5qUCl": {"name": "canonical", "uuid": "K9qqIn9p6OFG", "__type": "Var"}, "zwotsG5fMx56": {"variants": [{"__ref": "LM_CO7-VIRM7"}], "args": [], "attrs": {}, "rs": {"__ref": "SIaTpnJtkdJI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kLwfiribPrNR": {"name": "any", "__type": "AnyType"}, "zjhlaDFb8mXR": {"name": "dataOp", "uuid": "YO5dmJKCvosl", "__type": "Var"}, "DsBQZxyKGxkF": {"name": "text", "__type": "Text"}, "lV3tAjrEk9XX": {"name": "name", "uuid": "9gypE6V6wzaC", "__type": "Var"}, "8_LvkvRwVbAV": {"name": "renderFunc", "params": [{"__ref": "U38qafz94cFY"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "zm1MCCzg-Y0K": {"name": "children", "uuid": "cWw8aDBU2Gj9", "__type": "Var"}, "SulhjUUKjkRn": {"name": "num", "__type": "<PERSON><PERSON>"}, "GfYrBaElydXd": {"name": "pageSize", "uuid": "eEiOKyF7jIvG", "__type": "Var"}, "0wBLysZgnth3": {"name": "num", "__type": "<PERSON><PERSON>"}, "uVHgZVz7REAw": {"name": "pageIndex", "uuid": "efqe24DCPbfN", "__type": "Var"}, "okrH0_JtJSw1": {"param": {"__ref": "-h8YUSPlz4vt"}, "defaultContents": [], "uuid": "UgQ0bE3lT_fu", "parent": {"__ref": "2d2mlhl1yigH"}, "locked": null, "vsettings": [{"__ref": "O_ZOJ4LUTQiu"}], "__type": "TplSlot"}, "LvzcqNPxWOSO": {"variants": [{"__ref": "4nXP9u1wlvfN"}], "args": [], "attrs": {}, "rs": {"__ref": "eCwTecrvi5c_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SIaTpnJtkdJI": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "U38qafz94cFY": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "kvK6y-XAgyd4"}, "__type": "ArgType"}, "O_ZOJ4LUTQiu": {"variants": [{"__ref": "4nXP9u1wlvfN"}], "args": [], "attrs": {}, "rs": {"__ref": "Rj7GX2Iy8s0x"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eCwTecrvi5c_": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "kvK6y-XAgyd4": {"name": "any", "__type": "AnyType"}, "Rj7GX2Iy8s0x": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Gw3H3s8IkqRj": {"uuid": "H_NNvwlLKVxe", "name": "Component with element variants", "params": [], "states": [], "tplTree": {"__ref": "JtisrN1NusHg"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "oAfgDkOatN9v"}, {"__ref": "XVmhFlo9lynZ"}, {"__ref": "A8bMtcjhx0Zn"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "JtisrN1NusHg": {"tag": "div", "name": null, "children": [{"__ref": "vPFx_UeW0FuC"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "WZcrPN0jJgqj", "parent": null, "locked": null, "vsettings": [{"__ref": "oo4-KKHAN0NX"}], "__type": "TplTag"}, "oAfgDkOatN9v": {"uuid": "6XzIKh_UuNz4", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "oo4-KKHAN0NX": {"variants": [{"__ref": "oAfgDkOatN9v"}], "args": [], "attrs": {}, "rs": {"__ref": "PzuLxqyr9BW0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PzuLxqyr9BW0": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "UsMt9aWHCK5h": {"uuid": "53ewoTOB3To2", "name": "CodeComponentDebug", "params": [{"__ref": "ODYH7XXUJL_L"}, {"__ref": "lZ9QjhoSMb2I"}, {"__ref": "iXJoRJ4uTQ3b"}, {"__ref": "OthJgqnvSONr"}, {"__ref": "xDTP_v_hSFdb"}, {"__ref": "PtZh8JQI7qxk"}], "states": [], "tplTree": {"__ref": "xo79gRXcZoiZ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "cTzSZA3sjuNG"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "NmOd3KJuuYqD"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "mvwHRdv1InhQ": {"uuid": "3yXRWhosbcWS", "name": "CodeComponentWithVariants", "params": [], "states": [], "tplTree": {"__ref": "TozjRkFxHzWW"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "-zjBG66SCrzX"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "ZKPLwqu0lI4S"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "ODYH7XXUJL_L": {"type": {"__ref": "4w91y2IM4Eyw"}, "tplSlot": {"__ref": "lIbF96AYjFAS"}, "variable": {"__ref": "2SgpJPQ_fD9g"}, "uuid": "GcHAGVw64EJn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "lZ9QjhoSMb2I": {"type": {"__ref": "VatpNzcIVWYA"}, "variable": {"__ref": "u6s72le96DAb"}, "uuid": "4wGGARo7ca8b", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "iXJoRJ4uTQ3b": {"type": {"__ref": "W1F9Qfnti8RT"}, "variable": {"__ref": "oPb0gVqLIgbB"}, "uuid": "JG-rUtAgzlWf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "OthJgqnvSONr": {"type": {"__ref": "qZgc3Mt8aI8H"}, "variable": {"__ref": "3x04dczwRcMa"}, "uuid": "6Zwljjbjs2Pa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "xDTP_v_hSFdb": {"type": {"__ref": "h5qo4z8sLZ-z"}, "variable": {"__ref": "pGA8WrvBiKCZ"}, "uuid": "HJTkLNBGTF81", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "PtZh8JQI7qxk": {"type": {"__ref": "Nglk7Y1Uijf4"}, "variable": {"__ref": "6janwaMz8Rxv"}, "uuid": "gy9bGHT4KkID", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "xo79gRXcZoiZ": {"tag": "div", "name": null, "children": [{"__ref": "lIbF96AYjFAS"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uPscRdfiYnQw", "parent": null, "locked": null, "vsettings": [{"__ref": "Z3q7V-_-dEXZ"}], "__type": "TplTag"}, "cTzSZA3sjuNG": {"uuid": "SRlBhCFlUh-9", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "NmOd3KJuuYqD": {"importPath": "@/codecomponents/CodeComponentDebug", "defaultExport": false, "displayName": null, "importName": null, "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "TozjRkFxHzWW": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "k5zwi-xC1e5e", "parent": null, "locked": null, "vsettings": [{"__ref": "Et11DUnBEybr"}], "__type": "TplTag"}, "-zjBG66SCrzX": {"uuid": "aLE28YOuP0ri", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ZKPLwqu0lI4S": {"importPath": "@/codecomponents/CodeComponentWithVariants", "defaultExport": false, "displayName": null, "importName": null, "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {"a": {"__ref": "G8MeoATgjZHn"}, "b": {"__ref": "H4h3oXKmAibU"}}, "refActions": [], "__type": "CodeComponentMeta"}, "4w91y2IM4Eyw": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "2SgpJPQ_fD9g": {"name": "children", "uuid": "81lQW357xO6b", "__type": "Var"}, "VatpNzcIVWYA": {"name": "func", "params": [], "__type": "FunctionType"}, "u6s72le96DAb": {"name": "on change", "uuid": "FBV_IsqLX29_", "__type": "Var"}, "W1F9Qfnti8RT": {"name": "text", "__type": "Text"}, "oPb0gVqLIgbB": {"name": "aria-label", "uuid": "y39KSHnx-avX", "__type": "Var"}, "qZgc3Mt8aI8H": {"name": "text", "__type": "Text"}, "3x04dczwRcMa": {"name": "aria-invalid-attr", "uuid": "KRrxC6QzWP4c", "__type": "Var"}, "h5qo4z8sLZ-z": {"name": "text", "__type": "Text"}, "pGA8WrvBiKCZ": {"name": "data-tester", "uuid": "a9T8aN_FSDvo", "__type": "Var"}, "Nglk7Y1Uijf4": {"name": "text", "__type": "Text"}, "6janwaMz8Rxv": {"name": "data-camelCase", "uuid": "cccPUSn4-vEm", "__type": "Var"}, "lIbF96AYjFAS": {"param": {"__ref": "ODYH7XXUJL_L"}, "defaultContents": [], "uuid": "wfSJA3ixYKSB", "parent": {"__ref": "xo79gRXcZoiZ"}, "locked": null, "vsettings": [{"__ref": "A3q9gcssrFrt"}], "__type": "TplSlot"}, "Z3q7V-_-dEXZ": {"variants": [{"__ref": "cTzSZA3sjuNG"}], "args": [], "attrs": {}, "rs": {"__ref": "QLJlZKTY_C5x"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Et11DUnBEybr": {"variants": [{"__ref": "-zjBG66SCrzX"}], "args": [], "attrs": {}, "rs": {"__ref": "laexLeyAK5we"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "G8MeoATgjZHn": {"cssSelector": "[data-variant-a]", "displayName": "Variant A", "__type": "CodeComponentVariantMeta"}, "H4h3oXKmAibU": {"cssSelector": "[data-variant-b]", "displayName": "Variant B", "__type": "CodeComponentVariantMeta"}, "A3q9gcssrFrt": {"variants": [{"__ref": "cTzSZA3sjuNG"}], "args": [], "attrs": {}, "rs": {"__ref": "dDAknicpTzdb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QLJlZKTY_C5x": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "laexLeyAK5we": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "dDAknicpTzdb": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vPFx_UeW0FuC": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "1Ld_9HLQ6bZq", "parent": {"__ref": "JtisrN1NusHg"}, "locked": null, "vsettings": [{"__ref": "lMqsJC-pwdK8"}, {"__ref": "_UCXDLqqwZcT"}, {"__ref": "3dhKfYYsXNQQ"}], "__type": "TplTag"}, "lMqsJC-pwdK8": {"variants": [{"__ref": "oAfgDkOatN9v"}], "args": [], "attrs": {}, "rs": {"__ref": "NF7UUnmMIA-U"}, "dataCond": null, "dataRep": {"__ref": "68k91bg8agqH"}, "text": {"__ref": "G6f_L8V_N0OU"}, "columnsConfig": null, "__type": "VariantSetting"}, "NF7UUnmMIA-U": {"values": {"position": "relative", "font-size": "64px"}, "mixins": [], "__type": "RuleSet"}, "68k91bg8agqH": {"element": {"__ref": "Gz1slh7Tnf4F"}, "index": {"__ref": "CdbO2jJo38oY"}, "collection": {"__ref": "FH9Ag9jMu3Nu"}, "__type": "Rep"}, "Gz1slh7Tnf4F": {"name": "currentItem", "uuid": "iD3l042iB_Ey", "__type": "Var"}, "CdbO2jJo38oY": {"name": "currentIndex", "uuid": "Kf9t7ikqCpNd", "__type": "Var"}, "FH9Ag9jMu3Nu": {"code": "([1, 2, 3, 4, 5])", "fallback": {"__ref": "ln1G08cq61sT"}, "__type": "CustomCode"}, "ln1G08cq61sT": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "G6f_L8V_N0OU": {"expr": {"__ref": "KdyWzq-l7dRU"}, "html": false, "__type": "ExprText"}, "KdyWzq-l7dRU": {"code": "(`:nth-child(${currentItem})`)", "fallback": {"__ref": "oNIrUNLdBzpq"}, "__type": "CustomCode"}, "oNIrUNLdBzpq": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "MLdw7y6m_dlh": {"uuid": "uqomJhqE0Ik8", "name": "Code component with variants wrapper", "params": [], "states": [], "tplTree": {"__ref": "dcaZyW_uJ5za"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "PRONifsSgL21"}, {"__ref": "QaQzoP0b6wHe"}, {"__ref": "WxeegZvfKu6S"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "PRONifsSgL21": {"uuid": "H3bO4JPSbrk7", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "yU7yNPwz3-Vh": {"component": {"__ref": "Gw3H3s8IkqRj"}, "matrix": {"__ref": "YCHJkRmQhKPW"}, "customMatrix": {"__ref": "Y3LVgfeV_oXo"}, "__type": "ComponentArena"}, "YCHJkRmQhKPW": {"rows": [{"__ref": "8a1KPPfwwzR-"}], "__type": "ArenaFrameGrid"}, "Y3LVgfeV_oXo": {"rows": [{"__ref": "2Y5w28qGdY5l"}], "__type": "ArenaFrameGrid"}, "8a1KPPfwwzR-": {"cols": [{"__ref": "jyEArDU3sK0h"}], "rowKey": null, "__type": "ArenaFrameRow"}, "2Y5w28qGdY5l": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "jyEArDU3sK0h": {"frame": {"__ref": "K9D9wyPw9Rpr"}, "cellKey": {"__ref": "oAfgDkOatN9v"}, "__type": "ArenaFrameCell"}, "K9D9wyPw9Rpr": {"uuid": "D3r-ERfir6B1", "width": 1180, "height": 540, "container": {"__ref": "SZ8Sc30SR_Uc"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "SZ8Sc30SR_Uc": {"name": null, "component": {"__ref": "Gw3H3s8IkqRj"}, "uuid": "13-9SklPYs5L", "parent": null, "locked": null, "vsettings": [{"__ref": "Fi59uoCeseqz"}], "__type": "TplComponent"}, "Fi59uoCeseqz": {"variants": [{"__ref": "mdpjNxKVMBQg"}], "args": [], "attrs": {}, "rs": {"__ref": "7Vwl5rUoULAa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7Vwl5rUoULAa": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ui6lVf41FvfI": {"component": {"__ref": "MLdw7y6m_dlh"}, "matrix": {"__ref": "vUBQfnebNtvA"}, "customMatrix": {"__ref": "iwX6y6lejhaZ"}, "__type": "ComponentArena"}, "vUBQfnebNtvA": {"rows": [{"__ref": "H-b8yHM7D7K-"}], "__type": "ArenaFrameGrid"}, "iwX6y6lejhaZ": {"rows": [{"__ref": "p6J2uNCMhKPK"}], "__type": "ArenaFrameGrid"}, "H-b8yHM7D7K-": {"cols": [{"__ref": "tbOEZodCIUhF"}, {"__ref": "ekKDOzCBL4vS"}, {"__ref": "8rwW3icUVaF0"}], "rowKey": null, "__type": "ArenaFrameRow"}, "p6J2uNCMhKPK": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "tbOEZodCIUhF": {"frame": {"__ref": "SbdYTRT6u2tL"}, "cellKey": {"__ref": "PRONifsSgL21"}, "__type": "ArenaFrameCell"}, "SbdYTRT6u2tL": {"uuid": "B73Dj0Yo4j1a", "width": 1180, "height": 540, "container": {"__ref": "ynvOMSAhxoW3"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "PRONifsSgL21"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ynvOMSAhxoW3": {"name": null, "component": {"__ref": "MLdw7y6m_dlh"}, "uuid": "lziu1OSH_Ala", "parent": null, "locked": null, "vsettings": [{"__ref": "jEq0B6_vWzyn"}], "__type": "TplComponent"}, "jEq0B6_vWzyn": {"variants": [{"__ref": "mdpjNxKVMBQg"}], "args": [], "attrs": {}, "rs": {"__ref": "TL45D3HSlG5Q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TL45D3HSlG5Q": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dcaZyW_uJ5za": {"name": null, "component": {"__ref": "mvwHRdv1InhQ"}, "uuid": "1TAMWtuaGDm_", "parent": null, "locked": null, "vsettings": [{"__ref": "gd_Evj-8jliC"}, {"__ref": "85Qfymw9eHQa"}, {"__ref": "SpqVqB6Omk-L"}], "__type": "TplComponent"}, "gd_Evj-8jliC": {"variants": [{"__ref": "PRONifsSgL21"}], "args": [], "attrs": {}, "rs": {"__ref": "Jgh1sDwAjzF_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Jgh1sDwAjzF_": {"values": {"max-width": "100%", "object-fit": "cover"}, "mixins": [], "__type": "RuleSet"}, "QaQzoP0b6wHe": {"uuid": "OE1bgS8jhoKq", "name": "", "selectors": null, "codeComponentName": "CodeComponentWithVariants", "codeComponentVariantKeys": ["a"], "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ekKDOzCBL4vS": {"frame": {"__ref": "8hH307jjoY6W"}, "cellKey": {"__ref": "QaQzoP0b6wHe"}, "__type": "ArenaFrameCell"}, "8hH307jjoY6W": {"uuid": "qFsT2mD2OSZF", "width": 1180, "height": 540, "container": {"__ref": "ayKIAhk9TqrT"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QaQzoP0b6wHe"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ayKIAhk9TqrT": {"name": null, "component": {"__ref": "MLdw7y6m_dlh"}, "uuid": "x4u2bJL41rdC", "parent": null, "locked": null, "vsettings": [{"__ref": "M_QnPnOMCFSG"}], "__type": "TplComponent"}, "M_QnPnOMCFSG": {"variants": [{"__ref": "mdpjNxKVMBQg"}], "args": [], "attrs": {}, "rs": {"__ref": "ZBQV68CTXwLO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZBQV68CTXwLO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "85Qfymw9eHQa": {"variants": [{"__ref": "QaQzoP0b6wHe"}], "args": [], "attrs": {}, "rs": {"__ref": "yCaaYf_oveQs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yCaaYf_oveQs": {"values": {"font-style": "normal"}, "mixins": [], "__type": "RuleSet"}, "WxeegZvfKu6S": {"uuid": "PK7l-MQpUZeB", "name": "", "selectors": null, "codeComponentName": "CodeComponentWithVariants", "codeComponentVariantKeys": ["b"], "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8rwW3icUVaF0": {"frame": {"__ref": "Y25yO7jH8SDM"}, "cellKey": {"__ref": "WxeegZvfKu6S"}, "__type": "ArenaFrameCell"}, "Y25yO7jH8SDM": {"uuid": "S8uerti9kVU8", "width": 1180, "height": 540, "container": {"__ref": "RAZ0nf3Wu0xC"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "WxeegZvfKu6S"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "RAZ0nf3Wu0xC": {"name": null, "component": {"__ref": "MLdw7y6m_dlh"}, "uuid": "8YpK5LDTka4e", "parent": null, "locked": null, "vsettings": [{"__ref": "NAnkFAA1-rtI"}], "__type": "TplComponent"}, "NAnkFAA1-rtI": {"variants": [{"__ref": "mdpjNxKVMBQg"}], "args": [], "attrs": {}, "rs": {"__ref": "cgxAbzm9tya2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cgxAbzm9tya2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SpqVqB6Omk-L": {"variants": [{"__ref": "WxeegZvfKu6S"}], "args": [], "attrs": {}, "rs": {"__ref": "yRBceGLJRRjD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yRBceGLJRRjD": {"values": {"font-style": "italic"}, "mixins": [], "__type": "RuleSet"}, "XVmhFlo9lynZ": {"uuid": "RCxUBduyegbP", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "vPFx_UeW0FuC"}, "__type": "<PERSON><PERSON><PERSON>"}, "_UCXDLqqwZcT": {"variants": [{"__ref": "XVmhFlo9lynZ"}], "args": [], "attrs": {}, "rs": {"__ref": "pAA9ZfD17Fr-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pAA9ZfD17Fr-": {"values": {"font-weight": "700"}, "mixins": [], "__type": "RuleSet"}, "A8bMtcjhx0Zn": {"uuid": "JUirmWK7r4XU", "name": "", "selectors": [":nth-child(odd)"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "vPFx_UeW0FuC"}, "__type": "<PERSON><PERSON><PERSON>"}, "3dhKfYYsXNQQ": {"variants": [{"__ref": "A8bMtcjhx0Zn"}], "args": [], "attrs": {}, "rs": {"__ref": "kiDtZUbQogbA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kiDtZUbQogbA": {"values": {"text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "246-add-component-updated-at"}