{"root": "xlberAZ6R9ca", "map": {"veyYj5cfZUn9": {"name": "title", "uuid": "vxAzoSH80rM5", "__type": "Var"}, "MSWej4GyivUh": {"name": "text", "__type": "Text"}, "aWV829uTiD-O": {"type": {"__ref": "MSWej4GyivUh"}, "variable": {"__ref": "veyYj5cfZUn9"}, "uuid": "NKpNCRj4ActC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "rHOn9lri23Mc": {"name": "description", "uuid": "a3dM6Up08xmw", "__type": "Var"}, "NlQJum24lH4H": {"name": "text", "__type": "Text"}, "o5udqhg5gHfE": {"type": {"__ref": "NlQJum24lH4H"}, "variable": {"__ref": "rHOn9lri23Mc"}, "uuid": "I1_okUeQhpKK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "v0e3O81lf6Ef": {"name": "image", "uuid": "utxyMK-IaUVg", "__type": "Var"}, "q0UQCFYdNUXD": {"name": "img", "__type": "Img"}, "xIyd51pqc0FY": {"type": {"__ref": "q0UQCFYdNUXD"}, "variable": {"__ref": "v0e3O81lf6Ef"}, "uuid": "0CauUM7MPaD7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "MW3nU6fE5hjY": {"name": "canonical", "uuid": "bZewU9KBy4oL", "__type": "Var"}, "dejphpQO1jdi": {"name": "text", "__type": "Text"}, "fvs358zmuBYn": {"type": {"__ref": "dejphpQO1jdi"}, "variable": {"__ref": "MW3nU6fE5hjY"}, "uuid": "FGsYD97IIGwB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "R8ACrbhWa3L1": {"uuid": "bbDw5OVh94f8", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "U06gY0ra-9aR": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "Uy6Y28GC3zrZ": {"variants": [{"__ref": "R8ACrbhWa3L1"}], "args": [], "attrs": {}, "rs": {"__ref": "U06gY0ra-9aR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SQZb-S6M-AGe": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "-HK6eeSQkozm", "parent": null, "locked": null, "vsettings": [{"__ref": "Uy6Y28GC3zrZ"}], "__type": "TplTag"}, "IpsKprl0r8Dx": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "Lkkn1NhKdTlS": {"uuid": "GR5oj0sAtaa5", "name": "hostless-plasmic-head", "params": [{"__ref": "aWV829uTiD-O"}, {"__ref": "o5udqhg5gHfE"}, {"__ref": "xIyd51pqc0FY"}, {"__ref": "fvs358zmuBYn"}], "states": [], "tplTree": {"__ref": "SQZb-S6M-AGe"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "R8ACrbhWa3L1"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "IpsKprl0r8Dx"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "A7tO22KzyDAJ": {"name": "dataOp", "uuid": "9Upyayes7Auy", "__type": "Var"}, "hh7b3RZQgVRk": {"name": "any", "__type": "AnyType"}, "A7gT-c9930Cl": {"type": {"__ref": "hh7b3RZQgVRk"}, "variable": {"__ref": "A7tO22KzyDAJ"}, "uuid": "nTPKXNqFmc01", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "gGQJUG7AtZ0e": {"name": "name", "uuid": "SHpOD-mu8Cc_", "__type": "Var"}, "1eF3dP41ErSc": {"name": "text", "__type": "Text"}, "42bHf32Xfab4": {"type": {"__ref": "1eF3dP41ErSc"}, "variable": {"__ref": "gGQJUG7AtZ0e"}, "uuid": "gezeLYmU6OFS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "2jjrEHH4ROMv": {"name": "children", "uuid": "j-3Gnlhcl9li", "__type": "Var"}, "-3SLprsGch9Y": {"name": "any", "__type": "AnyType"}, "vUfyUfJ8ZgFW": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "-3SLprsGch9Y"}, "__type": "ArgType"}, "HRlxwW7Y-0M6": {"name": "renderFunc", "params": [{"__ref": "vUfyUfJ8ZgFW"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "uUYb_HiAYcfS": {"type": {"__ref": "HRlxwW7Y-0M6"}, "tplSlot": {"__ref": "drT49-2EL5nz"}, "variable": {"__ref": "2jjrEHH4ROMv"}, "uuid": "v0i3mpp6RhwS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "-Secp8Gc4ntq": {"name": "pageSize", "uuid": "UiZwuL_PDhQh", "__type": "Var"}, "GXLzZmxF_9I0": {"name": "num", "__type": "<PERSON><PERSON>"}, "GyFruzcnFgCt": {"type": {"__ref": "GXLzZmxF_9I0"}, "variable": {"__ref": "-Secp8Gc4ntq"}, "uuid": "fTIcAR7UjaDo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "K3l4LTwO9vhl": {"name": "pageIndex", "uuid": "ULwON8asP0No", "__type": "Var"}, "CXHoCPz0XpsU": {"name": "num", "__type": "<PERSON><PERSON>"}, "SXMiKdmHLmFZ": {"type": {"__ref": "CXHoCPz0XpsU"}, "variable": {"__ref": "K3l4LTwO9vhl"}, "uuid": "mIMlKKc3oRTa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "q8YmOeA1fRmS": {"uuid": "RtmgoIhqbx9s", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "sYyd2pwEvQLc": {"values": {}, "mixins": [], "__type": "RuleSet"}, "AfiWOdjivUn8": {"variants": [{"__ref": "q8YmOeA1fRmS"}], "args": [], "attrs": {}, "rs": {"__ref": "sYyd2pwEvQLc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "drT49-2EL5nz": {"param": {"__ref": "uUYb_HiAYcfS"}, "defaultContents": [], "uuid": "jtVUOKTVD9-j", "parent": {"__ref": "6Vl68lZdVtnB"}, "locked": null, "vsettings": [{"__ref": "AfiWOdjivUn8"}], "__type": "TplSlot"}, "TVryfbro6IO7": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "de98iw3Pu2LH": {"variants": [{"__ref": "q8YmOeA1fRmS"}], "args": [], "attrs": {}, "rs": {"__ref": "TVryfbro6IO7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6Vl68lZdVtnB": {"tag": "div", "name": null, "children": [{"__ref": "drT49-2EL5nz"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "oQjA51nzlQjt", "parent": null, "locked": null, "vsettings": [{"__ref": "de98iw3Pu2LH"}], "__type": "TplTag"}, "vtiKHbzFhL6X": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "eorX1CoEZz0q": {"uuid": "BaTKa3itTT6z", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "A7gT-c9930Cl"}, {"__ref": "42bHf32Xfab4"}, {"__ref": "uUYb_HiAYcfS"}, {"__ref": "GyFruzcnFgCt"}, {"__ref": "SXMiKdmHLmFZ"}], "states": [], "tplTree": {"__ref": "6Vl68lZdVtnB"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "q8YmOeA1fRmS"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "vtiKHbzFhL6X"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "o_Ged5koeyaS": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "AQsgD5fcS_XS": {"name": "Default Typography", "rs": {"__ref": "o_Ged5koeyaS"}, "preview": null, "uuid": "WrcgwMhrQYXt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ouFXsHauIm8n": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "nWiEm1kr9vcJ": {"name": "Default \"h1\"", "rs": {"__ref": "ouFXsHauIm8n"}, "preview": null, "uuid": "cA3NGCUq7_qO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "JAR4W739mxNU": {"selector": "h1", "style": {"__ref": "nWiEm1kr9vcJ"}, "__type": "ThemeStyle"}, "48CPm9N0nkXw": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "7zKDSejNEIAv": {"name": "Default \"h2\"", "rs": {"__ref": "48CPm9N0nkXw"}, "preview": null, "uuid": "poLmbQe4TLWU", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "DldMK5VI_Npu": {"selector": "h2", "style": {"__ref": "7zKDSejNEIAv"}, "__type": "ThemeStyle"}, "YCC68i_KV6QO": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "89QJfCyiihwJ": {"name": "Default \"a\"", "rs": {"__ref": "YCC68i_KV6QO"}, "preview": null, "uuid": "F8G716aQEIUQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "sPu_jLJ0HNgM": {"selector": "a", "style": {"__ref": "89QJfCyiihwJ"}, "__type": "ThemeStyle"}, "3lomtQIwDtiK": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "hOEiGHJzwfxf": {"name": "Default \"h3\"", "rs": {"__ref": "3lomtQIwDtiK"}, "preview": null, "uuid": "OU0kwsviyxhj", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Q0wcOz2S03Ty": {"selector": "h3", "style": {"__ref": "hOEiGHJzwfxf"}, "__type": "ThemeStyle"}, "GOOcFTdvinh5": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "DAP9y25Zogl_": {"name": "Default \"h4\"", "rs": {"__ref": "GOOcFTdvinh5"}, "preview": null, "uuid": "321QtG2hlylq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "dD02464ijq7b": {"selector": "h4", "style": {"__ref": "DAP9y25Zogl_"}, "__type": "ThemeStyle"}, "_6tWUO5b2Mxm": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "SikwlNCnjq_r": {"name": "Default \"code\"", "rs": {"__ref": "_6tWUO5b2Mxm"}, "preview": null, "uuid": "WUCOGUQw6g5z", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "fIf3CwJi918j": {"selector": "code", "style": {"__ref": "SikwlNCnjq_r"}, "__type": "ThemeStyle"}, "22jEBaVcJmTB": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "sS1nYoGwQgq1": {"name": "Default \"blockquote\"", "rs": {"__ref": "22jEBaVcJmTB"}, "preview": null, "uuid": "vhmMQgue3byN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "JnUHiitM86y-": {"selector": "blockquote", "style": {"__ref": "sS1nYoGwQgq1"}, "__type": "ThemeStyle"}, "wfj1SrL0ByM-": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "5TBJCAGNjwMN": {"name": "Default \"pre\"", "rs": {"__ref": "wfj1SrL0ByM-"}, "preview": null, "uuid": "NxXMEhsStxPG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8rg1qss19IlM": {"selector": "pre", "style": {"__ref": "5TBJCAGNjwMN"}, "__type": "ThemeStyle"}, "LsrxL375WNx9": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "ZIjnnA__KBi_": {"name": "Default \"ul\"", "rs": {"__ref": "LsrxL375WNx9"}, "preview": null, "uuid": "hvpzcz3iX2PD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "MG6RINTAZZtI": {"selector": "ul", "style": {"__ref": "ZIjnnA__KBi_"}, "__type": "ThemeStyle"}, "Kl582cVzM3hU": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "HWwZJBYcEKDl": {"name": "Default \"ol\"", "rs": {"__ref": "Kl582cVzM3hU"}, "preview": null, "uuid": "iVWPzl0M6qnV", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "KNp9DL6dale-": {"selector": "ol", "style": {"__ref": "HWwZJBYcEKDl"}, "__type": "ThemeStyle"}, "e2Ums_HlHiI2": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "_t7gDjNJbwO1": {"name": "Default \"h5\"", "rs": {"__ref": "e2Ums_HlHiI2"}, "preview": null, "uuid": "6gwXLwbcndek", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "f3XIMRnlTp2j": {"selector": "h5", "style": {"__ref": "_t7gDjNJbwO1"}, "__type": "ThemeStyle"}, "Tt7ED_UerV81": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "Hj_MW0YkdnJY": {"name": "Default \"h6\"", "rs": {"__ref": "Tt7ED_UerV81"}, "preview": null, "uuid": "-u8a_5boGS_t", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "WYT9hYyDuA1m": {"selector": "h6", "style": {"__ref": "Hj_MW0YkdnJY"}, "__type": "ThemeStyle"}, "WkjcWTjQ9ycj": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "sSvgtJ2ViNf0": {"name": "Default \"a:hover\"", "rs": {"__ref": "WkjcWTjQ9ycj"}, "preview": null, "uuid": "ve8Ccf-OieRn", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ZGs3YFor62Hs": {"selector": "a:hover", "style": {"__ref": "sSvgtJ2ViNf0"}, "__type": "ThemeStyle"}, "AFygx3PkkdFh": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Tx3xLVyrBEgT": {"name": "Default \"li\"", "rs": {"__ref": "AFygx3PkkdFh"}, "preview": null, "uuid": "hDnA8ckfs9O0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "VelYdwhIVCht": {"selector": "li", "style": {"__ref": "Tx3xLVyrBEgT"}, "__type": "ThemeStyle"}, "SDzWqwwLsQ6V": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZYHIqg_35pZe": {"name": "Default \"p\"", "rs": {"__ref": "SDzWqwwLsQ6V"}, "preview": null, "uuid": "KWh0x2QT8M_O", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5_yRwoN66GbR": {"selector": "p", "style": {"__ref": "ZYHIqg_35pZe"}, "__type": "ThemeStyle"}, "9X2IEh_mRxD6": {"defaultStyle": {"__ref": "AQsgD5fcS_XS"}, "styles": [{"__ref": "JAR4W739mxNU"}, {"__ref": "DldMK5VI_Npu"}, {"__ref": "sPu_jLJ0HNgM"}, {"__ref": "Q0wcOz2S03Ty"}, {"__ref": "dD02464ijq7b"}, {"__ref": "fIf3CwJi918j"}, {"__ref": "JnUHiitM86y-"}, {"__ref": "8rg1qss19IlM"}, {"__ref": "MG6RINTAZZtI"}, {"__ref": "KNp9DL6dale-"}, {"__ref": "f3XIMRnlTp2j"}, {"__ref": "WYT9hYyDuA1m"}, {"__ref": "ZGs3YFor62Hs"}, {"__ref": "VelYdwhIVCht"}, {"__ref": "5_yRwoN66GbR"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "vorfFhTlZQwL": {"name": "Screen", "uuid": "y6wMGoJzR4Zf", "__type": "Var"}, "6Hcc2vvwkqq6": {"name": "text", "__type": "Text"}, "Yz10wYxE20GR": {"type": {"__ref": "6Hcc2vvwkqq6"}, "variable": {"__ref": "vorfFhTlZQwL"}, "uuid": "GVkGEc_WUQBv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "jBEzkzvkgxFQ": {"uuid": "ZKArZCC8RXGo", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XgAdkJ4xbzlx"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "XgAdkJ4xbzlx": {"type": "global-screen", "param": {"__ref": "Yz10wYxE20GR"}, "uuid": "0pYtnXGOSapw", "variants": [{"__ref": "jBEzkzvkgxFQ"}], "multi": true, "__type": "GlobalVariantGroup"}, "qrEu-1YowNA0": {"uuid": "ULUXc7X88JE6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "xlberAZ6R9ca": {"components": [{"__ref": "Lkkn1NhKdTlS"}, {"__ref": "eorX1CoEZz0q"}, {"__ref": "ljv8yOuzh9MH"}, {"__ref": "xDyPhZ18fpFl"}], "arenas": [], "pageArenas": [], "componentArenas": [{"__ref": "wowqTKaldaLN"}, {"__ref": "mTrOuIrt9Iwv"}], "globalVariantGroups": [{"__ref": "XgAdkJ4xbzlx"}, {"__ref": "GybPk_6OSeDa"}], "userManagedFonts": [], "globalVariant": {"__ref": "qrEu-1YowNA0"}, "styleTokens": [{"__ref": "DQh9ssGzgMTy"}, {"__ref": "N4PgK3rl1spU"}, {"__ref": "xi3Z2G4TYNfs"}, {"__ref": "g_vQcGzo9CDB"}, {"__ref": "OA3zdtW_mQml"}, {"__ref": "xobgvxB4OsBu"}, {"__ref": "aDh7ZgsKOMWn"}, {"__ref": "uGRzoJ0DjE-k"}, {"__ref": "DrHgCmDfacnk"}, {"__ref": "8exTHu5_9byp"}, {"__ref": "xkZNmdV2xu1S"}, {"__ref": "RzYS9hlPbKQE"}, {"__ref": "rrpDuHbDbM6h"}, {"__ref": "QBxv_odKPcRh"}, {"__ref": "MfWa18eFhiss"}, {"__ref": "fKEeAR8T7A1P"}, {"__ref": "Rer1eK4EKqkN"}, {"__ref": "TrjizBGpJVhg"}, {"__ref": "gxVtRO4zFo2H"}, {"__ref": "6ri3IDucqVdp"}, {"__ref": "MT-k5vK8Luer"}, {"__ref": "EekmlxHzfCbo"}, {"__ref": "jL6viAXqOYGL"}, {"__ref": "f8wuH2CnTk0M"}, {"__ref": "HCkjWKNAQj_X"}, {"__ref": "zN6SUmmCVppI"}, {"__ref": "krzlF8lOrPVS"}, {"__ref": "2fzI-f4TeNN9"}, {"__ref": "yCGZGZaHoxu6"}, {"__ref": "mrYrwdHurizl"}, {"__ref": "YCC0Nv6LCLV_"}, {"__ref": "srmaT5qip2-n"}, {"__ref": "OSMeZrxp-itB"}, {"__ref": "lEcgD9pzZg38"}, {"__ref": "IhIbt-Jp8uv4"}, {"__ref": "wZl1gnEdUIQ3"}, {"__ref": "JY1uFyqr7UoU"}, {"__ref": "Q9SPHS89BUid"}, {"__ref": "Pi5znVbGR3cH"}, {"__ref": "_imGEK8lgRha"}, {"__ref": "dl_A1UxnuhM5"}, {"__ref": "w8jH2QHrqZ3V"}, {"__ref": "I2VKY7bWXl_W"}, {"__ref": "RTR6UmP_8F-w"}, {"__ref": "djsCUuUJawbO"}, {"__ref": "D0hQ6fYSkj06"}, {"__ref": "VkWeLUdUDBRP"}, {"__ref": "ODriI8TMLED-"}, {"__ref": "D0jnWynKYynC"}, {"__ref": "-6CD5OU_zJyl"}, {"__ref": "uKH5a_nGh1rX"}, {"__ref": "uui2fodtyWml"}, {"__ref": "A2tGWYWszwlO"}, {"__ref": "1KMBQ1LsqN7U"}, {"__ref": "KkCax4xxEyA8"}], "mixins": [{"__ref": "d2wqVvCWaOXS"}, {"__ref": "S85L2_4O1kMd"}, {"__ref": "UCq6MQsYiz0f"}, {"__ref": "zFT-3XBPlBPr"}], "themes": [{"__ref": "9X2IEh_mRxD6"}], "activeTheme": {"__ref": "9X2IEh_mRxD6"}, "imageAssets": [{"__ref": "afminywZ0mim"}, {"__ref": "sWGdqU6z6ble"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "XgAdkJ4xbzlx"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "DQh9ssGzgMTy": {"name": "Brand/Brand", "type": "Color", "uuid": "13qxbVpOuDgt", "value": "#2563EB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "N4PgK3rl1spU": {"name": "Size-8", "type": "Spacing", "uuid": "cQN-kLJ4Ac-I", "value": "0.5rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "xi3Z2G4TYNfs": {"name": "Sans-serif", "type": "FontFamily", "uuid": "mlpm_-b_JUr4", "value": "Inter", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "g_vQcGzo9CDB": {"name": "Font-MD", "type": "FontSize", "uuid": "I0yTqmdLxoaT", "value": "1rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "OA3zdtW_mQml": {"name": "Line-height-MD", "type": "LineHeight", "uuid": "TcVm0YOWmNuw", "value": "1.25", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "xobgvxB4OsBu": {"name": "Size-6", "type": "Spacing", "uuid": "GfeoM-SWpW1v", "value": "0.375rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "aDh7ZgsKOMWn": {"name": "Neutral/Neutral", "type": "Color", "uuid": "PR_u5pfSsnPK", "value": "#374151", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "uGRzoJ0DjE-k": {"name": "Destructive/Destructive", "type": "Color", "uuid": "vuyMrl2Fq064", "value": "#DC2626", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "DrHgCmDfacnk": {"name": "Muted/Muted", "type": "Color", "uuid": "WW-basFyl8NM", "value": "#6B7280", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "8exTHu5_9byp": {"name": "Size-4", "type": "Spacing", "uuid": "8-B0SG6-YfWh", "value": "0.25rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "xkZNmdV2xu1S": {"name": "Font-SM", "type": "FontSize", "uuid": "qI73uEjMK53F", "value": "0.75rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "RzYS9hlPbKQE": {"name": "Line-height-SM", "type": "LineHeight", "uuid": "MGsqLMc-5efH", "value": "1", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "rrpDuHbDbM6h": {"name": "Font-LG", "type": "FontSize", "uuid": "UxWoF0yly3rM", "value": "1.25rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "QBxv_odKPcRh": {"name": "Line-height-LG", "type": "LineHeight", "uuid": "DKtrJNf5L9-Y", "value": "1.4", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "MfWa18eFhiss": {"name": "Size-2", "type": "Spacing", "uuid": "cEZS1szVbBNo", "value": "0.125rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "fKEeAR8T7A1P": {"name": "Success/Success", "type": "Color", "uuid": "IBqViL16nZIr", "value": "#16A34A", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "Rer1eK4EKqkN": {"name": "Warning/Warning", "type": "Color", "uuid": "KIZ9-uPnarz6", "value": "#FACC15", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "TrjizBGpJVhg": {"name": "Brand/Brand-Soft", "type": "Color", "uuid": "vLT-CeUJw1LQ", "value": "#DBEAFE", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "gxVtRO4zFo2H": {"name": "Neutral/Neutral-Soft", "type": "Color", "uuid": "5s8Y-tXe9JFF", "value": "#E5E7EB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "6ri3IDucqVdp": {"name": "Muted/Muted-Soft", "type": "Color", "uuid": "CLjsut_QJrBa", "value": "#F3F4F6", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "MT-k5vK8Luer": {"name": "Success/Success-Soft", "type": "Color", "uuid": "E4zwwwJP2aFv", "value": "#DCFCE7", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "EekmlxHzfCbo": {"name": "Warning/Warning-Soft", "type": "Color", "uuid": "t4lZutBXM_eq", "value": "#FEF9C3", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "jL6viAXqOYGL": {"name": "Destructive/Destructive-Soft", "type": "Color", "uuid": "OxfM2Trf55an", "value": "#FEE2E2", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "f8wuH2CnTk0M": {"name": "Brand/Brand-Border", "type": "Color", "uuid": "-lRaQDG94JBf", "value": "#93C5FD", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "HCkjWKNAQj_X": {"name": "Neutral/Neutral-Border", "type": "Color", "uuid": "GqGauJCNQxT3", "value": "#D1D5DB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "zN6SUmmCVppI": {"name": "Muted/Muted-Border", "type": "Color", "uuid": "4zRLFrGuSQcP", "value": "#E5E7EB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "krzlF8lOrPVS": {"name": "Success/Success-Border", "type": "Color", "uuid": "Au8xLL5EM8AO", "value": "#86EFAC", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "2fzI-f4TeNN9": {"name": "Warning/Warning-Border", "type": "Color", "uuid": "XlfDEa7y8nop", "value": "#FDE047", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "yCGZGZaHoxu6": {"name": "Destructive/Destructive-Border", "type": "Color", "uuid": "h3U2Ry_FGYRC", "value": "#FCA5A5", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "mrYrwdHurizl": {"name": "Interaction / Hovered", "type": "Color", "uuid": "H_PWjrNajpMr", "value": "#0000001A", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "YCC0Nv6LCLV_": {"name": "Interaction / Disabled", "type": "Color", "uuid": "OdlvbQWMoWtu", "value": "#FFFFFF40", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "srmaT5qip2-n": {"name": "Interaction / Pressed", "type": "Color", "uuid": "63RjuJhgmrY9", "value": "#00000033", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "OSMeZrxp-itB": {"name": "Size-10", "type": "Spacing", "uuid": "Dn6n9dDOmbMt", "value": "0.625rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "lEcgD9pzZg38": {"name": "Size-12", "type": "Spacing", "uuid": "wqKGo5gkJ9Vn", "value": "0.75rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "IhIbt-Jp8uv4": {"name": "Brand/Brand-Foreground", "type": "Color", "uuid": "l5fwELN4LIgH", "value": "#EFF6FF", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "wZl1gnEdUIQ3": {"name": "Neutral/Neutral-Foreground", "type": "Color", "uuid": "-HrIa7J0fwfD", "value": "#F9FAFB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "JY1uFyqr7UoU": {"name": "Muted/Muted-Foreground", "type": "Color", "uuid": "F5OxCtMguADX", "value": "#E5E7EB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "Q9SPHS89BUid": {"name": "Brand/Brand-Soft-Foreground", "type": "Color", "uuid": "gHfbMzOjXgM8", "value": "#1E3A8A", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "Pi5znVbGR3cH": {"name": "Warning/Warning-Foreground", "type": "Color", "uuid": "rcFuqPnzqup4", "value": "#422006", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "_imGEK8lgRha": {"name": "Success/Success-Foreground", "type": "Color", "uuid": "fcSvMLNB4umE", "value": "#F0FDF4", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "dl_A1UxnuhM5": {"name": "Destructive/Destructive-Foreground", "type": "Color", "uuid": "XTAB895BmZLJ", "value": "#FEF2F2", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "w8jH2QHrqZ3V": {"name": "Neutral/Neutral-Soft-Foreground", "type": "Color", "uuid": "1wy4Ro6xnB1k", "value": "#111827", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "I2VKY7bWXl_W": {"name": "Muted/Muted-Soft-Foreground", "type": "Color", "uuid": "_Ai82dEJDiLQ", "value": "#6B7280", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "RTR6UmP_8F-w": {"name": "Success/Success-Soft-Foreground", "type": "Color", "uuid": "2x2eClthyXdG", "value": "#14532D", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "djsCUuUJawbO": {"name": "Warning/Warning-Soft-Foreground", "type": "Color", "uuid": "jbIN2KZX1xfm", "value": "#713F12", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "D0hQ6fYSkj06": {"name": "Destructive/Destructive-Soft-Foreground", "type": "Color", "uuid": "sRp7DgXuaCxj", "value": "#7F1D1D", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "afminywZ0mim": {"uuid": "lqXzT9DxZ7WS", "name": "circle", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iPgogIDxwYXRoIGQ9Ik0zIDEyYTkgOSAwIDEwMTguMDAxIDBBOSA5IDAgMDAzIDEyeiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg==", "width": 24, "height": 24, "aspectRatio": 1000000, "__type": "ImageAsset"}, "sWGdqU6z6ble": {"uuid": "-5yws2oWCzIx", "name": "chevron-down", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iPgogIDxwYXRoIGQ9Ik02IDlsNiA2IDYtNiIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg==", "width": 24, "height": 24, "aspectRatio": 1000000, "__type": "ImageAsset"}, "VkWeLUdUDBRP": {"name": "Size-32", "type": "Spacing", "uuid": "_a6OHIFPN3ym", "value": "2rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "ODriI8TMLED-": {"name": "Basic/Text-Secondary", "type": "Color", "uuid": "zjSxWULQDY9p", "value": "#6B7280", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "D0jnWynKYynC": {"name": "Basic/Text-Primary", "type": "Color", "uuid": "ByZ43wQz0fQJ", "value": "#030712", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "-6CD5OU_zJyl": {"name": "Basic/Border", "type": "Color", "uuid": "oHvVvYqJBByh", "value": "#D1D5DB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "uKH5a_nGh1rX": {"name": "Muted/Muted-Soft-Hover", "type": "Color", "uuid": "EukpEmanbO2T", "value": "#E5E7EB", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "uui2fodtyWml": {"name": "Basic/Container-Background", "type": "Color", "uuid": "0rM4f7UMA9Pn", "value": "#FFFFFF", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "A2tGWYWszwlO": {"name": "Size-16", "type": "Spacing", "uuid": "yEUdKyisgsIB", "value": "1rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "1KMBQ1LsqN7U": {"name": "Basic/Overlay-Background", "type": "Color", "uuid": "2rHwzuYyfar3", "value": "#0A0A0A80", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "KkCax4xxEyA8": {"name": "Size-24", "type": "Spacing", "uuid": "gw2VHefaVFnT", "value": "1.5rem", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "GybPk_6OSeDa": {"type": "global-user-defined", "param": {"__ref": "luXh2KwgAGhF"}, "uuid": "wZu5ZD-qQAt1", "variants": [{"__ref": "BGZUGQLPsNxJ"}], "multi": false, "__type": "GlobalVariantGroup"}, "luXh2KwgAGhF": {"type": {"__ref": "IIHB-IOrIsF9"}, "variable": {"__ref": "BXMOAcMXCKyF"}, "uuid": "7LI09LwH5WRy", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "IIHB-IOrIsF9": {"name": "text", "__type": "Text"}, "BXMOAcMXCKyF": {"name": "DarkMode", "uuid": "gTuK_JZ216DX", "__type": "Var"}, "BGZUGQLPsNxJ": {"uuid": "WaWVipSzUvlr", "name": "Yes", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "GybPk_6OSeDa"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ljv8yOuzh9MH": {"uuid": "DNzv9Zf0z1j4", "name": "Heading", "params": [{"__ref": "3iR9kwSn4QE8"}, {"__ref": "n8aoVcgBQJKE"}], "states": [{"__ref": "n857L-NQl-Rs"}], "tplTree": {"__ref": "pqfHgcaatOVB"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "rr8gdyMjYgKb"}, {"__ref": "E4HNuKFDF9JK"}], "variantGroups": [{"__ref": "YGNH-D2Rmspi"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "wowqTKaldaLN": {"component": {"__ref": "ljv8yOuzh9MH"}, "matrix": {"__ref": "6VwM9HCFu453"}, "customMatrix": {"__ref": "vlzjKgzDxuu2"}, "__type": "ComponentArena"}, "pqfHgcaatOVB": {"tag": "div", "name": null, "children": [{"__ref": "KoKYx1FIADps"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "6XRpYHnmTGAB", "parent": null, "locked": null, "vsettings": [{"__ref": "GBl1A7o3pjle"}, {"__ref": "W4TLO5HzhQ3x"}], "__type": "TplTag"}, "rr8gdyMjYgKb": {"uuid": "-PgcFSmDU8BO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "6VwM9HCFu453": {"rows": [{"__ref": "eh32cz687iJi"}, {"__ref": "CcmNYzn3iUyQ"}], "__type": "ArenaFrameGrid"}, "vlzjKgzDxuu2": {"rows": [{"__ref": "SbkkpH-eDW9A"}], "__type": "ArenaFrameGrid"}, "GBl1A7o3pjle": {"variants": [{"__ref": "rr8gdyMjYgKb"}], "args": [], "attrs": {}, "rs": {"__ref": "g21t9ge9B6VG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eh32cz687iJi": {"cols": [{"__ref": "l1Y6HQETvzJm"}], "rowKey": null, "__type": "ArenaFrameRow"}, "SbkkpH-eDW9A": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "g21t9ge9B6VG": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "l1Y6HQETvzJm": {"frame": {"__ref": "VRL_u-TGm8ck"}, "cellKey": {"__ref": "rr8gdyMjYgKb"}, "__type": "ArenaFrameCell"}, "VRL_u-TGm8ck": {"uuid": "22MKMGebpqAM", "width": 1180, "height": 540, "container": {"__ref": "4CjMBFTUNL-J"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "4CjMBFTUNL-J": {"name": null, "component": {"__ref": "ljv8yOuzh9MH"}, "uuid": "JRUN1wuf4Azm", "parent": null, "locked": null, "vsettings": [{"__ref": "FfcrVymoxbgO"}], "__type": "TplComponent"}, "FfcrVymoxbgO": {"variants": [{"__ref": "qrEu-1YowNA0"}], "args": [], "attrs": {}, "rs": {"__ref": "x3jWSK0cjS_-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x3jWSK0cjS_-": {"values": {}, "mixins": [], "__type": "RuleSet"}, "n857L-NQl-Rs": {"variantGroup": {"__ref": "YGNH-D2Rmspi"}, "param": {"__ref": "3iR9kwSn4QE8"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "n8aoVcgBQJKE"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "YGNH-D2Rmspi": {"type": "component", "param": {"__ref": "3iR9kwSn4QE8"}, "linkedState": {"__ref": "n857L-NQl-Rs"}, "uuid": "zicLItwyxpaF", "variants": [{"__ref": "CCbD03_3zQqc"}], "multi": false, "__type": "ComponentVariantGroup"}, "3iR9kwSn4QE8": {"type": {"__ref": "1FK8bObxxS8g"}, "state": {"__ref": "n857L-NQl-Rs"}, "variable": {"__ref": "VAzcTPQwKaOk"}, "uuid": "1eG8ty_JlAmi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "n8aoVcgBQJKE": {"type": {"__ref": "jglqGNKV8itB"}, "state": {"__ref": "n857L-NQl-Rs"}, "variable": {"__ref": "i78dVI5P8BjX"}, "uuid": "KYpvRtUnhq8d", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "CcmNYzn3iUyQ": {"cols": [{"__ref": "yzZF7_AEhq_r"}], "rowKey": {"__ref": "YGNH-D2Rmspi"}, "__type": "ArenaFrameRow"}, "W4TLO5HzhQ3x": {"variants": [{"__ref": "CCbD03_3zQqc"}], "args": [], "attrs": {}, "rs": {"__ref": "LROSwVgUbGjz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CCbD03_3zQqc": {"uuid": "wuGFChHeDPj5", "name": "IsActive", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "YGNH-D2Rmspi"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "1FK8bObxxS8g": {"name": "text", "__type": "Text"}, "VAzcTPQwKaOk": {"name": "IsActive", "uuid": "uqX-TKCMdOvl", "__type": "Var"}, "jglqGNKV8itB": {"name": "func", "params": [{"__ref": "r958x32hyBx9"}], "__type": "FunctionType"}, "i78dVI5P8BjX": {"name": "On IsActive change", "uuid": "3Lry_TZgVZ91", "__type": "Var"}, "yzZF7_AEhq_r": {"frame": {"__ref": "isH1U0BkQvXd"}, "cellKey": {"__ref": "CCbD03_3zQqc"}, "__type": "ArenaFrameCell"}, "LROSwVgUbGjz": {"values": {}, "mixins": [], "__type": "RuleSet"}, "r958x32hyBx9": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "_YZFg3XsmFDb"}, "__type": "ArgType"}, "isH1U0BkQvXd": {"uuid": "Ni328IUw5IVY", "width": 1180, "height": 540, "container": {"__ref": "fA6Z3Ts3WY-z"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "CCbD03_3zQqc"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "_YZFg3XsmFDb": {"name": "any", "__type": "AnyType"}, "fA6Z3Ts3WY-z": {"name": null, "component": {"__ref": "ljv8yOuzh9MH"}, "uuid": "QPd5CAhu235H", "parent": null, "locked": null, "vsettings": [{"__ref": "366NQqxeca8F"}], "__type": "TplComponent"}, "366NQqxeca8F": {"variants": [{"__ref": "qrEu-1YowNA0"}], "args": [], "attrs": {}, "rs": {"__ref": "JVALyJqM_9Wo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JVALyJqM_9Wo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "d2wqVvCWaOXS": {"name": "Color-Mixin", "rs": {"__ref": "-r8giYPBr4Sk"}, "preview": null, "uuid": "KLTEcLa7tRaL", "forTheme": false, "variantedRs": [], "__type": "Mixin"}, "-r8giYPBr4Sk": {"values": {"color": "var(--token-vuyMrl2Fq064)"}, "mixins": [], "__type": "RuleSet"}, "S85L2_4O1kMd": {"name": "VS-Mixin-Index-3", "rs": {"__ref": "3LXbPTxAlDbZ"}, "preview": null, "uuid": "xwcW0lf8Nh2i", "forTheme": false, "variantedRs": [], "__type": "Mixin"}, "3LXbPTxAlDbZ": {"values": {"border-top-style": "solid", "border-top-width": "1px", "border-right-style": "solid", "border-right-width": "1px", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-style": "solid", "border-left-width": "1px", "color": "#333999", "text-align": "left", "font-style": "italic"}, "mixins": [], "__type": "RuleSet"}, "UCq6MQsYiz0f": {"name": "VS-Mixin-Index-5", "rs": {"__ref": "SzY4PbFa3N8_"}, "preview": null, "uuid": "0hAGHbdZMSLw", "forTheme": false, "variantedRs": [], "__type": "Mixin"}, "SzY4PbFa3N8_": {"values": {"color": "#123123", "text-decoration-line": "line-through"}, "mixins": [], "__type": "RuleSet"}, "E4HNuKFDF9JK": {"uuid": "u84c2ppAZp5k", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "KoKYx1FIADps"}, "__type": "<PERSON><PERSON><PERSON>"}, "KoKYx1FIADps": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "9BvRPvFcj5Sf", "parent": {"__ref": "pqfHgcaatOVB"}, "locked": null, "vsettings": [{"__ref": "dUIMBdq00edy"}, {"__ref": "etpYtVligjAK"}, {"__ref": "Xong5NCMo-Zz"}, {"__ref": "DQTINKSbgqHx"}], "__type": "TplTag"}, "dUIMBdq00edy": {"variants": [{"__ref": "rr8gdyMjYgKb"}], "args": [], "attrs": {}, "rs": {"__ref": "TR5ia_FZ8HU8"}, "dataCond": null, "dataRep": null, "text": {"__ref": "6OavNIycYSpE"}, "columnsConfig": null, "__type": "VariantSetting"}, "Xong5NCMo-Zz": {"variants": [{"__ref": "CCbD03_3zQqc"}], "args": [], "attrs": {}, "rs": {"__ref": "s4aMTarF8IuP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TR5ia_FZ8HU8": {"values": {"width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "6OavNIycYSpE": {"markers": [], "text": "You won't believe what happens next.", "__type": "RawText"}, "s4aMTarF8IuP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "etpYtVligjAK": {"variants": [{"__ref": "E4HNuKFDF9JK"}], "args": [], "attrs": {}, "rs": {"__ref": "H3OMxChQu6FP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "H3OMxChQu6FP": {"values": {"color": "#888888"}, "mixins": [], "__type": "RuleSet"}, "zFT-3XBPlBPr": {"name": "VS-Mixin-Index-4", "rs": {"__ref": "qTr4WOcekknS"}, "preview": null, "uuid": "6pxt3TirAb2e", "forTheme": false, "variantedRs": [], "__type": "Mixin"}, "qTr4WOcekknS": {"values": {"color": "#666999", "text-align": "center", "font-style": "normal", "border-top-style": "solid", "border-top-width": "2px", "border-right-style": "solid", "border-right-width": "2px", "border-bottom-style": "solid", "border-bottom-width": "2px", "border-left-style": "solid", "border-left-width": "2px", "border-top-color": "#F57979", "border-right-color": "#F57979", "border-bottom-color": "#F57979", "border-left-color": "#F57979"}, "mixins": [], "__type": "RuleSet"}, "DQTINKSbgqHx": {"variants": [{"__ref": "CCbD03_3zQqc"}, {"__ref": "E4HNuKFDF9JK"}], "args": [], "attrs": {}, "rs": {"__ref": "XRcRuCqCsTV-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XRcRuCqCsTV-": {"values": {"text-decoration-line": "underline"}, "mixins": [{"__ref": "S85L2_4O1kMd"}, {"__ref": "zFT-3XBPlBPr"}, {"__ref": "UCq6MQsYiz0f"}], "__type": "RuleSet"}, "xDyPhZ18fpFl": {"uuid": "hTy3amag-ZaM", "name": "Heading 2", "params": [{"__ref": "v9SdurDmK2e5"}, {"__ref": "BzapbfCzHzVi"}], "states": [{"__ref": "0yK0m3eVRRn3"}], "tplTree": {"__ref": "oSTEyCBKD6sQ"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "sydUt2qLN2gr"}, {"__ref": "4eNThLImLB0D"}], "variantGroups": [{"__ref": "zyotM2vnJgmI"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "updatedAt": null, "__type": "Component"}, "mTrOuIrt9Iwv": {"component": {"__ref": "xDyPhZ18fpFl"}, "matrix": {"__ref": "5w1_DcApXmkN"}, "customMatrix": {"__ref": "Xph9moXp6zzf"}, "__type": "ComponentArena"}, "v9SdurDmK2e5": {"type": {"__ref": "_X--9US3Hvsa"}, "state": {"__ref": "0yK0m3eVRRn3"}, "variable": {"__ref": "y5cf26E4o5GQ"}, "uuid": "9TP9rsGpqgIf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "BzapbfCzHzVi": {"type": {"__ref": "t_2e6dQJf_Y0"}, "state": {"__ref": "0yK0m3eVRRn3"}, "variable": {"__ref": "_36WGgbHd_FJ"}, "uuid": "uaPqeCViNh7E", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "0yK0m3eVRRn3": {"variantGroup": {"__ref": "zyotM2vnJgmI"}, "param": {"__ref": "v9SdurDmK2e5"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "BzapbfCzHzVi"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "oSTEyCBKD6sQ": {"tag": "div", "name": null, "children": [{"__ref": "d1Mk91ijWXfs"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Eh0gy6x2G_gL", "parent": null, "locked": null, "vsettings": [{"__ref": "2tggW-J3gIOm"}, {"__ref": "KXL1Ly6aKC-t"}], "__type": "TplTag"}, "sydUt2qLN2gr": {"uuid": "GSh1BERutgee", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4eNThLImLB0D": {"uuid": "1FyjGjrAOs-Q", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "d1Mk91ijWXfs"}, "__type": "<PERSON><PERSON><PERSON>"}, "zyotM2vnJgmI": {"type": "component", "param": {"__ref": "v9SdurDmK2e5"}, "linkedState": {"__ref": "0yK0m3eVRRn3"}, "uuid": "8sd97l7xddqx", "variants": [{"__ref": "UXfe95Wf8ATm"}], "multi": false, "__type": "ComponentVariantGroup"}, "5w1_DcApXmkN": {"rows": [{"__ref": "zrBBKFbT94BE"}, {"__ref": "NrfMUHbPU-WZ"}], "__type": "ArenaFrameGrid"}, "Xph9moXp6zzf": {"rows": [{"__ref": "XAei9ANGR2LX"}], "__type": "ArenaFrameGrid"}, "_X--9US3Hvsa": {"name": "text", "__type": "Text"}, "y5cf26E4o5GQ": {"name": "IsActive", "uuid": "nIkRPR9xMVmo", "__type": "Var"}, "t_2e6dQJf_Y0": {"name": "func", "params": [{"__ref": "0p5Cl55Hj_RO"}], "__type": "FunctionType"}, "_36WGgbHd_FJ": {"name": "On IsActive change", "uuid": "XwmWQuNkGXzZ", "__type": "Var"}, "d1Mk91ijWXfs": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "qxIyKHyOoOqI", "parent": {"__ref": "oSTEyCBKD6sQ"}, "locked": null, "vsettings": [{"__ref": "P_Ou1FXghguJ"}, {"__ref": "EZk02L4iKsZP"}, {"__ref": "yP0Lvu9J7dW4"}, {"__ref": "M6o3RMQ1zNAm"}], "__type": "TplTag"}, "2tggW-J3gIOm": {"variants": [{"__ref": "sydUt2qLN2gr"}], "args": [], "attrs": {}, "rs": {"__ref": "Qvc9ff6bBbBX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KXL1Ly6aKC-t": {"variants": [{"__ref": "UXfe95Wf8ATm"}], "args": [], "attrs": {}, "rs": {"__ref": "49ozhuOesDxH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UXfe95Wf8ATm": {"uuid": "AmLnAK6PoJ2C", "name": "IsActive", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "zyotM2vnJgmI"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zrBBKFbT94BE": {"cols": [{"__ref": "bcUHy1OPaZUF"}], "rowKey": null, "__type": "ArenaFrameRow"}, "NrfMUHbPU-WZ": {"cols": [{"__ref": "VUfdQ-_PeCoO"}], "rowKey": {"__ref": "zyotM2vnJgmI"}, "__type": "ArenaFrameRow"}, "XAei9ANGR2LX": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "0p5Cl55Hj_RO": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "-ep2hd7k_xRP"}, "__type": "ArgType"}, "P_Ou1FXghguJ": {"variants": [{"__ref": "sydUt2qLN2gr"}], "args": [], "attrs": {}, "rs": {"__ref": "XFbJ2QDXpTZ1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "DfbMvnDmCNew"}, "columnsConfig": null, "__type": "VariantSetting"}, "yP0Lvu9J7dW4": {"variants": [{"__ref": "UXfe95Wf8ATm"}], "args": [], "attrs": {}, "rs": {"__ref": "rePXLoNwLhVo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EZk02L4iKsZP": {"variants": [{"__ref": "4eNThLImLB0D"}], "args": [], "attrs": {}, "rs": {"__ref": "ckBz0GuGUGWf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "M6o3RMQ1zNAm": {"variants": [{"__ref": "UXfe95Wf8ATm"}, {"__ref": "4eNThLImLB0D"}], "args": [], "attrs": {}, "rs": {"__ref": "FsrxQDbD4i6P"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Qvc9ff6bBbBX": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "49ozhuOesDxH": {"values": {}, "mixins": [], "__type": "RuleSet"}, "bcUHy1OPaZUF": {"frame": {"__ref": "HbiUmckqXN89"}, "cellKey": {"__ref": "sydUt2qLN2gr"}, "__type": "ArenaFrameCell"}, "VUfdQ-_PeCoO": {"frame": {"__ref": "afY359qMQh0P"}, "cellKey": {"__ref": "UXfe95Wf8ATm"}, "__type": "ArenaFrameCell"}, "-ep2hd7k_xRP": {"name": "any", "__type": "AnyType"}, "XFbJ2QDXpTZ1": {"values": {"width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "DfbMvnDmCNew": {"markers": [], "text": "You won't believe what happens next.", "__type": "RawText"}, "rePXLoNwLhVo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ckBz0GuGUGWf": {"values": {"color": "#888888"}, "mixins": [], "__type": "RuleSet"}, "FsrxQDbD4i6P": {"values": {"text-decoration-line": "underline"}, "mixins": [{"__ref": "S85L2_4O1kMd"}, {"__ref": "zFT-3XBPlBPr"}, {"__ref": "UCq6MQsYiz0f"}], "__type": "RuleSet"}, "HbiUmckqXN89": {"uuid": "CSmx2P4U0dHU", "width": 1180, "height": 540, "container": {"__ref": "oHYeRGnBOxem"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "sydUt2qLN2gr"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "afY359qMQh0P": {"uuid": "M_i_uFQZSSiN", "width": 1180, "height": 540, "container": {"__ref": "VI9dM5rTaX0R"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "UXfe95Wf8ATm"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "oHYeRGnBOxem": {"name": null, "component": {"__ref": "xDyPhZ18fpFl"}, "uuid": "sW914w1maazl", "parent": null, "locked": null, "vsettings": [{"__ref": "Duc1atqZxeLU"}], "__type": "TplComponent"}, "VI9dM5rTaX0R": {"name": null, "component": {"__ref": "xDyPhZ18fpFl"}, "uuid": "7PfMtUWM6lzV", "parent": null, "locked": null, "vsettings": [{"__ref": "de46UkMEB_sR"}], "__type": "TplComponent"}, "Duc1atqZxeLU": {"variants": [{"__ref": "qrEu-1YowNA0"}], "args": [], "attrs": {}, "rs": {"__ref": "H5L8lpY-m3v_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "de46UkMEB_sR": {"variants": [{"__ref": "qrEu-1YowNA0"}], "args": [], "attrs": {}, "rs": {"__ref": "uhQFT6I68Y8J"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "H5L8lpY-m3v_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uhQFT6I68Y8J": {"values": {}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "246-add-component-updated-at"}