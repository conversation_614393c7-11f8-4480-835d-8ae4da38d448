{"name": "analytics-rproxy", "packageManager": "pnpm@10.8.0", "scripts": {"dev": "pnpm dev:app --watch", "dev:app": "tsx src/entry-app.ts", "dev:prepare-salt": "tsx src/entry-prepare-salt.ts", "build": "esbuild src/entry-*.ts --bundle --target=es2022 --platform=node --format=cjs --outdir=dist --sourcemap", "test": "vitest", "typecheck": "tsc", "docker:build": "docker build -f Dockerfile .. -t analytics-rproxy", "docker:dev": "docker run -p 8787:8787 -e NATS_SERVERS='nats://host.docker.internal:4222' analytics-rproxy", "docker:dev:nats": "docker run -p 4222:4222 -v nats:/data nats -js -sd /data", "ci": "pnpm typecheck && pnpm test && pnpm build"}, "files": ["dist"], "dependencies": {"@godaddy/terminus": "4.12.1", "@hono/node-server": "^1.14.0", "fflate": "^0.8.2", "hono": "^4.7.6", "nats": "^2.29.3"}, "devDependencies": {"esbuild": "^0.25", "msw": "^2.7.3", "vitest": "^3.1.1", "tsx": "^4"}}