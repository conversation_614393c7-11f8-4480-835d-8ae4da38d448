import { createTerminus, HealthCheckError } from "@godaddy/terminus";
import { serve } from "@hono/node-server";
import http from "http";
import { createApp } from "./app";
import { createNatsSaltKV } from "./nats-salt-kv";

async function main() {
  const saltKV = await createNatsSaltKV();
  const app = await createApp({ saltKV });
  const server = serve(
    {
      fetch: app.fetch,
      port: 8787,
      createServer: http.createServer,
    },
    (addressInfo) => {
      console.log(`Server started on port ${addressInfo.port}`);
    }
  );

  // https://github.com/godaddy/terminus
  createTerminus(server, {
    healthChecks: {
      "/healthcheck": async ({ state }) => {
        if (state.isShuttingDown) {
          throw new HealthCheckError("Shutting down...", state);
        }
      },
    },
    signals: ["SIGTERM", "SIGINT"],
    beforeShutdown: () => {
      // Delay before shutdown should be slightly greater than `readinessProbe.periodSeconds` in
      // devops/deployments/clusters/platform-dev-us-central1/platform/analytics-rproxy/deploy.yaml
      const delayMs = 5_500;
      console.log(`Shutdown signal received. Waiting ${delayMs}ms...`);
      return new Promise((resolve) => setTimeout(resolve, delayMs));
    },
    onSignal: async () => {
      console.log(`Shutdown starting...`);
      await saltKV[Symbol.asyncDispose]();
    },
    onShutdown: async () => {
      console.log(`Shutdown complete, exiting.`);
    },
    useExit0: true,
  });
}

main().catch(console.error);
