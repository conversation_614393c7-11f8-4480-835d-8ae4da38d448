[["1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c", {"root": "2iS4Rdoh8uGr", "map": {"AqNfLbh5Lopf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "0BFg0226vaPY": {"name": "Default Typography", "rs": {"__ref": "AqNfLbh5Lopf"}, "preview": null, "uuid": "Vxueayilb9Ay", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "TiQruNdPzl1J": {"values": {}, "mixins": [], "__type": "RuleSet"}, "x87FBgFW3UWB": {"rs": {"__ref": "TiQruNdPzl1J"}, "__type": "ThemeLayoutSettings"}, "Rr61ia4MoPvg": {"defaultStyle": {"__ref": "0BFg0226vaPY"}, "styles": [], "layout": {"__ref": "x87FBgFW3UWB"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "XZ79xSE8-FuQ": {"uuid": "CiVNvrTucRHu", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "wdCZyqbt9XI7": {"components": [{"__ref": "RFFYx9oYuGrh"}], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "XZ79xSE8-<PERSON><PERSON>"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "Rr61ia4MoPvg"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "w-gEkHNsVIEo"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "RFFYx9oYuGrh": {"uuid": "RhBtuRUrLEgn", "name": "plasmic-link-preview", "params": [{"__ref": "AH4eAKS-ti91"}, {"__ref": "J-ZH7f9Hc-Co"}, {"__ref": "qelaNMPZIIFw"}, {"__ref": "lAJ_463nTQLj"}, {"__ref": "Oo0ABG9M3CUR"}, {"__ref": "eAO6tiLDYn0o"}], "states": [], "tplTree": {"__ref": "t1Gng-7wWyzz"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "XGkb1n_-_5Q4"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "KZok_Nwucun7"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "AH4eAKS-ti91": {"type": {"__ref": "uTs6_QB9_j9o"}, "variable": {"__ref": "UrPUUnofTnx4"}, "uuid": "RxvP1B4AzHQP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "1qA3qwmQ4tR8"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "URL", "about": "The URL for which you want to generate the link preview.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "t1Gng-7wWyzz": {"tag": "div", "name": null, "children": [{"__ref": "iNqQzfWMpcXM"}, {"__ref": "xucWIA05f9yW"}, {"__ref": "XRaBvKDu6z-y"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_EpFkblZ2A_3", "parent": null, "locked": null, "vsettings": [{"__ref": "VhziqCHyCOGP"}], "__type": "TplTag"}, "XGkb1n_-_5Q4": {"uuid": "moQcutr5km6u", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "KZok_Nwucun7": {"importPath": "@plasmicpkgs/plasmic-link-preview", "defaultExport": false, "displayName": "Link Preview", "importName": "LinkPreview", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": true, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {"noPreviewMessage": [{"type": "text", "value": "no preview..."}], "loadingMessage": [{"type": "text", "value": "loading preview..."}]}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "uTs6_QB9_j9o": {"name": "text", "__type": "Text"}, "UrPUUnofTnx4": {"name": "url", "uuid": "r7oBjug9_JHC", "__type": "Var"}, "1qA3qwmQ4tR8": {"code": "\"https://plasmic.app\"", "fallback": null, "__type": "CustomCode"}, "VhziqCHyCOGP": {"variants": [{"__ref": "XGkb1n_-_5Q4"}], "args": [], "attrs": {}, "rs": {"__ref": "AEWXXtVYoqCn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AEWXXtVYoqCn": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "J-ZH7f9Hc-Co": {"type": {"__ref": "mkPuLBmkSzA_"}, "tplSlot": {"__ref": "iNqQzfWMpcXM"}, "variable": {"__ref": "IoyrDjd1uy6G"}, "uuid": "inzL-o-71jO2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "iNqQzfWMpcXM": {"param": {"__ref": "J-ZH7f9Hc-Co"}, "defaultContents": [], "uuid": "_JOPFoObWYiA", "parent": {"__ref": "t1Gng-7wWyzz"}, "locked": null, "vsettings": [{"__ref": "zgQNXEMSaHqb"}], "__type": "TplSlot"}, "mkPuLBmkSzA_": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "IoyrDjd1uy6G": {"name": "children", "uuid": "8Ai7wX8ZiWnt", "__type": "Var"}, "zgQNXEMSaHqb": {"variants": [{"__ref": "XGkb1n_-_5Q4"}], "args": [], "attrs": {}, "rs": {"__ref": "yctfSAHefPi6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yctfSAHefPi6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "qelaNMPZIIFw": {"type": {"__ref": "x7AWeHNw-OCC"}, "tplSlot": {"__ref": "xucWIA05f9yW"}, "variable": {"__ref": "uUjhhG3LpsFs"}, "uuid": "QUfWydtDRzHC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "'No Preview' Message", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "lAJ_463nTQLj": {"type": {"__ref": "HrQb6hOY8NjT"}, "variable": {"__ref": "uTOqJi-ldwnk"}, "uuid": "BAXmUB6ixb46", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "You can enable this prop to show the loading message, so you can easily customize it. This prop has no effect in the live app.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "Oo0ABG9M3CUR": {"type": {"__ref": "CLE3swnJC8p3"}, "variable": {"__ref": "AgIrEJrBHzhA"}, "uuid": "bD5-aOi5KR8r", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "You can enable this prop to show the 'No Preview' message, so you can easily customize it. This prop has no effect in the live app.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "eAO6tiLDYn0o": {"type": {"__ref": "_sSYAK9fPfkg"}, "tplSlot": {"__ref": "XRaBvKDu6z-y"}, "variable": {"__ref": "jTj4pohZ0NjM"}, "uuid": "EMc7wcEoGRzL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Loading Message", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "xucWIA05f9yW": {"param": {"__ref": "qelaNMPZIIFw"}, "defaultContents": [], "uuid": "vyA2GGyORSTD", "parent": {"__ref": "t1Gng-7wWyzz"}, "locked": null, "vsettings": [{"__ref": "JDDCVvnFpa3E"}], "__type": "TplSlot"}, "XRaBvKDu6z-y": {"param": {"__ref": "eAO6tiLDYn0o"}, "defaultContents": [], "uuid": "K_wMSQU7LVlA", "parent": {"__ref": "t1Gng-7wWyzz"}, "locked": null, "vsettings": [{"__ref": "_QSKYO0g-Tia"}], "__type": "TplSlot"}, "uUjhhG3LpsFs": {"name": "noPreviewMessage", "uuid": "vhveH2UhKwSt", "__type": "Var"}, "HrQb6hOY8NjT": {"name": "bool", "__type": "BoolType"}, "uTOqJi-ldwnk": {"name": "showLoading", "uuid": "0cFvieFL5Sfm", "__type": "Var"}, "CLE3swnJC8p3": {"name": "bool", "__type": "BoolType"}, "AgIrEJrBHzhA": {"name": "showNoPreview", "uuid": "7-sXAQ7D6sIc", "__type": "Var"}, "jTj4pohZ0NjM": {"name": "loadingMessage", "uuid": "0HIMFXervcPw", "__type": "Var"}, "JDDCVvnFpa3E": {"variants": [{"__ref": "XGkb1n_-_5Q4"}], "args": [], "attrs": {}, "rs": {"__ref": "zpHgaiyCGC4b"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_QSKYO0g-Tia": {"variants": [{"__ref": "XGkb1n_-_5Q4"}], "args": [], "attrs": {}, "rs": {"__ref": "KMq3JThVY6MI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zpHgaiyCGC4b": {"values": {}, "mixins": [], "__type": "RuleSet"}, "KMq3JThVY6MI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "x7AWeHNw-OCC": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "_sSYAK9fPfkg": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "w-gEkHNsVIEo": {"name": "plasmic-link-preview", "npmPkg": ["@plasmicpkgs/plasmic-link-preview"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "2iS4Rdoh8uGr": {"uuid": "EUZCUbXXyh9y", "pkgId": "*************-4782-986b-a942a3a40dab", "projectId": "3qjBViX9wwT3bLhmjpBups", "version": "0.0.2", "name": "Imported Dep", "site": {"__ref": "wdCZyqbt9XI7"}, "__type": "ProjectDependency"}}, "deps": [], "version": "246-add-component-updated-at"}], ["s6PyiUNYmXyzQeAgkQWsfq", {"root": "KdZjWDkPk92r", "map": {"75FYNcBQPqxo": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "phOwZtIB1DJa": {"name": "Default Typography", "rs": {"__ref": "75FYNcBQPqxo"}, "preview": null, "uuid": "5P5ENeZjIeVk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "npD5gbZ3xF5A": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6iCxm79RCiAf": {"rs": {"__ref": "npD5gbZ3xF5A"}, "__type": "ThemeLayoutSettings"}, "-p-aGdYOYWKh": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "Fs0X9MqnLmlG": {"name": "Default \"h1\"", "rs": {"__ref": "-p-aGdYOYWKh"}, "preview": null, "uuid": "sXw0SIEgn8RM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ZERvv3-iLGPj": {"selector": "h1", "style": {"__ref": "Fs0X9MqnLmlG"}, "__type": "ThemeStyle"}, "vaInSvCyVssq": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "1Xwpata7IHcS": {"name": "Default \"h2\"", "rs": {"__ref": "vaInSvCyVssq"}, "preview": null, "uuid": "JKvnlkY0m0RE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8BZ8ZNj-962_": {"selector": "h2", "style": {"__ref": "1Xwpata7IHcS"}, "__type": "ThemeStyle"}, "Xg0Z95IHkcn0": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "TkYTT5Zqe3JF": {"name": "Default \"h3\"", "rs": {"__ref": "Xg0Z95IHkcn0"}, "preview": null, "uuid": "NwzvH3CRRhON", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "pnXfJCcMr5X5": {"selector": "h3", "style": {"__ref": "TkYTT5Zqe3JF"}, "__type": "ThemeStyle"}, "_2YCETjbFhXQ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "EiPxBjAheOz1": {"name": "Default \"h4\"", "rs": {"__ref": "_2YCETjbFhXQ"}, "preview": null, "uuid": "3vDmNtKj7xHk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nLURAR0S0Jz9": {"selector": "h4", "style": {"__ref": "EiPxBjAheOz1"}, "__type": "ThemeStyle"}, "ytTVj2wIHW6P": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "ISbNsQS8qx3d": {"name": "Default \"h5\"", "rs": {"__ref": "ytTVj2wIHW6P"}, "preview": null, "uuid": "RDbxrF99e-hz", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PsWVdvcaZ4qO": {"selector": "h5", "style": {"__ref": "ISbNsQS8qx3d"}, "__type": "ThemeStyle"}, "K0U0SQIsOSEV": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "3UBJ8D8_aLJ0": {"name": "Default \"h6\"", "rs": {"__ref": "K0U0SQIsOSEV"}, "preview": null, "uuid": "rGauVhZs4Fiv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "QeyG7Xr28cJk": {"selector": "h6", "style": {"__ref": "3UBJ8D8_aLJ0"}, "__type": "ThemeStyle"}, "2ngTWuZa-XsS": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "LgC3I-WgE_Nw": {"name": "Default \"a\"", "rs": {"__ref": "2ngTWuZa-XsS"}, "preview": null, "uuid": "mF8Hf4BUAF4d", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "1JQk_5ToC4Uu": {"selector": "a", "style": {"__ref": "LgC3I-WgE_Nw"}, "__type": "ThemeStyle"}, "hCirW5ZCK4nD": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "-xpDWS3R92jy": {"name": "Default \"a:hover\"", "rs": {"__ref": "hCirW5ZCK4nD"}, "preview": null, "uuid": "q4NhhduiLtiD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "dWt8o_fed0An": {"selector": "a:hover", "style": {"__ref": "-xpDWS3R92jy"}, "__type": "ThemeStyle"}, "ytmhsoRa1_-C": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "naQEexJkaMEu": {"name": "Default \"blockquote\"", "rs": {"__ref": "ytmhsoRa1_-C"}, "preview": null, "uuid": "386U-5MzNB8E", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ZqVHzCKfBklj": {"selector": "blockquote", "style": {"__ref": "naQEexJkaMEu"}, "__type": "ThemeStyle"}, "XTfRwyfSZEUY": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "WWmjAH187kwr": {"name": "Default \"code\"", "rs": {"__ref": "XTfRwyfSZEUY"}, "preview": null, "uuid": "PUC7o4iXzMlb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "El-Deh0ZumJX": {"selector": "code", "style": {"__ref": "WWmjAH187kwr"}, "__type": "ThemeStyle"}, "G3DE01LXSaws": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "FPWNoSkUWy__": {"name": "Default \"pre\"", "rs": {"__ref": "G3DE01LXSaws"}, "preview": null, "uuid": "r8OuAGt2Wufz", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "SaN24UCK9zIM": {"selector": "pre", "style": {"__ref": "FPWNoSkUWy__"}, "__type": "ThemeStyle"}, "2mh8M8AhxbsV": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "u-atht087g1l": {"name": "Default \"ol\"", "rs": {"__ref": "2mh8M8AhxbsV"}, "preview": null, "uuid": "pkxvt7z-CM-v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "VVzvjESg2mQj": {"selector": "ol", "style": {"__ref": "u-atht087g1l"}, "__type": "ThemeStyle"}, "kTbCZV0T-IOw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "dgUWNAdHogYI": {"name": "Default \"ul\"", "rs": {"__ref": "kTbCZV0T-IOw"}, "preview": null, "uuid": "hKzdPxVDenve", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "rjxY1qmLuCZn": {"selector": "ul", "style": {"__ref": "dgUWNAdHogYI"}, "__type": "ThemeStyle"}, "J_FXwqNhXDjf": {"defaultStyle": {"__ref": "phOwZtIB1DJa"}, "styles": [{"__ref": "ZERvv3-iLGPj"}, {"__ref": "8BZ8ZNj-962_"}, {"__ref": "pnXfJCcMr5X5"}, {"__ref": "nLURAR0S0Jz9"}, {"__ref": "PsWVdvcaZ4qO"}, {"__ref": "QeyG7Xr28cJk"}, {"__ref": "1JQk_5ToC4Uu"}, {"__ref": "dWt8o_fed0An"}, {"__ref": "ZqVHzCKfBklj"}, {"__ref": "El-Deh0ZumJX"}, {"__ref": "SaN24UCK9zIM"}, {"__ref": "VVzvjESg2mQj"}, {"__ref": "rjxY1qmLuCZn"}], "layout": {"__ref": "6iCxm79RCiAf"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "9NpRy7Zicepi": {"name": "text", "__type": "Text"}, "zvw6SpoikEUX": {"name": "Screen", "uuid": "W7HM7VlW0l6x", "__type": "Var"}, "S5Y_frRn3JgF": {"type": {"__ref": "9NpRy7Zicepi"}, "variable": {"__ref": "zvw6SpoikEUX"}, "uuid": "eUsef7W-cYmE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "V3cgtGVOXvyd": {"type": "global-screen", "param": {"__ref": "S5Y_frRn3JgF"}, "uuid": "jW-nCAfEMJ4S", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "F3SsaesVCj8C": {"uuid": "1XmkC1437ED-", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "KdZjWDkPk92r": {"components": [{"__ref": "1PqogIdcgxTP"}, {"__ref": "mX2xRV88AfNw"}, {"__ref": "FSC0WCHK0gr8"}], "arenas": [{"__ref": "odnCbQgqES9G"}], "pageArenas": [{"__ref": "JgBc3Q_Rmdog"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "V3cgtGVOXvyd"}], "userManagedFonts": [], "globalVariant": {"__ref": "F3SsaesVCj8C"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "J_FXwqNhXDjf"}], "activeTheme": {"__ref": "J_FXwqNhXDjf"}, "imageAssets": [], "projectDependencies": [{"__xref": {"uuid": "1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c", "iid": "2iS4Rdoh8uGr"}}], "activeScreenVariantGroup": {"__ref": "V3cgtGVOXvyd"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "odnCbQgqES9G": {"name": "Custom arena 1", "children": [{"__ref": "Ksh9h8s0BhvJ"}], "__type": "Arena"}, "1PqogIdcgxTP": {"uuid": "_9JYOTNN6Eqr", "name": "hostless-plasmic-head", "params": [{"__ref": "w8JelNinn9se"}, {"__ref": "K5MHzSg_D-An"}, {"__ref": "vXIUI7OYMn2D"}, {"__ref": "uvGU5O_Cdfbg"}], "states": [], "tplTree": {"__ref": "FXknqkowEYPn"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "DVKhan_xwGxM"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "nzBxEpEFMXHH"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "mX2xRV88AfNw": {"uuid": "RUhtyXVHBWMJ", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "mrioyw9MDYES"}, {"__ref": "kLKgNcWESpqj"}, {"__ref": "u3cm_CDN_6F_"}, {"__ref": "_aN6PWJICGQk"}, {"__ref": "cW1kT8Zom4Ln"}], "states": [], "tplTree": {"__ref": "Tw_i2bfJ147D"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BSAf_k0_PfjG"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "FNUi4_IrkBmV"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "w8JelNinn9se": {"type": {"__ref": "4ghiC-lruuSI"}, "variable": {"__ref": "Yhi1znCN9Igk"}, "uuid": "Wu_a835si88p", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "K5MHzSg_D-An": {"type": {"__ref": "hfPWMV-HI44F"}, "variable": {"__ref": "6VqoAM0UnyKy"}, "uuid": "eCJWdvBnL4d0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "vXIUI7OYMn2D": {"type": {"__ref": "3oZYq-f1XbVe"}, "variable": {"__ref": "5TeSaoYmPv_L"}, "uuid": "GXWOP0hAgwS8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "uvGU5O_Cdfbg": {"type": {"__ref": "efNztPwAeHG8"}, "variable": {"__ref": "OVY5M8OOA9Li"}, "uuid": "XhN8Y2qDFYgd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "FXknqkowEYPn": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "09kKnqDJRfof", "parent": null, "locked": null, "vsettings": [{"__ref": "Aosb51fhLjLq"}], "__type": "TplTag"}, "DVKhan_xwGxM": {"uuid": "upyxfJ2NFKAq", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "nzBxEpEFMXHH": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "mrioyw9MDYES": {"type": {"__ref": "52-kKkKq6NiW"}, "variable": {"__ref": "w3x_FrH2xsqZ"}, "uuid": "_KVE_Z4D3bvK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "kLKgNcWESpqj": {"type": {"__ref": "tiaznLMZqDOE"}, "variable": {"__ref": "1mV2gQjecasW"}, "uuid": "rekXO2DJVvcF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "u3cm_CDN_6F_": {"type": {"__ref": "yJD-47ehruH4"}, "tplSlot": {"__ref": "Scw0GnGScfoD"}, "variable": {"__ref": "GOs5CnOp4NA8"}, "uuid": "tk3PeDfq_F4X", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "_aN6PWJICGQk": {"type": {"__ref": "upYOeAVg2hl1"}, "variable": {"__ref": "ypYHWTHg9yXE"}, "uuid": "yrMEfVlTTgNq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "cW1kT8Zom4Ln": {"type": {"__ref": "pW1NjhjDbW_f"}, "variable": {"__ref": "9Dc3kq7hCGRw"}, "uuid": "KDYRI6ZtQ-pE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "Tw_i2bfJ147D": {"tag": "div", "name": null, "children": [{"__ref": "Scw0GnGScfoD"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ndKKocWk-TeM", "parent": null, "locked": null, "vsettings": [{"__ref": "O1EU9ajcr_J8"}], "__type": "TplTag"}, "BSAf_k0_PfjG": {"uuid": "wCZKL4tCEo0o", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "FNUi4_IrkBmV": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "4ghiC-lruuSI": {"name": "text", "__type": "Text"}, "Yhi1znCN9Igk": {"name": "title", "uuid": "eTDvOK6wyqpg", "__type": "Var"}, "hfPWMV-HI44F": {"name": "text", "__type": "Text"}, "6VqoAM0UnyKy": {"name": "description", "uuid": "UoMjY8Y31y-v", "__type": "Var"}, "3oZYq-f1XbVe": {"name": "img", "__type": "Img"}, "5TeSaoYmPv_L": {"name": "image", "uuid": "YMgAL2Yp9UTp", "__type": "Var"}, "efNztPwAeHG8": {"name": "text", "__type": "Text"}, "OVY5M8OOA9Li": {"name": "canonical", "uuid": "HNGYxvFz5AfG", "__type": "Var"}, "Aosb51fhLjLq": {"variants": [{"__ref": "DVKhan_xwGxM"}], "args": [], "attrs": {}, "rs": {"__ref": "mt38g0xu_QkV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52-kKkKq6NiW": {"name": "any", "__type": "AnyType"}, "w3x_FrH2xsqZ": {"name": "dataOp", "uuid": "OrTa7tW6gDKH", "__type": "Var"}, "tiaznLMZqDOE": {"name": "text", "__type": "Text"}, "1mV2gQjecasW": {"name": "name", "uuid": "Si65S3LqvTED", "__type": "Var"}, "yJD-47ehruH4": {"name": "renderFunc", "params": [{"__ref": "0ATjxP71o2C5"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "GOs5CnOp4NA8": {"name": "children", "uuid": "1HEKbQjw6CUG", "__type": "Var"}, "upYOeAVg2hl1": {"name": "num", "__type": "<PERSON><PERSON>"}, "ypYHWTHg9yXE": {"name": "pageSize", "uuid": "hsmVKM9NgAA1", "__type": "Var"}, "pW1NjhjDbW_f": {"name": "num", "__type": "<PERSON><PERSON>"}, "9Dc3kq7hCGRw": {"name": "pageIndex", "uuid": "vbt2rTd0gJk7", "__type": "Var"}, "Scw0GnGScfoD": {"param": {"__ref": "u3cm_CDN_6F_"}, "defaultContents": [], "uuid": "aeqc-O59K7TH", "parent": {"__ref": "Tw_i2bfJ147D"}, "locked": null, "vsettings": [{"__ref": "YUgckZwzUehb"}], "__type": "TplSlot"}, "O1EU9ajcr_J8": {"variants": [{"__ref": "BSAf_k0_PfjG"}], "args": [], "attrs": {}, "rs": {"__ref": "c-HWWJSCVFPN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mt38g0xu_QkV": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "0ATjxP71o2C5": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "ImAwhcppqW9e"}, "__type": "ArgType"}, "YUgckZwzUehb": {"variants": [{"__ref": "BSAf_k0_PfjG"}], "args": [], "attrs": {}, "rs": {"__ref": "KQTwRnyDLZeW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "c-HWWJSCVFPN": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "ImAwhcppqW9e": {"name": "any", "__type": "AnyType"}, "KQTwRnyDLZeW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FSC0WCHK0gr8": {"uuid": "4uwO1HPPYPDp", "name": "link-preview-test", "params": [], "states": [], "tplTree": {"__ref": "AxMLMVJ1Q-Mb"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "BqOjgLzC0Wtg"}], "variantGroups": [], "pageMeta": {"__ref": "AeJzGLImzbpr"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "JgBc3Q_Rmdog": {"component": {"__ref": "FSC0WCHK0gr8"}, "matrix": {"__ref": "wp6w3CaZhlSF"}, "customMatrix": {"__ref": "IN5gHtgdTal7"}, "__type": "PageArena"}, "Ksh9h8s0BhvJ": {"uuid": "Pzx62PeSjNdb", "width": 800, "height": 800, "container": {"__ref": "IckiaskRzyLy"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "BqOjgLzC0Wtg"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": 61, "left": 268, "__type": "ArenaFrame"}, "AxMLMVJ1Q-Mb": {"tag": "div", "name": null, "children": [{"__ref": "BQpKH4xHGszf"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SwrlRKKTpCc_", "parent": null, "locked": null, "vsettings": [{"__ref": "DXSdyDH_OY2_"}], "__type": "TplTag"}, "BqOjgLzC0Wtg": {"uuid": "5Eoizgtlf1cQ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "AeJzGLImzbpr": {"path": "/link-preview-test", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "wp6w3CaZhlSF": {"rows": [{"__ref": "1D8KtrvSdm1z"}], "__type": "ArenaFrameGrid"}, "IN5gHtgdTal7": {"rows": [{"__ref": "vRTG5yHv-Irw"}], "__type": "ArenaFrameGrid"}, "IckiaskRzyLy": {"name": null, "component": {"__ref": "FSC0WCHK0gr8"}, "uuid": "OTan4KqqgLuo", "parent": null, "locked": null, "vsettings": [{"__ref": "w8bDuXDAUfIe"}], "__type": "TplComponent"}, "DXSdyDH_OY2_": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {}, "rs": {"__ref": "TjAaWZszzpaT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1D8KtrvSdm1z": {"cols": [{"__ref": "7evO32TQmot2"}, {"__ref": "THRMH3wCrIt3"}], "rowKey": {"__ref": "BqOjgLzC0Wtg"}, "__type": "ArenaFrameRow"}, "vRTG5yHv-Irw": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "w8bDuXDAUfIe": {"variants": [{"__ref": "F3SsaesVCj8C"}], "args": [], "attrs": {}, "rs": {"__ref": "tYjo9959YyhU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TjAaWZszzpaT": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "7evO32TQmot2": {"frame": {"__ref": "NodUfdlxx4Mr"}, "cellKey": null, "__type": "ArenaFrameCell"}, "THRMH3wCrIt3": {"frame": {"__ref": "NzJBPxooM-OS"}, "cellKey": null, "__type": "ArenaFrameCell"}, "tYjo9959YyhU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NodUfdlxx4Mr": {"uuid": "hvJZbHmDvWhI", "width": 1366, "height": 768, "container": {"__ref": "T-2kdOYAMJDy"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "BqOjgLzC0Wtg"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "NzJBPxooM-OS": {"uuid": "1CFL48xA8pjA", "width": 414, "height": 736, "container": {"__ref": "Aac6wEs2942q"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "BqOjgLzC0Wtg"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "T-2kdOYAMJDy": {"name": null, "component": {"__ref": "FSC0WCHK0gr8"}, "uuid": "AaXtS_3cvyVB", "parent": null, "locked": null, "vsettings": [{"__ref": "b4HMD-TLLadJ"}], "__type": "TplComponent"}, "Aac6wEs2942q": {"name": null, "component": {"__ref": "FSC0WCHK0gr8"}, "uuid": "owNPvo6deaHp", "parent": null, "locked": null, "vsettings": [{"__ref": "RQFk8EdJ6fAX"}], "__type": "TplComponent"}, "b4HMD-TLLadJ": {"variants": [{"__ref": "F3SsaesVCj8C"}], "args": [], "attrs": {}, "rs": {"__ref": "neIXY8WGr3Uf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RQFk8EdJ6fAX": {"variants": [{"__ref": "F3SsaesVCj8C"}], "args": [], "attrs": {}, "rs": {"__ref": "7KFcoYP56J1v"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "neIXY8WGr3Uf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7KFcoYP56J1v": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BQpKH4xHGszf": {"name": null, "component": {"__xref": {"uuid": "1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c", "iid": "RFFYx9oYuGrh"}}, "uuid": "TtnKenWRhV6D", "parent": {"__ref": "AxMLMVJ1Q-Mb"}, "locked": null, "vsettings": [{"__ref": "kzajrnY5yD0z"}], "__type": "TplComponent"}, "kzajrnY5yD0z": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [{"__ref": "iBmAYAv-YZbp"}, {"__ref": "f39BM9BZb64E"}, {"__ref": "s6Ec5M2Gx94Y"}, {"__ref": "LMxw0mqmqbmd"}], "attrs": {}, "rs": {"__ref": "Vh0mNvMZzkVp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Vh0mNvMZzkVp": {"values": {"max-width": "100%", "object-fit": "cover", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "8Zs04u7JO6XG": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Atyh-rLBkYDi", "parent": {"__ref": "BQpKH4xHGszf"}, "locked": null, "vsettings": [{"__ref": "o4ia31fUBMgS"}], "__type": "TplTag"}, "rbxFX72RR6bM": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "g2V4-Aq8iX6v", "parent": {"__ref": "BQpKH4xHGszf"}, "locked": null, "vsettings": [{"__ref": "M1nkJXyl8Lb8"}], "__type": "TplTag"}, "o4ia31fUBMgS": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {}, "rs": {"__ref": "sxmDvZl33etB"}, "dataCond": null, "dataRep": null, "text": {"__ref": "n9HVAKHV0jBq"}, "columnsConfig": null, "__type": "VariantSetting"}, "M1nkJXyl8Lb8": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {}, "rs": {"__ref": "-J5RbKcCXMjt"}, "dataCond": null, "dataRep": null, "text": {"__ref": "esy-UVbrNRJ-"}, "columnsConfig": null, "__type": "VariantSetting"}, "sxmDvZl33etB": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "-J5RbKcCXMjt": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "iBmAYAv-YZbp": {"param": {"__xref": {"uuid": "1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c", "iid": "AH4eAKS-ti91"}}, "expr": {"__ref": "nT4SojTJAJ2P"}, "__type": "Arg"}, "lcIUWJ7YSlPJ": {"tag": "div", "name": null, "children": [{"__ref": "xhja9azdXoBI"}, {"__ref": "wwrI3QVZyS-w"}, {"__ref": "sXSifXe3WpVP"}, {"__ref": "iLxWVU1Duxt0"}, {"__ref": "KlkNnuthnt03"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "qeGUPpYCP9xo", "parent": {"__ref": "BQpKH4xHGszf"}, "locked": null, "vsettings": [{"__ref": "Aj06smQtCRaH"}], "__type": "TplTag"}, "Aj06smQtCRaH": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {}, "rs": {"__ref": "EmFeuSK7Fh6V"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EmFeuSK7Fh6V": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "xhja9azdXoBI": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "s1qxBWn_D1Uw", "parent": {"__ref": "lcIUWJ7YSlPJ"}, "locked": null, "vsettings": [{"__ref": "yegRnDh9-koC"}], "__type": "TplTag"}, "yegRnDh9-koC": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {"loading": {"__ref": "FgmnS52Rbr0k"}, "src": {"__ref": "uSHplkeiv57-"}, "id": {"__ref": "kubstsVyrxNs"}}, "rs": {"__ref": "OcVLeti93wZG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FgmnS52Rbr0k": {"code": "\"lazy\"", "fallback": null, "__type": "CustomCode"}, "OcVLeti93wZG": {"values": {"position": "relative", "object-fit": "cover", "max-width": "100%", "width": "300px"}, "mixins": [], "__type": "RuleSet"}, "wwrI3QVZyS-w": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "c85P27E0Dmj9", "parent": {"__ref": "lcIUWJ7YSlPJ"}, "locked": null, "vsettings": [{"__ref": "qaANEvdBHsxY"}], "__type": "TplTag"}, "qaANEvdBHsxY": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {"id": {"__ref": "gYxZsQjkEyPg"}}, "rs": {"__ref": "BRE6LAJAIfyE"}, "dataCond": null, "dataRep": null, "text": {"__ref": "VW61dSrFxxB1"}, "columnsConfig": null, "__type": "VariantSetting"}, "BRE6LAJAIfyE": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "sXSifXe3WpVP": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "_tqCfw_itGHA", "parent": {"__ref": "lcIUWJ7YSlPJ"}, "locked": null, "vsettings": [{"__ref": "Chpq6USoX7qT"}], "__type": "TplTag"}, "Chpq6USoX7qT": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {"id": {"__ref": "r5adf0dHEF25"}}, "rs": {"__ref": "RpxkzplUxBZn"}, "dataCond": null, "dataRep": null, "text": {"__ref": "323Q-wy7VCTD"}, "columnsConfig": null, "__type": "VariantSetting"}, "RpxkzplUxBZn": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "iLxWVU1Duxt0": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "zhoxE6fuctn_", "parent": {"__ref": "lcIUWJ7YSlPJ"}, "locked": null, "vsettings": [{"__ref": "qHqvn3ySgUhl"}], "__type": "TplTag"}, "qHqvn3ySgUhl": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {"id": {"__ref": "JliFEY8_y3f7"}}, "rs": {"__ref": "6_-7SaI5cjiP"}, "dataCond": null, "dataRep": null, "text": {"__ref": "-dzfyzf8QPOE"}, "columnsConfig": null, "__type": "VariantSetting"}, "6_-7SaI5cjiP": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "KlkNnuthnt03": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "2AtXUF9hxjEe", "parent": {"__ref": "lcIUWJ7YSlPJ"}, "locked": null, "vsettings": [{"__ref": "u8FNHIAwwMAo"}], "__type": "TplTag"}, "u8FNHIAwwMAo": {"variants": [{"__ref": "BqOjgLzC0Wtg"}], "args": [], "attrs": {"id": {"__ref": "3Hg2ZIGYV2us"}}, "rs": {"__ref": "8bn8ZBwCFLiN"}, "dataCond": null, "dataRep": null, "text": {"__ref": "b0UWrLpIkfe8"}, "columnsConfig": null, "__type": "VariantSetting"}, "8bn8ZBwCFLiN": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "nT4SojTJAJ2P": {"text": ["https://github.com"], "__type": "TemplatedString"}, "uSHplkeiv57-": {"path": ["$ctx", "metadata", "image"], "fallback": {"__ref": "t2DYxUVqeQW0"}, "__type": "ObjectPath"}, "t2DYxUVqeQW0": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "VW61dSrFxxB1": {"expr": {"__ref": "KI7lXj1p26oH"}, "html": false, "__type": "ExprText"}, "KI7lXj1p26oH": {"path": ["$ctx", "metadata", "hostname"], "fallback": {"__ref": "Z5smC3q-O-7g"}, "__type": "ObjectPath"}, "Z5smC3q-O-7g": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "323Q-wy7VCTD": {"expr": {"__ref": "oyDRdLVJLPY5"}, "html": false, "__type": "ExprText"}, "oyDRdLVJLPY5": {"path": ["$ctx", "metadata", "title"], "fallback": {"__ref": "96xNe4qomb7m"}, "__type": "ObjectPath"}, "96xNe4qomb7m": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "-dzfyzf8QPOE": {"expr": {"__ref": "cpI-bpHinGqu"}, "html": false, "__type": "ExprText"}, "cpI-bpHinGqu": {"path": ["$ctx", "metadata", "description"], "fallback": {"__ref": "8MOWHyLa3fcL"}, "__type": "ObjectPath"}, "8MOWHyLa3fcL": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "b0UWrLpIkfe8": {"expr": {"__ref": "ZC3TvPh6imuE"}, "html": false, "__type": "ExprText"}, "ZC3TvPh6imuE": {"path": ["$ctx", "metadata", "site_name"], "fallback": {"__ref": "uDUG1F0Ioe5t"}, "__type": "ObjectPath"}, "uDUG1F0Ioe5t": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "n9HVAKHV0jBq": {"expr": {"__ref": "bhrLx7WmsDOu"}, "html": false, "__type": "ExprText"}, "bhrLx7WmsDOu": {"code": "(\"No preview custom...\")", "fallback": {"__ref": "VPyI_UXAYBDf"}, "__type": "CustomCode"}, "VPyI_UXAYBDf": {"code": "\"no preview...\"", "fallback": null, "__type": "CustomCode"}, "esy-UVbrNRJ-": {"expr": {"__ref": "VuSKETaOcaPU"}, "html": false, "__type": "ExprText"}, "VuSKETaOcaPU": {"code": "(\"loading preview custom...\")", "fallback": {"__ref": "x37BIMT1FkGE"}, "__type": "CustomCode"}, "x37BIMT1FkGE": {"code": "\"loading preview...\"", "fallback": null, "__type": "CustomCode"}, "gYxZsQjkEyPg": {"text": ["hostname-text"], "__type": "TemplatedString"}, "r5adf0dHEF25": {"text": ["title-text"], "__type": "TemplatedString"}, "JliFEY8_y3f7": {"text": ["description-text"], "__type": "TemplatedString"}, "3Hg2ZIGYV2us": {"text": ["site-name-text"], "__type": "TemplatedString"}, "kubstsVyrxNs": {"text": ["preview-image"], "__type": "TemplatedString"}, "f39BM9BZb64E": {"param": {"__xref": {"uuid": "1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c", "iid": "J-ZH7f9Hc-Co"}}, "expr": {"__ref": "si_LWwkC6gE4"}, "__type": "Arg"}, "si_LWwkC6gE4": {"tpl": [{"__ref": "lcIUWJ7YSlPJ"}], "__type": "RenderExpr"}, "s6Ec5M2Gx94Y": {"param": {"__xref": {"uuid": "1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c", "iid": "qelaNMPZIIFw"}}, "expr": {"__ref": "keyY-85BhimA"}, "__type": "Arg"}, "LMxw0mqmqbmd": {"param": {"__xref": {"uuid": "1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c", "iid": "eAO6tiLDYn0o"}}, "expr": {"__ref": "S3M-gg3L9tNL"}, "__type": "Arg"}, "S3M-gg3L9tNL": {"tpl": [{"__ref": "rbxFX72RR6bM"}], "__type": "RenderExpr"}, "keyY-85BhimA": {"tpl": [{"__ref": "8Zs04u7JO6XG"}], "__type": "RenderExpr"}}, "deps": ["1f6fbbaf-82a8-43b5-abd3-ead1dee4ab4c"], "version": "246-add-component-updated-at"}]]