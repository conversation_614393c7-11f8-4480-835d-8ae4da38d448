[["bFHHfYQ7wXpVHsXTeXbivS", {"root": "c3L9v_qZRs3F", "map": {"oB4WuCaXjKAZ": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "FtY7sHAGOyuZ": {"name": "Default Typography", "rs": {"__ref": "oB4WuCaXjKAZ"}, "preview": null, "uuid": "OxrNO4LtIg_z", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "uoC_z4ts5_hN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7-np99YwWm5Y": {"rs": {"__ref": "uoC_z4ts5_hN"}, "__type": "ThemeLayoutSettings"}, "3v9MXZKZ11el": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "VnBSKlS4n-O0": {"name": "Default \"h1\"", "rs": {"__ref": "3v9MXZKZ11el"}, "preview": null, "uuid": "qyKbYsZNB11D", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9P-tE5Pq566m": {"selector": "h1", "style": {"__ref": "VnBSKlS4n-O0"}, "__type": "ThemeStyle"}, "0GKRwlVVSBpn": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "X3TGFrVTVYvE": {"name": "Default \"h2\"", "rs": {"__ref": "0GKRwlVVSBpn"}, "preview": null, "uuid": "UhiB9zfceCaX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "f8o08_gx6F1h": {"selector": "h2", "style": {"__ref": "X3TGFrVTVYvE"}, "__type": "ThemeStyle"}, "Kfjjgy1dSQuw": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "xx2HlcL6NbVd": {"name": "Default \"h3\"", "rs": {"__ref": "Kfjjgy1dSQuw"}, "preview": null, "uuid": "2VMGDCGQjC0P", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "3D2zWU7DZlqA": {"selector": "h3", "style": {"__ref": "xx2HlcL6NbVd"}, "__type": "ThemeStyle"}, "x-ScR9IuqEz2": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "Ed_ByvUTz72C": {"name": "Default \"h4\"", "rs": {"__ref": "x-ScR9IuqEz2"}, "preview": null, "uuid": "LZurb5HB-Kx1", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "cxhefbBT_-Oh": {"selector": "h4", "style": {"__ref": "Ed_ByvUTz72C"}, "__type": "ThemeStyle"}, "gh5Uehh588pz": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "DO9mwxxBS4xR": {"name": "Default \"h5\"", "rs": {"__ref": "gh5Uehh588pz"}, "preview": null, "uuid": "RKwe5OTEbQPp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "GIfd0qxObAXq": {"selector": "h5", "style": {"__ref": "DO9mwxxBS4xR"}, "__type": "ThemeStyle"}, "p5TIUdAnNKl_": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "7OMlRjs7YPXj": {"name": "Default \"h6\"", "rs": {"__ref": "p5TIUdAnNKl_"}, "preview": null, "uuid": "tXQcCP7BmW3-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "JCIEOyw0QyNY": {"selector": "h6", "style": {"__ref": "7OMlRjs7YPXj"}, "__type": "ThemeStyle"}, "h8GvVriIh4_-": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "knrLsq8_C_q2": {"name": "Default \"a\"", "rs": {"__ref": "h8GvVriIh4_-"}, "preview": null, "uuid": "-g6RkghJj7lE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jahqAVSrUQpV": {"selector": "a", "style": {"__ref": "knrLsq8_C_q2"}, "__type": "ThemeStyle"}, "5-6s3ZAh-f4Z": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "_3VyFOfRTb3n": {"name": "Default \"a:hover\"", "rs": {"__ref": "5-6s3ZAh-f4Z"}, "preview": null, "uuid": "4eEg2YvaD93s", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7WzvLPHAN10F": {"selector": "a:hover", "style": {"__ref": "_3VyFOfRTb3n"}, "__type": "ThemeStyle"}, "2mDxKSwZxsox": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "J-dggoOt83HE": {"name": "Default \"blockquote\"", "rs": {"__ref": "2mDxKSwZxsox"}, "preview": null, "uuid": "PvT6AdIiM93P", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "KgEKmcJ0srZk": {"selector": "blockquote", "style": {"__ref": "J-dggoOt83HE"}, "__type": "ThemeStyle"}, "H0ZoMYG7H55y": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "5h2XwQ1k1hNE": {"name": "Default \"code\"", "rs": {"__ref": "H0ZoMYG7H55y"}, "preview": null, "uuid": "wpSyamPq4qHX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "eqCwnUoJkGgG": {"selector": "code", "style": {"__ref": "5h2XwQ1k1hNE"}, "__type": "ThemeStyle"}, "6vn94lnS0E2X": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "GYXpuHa1Uet1": {"name": "Default \"pre\"", "rs": {"__ref": "6vn94lnS0E2X"}, "preview": null, "uuid": "M8i4b1xqDlP3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "WxepgaAnW1TS": {"selector": "pre", "style": {"__ref": "GYXpuHa1Uet1"}, "__type": "ThemeStyle"}, "TA67-p4cDq1d": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "if_K78_YaHsU": {"name": "Default \"ol\"", "rs": {"__ref": "TA67-p4cDq1d"}, "preview": null, "uuid": "mAmUwPdPLAcR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ax-jVp-Y3n6t": {"selector": "ol", "style": {"__ref": "if_K78_YaHsU"}, "__type": "ThemeStyle"}, "0Dfe8cE0Obfp": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "THhPkNTauCun": {"name": "Default \"ul\"", "rs": {"__ref": "0Dfe8cE0Obfp"}, "preview": null, "uuid": "-DMDxPqHoPdo", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "wRQ5JctQp-fB": {"selector": "ul", "style": {"__ref": "THhPkNTauCun"}, "__type": "ThemeStyle"}, "73KOEQyL7X6-": {"defaultStyle": {"__ref": "FtY7sHAGOyuZ"}, "styles": [{"__ref": "9P-tE5Pq566m"}, {"__ref": "f8o08_gx6F1h"}, {"__ref": "3D2zWU7DZlqA"}, {"__ref": "cxhefbBT_-Oh"}, {"__ref": "GIfd0qxObAXq"}, {"__ref": "JCIEOyw0QyNY"}, {"__ref": "jahqAVSrUQpV"}, {"__ref": "7WzvLPHAN10F"}, {"__ref": "KgEKmcJ0srZk"}, {"__ref": "eqCwnUoJkGgG"}, {"__ref": "WxepgaAnW1TS"}, {"__ref": "Ax-jVp-Y3n6t"}, {"__ref": "wRQ5JctQp-fB"}], "layout": {"__ref": "7-np99YwWm5Y"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "4oA0JtaNoGki": {"name": "text", "__type": "Text"}, "GJtksx_9rJLK": {"name": "Screen", "uuid": "l-IKgYEYYTv3", "__type": "Var"}, "r1ZqVT7Lw5jV": {"type": {"__ref": "4oA0JtaNoGki"}, "variable": {"__ref": "GJtksx_9rJLK"}, "uuid": "AQMisKzqJXyb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "J89_rP8B-QaS": {"type": "global-screen", "param": {"__ref": "r1ZqVT7Lw5jV"}, "uuid": "mXBJlOCaKGWy", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "pTY6zCfBeZCi": {"uuid": "4c7wT-WOGjkA", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "c3L9v_qZRs3F": {"components": [{"__ref": "dEF8zuydwmXa"}, {"__ref": "2VH-5YdWosjy"}, {"__ref": "hSSsIgYX9Ez_"}, {"__ref": "a8rMN9AF25rn"}, {"__ref": "uhJY0og8JLqe"}, {"__ref": "zc3muSrtBGCI"}, {"__ref": "J7HHtq-clz9l"}, {"__ref": "y3Gi1ZRR3zFC"}], "arenas": [], "pageArenas": [{"__ref": "QaKiYYH09Wmz"}, {"__ref": "uy3E8aDmqevI"}, {"__ref": "r3WQlx6a2XXp"}, {"__ref": "alKfaUuyISex"}], "componentArenas": [{"__ref": "ct4K1tklAIDs"}, {"__ref": "TjZyhY5wTxsV"}], "globalVariantGroups": [{"__ref": "J89_rP8B-QaS"}], "userManagedFonts": [], "globalVariant": {"__ref": "pTY6zCfBeZCi"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "73KOEQyL7X6-"}], "activeTheme": {"__ref": "73KOEQyL7X6-"}, "imageAssets": [{"__ref": "xevvXe89XAHn"}, {"__ref": "i9xJcsRFqi76"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "J89_rP8B-QaS"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"button": {"__ref": "zc3muSrtBGCI"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "dEF8zuydwmXa": {"uuid": "Ptt9zC5_VFf7", "name": "hostless-plasmic-head", "params": [{"__ref": "XLJSGB8O24nV"}, {"__ref": "gDAbIO0c2Of9"}, {"__ref": "68LBs-9dEv75"}, {"__ref": "HxCyq5odOClw"}], "states": [], "tplTree": {"__ref": "L4ImnHiGJrAu"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "sCHBwHJN5eJa"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "F_7MyoVvt6SJ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "2VH-5YdWosjy": {"uuid": "Bv3aRg6doUcD", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "3tqRb4NEnzQU"}, {"__ref": "z2T0EsdLzxEc"}, {"__ref": "7HGtw2kZWbQ2"}, {"__ref": "DdSMRDDl9dPC"}, {"__ref": "KTGDxyal9SRY"}], "states": [], "tplTree": {"__ref": "pH7qkmiarLpZ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "DCWFJx8gDwGx"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "zUFp_kXCGBfM"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "hSSsIgYX9Ez_": {"uuid": "fdq84SoGgUkZ", "name": "Home", "params": [], "states": [], "tplTree": {"__ref": "M-GZGqFbu66B"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "seqJtOa-qmF5"}], "variantGroups": [], "pageMeta": {"__ref": "m_Nh1eejQLue"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "a8rMN9AF25rn": {"uuid": "gzzccGfYfnC_", "name": "ShowCurrentUser", "params": [], "states": [], "tplTree": {"__ref": "KQCZcNJwTDwX"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "v4tW8WYIop8_"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "uhJY0og8JLqe": {"uuid": "oXQ62Fv4aXXe", "name": "Page 1", "params": [], "states": [], "tplTree": {"__ref": "3rdHWhqpl7__"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "AALHp7VW3AZi"}], "variantGroups": [], "pageMeta": {"__ref": "JsdiEM2psx6H"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "zc3muSrtBGCI": {"uuid": "HJKweunsdmsG", "name": "<PERSON><PERSON>", "params": [{"__ref": "7O6qHUygi0o-"}, {"__ref": "xew8B284h7On"}, {"__ref": "y1XBEvVeKLyL"}, {"__ref": "8sRSz5t67XL5"}, {"__ref": "eOpE3ydfotZ1"}, {"__ref": "nlmylrEkDc56"}, {"__ref": "mxbtaotUBiEU"}, {"__ref": "SQ7JAXJhmdK7"}, {"__ref": "SvOafmLO1SXA"}, {"__ref": "cEH_-a0jGzkO"}, {"__ref": "3jH2wGkYrNml"}, {"__ref": "YKAzl6DgXjub"}, {"__ref": "qnDOC_U6felX"}, {"__ref": "jcwiDwX2B-OY"}, {"__ref": "7XNugFv__ek5"}, {"__ref": "hOcnMsdv_DS8"}, {"__ref": "0BbvK9LE8NtK"}, {"__ref": "pD3TUf_j-AHW"}], "states": [{"__ref": "Kx2dQTzxJvHf"}, {"__ref": "VgKiwxmOTAgw"}, {"__ref": "ORGQoqgIYUWi"}, {"__ref": "Q4m0_4B8sYqu"}, {"__ref": "IbnkwnsI2SYt"}, {"__ref": "FdFL66HfxtB9"}], "tplTree": {"__ref": "9zjLiEIg0Ncz"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "Z3-1oAqJmta8"}, {"__ref": "7JaZkaz7FKrX"}, {"__ref": "ccD0O-b22xgL"}, {"__ref": "aMV_xZLTjaiR"}, {"__ref": "MkyOndX3NfEk"}], "variantGroups": [{"__ref": "9Wl6VZg3d1J-"}, {"__ref": "88t-l2ADtFF4"}, {"__ref": "ZY4ClNl1HTUM"}, {"__ref": "VnWWeW7kkZg-"}, {"__ref": "FPozX2FFynfB"}, {"__ref": "kCWv_SoTcPf2"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "yscZyN_fS53i"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true, "__type": "Component", "updatedAt": null}, "J7HHtq-clz9l": {"uuid": "HL30oCyfwH5U", "name": "Page 2", "params": [], "states": [], "tplTree": {"__ref": "fZZ26QuQBwEJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "jY5r_aRO-4Eb"}], "variantGroups": [], "pageMeta": {"__ref": "PeouyNVpN23L"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "y3Gi1ZRR3zFC": {"uuid": "aorRODuPYl3R", "name": "Page 3", "params": [], "states": [], "tplTree": {"__ref": "5cyCIeJxHOMF"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "1sj2PKIZW7Yv"}], "variantGroups": [], "pageMeta": {"__ref": "vk7F7oxpfpue"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "QaKiYYH09Wmz": {"component": {"__ref": "hSSsIgYX9Ez_"}, "matrix": {"__ref": "Qj9zWFaTcDjD"}, "customMatrix": {"__ref": "RHZMOGRP2cRr"}, "__type": "PageArena"}, "uy3E8aDmqevI": {"component": {"__ref": "uhJY0og8JLqe"}, "matrix": {"__ref": "jljcJOrlZBM_"}, "customMatrix": {"__ref": "peHgdyWpyMde"}, "__type": "PageArena"}, "r3WQlx6a2XXp": {"component": {"__ref": "J7HHtq-clz9l"}, "matrix": {"__ref": "9v8NYRtUSM1N"}, "customMatrix": {"__ref": "GbVaITGTliW3"}, "__type": "PageArena"}, "alKfaUuyISex": {"component": {"__ref": "y3Gi1ZRR3zFC"}, "matrix": {"__ref": "1baXCjxzAp9o"}, "customMatrix": {"__ref": "6sqKUKTaKTgU"}, "__type": "PageArena"}, "ct4K1tklAIDs": {"component": {"__ref": "a8rMN9AF25rn"}, "matrix": {"__ref": "5ApwZXQPSA5a"}, "customMatrix": {"__ref": "t4B--gkLwm1v"}, "__type": "ComponentArena"}, "TjZyhY5wTxsV": {"component": {"__ref": "zc3muSrtBGCI"}, "matrix": {"__ref": "UVzrBevMmR2Z"}, "customMatrix": {"__ref": "6J-AyYUA5I8l"}, "__type": "ComponentArena"}, "xevvXe89XAHn": {"uuid": "NSHpCvyKFamQ", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "i9xJcsRFqi76": {"uuid": "nH4fDsauK_rU", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "XLJSGB8O24nV": {"type": {"__ref": "0qZgZKOTWbwu"}, "variable": {"__ref": "kioHuhbtnFbD"}, "uuid": "Abn2dRWk0Qv9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "gDAbIO0c2Of9": {"type": {"__ref": "h_Ff9cKTymqs"}, "variable": {"__ref": "aNKWCMFe_2H-"}, "uuid": "AKk9EXmb7FbC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "68LBs-9dEv75": {"type": {"__ref": "_DtYm2wbJp6y"}, "variable": {"__ref": "SMeYS8eu6OuY"}, "uuid": "Kqir_S8GzOAF", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "HxCyq5odOClw": {"type": {"__ref": "j4StCyf9ao61"}, "variable": {"__ref": "bCBJdkX_g1Qv"}, "uuid": "1TtoE_QfvOlb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "L4ImnHiGJrAu": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "H_611uHyPcky", "parent": null, "locked": null, "vsettings": [{"__ref": "ESef3lbJssZN"}], "__type": "TplTag"}, "sCHBwHJN5eJa": {"uuid": "UVkFhwpJvdgM", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "F_7MyoVvt6SJ": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "3tqRb4NEnzQU": {"type": {"__ref": "yAh_AWvSL8YF"}, "variable": {"__ref": "XUtYYMyzEb68"}, "uuid": "ilM3qhPPv5EU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "z2T0EsdLzxEc": {"type": {"__ref": "icgfrbsrFMHh"}, "variable": {"__ref": "hlaR7ihQiy9F"}, "uuid": "ldzCKzNHnuEv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "7HGtw2kZWbQ2": {"type": {"__ref": "_imNgNMxusbS"}, "tplSlot": {"__ref": "mVoXi7fGYZqG"}, "variable": {"__ref": "HS2722oq16cA"}, "uuid": "aYJan6OvswpJ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "DdSMRDDl9dPC": {"type": {"__ref": "m7i7gfohI4cM"}, "variable": {"__ref": "6i1f-umois7F"}, "uuid": "Y0JpaAmuQcSM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "KTGDxyal9SRY": {"type": {"__ref": "nRixB1bCLKpS"}, "variable": {"__ref": "2vhSfbjQ91RI"}, "uuid": "qonNtABex__X", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "pH7qkmiarLpZ": {"tag": "div", "name": null, "children": [{"__ref": "mVoXi7fGYZqG"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OfPMdCsP9iWf", "parent": null, "locked": null, "vsettings": [{"__ref": "N6OdLZb7-_F3"}], "__type": "TplTag"}, "DCWFJx8gDwGx": {"uuid": "PhWyYdo_Nqfs", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zUFp_kXCGBfM": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "M-GZGqFbu66B": {"tag": "div", "name": null, "children": [{"__ref": "hHIXOwrGNB7O"}, {"__ref": "apcSvlGjB4fH"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uASY0RrvSGFU", "parent": null, "locked": null, "vsettings": [{"__ref": "zpLDVO6Xvbhl"}], "__type": "TplTag"}, "seqJtOa-qmF5": {"uuid": "Epke1eAuIzoB", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "m_Nh1eejQLue": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "KQCZcNJwTDwX": {"tag": "div", "name": null, "children": [{"__ref": "LPLkkbcKU8Yl"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "IVgy-kJvRkGz", "parent": null, "locked": null, "vsettings": [{"__ref": "20dsycwxtDZB"}], "__type": "TplTag"}, "v4tW8WYIop8_": {"uuid": "njgvo9-uz6ik", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3rdHWhqpl7__": {"tag": "div", "name": null, "children": [{"__ref": "OZBTZr1FYqGL"}, {"__ref": "pDnJq6zhw1Q-"}, {"__ref": "jCSWpHWzU3m7"}, {"__ref": "Og8X1GYkG4Mw"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "NgW8Sqi_vt3J", "parent": null, "locked": null, "vsettings": [{"__ref": "z2glAxrSPuji"}], "__type": "TplTag"}, "AALHp7VW3AZi": {"uuid": "lDrd5cWe5d5s", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "JsdiEM2psx6H": {"path": "/page-1", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "7O6qHUygi0o-": {"type": {"__ref": "91Rl2hlXFvsp"}, "tplSlot": {"__ref": "3U__C9ILXC6x"}, "variable": {"__ref": "aDIj5niPR2FU"}, "uuid": "wyELtscfvSQX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false, "__type": "SlotParam"}, "xew8B284h7On": {"type": {"__ref": "q83_8SkBRcVr"}, "state": {"__ref": "Kx2dQTzxJvHf"}, "variable": {"__ref": "hbv22zzGbjIX"}, "uuid": "M7h04qzrUPy5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "y1XBEvVeKLyL": {"type": {"__ref": "8y4KWFPh0Nk2"}, "state": {"__ref": "VgKiwxmOTAgw"}, "variable": {"__ref": "4Q4nuoSX4tPj"}, "uuid": "XrN7vl5wVeP6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "8sRSz5t67XL5": {"type": {"__ref": "Afe4RlOHyyda"}, "tplSlot": {"__ref": "3O7tMnP9o6kR"}, "variable": {"__ref": "wRphmjtG0DZi"}, "uuid": "exb8aMlhP5t9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "eOpE3ydfotZ1": {"type": {"__ref": "q6gEws74zvgJ"}, "tplSlot": {"__ref": "1mms1CPIzu7Q"}, "variable": {"__ref": "OB0NGo0lYLCq"}, "uuid": "ThOUYSytVYv-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "nlmylrEkDc56": {"type": {"__ref": "UAdADEPT2-OP"}, "state": {"__ref": "ORGQoqgIYUWi"}, "variable": {"__ref": "t-_6kljUfKgW"}, "uuid": "-brpJ9Z3syWa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "mxbtaotUBiEU": {"type": {"__ref": "b3O-OeGZoltC"}, "variable": {"__ref": "nREngWGvUp0v"}, "uuid": "SB0ruiLnHAGZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "SQ7JAXJhmdK7": {"type": {"__ref": "llI7RiPCqxCb"}, "state": {"__ref": "FdFL66HfxtB9"}, "variable": {"__ref": "rV9LtUlE7S7x"}, "uuid": "zOGi_P2AD2mz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "SvOafmLO1SXA": {"type": {"__ref": "u5g4Hc86bBwx"}, "state": {"__ref": "IbnkwnsI2SYt"}, "variable": {"__ref": "6Z2aUwUpsYNm"}, "uuid": "zoqqUjXZNRLc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "cEH_-a0jGzkO": {"type": {"__ref": "wqrvm2dz6iU5"}, "state": {"__ref": "Q4m0_4B8sYqu"}, "variable": {"__ref": "rUOi7hjSAe2b"}, "uuid": "cAlpVgn59y-5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "3jH2wGkYrNml": {"type": {"__ref": "VG6winUXE2jo"}, "variable": {"__ref": "HLQNE90XqvIW"}, "uuid": "RuTALQlGQFa9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Submits form?", "about": "Whether clicking on this button submits the enclosing form or not", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "YKAzl6DgXjub": {"type": {"__ref": "sPKsJm2pYwNR"}, "variable": {"__ref": "uH0TssZhMX1I"}, "uuid": "JBUGeFq7qBYR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Open in new tab?", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "qnDOC_U6felX": {"type": {"__ref": "mnhlbHCWJa23"}, "state": {"__ref": "Kx2dQTzxJvHf"}, "variable": {"__ref": "VlmbL-8kAXuO"}, "uuid": "jZC8BGW1XpIz", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "jcwiDwX2B-OY": {"type": {"__ref": "GKY65zdyaGm5"}, "state": {"__ref": "VgKiwxmOTAgw"}, "variable": {"__ref": "eYI9O6t7OnKb"}, "uuid": "xBqAuyHhD8ML", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "7XNugFv__ek5": {"type": {"__ref": "zzIVxGGzdONS"}, "state": {"__ref": "ORGQoqgIYUWi"}, "variable": {"__ref": "iKExVzQU5s2P"}, "uuid": "ZLMt9vYUpJZ0", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "hOcnMsdv_DS8": {"type": {"__ref": "TgbZ81fKZV0z"}, "state": {"__ref": "Q4m0_4B8sYqu"}, "variable": {"__ref": "HH0gHf_oxbxU"}, "uuid": "AC5E4VHt8dO6", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "0BbvK9LE8NtK": {"type": {"__ref": "6LLvqWFBePG4"}, "state": {"__ref": "IbnkwnsI2SYt"}, "variable": {"__ref": "1xZKjlpnQ-As"}, "uuid": "AdlwcALO4XTk", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "pD3TUf_j-AHW": {"type": {"__ref": "Y31P8XVdxCbb"}, "state": {"__ref": "FdFL66HfxtB9"}, "variable": {"__ref": "xrCqx2QJpDzl"}, "uuid": "m99Fb1GkYySv", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "Kx2dQTzxJvHf": {"variantGroup": {"__ref": "9Wl6VZg3d1J-"}, "param": {"__ref": "xew8B284h7On"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "qnDOC_U6felX"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "VgKiwxmOTAgw": {"variantGroup": {"__ref": "88t-l2ADtFF4"}, "param": {"__ref": "y1XBEvVeKLyL"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "jcwiDwX2B-OY"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "ORGQoqgIYUWi": {"variantGroup": {"__ref": "ZY4ClNl1HTUM"}, "param": {"__ref": "nlmylrEkDc56"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "7XNugFv__ek5"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "Q4m0_4B8sYqu": {"variantGroup": {"__ref": "VnWWeW7kkZg-"}, "param": {"__ref": "cEH_-a0jGzkO"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "hOcnMsdv_DS8"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "IbnkwnsI2SYt": {"variantGroup": {"__ref": "FPozX2FFynfB"}, "param": {"__ref": "SvOafmLO1SXA"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "0BbvK9LE8NtK"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "FdFL66HfxtB9": {"variantGroup": {"__ref": "kCWv_SoTcPf2"}, "param": {"__ref": "SQ7JAXJhmdK7"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "pD3TUf_j-AHW"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "9zjLiEIg0Ncz": {"tag": "button", "name": null, "children": [{"__ref": "2Dgc6Ig54uEo"}, {"__ref": "Lo6MQPm-J7NA"}, {"__ref": "Dg69fxMRfg-o"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8S-kxahnmAAw", "parent": null, "locked": null, "vsettings": [{"__ref": "qV-mG7Xz4iIP"}, {"__ref": "f404EbS6Whop"}, {"__ref": "BMNzVMR6NfX-"}, {"__ref": "KjBk-MgP_v2Z"}, {"__ref": "An7KPcDOUbz1"}, {"__ref": "dtH2bM7lEaae"}, {"__ref": "Io3m9Fh1mSnN"}, {"__ref": "712uGGmYvevN"}, {"__ref": "b31gMtKvtTOR"}, {"__ref": "aJsy6tOM7J4H"}, {"__ref": "g9SJOv7RltaO"}, {"__ref": "05TYtFaIBmsR"}, {"__ref": "kxbW1J3Slrwa"}, {"__ref": "Ws6wQk_vhcFs"}, {"__ref": "-mb5rzHbA43-"}, {"__ref": "Ftw9bK1goewL"}, {"__ref": "TZzaMqukaS5X"}, {"__ref": "G9bN6EiBBdpv"}, {"__ref": "lsCyPZG3fTiW"}, {"__ref": "-licVd-vRaZn"}, {"__ref": "6mV0YGv0RX0X"}, {"__ref": "4fkok7d1PrTN"}, {"__ref": "KKyOlTp6Tp6P"}, {"__ref": "IKmXiEjcK_1G"}, {"__ref": "RgSxUQrMyKF5"}, {"__ref": "NF7x8L3YQKyq"}, {"__ref": "qJ0p0uNNu3rf"}, {"__ref": "PkQJMCg6ESUJ"}, {"__ref": "L9pCWLCereEq"}, {"__ref": "HvMndTShdgHX"}, {"__ref": "sCdOBDvQtfvg"}, {"__ref": "anJ4azG8ebou"}, {"__ref": "0OMwg6FXmOX0"}, {"__ref": "t7d8xUHE0ntQ"}, {"__ref": "d9x0iiyIr168"}, {"__ref": "NIvOLY_okG5K"}, {"__ref": "AzQdzKvaudfB"}, {"__ref": "HwS5GCfC2ksC"}, {"__ref": "IBDWfX6_7REc"}, {"__ref": "zJY2V-tgfCFV"}, {"__ref": "3En0jRSe58QI"}, {"__ref": "nzz7xwj_3ba3"}, {"__ref": "Hf-aUi2s6-TN"}, {"__ref": "U2lAij9bEg6V"}, {"__ref": "PjUl44BjdlfP"}, {"__ref": "oAjs76W1jI0N"}, {"__ref": "Q6e0SsSj9mFp"}, {"__ref": "v_Sx897QKApm"}, {"__ref": "zcsAZ0YWGiND"}, {"__ref": "I9SyyKsjaCpB"}, {"__ref": "snIGTb3PXoA3"}, {"__ref": "MYDdWFj8w6iS"}, {"__ref": "-qLExC1MruoH"}, {"__ref": "YqgIb0eJCezx"}, {"__ref": "OYniY4RC4-CG"}, {"__ref": "NZoKFIbLdQ-F"}, {"__ref": "IunVG_pB4NQX"}, {"__ref": "5OINqmDHJLLN"}, {"__ref": "fWVH5jKH_zt-"}, {"__ref": "bpG2n6yhqlqH"}], "__type": "TplTag"}, "Z3-1oAqJmta8": {"uuid": "qqWFNPP1cCKi", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "7JaZkaz7FKrX": {"uuid": "aUExF6eJ4rnv", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ccD0O-b22xgL": {"uuid": "Fxt2X3wP7MpU", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "aMV_xZLTjaiR": {"uuid": "o3_eL4iVBP2X", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "MkyOndX3NfEk": {"uuid": "ceA2E9ruHbDb", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "9Wl6VZg3d1J-": {"type": "component", "param": {"__ref": "xew8B284h7On"}, "linkedState": {"__ref": "Kx2dQTzxJvHf"}, "uuid": "I6vwiT0dhiOn", "variants": [{"__ref": "lw1r0YN71__P"}], "multi": false, "__type": "ComponentVariantGroup"}, "88t-l2ADtFF4": {"type": "component", "param": {"__ref": "y1XBEvVeKLyL"}, "linkedState": {"__ref": "VgKiwxmOTAgw"}, "uuid": "oIhh9GYxzyW3", "variants": [{"__ref": "fiZpOiUmqWUj"}], "multi": false, "__type": "ComponentVariantGroup"}, "ZY4ClNl1HTUM": {"type": "component", "param": {"__ref": "nlmylrEkDc56"}, "linkedState": {"__ref": "ORGQoqgIYUWi"}, "uuid": "HzIW0MnN6jyr", "variants": [{"__ref": "3jNcACuRZH2N"}], "multi": false, "__type": "ComponentVariantGroup"}, "VnWWeW7kkZg-": {"type": "component", "param": {"__ref": "cEH_-a0jGzkO"}, "linkedState": {"__ref": "Q4m0_4B8sYqu"}, "uuid": "4bkzSCrEtimD", "variants": [{"__ref": "M7BJw2PREMmA"}, {"__ref": "v558s9LW1FOf"}, {"__ref": "OrW3UwYyLXo2"}], "multi": false, "__type": "ComponentVariantGroup"}, "FPozX2FFynfB": {"type": "component", "param": {"__ref": "SvOafmLO1SXA"}, "linkedState": {"__ref": "IbnkwnsI2SYt"}, "uuid": "fHl5ZvgLg01S", "variants": [{"__ref": "UlUyAvXIG8Ed"}, {"__ref": "YzEzC7zE3c0B"}], "multi": false, "__type": "ComponentVariantGroup"}, "kCWv_SoTcPf2": {"type": "component", "param": {"__ref": "SQ7JAXJhmdK7"}, "linkedState": {"__ref": "FdFL66HfxtB9"}, "uuid": "5gyvYTHljgHB", "variants": [{"__ref": "-4w4Dy07a9cq"}, {"__ref": "_3z_P-NNtxuv"}, {"__ref": "boNkne008VC3"}, {"__ref": "8O-Ip7hwZDj3"}, {"__ref": "vHnYd1i6wg4G"}, {"__ref": "jyyU9ZyH-Cnl"}, {"__ref": "x2ZuXQtQ7fsO"}, {"__ref": "TMmVv5yI_Gl6"}, {"__ref": "r8t_K3NKv6FJ"}, {"__ref": "92QGXrl6wD1Q"}, {"__ref": "bgUfLcScXBSM"}, {"__ref": "pLXcb85mRSWS"}, {"__ref": "cEIRMhDKPhmd"}], "multi": false, "__type": "ComponentVariantGroup"}, "yscZyN_fS53i": {"type": "button", "__type": "PlumeInfo"}, "fZZ26QuQBwEJ": {"tag": "div", "name": null, "children": [{"__ref": "tpcTq70pG41Q"}, {"__ref": "ENNBUiWlvnaB"}, {"__ref": "huPurRLsIC7Q"}, {"__ref": "OueZnU6ai0mQ"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "C_is-pg0FOKr", "parent": null, "locked": null, "vsettings": [{"__ref": "zNDImrHBVakX"}], "__type": "TplTag"}, "jY5r_aRO-4Eb": {"uuid": "_eLIy44IQrdR", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "PeouyNVpN23L": {"path": "/page-2", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "5cyCIeJxHOMF": {"tag": "div", "name": null, "children": [{"__ref": "7Znh9Au23Oj1"}, {"__ref": "VtNdlR3M1IIE"}, {"__ref": "shm24WXzWsGX"}, {"__ref": "T_DgB2IOuVM6"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Bs08pSK_Tr-G", "parent": null, "locked": null, "vsettings": [{"__ref": "YC3ptNOU57XD"}], "__type": "TplTag"}, "1sj2PKIZW7Yv": {"uuid": "GPploaM6wo5p", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "vk7F7oxpfpue": {"path": "/page-3", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "Qj9zWFaTcDjD": {"rows": [{"__ref": "feIoxP-v0E7k"}], "__type": "ArenaFrameGrid"}, "RHZMOGRP2cRr": {"rows": [{"__ref": "KSQ0o-UIRfeV"}], "__type": "ArenaFrameGrid"}, "jljcJOrlZBM_": {"rows": [{"__ref": "iZPPFrpI3Gjn"}], "__type": "ArenaFrameGrid"}, "peHgdyWpyMde": {"rows": [{"__ref": "8x91rEnzly8M"}], "__type": "ArenaFrameGrid"}, "9v8NYRtUSM1N": {"rows": [{"__ref": "nrrGLcfj4n5Q"}], "__type": "ArenaFrameGrid"}, "GbVaITGTliW3": {"rows": [{"__ref": "G64dAjy6QOUQ"}], "__type": "ArenaFrameGrid"}, "1baXCjxzAp9o": {"rows": [{"__ref": "sfvf4fyk_c-R"}], "__type": "ArenaFrameGrid"}, "6sqKUKTaKTgU": {"rows": [{"__ref": "WCtsOR41Cx8v"}], "__type": "ArenaFrameGrid"}, "5ApwZXQPSA5a": {"rows": [{"__ref": "OsSo8Cn6ZFx-"}], "__type": "ArenaFrameGrid"}, "t4B--gkLwm1v": {"rows": [{"__ref": "N9DhmRz86rBm"}], "__type": "ArenaFrameGrid"}, "UVzrBevMmR2Z": {"rows": [{"__ref": "uQu_90ooan47"}, {"__ref": "X-trpfODfO6G"}, {"__ref": "PCmjjbiypvWn"}, {"__ref": "KJ6dirPs9Z0L"}, {"__ref": "VlKXajVmGkoA"}, {"__ref": "m5xVkDKI23iD"}, {"__ref": "pv1TwBeEwboQ"}], "__type": "ArenaFrameGrid"}, "6J-AyYUA5I8l": {"rows": [{"__ref": "uYIMxDI-r4r4"}], "__type": "ArenaFrameGrid"}, "0qZgZKOTWbwu": {"name": "text", "__type": "Text"}, "kioHuhbtnFbD": {"name": "title", "uuid": "L9AwWXD4pnbL", "__type": "Var"}, "h_Ff9cKTymqs": {"name": "text", "__type": "Text"}, "aNKWCMFe_2H-": {"name": "description", "uuid": "c9aYmkHf4m4v", "__type": "Var"}, "_DtYm2wbJp6y": {"name": "img", "__type": "Img"}, "SMeYS8eu6OuY": {"name": "image", "uuid": "8pOnDtBMIgTE", "__type": "Var"}, "j4StCyf9ao61": {"name": "text", "__type": "Text"}, "bCBJdkX_g1Qv": {"name": "canonical", "uuid": "jaB0eiq6_YDt", "__type": "Var"}, "ESef3lbJssZN": {"variants": [{"__ref": "sCHBwHJN5eJa"}], "args": [], "attrs": {}, "rs": {"__ref": "1zwSlcpRmrLN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yAh_AWvSL8YF": {"name": "any", "__type": "AnyType"}, "XUtYYMyzEb68": {"name": "dataOp", "uuid": "r87JhVWiv3MG", "__type": "Var"}, "icgfrbsrFMHh": {"name": "text", "__type": "Text"}, "hlaR7ihQiy9F": {"name": "name", "uuid": "cECp3AwLbrw-", "__type": "Var"}, "_imNgNMxusbS": {"name": "renderFunc", "params": [{"__ref": "OR5ye9_9daOX"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "HS2722oq16cA": {"name": "children", "uuid": "EpkL2uGn9IEh", "__type": "Var"}, "m7i7gfohI4cM": {"name": "num", "__type": "<PERSON><PERSON>"}, "6i1f-umois7F": {"name": "pageSize", "uuid": "NmFESgLOOH6C", "__type": "Var"}, "nRixB1bCLKpS": {"name": "num", "__type": "<PERSON><PERSON>"}, "2vhSfbjQ91RI": {"name": "pageIndex", "uuid": "md1d2RJOmfSz", "__type": "Var"}, "mVoXi7fGYZqG": {"param": {"__ref": "7HGtw2kZWbQ2"}, "defaultContents": [], "uuid": "ygPLNAqytoWp", "parent": {"__ref": "pH7qkmiarLpZ"}, "locked": null, "vsettings": [{"__ref": "xS07QNqBEthj"}], "__type": "TplSlot"}, "N6OdLZb7-_F3": {"variants": [{"__ref": "DCWFJx8gDwGx"}], "args": [], "attrs": {}, "rs": {"__ref": "bmcT-vL03pVQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hHIXOwrGNB7O": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "XiA3TY4pVrEt", "parent": {"__ref": "M-GZGqFbu66B"}, "locked": null, "vsettings": [{"__ref": "rzekLu49ngdg"}], "__type": "TplTag"}, "apcSvlGjB4fH": {"name": null, "component": {"__ref": "a8rMN9AF25rn"}, "uuid": "wzWIml4QveD_", "parent": {"__ref": "M-GZGqFbu66B"}, "locked": null, "vsettings": [{"__ref": "O4X9JWM_SWPJ"}], "__type": "TplComponent"}, "zpLDVO6Xvbhl": {"variants": [{"__ref": "seqJtOa-qmF5"}], "args": [], "attrs": {}, "rs": {"__ref": "zP7KGIURvA4h"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LPLkkbcKU8Yl": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "DatDwL0eAFAS", "parent": {"__ref": "KQCZcNJwTDwX"}, "locked": null, "vsettings": [{"__ref": "vrjxnL_es4vC"}], "__type": "TplTag"}, "20dsycwxtDZB": {"variants": [{"__ref": "v4tW8WYIop8_"}], "args": [], "attrs": {}, "rs": {"__ref": "59cY9X6Rbcvt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OZBTZr1FYqGL": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jK6mp9OfgvtT", "parent": {"__ref": "3rdHWhqpl7__"}, "locked": null, "vsettings": [{"__ref": "cFlFZJVy8J58"}], "__type": "TplTag"}, "pDnJq6zhw1Q-": {"name": null, "component": {"__ref": "a8rMN9AF25rn"}, "uuid": "kO_u9obTB4DG", "parent": {"__ref": "3rdHWhqpl7__"}, "locked": null, "vsettings": [{"__ref": "zOLjt3USQzVM"}], "__type": "TplComponent"}, "jCSWpHWzU3m7": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "ZW2nFkErvYFm", "parent": {"__ref": "3rdHWhqpl7__"}, "locked": null, "vsettings": [{"__ref": "LN2KtYKC-tWN"}], "__type": "TplComponent"}, "Og8X1GYkG4Mw": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "rIj_mw2NDLmW", "parent": {"__ref": "3rdHWhqpl7__"}, "locked": null, "vsettings": [{"__ref": "GKRoyHHvZdJa"}], "__type": "TplComponent"}, "z2glAxrSPuji": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {}, "rs": {"__ref": "XR2JqVxIpl72"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "91Rl2hlXFvsp": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "aDIj5niPR2FU": {"name": "children", "uuid": "FvOLjX5wJUK-", "__type": "Var"}, "q83_8SkBRcVr": {"name": "any", "__type": "AnyType"}, "hbv22zzGbjIX": {"name": "Show Start Icon", "uuid": "fj27hj_goFW-", "__type": "Var"}, "8y4KWFPh0Nk2": {"name": "any", "__type": "AnyType"}, "4Q4nuoSX4tPj": {"name": "Show End Icon", "uuid": "F4nexFLSAYIM", "__type": "Var"}, "Afe4RlOHyyda": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "wRphmjtG0DZi": {"name": "start icon", "uuid": "sD-NqNfSMe2D", "__type": "Var"}, "q6gEws74zvgJ": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "OB0NGo0lYLCq": {"name": "end icon", "uuid": "BT5AJSX9Aupf", "__type": "Var"}, "UAdADEPT2-OP": {"name": "any", "__type": "AnyType"}, "t-_6kljUfKgW": {"name": "Is Disabled", "uuid": "5cXTJl8CJVjd", "__type": "Var"}, "b3O-OeGZoltC": {"name": "href", "__type": "HrefType"}, "nREngWGvUp0v": {"name": "link", "uuid": "yLw5NUwFtuTo", "__type": "Var"}, "llI7RiPCqxCb": {"name": "any", "__type": "AnyType"}, "rV9LtUlE7S7x": {"name": "Color", "uuid": "izLE7OMBGnrk", "__type": "Var"}, "u5g4Hc86bBwx": {"name": "any", "__type": "AnyType"}, "6Z2aUwUpsYNm": {"name": "Size", "uuid": "NRptzCArUGcY", "__type": "Var"}, "wqrvm2dz6iU5": {"name": "any", "__type": "AnyType"}, "rUOi7hjSAe2b": {"name": "<PERSON><PERSON><PERSON>", "uuid": "HL1Kh6lixD6N", "__type": "Var"}, "VG6winUXE2jo": {"name": "bool", "__type": "BoolType"}, "HLQNE90XqvIW": {"name": "submitsForm", "uuid": "ytZyx8EWCm9r", "__type": "Var"}, "sPKsJm2pYwNR": {"name": "bool", "__type": "BoolType"}, "uH0TssZhMX1I": {"name": "target", "uuid": "rfVip789wFGb", "__type": "Var"}, "mnhlbHCWJa23": {"name": "func", "params": [{"__ref": "-iyRC1m7wHtK"}], "__type": "FunctionType"}, "VlmbL-8kAXuO": {"name": "On Show Start Icon change", "uuid": "ww3Si1IosQNB", "__type": "Var"}, "GKY65zdyaGm5": {"name": "func", "params": [{"__ref": "JxJzdL4--2Oj"}], "__type": "FunctionType"}, "eYI9O6t7OnKb": {"name": "On Show End Icon change", "uuid": "ppqg7wew2q2i", "__type": "Var"}, "zzIVxGGzdONS": {"name": "func", "params": [{"__ref": "LBO_elsHqmIw"}], "__type": "FunctionType"}, "iKExVzQU5s2P": {"name": "On Is Disabled change", "uuid": "Jh4mSRYjoyjV", "__type": "Var"}, "TgbZ81fKZV0z": {"name": "func", "params": [{"__ref": "3v1bLAfrEZWG"}], "__type": "FunctionType"}, "HH0gHf_oxbxU": {"name": "On Shape change", "uuid": "AlH0NUKVbNVf", "__type": "Var"}, "6LLvqWFBePG4": {"name": "func", "params": [{"__ref": "5dw7XUPa1LxK"}], "__type": "FunctionType"}, "1xZKjlpnQ-As": {"name": "On Size change", "uuid": "GHdBwEhHs3X5", "__type": "Var"}, "Y31P8XVdxCbb": {"name": "func", "params": [{"__ref": "fU8MT4fpD-Ii"}], "__type": "FunctionType"}, "xrCqx2QJpDzl": {"name": "On Color change", "uuid": "4BIcQFAAJJUH", "__type": "Var"}, "2Dgc6Ig54uEo": {"tag": "div", "name": "start icon container", "children": [{"__ref": "3O7tMnP9o6kR"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "qjiYNBhvsfRc", "parent": {"__ref": "9zjLiEIg0Ncz"}, "locked": null, "vsettings": [{"__ref": "zARU38gSwZGD"}, {"__ref": "GhFSfx8Y5-A0"}, {"__ref": "B6OU2KFYfsuv"}, {"__ref": "1EaOXQCvgoQ5"}], "__type": "TplTag"}, "Lo6MQPm-J7NA": {"tag": "div", "name": "content container", "children": [{"__ref": "3U__C9ILXC6x"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "vTyzRu5r-dFO", "parent": {"__ref": "9zjLiEIg0Ncz"}, "locked": null, "vsettings": [{"__ref": "qhUlWlTuFynh"}, {"__ref": "vMNr7g_iaVYY"}, {"__ref": "ej2Hx9aV3v2o"}, {"__ref": "11TaqDEP0b2p"}, {"__ref": "dSTtlbldAzQ5"}], "__type": "TplTag"}, "Dg69fxMRfg-o": {"tag": "div", "name": "end icon container", "children": [{"__ref": "1mms1CPIzu7Q"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fPFdPYQi1LDS", "parent": {"__ref": "9zjLiEIg0Ncz"}, "locked": null, "vsettings": [{"__ref": "q5u4qLPleTd5"}, {"__ref": "pS21x2bcKn0b"}, {"__ref": "e2Pb1Xz1pKKP"}, {"__ref": "5FFytcEU2mWD"}], "__type": "TplTag"}, "qV-mG7Xz4iIP": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "CVUvn4iL4GI_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "f404EbS6Whop": {"variants": [{"__ref": "7JaZkaz7FKrX"}], "args": [], "attrs": {}, "rs": {"__ref": "evGYQBeYSOw_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BMNzVMR6NfX-": {"variants": [{"__ref": "ccD0O-b22xgL"}], "args": [], "attrs": {}, "rs": {"__ref": "CfO4l0frCv-m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KjBk-MgP_v2Z": {"variants": [{"__ref": "3jNcACuRZH2N"}], "args": [], "attrs": {}, "rs": {"__ref": "K3XSWhLHyACX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "An7KPcDOUbz1": {"variants": [{"__ref": "fiZpOiUmqWUj"}], "args": [], "attrs": {}, "rs": {"__ref": "MF_yj2PyURdM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dtH2bM7lEaae": {"variants": [{"__ref": "lw1r0YN71__P"}], "args": [], "attrs": {}, "rs": {"__ref": "T1PBFFCxhbbi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Io3m9Fh1mSnN": {"variants": [{"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "cz1yqWokPdG4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "712uGGmYvevN": {"variants": [{"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "pY69vQLRiiQK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "b31gMtKvtTOR": {"variants": [{"__ref": "x2ZuXQtQ7fsO"}], "args": [], "attrs": {}, "rs": {"__ref": "7B7glDOqsbZH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aJsy6tOM7J4H": {"variants": [{"__ref": "x2ZuXQtQ7fsO"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "aGJr_fHlPDjv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "g9SJOv7RltaO": {"variants": [{"__ref": "x2ZuXQtQ7fsO"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "UmOah5czpT8z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "05TYtFaIBmsR": {"variants": [{"__ref": "TMmVv5yI_Gl6"}], "args": [], "attrs": {}, "rs": {"__ref": "2Sxm63rpbVpi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kxbW1J3Slrwa": {"variants": [{"__ref": "TMmVv5yI_Gl6"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "j6rI2PbuXtgL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Ws6wQk_vhcFs": {"variants": [{"__ref": "TMmVv5yI_Gl6"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "3nUbO3jZMT6W"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-mb5rzHbA43-": {"variants": [{"__ref": "boNkne008VC3"}], "args": [], "attrs": {}, "rs": {"__ref": "RqG3BY5gnct6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Ftw9bK1goewL": {"variants": [{"__ref": "boNkne008VC3"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "br8zHIPnUjhA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TZzaMqukaS5X": {"variants": [{"__ref": "boNkne008VC3"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "09EhqKOZyES8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "G9bN6EiBBdpv": {"variants": [{"__ref": "8O-Ip7hwZDj3"}], "args": [], "attrs": {}, "rs": {"__ref": "R86lSFROT-AE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lsCyPZG3fTiW": {"variants": [{"__ref": "8O-Ip7hwZDj3"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "UKkdz_OPOFLO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-licVd-vRaZn": {"variants": [{"__ref": "8O-Ip7hwZDj3"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "Pu5IWPJvJ80c"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6mV0YGv0RX0X": {"variants": [{"__ref": "r8t_K3NKv6FJ"}], "args": [], "attrs": {}, "rs": {"__ref": "eF60aYXs9mdy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4fkok7d1PrTN": {"variants": [{"__ref": "r8t_K3NKv6FJ"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "8tDzrGsDxxES"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KKyOlTp6Tp6P": {"variants": [{"__ref": "r8t_K3NKv6FJ"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "IJrDItE4Hb4q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IKmXiEjcK_1G": {"variants": [{"__ref": "92QGXrl6wD1Q"}], "args": [], "attrs": {}, "rs": {"__ref": "4LOys6gdNogn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RgSxUQrMyKF5": {"variants": [{"__ref": "92QGXrl6wD1Q"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "6T8c700n032c"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NF7x8L3YQKyq": {"variants": [{"__ref": "92QGXrl6wD1Q"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "_eowpJVCbNtv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qJ0p0uNNu3rf": {"variants": [{"__ref": "bgUfLcScXBSM"}], "args": [], "attrs": {}, "rs": {"__ref": "AoKTzx-KKvMc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PkQJMCg6ESUJ": {"variants": [{"__ref": "bgUfLcScXBSM"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "mUD4BOjA93KM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "L9pCWLCereEq": {"variants": [{"__ref": "bgUfLcScXBSM"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "uNNXwKhR8tE5"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HvMndTShdgHX": {"variants": [{"__ref": "-4w4Dy07a9cq"}], "args": [], "attrs": {}, "rs": {"__ref": "j321L_yDx2DW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sCdOBDvQtfvg": {"variants": [{"__ref": "-4w4Dy07a9cq"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "YdWlyqFUbKYz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "anJ4azG8ebou": {"variants": [{"__ref": "-4w4Dy07a9cq"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "QQ0k3_JoJz97"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0OMwg6FXmOX0": {"variants": [{"__ref": "_3z_P-NNtxuv"}], "args": [], "attrs": {}, "rs": {"__ref": "Sr-3fCyPVhFj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "t7d8xUHE0ntQ": {"variants": [{"__ref": "_3z_P-NNtxuv"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "XKz-x5z0UMuM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "d9x0iiyIr168": {"variants": [{"__ref": "_3z_P-NNtxuv"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "mOT2gjv0mOlr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NIvOLY_okG5K": {"variants": [{"__ref": "vHnYd1i6wg4G"}], "args": [], "attrs": {}, "rs": {"__ref": "zZGWKw0-O4jN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AzQdzKvaudfB": {"variants": [{"__ref": "vHnYd1i6wg4G"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "6DInHNha74AS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HwS5GCfC2ksC": {"variants": [{"__ref": "vHnYd1i6wg4G"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "sG2LbEdMGk-D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IBDWfX6_7REc": {"variants": [{"__ref": "UlUyAvXIG8Ed"}], "args": [], "attrs": {}, "rs": {"__ref": "j89ga6RHtmGt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zJY2V-tgfCFV": {"variants": [{"__ref": "UlUyAvXIG8Ed"}, {"__ref": "lw1r0YN71__P"}], "args": [], "attrs": {}, "rs": {"__ref": "9-FZ1BRqx4Pj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3En0jRSe58QI": {"variants": [{"__ref": "UlUyAvXIG8Ed"}, {"__ref": "lw1r0YN71__P"}, {"__ref": "fiZpOiUmqWUj"}], "args": [], "attrs": {}, "rs": {"__ref": "IambZNlzXiyk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nzz7xwj_3ba3": {"variants": [{"__ref": "UlUyAvXIG8Ed"}, {"__ref": "fiZpOiUmqWUj"}], "args": [], "attrs": {}, "rs": {"__ref": "gi4BXkBOxPkN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Hf-aUi2s6-TN": {"variants": [{"__ref": "M7BJw2PREMmA"}], "args": [], "attrs": {}, "rs": {"__ref": "hRdaP4YseHVk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "U2lAij9bEg6V": {"variants": [{"__ref": "M7BJw2PREMmA"}, {"__ref": "lw1r0YN71__P"}], "args": [], "attrs": {}, "rs": {"__ref": "9FOtRBHsLB7m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PjUl44BjdlfP": {"variants": [{"__ref": "fiZpOiUmqWUj"}, {"__ref": "M7BJw2PREMmA"}], "args": [], "attrs": {}, "rs": {"__ref": "j31tCzJTN0iF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oAjs76W1jI0N": {"variants": [{"__ref": "UlUyAvXIG8Ed"}, {"__ref": "M7BJw2PREMmA"}], "args": [], "attrs": {}, "rs": {"__ref": "VmD8RxkwkQgQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Q6e0SsSj9mFp": {"variants": [{"__ref": "pLXcb85mRSWS"}], "args": [], "attrs": {}, "rs": {"__ref": "NTWKpUInxySE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "v_Sx897QKApm": {"variants": [{"__ref": "pLXcb85mRSWS"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "YQt89WM2b7G7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zcsAZ0YWGiND": {"variants": [{"__ref": "pLXcb85mRSWS"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "AEfMqGTJ4FBT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I9SyyKsjaCpB": {"variants": [{"__ref": "v558s9LW1FOf"}], "args": [], "attrs": {}, "rs": {"__ref": "9V92DxsY9Rdj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "snIGTb3PXoA3": {"variants": [{"__ref": "v558s9LW1FOf"}, {"__ref": "UlUyAvXIG8Ed"}], "args": [], "attrs": {}, "rs": {"__ref": "qS_9okpBMl8_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MYDdWFj8w6iS": {"variants": [{"__ref": "cEIRMhDKPhmd"}], "args": [], "attrs": {}, "rs": {"__ref": "xChZKiCNi26s"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-qLExC1MruoH": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "B4IbxjTSZbE-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YqgIb0eJCezx": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "KDMu-TjFh2Va"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OYniY4RC4-CG": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "YzEzC7zE3c0B"}], "args": [], "attrs": {}, "rs": {"__ref": "os9pJH8eazCP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NZoKFIbLdQ-F": {"variants": [{"__ref": "YzEzC7zE3c0B"}], "args": [], "attrs": {}, "rs": {"__ref": "4swmnS4mCg2T"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IunVG_pB4NQX": {"variants": [{"__ref": "jyyU9ZyH-Cnl"}], "args": [], "attrs": {}, "rs": {"__ref": "4SWOQzcYytyS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5OINqmDHJLLN": {"variants": [{"__ref": "jyyU9ZyH-Cnl"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "HfoeerIZPK6M"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fWVH5jKH_zt-": {"variants": [{"__ref": "jyyU9ZyH-Cnl"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "JH0vrRgTt3-B"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bpG2n6yhqlqH": {"variants": [{"__ref": "OrW3UwYyLXo2"}], "args": [], "attrs": {}, "rs": {"__ref": "ZPberRVdHRds"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lw1r0YN71__P": {"uuid": "7N879-N-ObFN", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "9Wl6VZg3d1J-"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "fiZpOiUmqWUj": {"uuid": "QM4aKQL1ZRPM", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "88t-l2ADtFF4"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3jNcACuRZH2N": {"uuid": "-5qxcNAiejVc", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "ZY4ClNl1HTUM"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "M7BJw2PREMmA": {"uuid": "7ROQKo01u84T", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "VnWWeW7kkZg-"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "v558s9LW1FOf": {"uuid": "n-bXx31UXhmS", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "VnWWeW7kkZg-"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "OrW3UwYyLXo2": {"uuid": "1WGnPYVINrRK", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "VnWWeW7kkZg-"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "UlUyAvXIG8Ed": {"uuid": "f048DQAMqZ8l", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "FPozX2FFynfB"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "YzEzC7zE3c0B": {"uuid": "_kxXdqbwDk4b", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "FPozX2FFynfB"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "-4w4Dy07a9cq": {"uuid": "hX_XBc2D8SQO", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "_3z_P-NNtxuv": {"uuid": "CxJdwKJtpMPt", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "boNkne008VC3": {"uuid": "qtFk_pcfWbxR", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8O-Ip7hwZDj3": {"uuid": "eHwfYkiHfGJS", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "vHnYd1i6wg4G": {"uuid": "HcfO9rj6fcna", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "jyyU9ZyH-Cnl": {"uuid": "VNX7huUnUzoI", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "x2ZuXQtQ7fsO": {"uuid": "FgRPgjOf_V3P", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "TMmVv5yI_Gl6": {"uuid": "gMLod8tWx378", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "r8t_K3NKv6FJ": {"uuid": "N6FuIllUAhuP", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "92QGXrl6wD1Q": {"uuid": "SNFyJA-IXgw9", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "bgUfLcScXBSM": {"uuid": "AskHxUtyC6OG", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "pLXcb85mRSWS": {"uuid": "t5YsSmixqExL", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "cEIRMhDKPhmd": {"uuid": "MJF5xZJFe13B", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "kCWv_SoTcPf2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "tpcTq70pG41Q": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "405K-lW9QuJh", "parent": {"__ref": "fZZ26QuQBwEJ"}, "locked": null, "vsettings": [{"__ref": "ii0tJ-xJ9g0S"}], "__type": "TplTag"}, "ENNBUiWlvnaB": {"name": null, "component": {"__ref": "a8rMN9AF25rn"}, "uuid": "-6adr0OBJ2uX", "parent": {"__ref": "fZZ26QuQBwEJ"}, "locked": null, "vsettings": [{"__ref": "6dt-qRgcM4cK"}], "__type": "TplComponent"}, "huPurRLsIC7Q": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "Qw6m13SM0E3a", "parent": {"__ref": "fZZ26QuQBwEJ"}, "locked": null, "vsettings": [{"__ref": "EtXM5a0qmPaY"}], "__type": "TplComponent"}, "OueZnU6ai0mQ": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "HhyBaHwJBzte", "parent": {"__ref": "fZZ26QuQBwEJ"}, "locked": null, "vsettings": [{"__ref": "LtFolduluWQZ"}], "__type": "TplComponent"}, "zNDImrHBVakX": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {}, "rs": {"__ref": "nhCLNN3T7wNA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7Znh9Au23Oj1": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "iftBEnRnajt4", "parent": {"__ref": "5cyCIeJxHOMF"}, "locked": null, "vsettings": [{"__ref": "fV9dZ4--7pik"}], "__type": "TplTag"}, "VtNdlR3M1IIE": {"name": null, "component": {"__ref": "a8rMN9AF25rn"}, "uuid": "EQ7c8JzPK8lo", "parent": {"__ref": "5cyCIeJxHOMF"}, "locked": null, "vsettings": [{"__ref": "ot3yt799v2L2"}], "__type": "TplComponent"}, "shm24WXzWsGX": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "0Y6ba6JDCETF", "parent": {"__ref": "5cyCIeJxHOMF"}, "locked": null, "vsettings": [{"__ref": "TNsYFPyxXbSA"}], "__type": "TplComponent"}, "T_DgB2IOuVM6": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "9pwSpE1oLRjg", "parent": {"__ref": "5cyCIeJxHOMF"}, "locked": null, "vsettings": [{"__ref": "J2Mb-N__9YZ1"}], "__type": "TplComponent"}, "YC3ptNOU57XD": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {}, "rs": {"__ref": "FeCp-RDIH_bh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "feIoxP-v0E7k": {"cols": [{"__ref": "-RwRtxrGjdxm"}, {"__ref": "gK7QK2PK8gWR"}], "rowKey": {"__ref": "seqJtOa-qmF5"}, "__type": "ArenaFrameRow"}, "KSQ0o-UIRfeV": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "iZPPFrpI3Gjn": {"cols": [{"__ref": "YM6MKPvTzcc0"}, {"__ref": "cTDOI8ApAfrW"}], "rowKey": {"__ref": "AALHp7VW3AZi"}, "__type": "ArenaFrameRow"}, "8x91rEnzly8M": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "nrrGLcfj4n5Q": {"cols": [{"__ref": "2J14VNy-uyAp"}, {"__ref": "GIBAPerGBs6n"}], "rowKey": {"__ref": "jY5r_aRO-4Eb"}, "__type": "ArenaFrameRow"}, "G64dAjy6QOUQ": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "sfvf4fyk_c-R": {"cols": [{"__ref": "so7YdZMbUnVf"}, {"__ref": "_lamLQKsUN35"}], "rowKey": {"__ref": "1sj2PKIZW7Yv"}, "__type": "ArenaFrameRow"}, "WCtsOR41Cx8v": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "OsSo8Cn6ZFx-": {"cols": [{"__ref": "l1iUiOaCmDTr"}], "rowKey": null, "__type": "ArenaFrameRow"}, "N9DhmRz86rBm": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "uQu_90ooan47": {"cols": [{"__ref": "AYvEiJBCYS0F"}, {"__ref": "jtWmSfYEVTX_"}, {"__ref": "X0yjaMsCMwlL"}, {"__ref": "Bs_SOYBGAmyX"}, {"__ref": "PLypjSOG3T7n"}], "rowKey": null, "__type": "ArenaFrameRow"}, "X-trpfODfO6G": {"cols": [{"__ref": "XSJxdrIJpg9d"}], "rowKey": {"__ref": "9Wl6VZg3d1J-"}, "__type": "ArenaFrameRow"}, "PCmjjbiypvWn": {"cols": [{"__ref": "XhcK80u5VLpV"}], "rowKey": {"__ref": "88t-l2ADtFF4"}, "__type": "ArenaFrameRow"}, "KJ6dirPs9Z0L": {"cols": [{"__ref": "z0jVRikrvrC7"}], "rowKey": {"__ref": "ZY4ClNl1HTUM"}, "__type": "ArenaFrameRow"}, "VlKXajVmGkoA": {"cols": [{"__ref": "igz0Ebwz1qNz"}, {"__ref": "clinUicTx4x8"}, {"__ref": "FxeczH2v1713"}], "rowKey": {"__ref": "VnWWeW7kkZg-"}, "__type": "ArenaFrameRow"}, "m5xVkDKI23iD": {"cols": [{"__ref": "pUE56FSlGotx"}, {"__ref": "PWnIepxyrUZk"}], "rowKey": {"__ref": "FPozX2FFynfB"}, "__type": "ArenaFrameRow"}, "pv1TwBeEwboQ": {"cols": [{"__ref": "EFzacB_rmDOx"}, {"__ref": "cOGzwNWmK4AU"}, {"__ref": "S98aZb8kk6Id"}, {"__ref": "DoKrf81eBNkW"}, {"__ref": "qlzSxFi8TkDN"}, {"__ref": "MlhUgYgQoAuK"}, {"__ref": "gy5J2qByAsaE"}, {"__ref": "6GXMnG_zLki8"}, {"__ref": "J6K9PcBXIJ0M"}, {"__ref": "xrpAStH_y9vm"}, {"__ref": "BCWRs__ojVsW"}, {"__ref": "eOLdOMKhAdL0"}, {"__ref": "8Ya8XSSL2ZMF"}], "rowKey": {"__ref": "kCWv_SoTcPf2"}, "__type": "ArenaFrameRow"}, "uYIMxDI-r4r4": {"cols": [{"__ref": "WtinUrTB8xNM"}, {"__ref": "LmcfLBB_lfVK"}, {"__ref": "y3aElyv1nWFV"}, {"__ref": "d4_FW7i2xk4t"}, {"__ref": "SzM45sXWzosf"}, {"__ref": "DDlsk9DTT1p2"}, {"__ref": "S8y_aw1RAxP4"}, {"__ref": "FrBc5DvZkp9U"}, {"__ref": "LzqzL9XTMrR8"}, {"__ref": "Iv-JDzlnVDaU"}, {"__ref": "Vj923I55L0-R"}, {"__ref": "9ZLLCUNJbX9f"}, {"__ref": "Kz2V4LXzhJYL"}, {"__ref": "B5bgpbakBPwU"}, {"__ref": "Vvwzbh6POXj8"}, {"__ref": "R5i_SeJqJGSB"}, {"__ref": "Bmc4VD7-A6YT"}, {"__ref": "fko7kWt1Cw4r"}, {"__ref": "3VJe-SC8Ut24"}, {"__ref": "dQRFk3U4M6rR"}, {"__ref": "5qhGpIvoFsAn"}, {"__ref": "hBSaP0selCWw"}, {"__ref": "bu56l7IUAvZP"}, {"__ref": "zkT4Jbm_kBoV"}, {"__ref": "0v0Mm9VLM6nK"}, {"__ref": "KExLzFZv_Z2-"}, {"__ref": "i8wnZaks0J8C"}, {"__ref": "ZqCoqaOJg5TZ"}, {"__ref": "KAq0z4xpAadn"}], "rowKey": null, "__type": "ArenaFrameRow"}, "1zwSlcpRmrLN": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "OR5ye9_9daOX": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "DDyaN3nh2FwC"}, "__type": "ArgType"}, "xS07QNqBEthj": {"variants": [{"__ref": "DCWFJx8gDwGx"}], "args": [], "attrs": {}, "rs": {"__ref": "UQlnBpfFE7EU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bmcT-vL03pVQ": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "rzekLu49ngdg": {"variants": [{"__ref": "seqJtOa-qmF5"}], "args": [], "attrs": {}, "rs": {"__ref": "sJG4KQlk3Ry2"}, "dataCond": null, "dataRep": null, "text": {"__ref": "tGUYaoCEC0-8"}, "columnsConfig": null, "__type": "VariantSetting"}, "O4X9JWM_SWPJ": {"variants": [{"__ref": "seqJtOa-qmF5"}], "args": [], "attrs": {}, "rs": {"__ref": "qYYa5RJWh6-N"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zP7KGIURvA4h": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "grid-row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "vrjxnL_es4vC": {"variants": [{"__ref": "v4tW8WYIop8_"}], "args": [], "attrs": {}, "rs": {"__ref": "6MkmWl_dGLJb"}, "dataCond": null, "dataRep": null, "text": {"__ref": "iuBLlxu6D9Se"}, "columnsConfig": null, "__type": "VariantSetting"}, "59cY9X6Rbcvt": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch"}, "mixins": [], "__type": "RuleSet"}, "cFlFZJVy8J58": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {}, "rs": {"__ref": "8GDcAHZJ9Iex"}, "dataCond": null, "dataRep": null, "text": {"__ref": "jelGYWgM7H9M"}, "columnsConfig": null, "__type": "VariantSetting"}, "zOLjt3USQzVM": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {}, "rs": {"__ref": "R_tKhFLjnaeM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LN2KtYKC-tWN": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [{"__ref": "47MrJovNXlLg"}, {"__ref": "XcsLNRqrcgQN"}, {"__ref": "9gaSfwK5aKWq"}], "attrs": {"onClick": {"__ref": "rLPn6UezMT8a"}}, "rs": {"__ref": "hYIsthKbDPVU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GKRoyHHvZdJa": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [{"__ref": "jfsvpuV6Op2W"}, {"__ref": "Ijaj73-ail3E"}, {"__ref": "ZB3FzOFeBqaa"}], "attrs": {"onClick": {"__ref": "4NdwgWegFCX6"}}, "rs": {"__ref": "08jBYagWlrJm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XR2JqVxIpl72": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "grid-row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "-iyRC1m7wHtK": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "YuJ_X4FniFUD"}, "__type": "ArgType"}, "JxJzdL4--2Oj": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "nA51w0wNkyz5"}, "__type": "ArgType"}, "LBO_elsHqmIw": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "QbbrzktVT7Mt"}, "__type": "ArgType"}, "3v1bLAfrEZWG": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "iKMKVPkgjqCc"}, "__type": "ArgType"}, "5dw7XUPa1LxK": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "bIzq4QqjEEL2"}, "__type": "ArgType"}, "fU8MT4fpD-Ii": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "WCunkaItA1ZS"}, "__type": "ArgType"}, "3O7tMnP9o6kR": {"param": {"__ref": "8sRSz5t67XL5"}, "defaultContents": [{"__ref": "G3yjMuLcVYsv"}], "uuid": "ORdAW89gxqtg", "parent": {"__ref": "2Dgc6Ig54uEo"}, "locked": null, "vsettings": [{"__ref": "1JKgjWF9Ik6o"}, {"__ref": "vnPSt0Dy3H7V"}, {"__ref": "HTJb2Hpb3U6D"}, {"__ref": "bkDEj9SvOMM1"}, {"__ref": "sOfn3Iqim__s"}, {"__ref": "xjsvFgXkLGbw"}, {"__ref": "nrPY582-tCZI"}, {"__ref": "2R6A4TiR7SFy"}, {"__ref": "ZXhjmCvqhoVR"}, {"__ref": "Qa6vUOd3HtVg"}, {"__ref": "GrFRKNVf5BQg"}, {"__ref": "TI4vHDQomJah"}, {"__ref": "nuk_ru50x-Uj"}, {"__ref": "5AhP2A1ZtJyp"}], "__type": "TplSlot"}, "zARU38gSwZGD": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "4iwuWXYy8hIJ"}, "dataCond": {"__ref": "EyrySm_JlqRX"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GhFSfx8Y5-A0": {"variants": [{"__ref": "lw1r0YN71__P"}], "args": [], "attrs": {}, "rs": {"__ref": "p-WQK9goOhOx"}, "dataCond": {"__ref": "xvh9E2RWsrCI"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B6OU2KFYfsuv": {"variants": [{"__ref": "-4w4Dy07a9cq"}], "args": [], "attrs": {}, "rs": {"__ref": "ufh06HcEznww"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1EaOXQCvgoQ5": {"variants": [{"__ref": "M7BJw2PREMmA"}, {"__ref": "lw1r0YN71__P"}], "args": [], "attrs": {}, "rs": {"__ref": "WUvFUhZAI6hd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3U__C9ILXC6x": {"param": {"__ref": "7O6qHUygi0o-"}, "defaultContents": [{"__ref": "GdE9cpufg4IG"}], "uuid": "DU0ISQtuTo8d", "parent": {"__ref": "Lo6MQPm-J7NA"}, "locked": null, "vsettings": [{"__ref": "ijHgpCgD-1TV"}, {"__ref": "oE1LlbozY7Zd"}, {"__ref": "5j8KWZX3A5js"}, {"__ref": "E_6K9CmavLmj"}, {"__ref": "lGoQkH8CCIQr"}, {"__ref": "Svpk3USpGwfe"}, {"__ref": "LVFiEqqyWrQT"}, {"__ref": "IByuipLkjCEc"}, {"__ref": "lAea1-pzWhZE"}, {"__ref": "z33lEm_XcSIs"}, {"__ref": "zXGAZXAjCZ1t"}, {"__ref": "MU0NBoDwzpaT"}, {"__ref": "5tbPery-Gt_j"}, {"__ref": "42dE3owkuD31"}, {"__ref": "xFMOCm48oJ1v"}, {"__ref": "J7yQBbqazPaS"}, {"__ref": "7TeA4-Rz7IgY"}, {"__ref": "3GQD5UbE8k-p"}, {"__ref": "fZZs6XFTyHpk"}, {"__ref": "OvLc86KL2K4q"}, {"__ref": "bYc9UrNKqTnA"}, {"__ref": "43VOt51xbrej"}, {"__ref": "ndYGmlAeSS7L"}, {"__ref": "t9uiZXOt3f26"}, {"__ref": "kGSPlVDaAiHe"}, {"__ref": "MfGbveLrpIFQ"}], "__type": "TplSlot"}, "qhUlWlTuFynh": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "Pl_6uJJVhX5r"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vMNr7g_iaVYY": {"variants": [{"__ref": "3jNcACuRZH2N"}], "args": [], "attrs": {}, "rs": {"__ref": "jyKa-rSTaQ84"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ej2Hx9aV3v2o": {"variants": [{"__ref": "fiZpOiUmqWUj"}], "args": [], "attrs": {}, "rs": {"__ref": "Ua_5nDnQw-s0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "11TaqDEP0b2p": {"variants": [{"__ref": "7JaZkaz7FKrX"}], "args": [], "attrs": {}, "rs": {"__ref": "g4_Y5xanjpDV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dSTtlbldAzQ5": {"variants": [{"__ref": "M7BJw2PREMmA"}], "args": [], "attrs": {}, "rs": {"__ref": "8RsRbMWkC4LW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1mms1CPIzu7Q": {"param": {"__ref": "eOpE3ydfotZ1"}, "defaultContents": [{"__ref": "DcOJdEp5pPqi"}], "uuid": "gZ1hOMDa9xoA", "parent": {"__ref": "Dg69fxMRfg-o"}, "locked": null, "vsettings": [{"__ref": "t1hYe0CJiBPF"}, {"__ref": "nbM21PRxfqqV"}, {"__ref": "Vn23UMIOa9A6"}, {"__ref": "mzx-ROXMy8-g"}, {"__ref": "e_ScvkFRV4gb"}, {"__ref": "j2s0Wz_CtB0g"}, {"__ref": "xY0Lr4ZonU7d"}, {"__ref": "os1CEoCywoSl"}, {"__ref": "H54aAfQmZCjP"}, {"__ref": "C6mYJFvmChBQ"}, {"__ref": "4MySJTw2bT2G"}, {"__ref": "PW3Nu8_M9DpA"}, {"__ref": "TScG9C6vWwup"}], "__type": "TplSlot"}, "q5u4qLPleTd5": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "hi59mUdDrVSY"}, "dataCond": {"__ref": "rQAn_1f5oje1"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pS21x2bcKn0b": {"variants": [{"__ref": "fiZpOiUmqWUj"}], "args": [], "attrs": {}, "rs": {"__ref": "p_cFdKMG800Z"}, "dataCond": {"__ref": "n85x50wTfAso"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "e2Pb1Xz1pKKP": {"variants": [{"__ref": "boNkne008VC3"}], "args": [], "attrs": {}, "rs": {"__ref": "Z0YiCFU3ilUt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5FFytcEU2mWD": {"variants": [{"__ref": "jyyU9ZyH-Cnl"}], "args": [], "attrs": {}, "rs": {"__ref": "7V2LHk43Gz1m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CVUvn4iL4GI_": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "flex-column-gap": "8px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px"}, "mixins": [], "__type": "RuleSet"}, "evGYQBeYSOw_": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "CfO4l0frCv-m": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "K3XSWhLHyACX": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "MF_yj2PyURdM": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "T1PBFFCxhbbi": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "cz1yqWokPdG4": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "pY69vQLRiiQK": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "7B7glDOqsbZH": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "aGJr_fHlPDjv": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "UmOah5czpT8z": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "2Sxm63rpbVpi": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "j6rI2PbuXtgL": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "3nUbO3jZMT6W": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "RqG3BY5gnct6": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "br8zHIPnUjhA": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "09EhqKOZyES8": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "R86lSFROT-AE": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "UKkdz_OPOFLO": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "Pu5IWPJvJ80c": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "eF60aYXs9mdy": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "8tDzrGsDxxES": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "IJrDItE4Hb4q": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "4LOys6gdNogn": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "6T8c700n032c": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "_eowpJVCbNtv": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "AoKTzx-KKvMc": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "mUD4BOjA93KM": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "uNNXwKhR8tE5": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "j321L_yDx2DW": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "YdWlyqFUbKYz": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "QQ0k3_JoJz97": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "Sr-3fCyPVhFj": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "XKz-x5z0UMuM": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "mOT2gjv0mOlr": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "zZGWKw0-O4jN": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "6DInHNha74AS": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "sG2LbEdMGk-D": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "j89ga6RHtmGt": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "9-FZ1BRqx4Pj": {"values": {}, "mixins": [], "__type": "RuleSet"}, "IambZNlzXiyk": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gi4BXkBOxPkN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "hRdaP4YseHVk": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "9FOtRBHsLB7m": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "j31tCzJTN0iF": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "VmD8RxkwkQgQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NTWKpUInxySE": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "YQt89WM2b7G7": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "AEfMqGTJ4FBT": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "9V92DxsY9Rdj": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "qS_9okpBMl8_": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "xChZKiCNi26s": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "B4IbxjTSZbE-": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "KDMu-TjFh2Va": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "os9pJH8eazCP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4swmnS4mCg2T": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "4SWOQzcYytyS": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "HfoeerIZPK6M": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "JH0vrRgTt3-B": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "ZPberRVdHRds": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "ii0tJ-xJ9g0S": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {}, "rs": {"__ref": "uveZh_2HwvMj"}, "dataCond": null, "dataRep": null, "text": {"__ref": "zfWUGu0urqwE"}, "columnsConfig": null, "__type": "VariantSetting"}, "6dt-qRgcM4cK": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {}, "rs": {"__ref": "HsvekfSqJlhl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EtXM5a0qmPaY": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [{"__ref": "eIFykPLJnRq3"}, {"__ref": "dLHfuiSjLaFO"}, {"__ref": "vsoZw2r8Qt-s"}], "attrs": {"onClick": {"__ref": "KRqswG6nDJHl"}}, "rs": {"__ref": "YtBnpY4Dfu5T"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LtFolduluWQZ": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [{"__ref": "xx6EjKSolSDr"}, {"__ref": "4q2gsf2hQLQR"}, {"__ref": "OcjDizLeIgFx"}], "attrs": {"onClick": {"__ref": "TNtOoFfMzX5-"}}, "rs": {"__ref": "6Zty69AUqbRX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nhCLNN3T7wNA": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "grid-row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "fV9dZ4--7pik": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {}, "rs": {"__ref": "ss3u74clESh9"}, "dataCond": null, "dataRep": null, "text": {"__ref": "8GOZkDH74JpY"}, "columnsConfig": null, "__type": "VariantSetting"}, "ot3yt799v2L2": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {}, "rs": {"__ref": "2ltzCKZcd-_D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TNsYFPyxXbSA": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [{"__ref": "vaA43Os-fXun"}, {"__ref": "tZkYRHbhR--O"}, {"__ref": "AZDV24U5W13U"}], "attrs": {"onClick": {"__ref": "CgI5PPe4JnHQ"}}, "rs": {"__ref": "4BLDFTfpRunB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "J2Mb-N__9YZ1": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [{"__ref": "2YAg-Q4Vz8zW"}, {"__ref": "Lmt5A83tYQGf"}, {"__ref": "c_sDW1gzRmeB"}], "attrs": {"onClick": {"__ref": "u0KLSO9FxGHG"}}, "rs": {"__ref": "YiE-Hmt0GfLM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FeCp-RDIH_bh": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "grid-row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "-RwRtxrGjdxm": {"frame": {"__ref": "iB2jXa4ARSRj"}, "cellKey": null, "__type": "ArenaFrameCell"}, "gK7QK2PK8gWR": {"frame": {"__ref": "LcRXJcqzmYmK"}, "cellKey": null, "__type": "ArenaFrameCell"}, "YM6MKPvTzcc0": {"frame": {"__ref": "fTts4YXlI_4-"}, "cellKey": null, "__type": "ArenaFrameCell"}, "cTDOI8ApAfrW": {"frame": {"__ref": "B97759w0V8ll"}, "cellKey": null, "__type": "ArenaFrameCell"}, "2J14VNy-uyAp": {"frame": {"__ref": "BxK-LplzAY3d"}, "cellKey": null, "__type": "ArenaFrameCell"}, "GIBAPerGBs6n": {"frame": {"__ref": "eHpqEXtoWK-1"}, "cellKey": null, "__type": "ArenaFrameCell"}, "so7YdZMbUnVf": {"frame": {"__ref": "y9I1fHnjE5N0"}, "cellKey": null, "__type": "ArenaFrameCell"}, "_lamLQKsUN35": {"frame": {"__ref": "0pte92v1qO5d"}, "cellKey": null, "__type": "ArenaFrameCell"}, "l1iUiOaCmDTr": {"frame": {"__ref": "gBhNjGWJMVrM"}, "cellKey": {"__ref": "v4tW8WYIop8_"}, "__type": "ArenaFrameCell"}, "AYvEiJBCYS0F": {"frame": {"__ref": "bdpgGokqeqEk"}, "cellKey": {"__ref": "Z3-1oAqJmta8"}, "__type": "ArenaFrameCell"}, "jtWmSfYEVTX_": {"frame": {"__ref": "vxCT8sK37Dlq"}, "cellKey": {"__ref": "7JaZkaz7FKrX"}, "__type": "ArenaFrameCell"}, "X0yjaMsCMwlL": {"frame": {"__ref": "tRzzrDf61TT0"}, "cellKey": {"__ref": "ccD0O-b22xgL"}, "__type": "ArenaFrameCell"}, "Bs_SOYBGAmyX": {"frame": {"__ref": "5ATorNB9aiqc"}, "cellKey": {"__ref": "aMV_xZLTjaiR"}, "__type": "ArenaFrameCell"}, "PLypjSOG3T7n": {"frame": {"__ref": "4w1T4BsQNxnF"}, "cellKey": {"__ref": "MkyOndX3NfEk"}, "__type": "ArenaFrameCell"}, "XSJxdrIJpg9d": {"frame": {"__ref": "MeMy2-JIZJL-"}, "cellKey": {"__ref": "lw1r0YN71__P"}, "__type": "ArenaFrameCell"}, "XhcK80u5VLpV": {"frame": {"__ref": "TaJyNlAWWH6t"}, "cellKey": {"__ref": "fiZpOiUmqWUj"}, "__type": "ArenaFrameCell"}, "z0jVRikrvrC7": {"frame": {"__ref": "timPbepPGZvb"}, "cellKey": {"__ref": "3jNcACuRZH2N"}, "__type": "ArenaFrameCell"}, "igz0Ebwz1qNz": {"frame": {"__ref": "SbQ3EGISK4TB"}, "cellKey": {"__ref": "M7BJw2PREMmA"}, "__type": "ArenaFrameCell"}, "clinUicTx4x8": {"frame": {"__ref": "gH9MQ-uiXd7G"}, "cellKey": {"__ref": "v558s9LW1FOf"}, "__type": "ArenaFrameCell"}, "FxeczH2v1713": {"frame": {"__ref": "MtdKXE-eNwIA"}, "cellKey": {"__ref": "OrW3UwYyLXo2"}, "__type": "ArenaFrameCell"}, "pUE56FSlGotx": {"frame": {"__ref": "IYbe4Bb8b8YO"}, "cellKey": {"__ref": "UlUyAvXIG8Ed"}, "__type": "ArenaFrameCell"}, "PWnIepxyrUZk": {"frame": {"__ref": "Rw8M7LjxyeMq"}, "cellKey": {"__ref": "YzEzC7zE3c0B"}, "__type": "ArenaFrameCell"}, "EFzacB_rmDOx": {"frame": {"__ref": "TC8R2ZhDv2Je"}, "cellKey": {"__ref": "-4w4Dy07a9cq"}, "__type": "ArenaFrameCell"}, "cOGzwNWmK4AU": {"frame": {"__ref": "AdMWIkfUDFWw"}, "cellKey": {"__ref": "_3z_P-NNtxuv"}, "__type": "ArenaFrameCell"}, "S98aZb8kk6Id": {"frame": {"__ref": "fr5UlqSsYaKo"}, "cellKey": {"__ref": "boNkne008VC3"}, "__type": "ArenaFrameCell"}, "DoKrf81eBNkW": {"frame": {"__ref": "yEu8YctwbEut"}, "cellKey": {"__ref": "8O-Ip7hwZDj3"}, "__type": "ArenaFrameCell"}, "qlzSxFi8TkDN": {"frame": {"__ref": "TrZQZuQnOoai"}, "cellKey": {"__ref": "vHnYd1i6wg4G"}, "__type": "ArenaFrameCell"}, "MlhUgYgQoAuK": {"frame": {"__ref": "mI3VHOUxObVS"}, "cellKey": {"__ref": "jyyU9ZyH-Cnl"}, "__type": "ArenaFrameCell"}, "gy5J2qByAsaE": {"frame": {"__ref": "pfJuL170arYR"}, "cellKey": {"__ref": "x2ZuXQtQ7fsO"}, "__type": "ArenaFrameCell"}, "6GXMnG_zLki8": {"frame": {"__ref": "OGPYwTACdE92"}, "cellKey": {"__ref": "TMmVv5yI_Gl6"}, "__type": "ArenaFrameCell"}, "J6K9PcBXIJ0M": {"frame": {"__ref": "RMxDqcHlKXw5"}, "cellKey": {"__ref": "r8t_K3NKv6FJ"}, "__type": "ArenaFrameCell"}, "xrpAStH_y9vm": {"frame": {"__ref": "yzhfUytUgLEk"}, "cellKey": {"__ref": "92QGXrl6wD1Q"}, "__type": "ArenaFrameCell"}, "BCWRs__ojVsW": {"frame": {"__ref": "anSPv4zrLP5G"}, "cellKey": {"__ref": "bgUfLcScXBSM"}, "__type": "ArenaFrameCell"}, "eOLdOMKhAdL0": {"frame": {"__ref": "ruolXQinaQgK"}, "cellKey": {"__ref": "pLXcb85mRSWS"}, "__type": "ArenaFrameCell"}, "8Ya8XSSL2ZMF": {"frame": {"__ref": "9q2G0sfrnwfW"}, "cellKey": {"__ref": "cEIRMhDKPhmd"}, "__type": "ArenaFrameCell"}, "WtinUrTB8xNM": {"frame": {"__ref": "kCkPkuH4CwxV"}, "cellKey": [{"__ref": "x2ZuXQtQ7fsO"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "LmcfLBB_lfVK": {"frame": {"__ref": "7VLp-7kjEKt1"}, "cellKey": [{"__ref": "x2ZuXQtQ7fsO"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "y3aElyv1nWFV": {"frame": {"__ref": "Mm_-RO03WHmO"}, "cellKey": [{"__ref": "TMmVv5yI_Gl6"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "d4_FW7i2xk4t": {"frame": {"__ref": "_znN_r97IueL"}, "cellKey": [{"__ref": "TMmVv5yI_Gl6"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "SzM45sXWzosf": {"frame": {"__ref": "MP9F9PgknpXk"}, "cellKey": [{"__ref": "boNkne008VC3"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "DDlsk9DTT1p2": {"frame": {"__ref": "jpWJhmAA0F1b"}, "cellKey": [{"__ref": "boNkne008VC3"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "S8y_aw1RAxP4": {"frame": {"__ref": "6cph0cXF5aMu"}, "cellKey": [{"__ref": "8O-Ip7hwZDj3"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "FrBc5DvZkp9U": {"frame": {"__ref": "Qp3gc7fhPVU8"}, "cellKey": [{"__ref": "8O-Ip7hwZDj3"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "LzqzL9XTMrR8": {"frame": {"__ref": "E2GqrUCXaqgT"}, "cellKey": [{"__ref": "r8t_K3NKv6FJ"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "Iv-JDzlnVDaU": {"frame": {"__ref": "os2TRFCcoj8u"}, "cellKey": [{"__ref": "r8t_K3NKv6FJ"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "Vj923I55L0-R": {"frame": {"__ref": "EDWPgKX8bevF"}, "cellKey": [{"__ref": "92QGXrl6wD1Q"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "9ZLLCUNJbX9f": {"frame": {"__ref": "VvMFOceMTEgB"}, "cellKey": [{"__ref": "92QGXrl6wD1Q"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "Kz2V4LXzhJYL": {"frame": {"__ref": "tULF6FFyyylV"}, "cellKey": [{"__ref": "bgUfLcScXBSM"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "B5bgpbakBPwU": {"frame": {"__ref": "qP93fjX6DwcD"}, "cellKey": [{"__ref": "bgUfLcScXBSM"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "Vvwzbh6POXj8": {"frame": {"__ref": "5B_fhU_FRrHx"}, "cellKey": [{"__ref": "-4w4Dy07a9cq"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "R5i_SeJqJGSB": {"frame": {"__ref": "m6yX4Ij4xVlI"}, "cellKey": [{"__ref": "-4w4Dy07a9cq"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "Bmc4VD7-A6YT": {"frame": {"__ref": "AL28-xv8co9c"}, "cellKey": [{"__ref": "_3z_P-NNtxuv"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "fko7kWt1Cw4r": {"frame": {"__ref": "8lJbOj5Eo79S"}, "cellKey": [{"__ref": "_3z_P-NNtxuv"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "3VJe-SC8Ut24": {"frame": {"__ref": "wIr6l4iFnfJI"}, "cellKey": [{"__ref": "vHnYd1i6wg4G"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "dQRFk3U4M6rR": {"frame": {"__ref": "6aVo0AaN2XZ_"}, "cellKey": [{"__ref": "vHnYd1i6wg4G"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "5qhGpIvoFsAn": {"frame": {"__ref": "mflnTde2im55"}, "cellKey": [{"__ref": "M7BJw2PREMmA"}, {"__ref": "lw1r0YN71__P"}], "__type": "ArenaFrameCell"}, "hBSaP0selCWw": {"frame": {"__ref": "hrlO2Q09zkru"}, "cellKey": [{"__ref": "fiZpOiUmqWUj"}, {"__ref": "M7BJw2PREMmA"}], "__type": "ArenaFrameCell"}, "bu56l7IUAvZP": {"frame": {"__ref": "6Gb8FkfMjYKn"}, "cellKey": [{"__ref": "pLXcb85mRSWS"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "zkT4Jbm_kBoV": {"frame": {"__ref": "2goYqXI2rRtT"}, "cellKey": [{"__ref": "pLXcb85mRSWS"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "0v0Mm9VLM6nK": {"frame": {"__ref": "VhXUuPakbrI0"}, "cellKey": [{"__ref": "v558s9LW1FOf"}, {"__ref": "UlUyAvXIG8Ed"}], "__type": "ArenaFrameCell"}, "KExLzFZv_Z2-": {"frame": {"__ref": "Lla01SvUtiek"}, "cellKey": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "i8wnZaks0J8C": {"frame": {"__ref": "ZGjQKY3nngC6"}, "cellKey": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "ZqCoqaOJg5TZ": {"frame": {"__ref": "DzlEoJztOW9-"}, "cellKey": [{"__ref": "jyyU9ZyH-Cnl"}, {"__ref": "aMV_xZLTjaiR"}], "__type": "ArenaFrameCell"}, "KAq0z4xpAadn": {"frame": {"__ref": "fCbOi3IQNpIK"}, "cellKey": [{"__ref": "jyyU9ZyH-Cnl"}, {"__ref": "MkyOndX3NfEk"}], "__type": "ArenaFrameCell"}, "DDyaN3nh2FwC": {"name": "any", "__type": "AnyType"}, "UQlnBpfFE7EU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sJG4KQlk3Ry2": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "tGUYaoCEC0-8": {"markers": [], "text": "Homepage", "__type": "RawText"}, "qYYa5RJWh6-N": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "6MkmWl_dGLJb": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "iuBLlxu6D9Se": {"expr": {"__ref": "9PJtc3F4PZYM"}, "html": false, "__type": "ExprText"}, "8GDcAHZJ9Iex": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "jelGYWgM7H9M": {"markers": [], "text": "Page 1", "__type": "RawText"}, "R_tKhFLjnaeM": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "47MrJovNXlLg": {"param": {"__ref": "8sRSz5t67XL5"}, "expr": {"__ref": "CZu6QWPC5YFv"}, "__type": "Arg"}, "XcsLNRqrcgQN": {"param": {"__ref": "7O6qHUygi0o-"}, "expr": {"__ref": "Edv_8E3wxkbD"}, "__type": "Arg"}, "9gaSfwK5aKWq": {"param": {"__ref": "eOpE3ydfotZ1"}, "expr": {"__ref": "AAltDIpDpumM"}, "__type": "Arg"}, "rLPn6UezMT8a": {"interactions": [{"__ref": "QamAKdBEybUa"}], "__type": "EventHandler"}, "hYIsthKbDPVU": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "jfsvpuV6Op2W": {"param": {"__ref": "8sRSz5t67XL5"}, "expr": {"__ref": "zQji7syGukay"}, "__type": "Arg"}, "Ijaj73-ail3E": {"param": {"__ref": "7O6qHUygi0o-"}, "expr": {"__ref": "wHJwsIGE4gE2"}, "__type": "Arg"}, "ZB3FzOFeBqaa": {"param": {"__ref": "eOpE3ydfotZ1"}, "expr": {"__ref": "qEh9ajxkCdUx"}, "__type": "Arg"}, "4NdwgWegFCX6": {"interactions": [{"__ref": "PWcZU2onP1sk"}], "__type": "EventHandler"}, "08jBYagWlrJm": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "YuJ_X4FniFUD": {"name": "any", "__type": "AnyType"}, "nA51w0wNkyz5": {"name": "any", "__type": "AnyType"}, "QbbrzktVT7Mt": {"name": "any", "__type": "AnyType"}, "iKMKVPkgjqCc": {"name": "any", "__type": "AnyType"}, "bIzq4QqjEEL2": {"name": "any", "__type": "AnyType"}, "WCunkaItA1ZS": {"name": "any", "__type": "AnyType"}, "G3yjMuLcVYsv": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "UtZe0AC9SYrR", "parent": {"__ref": "3O7tMnP9o6kR"}, "locked": null, "vsettings": [{"__ref": "nDzaMRIZQDaD"}], "__type": "TplTag"}, "1JKgjWF9Ik6o": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "u31-YFxsjqaa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vnPSt0Dy3H7V": {"variants": [{"__ref": "lw1r0YN71__P"}], "args": [], "attrs": {}, "rs": {"__ref": "8-FFcJkcdgoz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HTJb2Hpb3U6D": {"variants": [{"__ref": "-4w4Dy07a9cq"}], "args": [], "attrs": {}, "rs": {"__ref": "-32mf8hS2VvX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bkDEj9SvOMM1": {"variants": [{"__ref": "x2ZuXQtQ7fsO"}], "args": [], "attrs": {}, "rs": {"__ref": "jlprwBjVYMIf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sOfn3Iqim__s": {"variants": [{"__ref": "TMmVv5yI_Gl6"}], "args": [], "attrs": {}, "rs": {"__ref": "yDXpkozHs3bH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xjsvFgXkLGbw": {"variants": [{"__ref": "r8t_K3NKv6FJ"}], "args": [], "attrs": {}, "rs": {"__ref": "tUG7i5hwYvB2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nrPY582-tCZI": {"variants": [{"__ref": "92QGXrl6wD1Q"}], "args": [], "attrs": {}, "rs": {"__ref": "K0VyFbDe2WxU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2R6A4TiR7SFy": {"variants": [{"__ref": "bgUfLcScXBSM"}], "args": [], "attrs": {}, "rs": {"__ref": "71eXqU1lNMlo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZXhjmCvqhoVR": {"variants": [{"__ref": "boNkne008VC3"}], "args": [], "attrs": {}, "rs": {"__ref": "Fmz2FN6xh6tP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Qa6vUOd3HtVg": {"variants": [{"__ref": "cEIRMhDKPhmd"}], "args": [], "attrs": {}, "rs": {"__ref": "IqzOQw0IFntP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GrFRKNVf5BQg": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "hSlzo4PbkWFp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TI4vHDQomJah": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "LxZE_ujXrZ_-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nuk_ru50x-Uj": {"variants": [{"__ref": "pLXcb85mRSWS"}], "args": [], "attrs": {}, "rs": {"__ref": "ts8f637y-5l8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5AhP2A1ZtJyp": {"variants": [{"__ref": "jyyU9ZyH-Cnl"}], "args": [], "attrs": {}, "rs": {"__ref": "eGEUgG-xdZdK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4iwuWXYy8hIJ": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "EyrySm_JlqRX": {"code": "false", "fallback": null, "__type": "CustomCode"}, "p-WQK9goOhOx": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "xvh9E2RWsrCI": {"code": "true", "fallback": null, "__type": "CustomCode"}, "ufh06HcEznww": {"values": {}, "mixins": [], "__type": "RuleSet"}, "WUvFUhZAI6hd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "GdE9cpufg4IG": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "s8VUJFDeOxO9", "parent": {"__ref": "3U__C9ILXC6x"}, "locked": null, "vsettings": [{"__ref": "Gwrjc-ddI3vs"}], "__type": "TplTag"}, "ijHgpCgD-1TV": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "j-XykZrJdkYn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oE1LlbozY7Zd": {"variants": [{"__ref": "ccD0O-b22xgL"}], "args": [], "attrs": {}, "rs": {"__ref": "htVnZ9ZZjEhW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5j8KWZX3A5js": {"variants": [{"__ref": "7JaZkaz7FKrX"}], "args": [], "attrs": {}, "rs": {"__ref": "0EzueaqEr5pX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "E_6K9CmavLmj": {"variants": [{"__ref": "lw1r0YN71__P"}], "args": [], "attrs": {}, "rs": {"__ref": "iwLMM6zvEL5M"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lGoQkH8CCIQr": {"variants": [{"__ref": "fiZpOiUmqWUj"}], "args": [], "attrs": {}, "rs": {"__ref": "H5WqGjpLzT93"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Svpk3USpGwfe": {"variants": [{"__ref": "3jNcACuRZH2N"}], "args": [], "attrs": {}, "rs": {"__ref": "W6CUptXqarsk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LVFiEqqyWrQT": {"variants": [{"__ref": "x2ZuXQtQ7fsO"}], "args": [], "attrs": {}, "rs": {"__ref": "vNrWOjA854eq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IByuipLkjCEc": {"variants": [{"__ref": "TMmVv5yI_Gl6"}], "args": [], "attrs": {}, "rs": {"__ref": "s4tUr77nl5uC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lAea1-pzWhZE": {"variants": [{"__ref": "boNkne008VC3"}], "args": [], "attrs": {}, "rs": {"__ref": "MUw_rs8GqGXA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "z33lEm_XcSIs": {"variants": [{"__ref": "r8t_K3NKv6FJ"}], "args": [], "attrs": {}, "rs": {"__ref": "imymkYMtp-MD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zXGAZXAjCZ1t": {"variants": [{"__ref": "92QGXrl6wD1Q"}], "args": [], "attrs": {}, "rs": {"__ref": "34oHCw93l941"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MU0NBoDwzpaT": {"variants": [{"__ref": "bgUfLcScXBSM"}], "args": [], "attrs": {}, "rs": {"__ref": "P1iUjTPoy4FJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5tbPery-Gt_j": {"variants": [{"__ref": "-4w4Dy07a9cq"}], "args": [], "attrs": {}, "rs": {"__ref": "EHP02hSU45PT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "42dE3owkuD31": {"variants": [{"__ref": "_3z_P-NNtxuv"}], "args": [], "attrs": {}, "rs": {"__ref": "_aBY8AcsrJ0l"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xFMOCm48oJ1v": {"variants": [{"__ref": "vHnYd1i6wg4G"}], "args": [], "attrs": {}, "rs": {"__ref": "TIRpLibC1bqW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "J7yQBbqazPaS": {"variants": [{"__ref": "8O-Ip7hwZDj3"}], "args": [], "attrs": {}, "rs": {"__ref": "ZogLwIbf0-7m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7TeA4-Rz7IgY": {"variants": [{"__ref": "M7BJw2PREMmA"}], "args": [], "attrs": {}, "rs": {"__ref": "YZp6oMMjrc9S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3GQD5UbE8k-p": {"variants": [{"__ref": "pLXcb85mRSWS"}], "args": [], "attrs": {}, "rs": {"__ref": "ELRaCNMFmIg-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fZZs6XFTyHpk": {"variants": [{"__ref": "cEIRMhDKPhmd"}], "args": [], "attrs": {}, "rs": {"__ref": "qegt3EluE8fx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OvLc86KL2K4q": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "RYv2hJQXsRRt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bYc9UrNKqTnA": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "qmnFdp_XWKXJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "43VOt51xbrej": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "YzEzC7zE3c0B"}], "args": [], "attrs": {}, "rs": {"__ref": "nDJqX74DTwXV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ndYGmlAeSS7L": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "YzEzC7zE3c0B"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "Nq8dWL9DB98V"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "t9uiZXOt3f26": {"variants": [{"__ref": "YzEzC7zE3c0B"}], "args": [], "attrs": {}, "rs": {"__ref": "HMC_nGDcrVv7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kGSPlVDaAiHe": {"variants": [{"__ref": "YzEzC7zE3c0B"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "2R3FLmr6otQZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MfGbveLrpIFQ": {"variants": [{"__ref": "jyyU9ZyH-Cnl"}], "args": [], "attrs": {}, "rs": {"__ref": "xbeYB-kN53Ua"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Pl_6uJJVhX5r": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "jyKa-rSTaQ84": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Ua_5nDnQw-s0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "g4_Y5xanjpDV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8RsRbMWkC4LW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "DcOJdEp5pPqi": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "EZEU5ifxe9AE", "parent": {"__ref": "1mms1CPIzu7Q"}, "locked": null, "vsettings": [{"__ref": "pODT88IfdXvI"}], "__type": "TplTag"}, "t1hYe0CJiBPF": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "h5wjJUxS9VQf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nbM21PRxfqqV": {"variants": [{"__ref": "fiZpOiUmqWUj"}], "args": [], "attrs": {}, "rs": {"__ref": "9E9ChaFnyK-O"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Vn23UMIOa9A6": {"variants": [{"__ref": "x2ZuXQtQ7fsO"}], "args": [], "attrs": {}, "rs": {"__ref": "B0zHxak-PBGy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mzx-ROXMy8-g": {"variants": [{"__ref": "TMmVv5yI_Gl6"}], "args": [], "attrs": {}, "rs": {"__ref": "zkvqtMYE9Qjb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "e_ScvkFRV4gb": {"variants": [{"__ref": "r8t_K3NKv6FJ"}], "args": [], "attrs": {}, "rs": {"__ref": "SSikJv8kUAPq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "j2s0Wz_CtB0g": {"variants": [{"__ref": "92QGXrl6wD1Q"}], "args": [], "attrs": {}, "rs": {"__ref": "1vbjeo2cukxW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xY0Lr4ZonU7d": {"variants": [{"__ref": "bgUfLcScXBSM"}], "args": [], "attrs": {}, "rs": {"__ref": "SfAf7OYrRM8O"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "os1CEoCywoSl": {"variants": [{"__ref": "boNkne008VC3"}], "args": [], "attrs": {}, "rs": {"__ref": "4l97bEupybXC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "H54aAfQmZCjP": {"variants": [{"__ref": "cEIRMhDKPhmd"}], "args": [], "attrs": {}, "rs": {"__ref": "-839TO9qHvvH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "C6mYJFvmChBQ": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "aMV_xZLTjaiR"}], "args": [], "attrs": {}, "rs": {"__ref": "rdOPApWQSuB3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4MySJTw2bT2G": {"variants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "MkyOndX3NfEk"}], "args": [], "attrs": {}, "rs": {"__ref": "yAPaUIDPN6WX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PW3Nu8_M9DpA": {"variants": [{"__ref": "pLXcb85mRSWS"}], "args": [], "attrs": {}, "rs": {"__ref": "agi-LJpehzan"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TScG9C6vWwup": {"variants": [{"__ref": "jyyU9ZyH-Cnl"}], "args": [], "attrs": {}, "rs": {"__ref": "Nqjle3ZSPBRs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hi59mUdDrVSY": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "rQAn_1f5oje1": {"code": "false", "fallback": null, "__type": "CustomCode"}, "p_cFdKMG800Z": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "n85x50wTfAso": {"code": "true", "fallback": null, "__type": "CustomCode"}, "Z0YiCFU3ilUt": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7V2LHk43Gz1m": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uveZh_2HwvMj": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "zfWUGu0urqwE": {"markers": [], "text": "Page 1", "__type": "RawText"}, "HsvekfSqJlhl": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "eIFykPLJnRq3": {"param": {"__ref": "8sRSz5t67XL5"}, "expr": {"__ref": "Ll9oOxb73Kl2"}, "__type": "Arg"}, "dLHfuiSjLaFO": {"param": {"__ref": "7O6qHUygi0o-"}, "expr": {"__ref": "SPaVerY9KOWq"}, "__type": "Arg"}, "vsoZw2r8Qt-s": {"param": {"__ref": "eOpE3ydfotZ1"}, "expr": {"__ref": "7wdTVJDyKN1n"}, "__type": "Arg"}, "KRqswG6nDJHl": {"interactions": [{"__ref": "SG6s-yNM4JoV"}], "__type": "EventHandler"}, "YtBnpY4Dfu5T": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "xx6EjKSolSDr": {"param": {"__ref": "8sRSz5t67XL5"}, "expr": {"__ref": "HYXlxDa57mdu"}, "__type": "Arg"}, "4q2gsf2hQLQR": {"param": {"__ref": "7O6qHUygi0o-"}, "expr": {"__ref": "OJe6TLQSYSDA"}, "__type": "Arg"}, "OcjDizLeIgFx": {"param": {"__ref": "eOpE3ydfotZ1"}, "expr": {"__ref": "E2t9b1FzYudk"}, "__type": "Arg"}, "TNtOoFfMzX5-": {"interactions": [{"__ref": "nH_j7F5iOutE"}], "__type": "EventHandler"}, "6Zty69AUqbRX": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "ss3u74clESh9": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "8GOZkDH74JpY": {"markers": [], "text": "Page 1", "__type": "RawText"}, "2ltzCKZcd-_D": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "vaA43Os-fXun": {"param": {"__ref": "8sRSz5t67XL5"}, "expr": {"__ref": "1jaEfgMFRldb"}, "__type": "Arg"}, "tZkYRHbhR--O": {"param": {"__ref": "7O6qHUygi0o-"}, "expr": {"__ref": "AAfpXF_gawlO"}, "__type": "Arg"}, "AZDV24U5W13U": {"param": {"__ref": "eOpE3ydfotZ1"}, "expr": {"__ref": "toNffJ2jXGpC"}, "__type": "Arg"}, "CgI5PPe4JnHQ": {"interactions": [{"__ref": "wLu1lQUyI4OX"}], "__type": "EventHandler"}, "4BLDFTfpRunB": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "2YAg-Q4Vz8zW": {"param": {"__ref": "8sRSz5t67XL5"}, "expr": {"__ref": "hjXBISELwoVL"}, "__type": "Arg"}, "Lmt5A83tYQGf": {"param": {"__ref": "7O6qHUygi0o-"}, "expr": {"__ref": "1017Zcp1o2JI"}, "__type": "Arg"}, "c_sDW1gzRmeB": {"param": {"__ref": "eOpE3ydfotZ1"}, "expr": {"__ref": "ry5IWxyaVeHv"}, "__type": "Arg"}, "u0KLSO9FxGHG": {"interactions": [{"__ref": "jcnk-mihIumX"}], "__type": "EventHandler"}, "YiE-Hmt0GfLM": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "iB2jXa4ARSRj": {"uuid": "tjvO6iBdLzyR", "width": 1366, "height": 768, "container": {"__ref": "PM1oklfjONb0"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "seqJtOa-qmF5"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "LcRXJcqzmYmK": {"uuid": "8qC1AS_79ecq", "width": 414, "height": 736, "container": {"__ref": "8R6K2rmMMbqH"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "seqJtOa-qmF5"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "fTts4YXlI_4-": {"uuid": "bECF9Tot7x5C", "width": 1366, "height": 768, "container": {"__ref": "U0wGhHNkbqyn"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "AALHp7VW3AZi"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "B97759w0V8ll": {"uuid": "7vKXWoU5sjBd", "width": 414, "height": 736, "container": {"__ref": "P8Av7Vhgtnf5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "AALHp7VW3AZi"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "BxK-LplzAY3d": {"uuid": "eRBa-HRgP5Lz", "width": 1366, "height": 768, "container": {"__ref": "PxniD9PaIQb3"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "jY5r_aRO-4Eb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eHpqEXtoWK-1": {"uuid": "sIsvQQHEswdL", "width": 414, "height": 736, "container": {"__ref": "sLBMwwJUjhsB"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "jY5r_aRO-4Eb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "y9I1fHnjE5N0": {"uuid": "IMqJ7hN6RSh2", "width": 1366, "height": 768, "container": {"__ref": "jW6xqvXbQMjv"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1sj2PKIZW7Yv"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "0pte92v1qO5d": {"uuid": "4h6QyMmxBVBf", "width": 414, "height": 736, "container": {"__ref": "nakOsPDPxi79"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1sj2PKIZW7Yv"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "gBhNjGWJMVrM": {"uuid": "PueyiMjhJXvw", "width": 1180, "height": 540, "container": {"__ref": "SQXVzvq5FmQ0"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "v4tW8WYIop8_"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "bdpgGokqeqEk": {"uuid": "QbT0q83H-n2B", "width": 1180, "height": 540, "container": {"__ref": "7p_903QbTWtQ"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Z3-1oAqJmta8"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "vxCT8sK37Dlq": {"uuid": "TCtZbeay5QCx", "width": 1180, "height": 540, "container": {"__ref": "l_BEMx_WkOLG"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "7JaZkaz7FKrX"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "tRzzrDf61TT0": {"uuid": "uSwj2TjUWNY3", "width": 1180, "height": 540, "container": {"__ref": "ZE7ivo3so_Yp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "ccD0O-b22xgL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5ATorNB9aiqc": {"uuid": "BJJB6KbD0FVD", "width": 1180, "height": 540, "container": {"__ref": "APck6flvAD8Y"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "4w1T4BsQNxnF": {"uuid": "KBl0lgkO5euU", "width": 1180, "height": 540, "container": {"__ref": "dcwV03gJkZDv"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "MeMy2-JIZJL-": {"uuid": "gzTwB2whanaZ", "width": 1180, "height": 540, "container": {"__ref": "AZ01cpNzDl-F"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "lw1r0YN71__P"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "TaJyNlAWWH6t": {"uuid": "qbwrUS_6_pEF", "width": 1180, "height": 540, "container": {"__ref": "wIIecC1clAl5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "fiZpOiUmqWUj"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "timPbepPGZvb": {"uuid": "VcYib17xIJuo", "width": 1180, "height": 540, "container": {"__ref": "PRjzrubuaz9q"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3jNcACuRZH2N"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "SbQ3EGISK4TB": {"uuid": "2a-4FJMQeMDN", "width": 1180, "height": 540, "container": {"__ref": "uYYYv1en3jWB"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "M7BJw2PREMmA"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "gH9MQ-uiXd7G": {"uuid": "yqYy2cSkAwLG", "width": 1180, "height": 540, "container": {"__ref": "qFhK14UWLQfS"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "v558s9LW1FOf"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "MtdKXE-eNwIA": {"uuid": "pTgpQZs3aZlm", "width": 1180, "height": 540, "container": {"__ref": "FX0lXWcC2Bwa"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "OrW3UwYyLXo2"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "IYbe4Bb8b8YO": {"uuid": "iCcnWPIHX7z-", "width": 1180, "height": 540, "container": {"__ref": "nPHbMMaOaVec"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "UlUyAvXIG8Ed"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Rw8M7LjxyeMq": {"uuid": "Vvl8nGrRRBbU", "width": 1180, "height": 540, "container": {"__ref": "pomjFihpSeeg"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YzEzC7zE3c0B"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "TC8R2ZhDv2Je": {"uuid": "ksnPPC7ZOy5j", "width": 1180, "height": 540, "container": {"__ref": "1WNM10bQK1Ic"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "-4w4Dy07a9cq"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "AdMWIkfUDFWw": {"uuid": "_3-xFg7AgpeC", "width": 1180, "height": 540, "container": {"__ref": "tSK27n-ecB2S"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "_3z_P-NNtxuv"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "fr5UlqSsYaKo": {"uuid": "UA4REqlW3eGi", "width": 1180, "height": 540, "container": {"__ref": "uuS5JEoPSTv1"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "boNkne008VC3"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "yEu8YctwbEut": {"uuid": "syCoW4Hz_zoc", "width": 1180, "height": 540, "container": {"__ref": "IYWM45lCREFq"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8O-Ip7hwZDj3"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "TrZQZuQnOoai": {"uuid": "bvEFmQ5fbS7i", "width": 1180, "height": 540, "container": {"__ref": "fTNSRu7Y9Bjv"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "vHnYd1i6wg4G"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "mI3VHOUxObVS": {"uuid": "72mTvZX-sbV-", "width": 1180, "height": 540, "container": {"__ref": "a7KeLXYOsUSF"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "jyyU9ZyH-Cnl"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "pfJuL170arYR": {"uuid": "3ekXPT0rAmMU", "width": 1180, "height": 540, "container": {"__ref": "OfEfajgf5rNg"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "x2ZuXQtQ7fsO"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "OGPYwTACdE92": {"uuid": "YZQXtPRHtRAj", "width": 1180, "height": 540, "container": {"__ref": "AFQzh3qGjCmU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "TMmVv5yI_Gl6"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "RMxDqcHlKXw5": {"uuid": "FBQDjEzJBCng", "width": 1180, "height": 540, "container": {"__ref": "qkyoMm7cEBRL"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "r8t_K3NKv6FJ"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "yzhfUytUgLEk": {"uuid": "yqJHC8Tov79r", "width": 1180, "height": 540, "container": {"__ref": "DjVAIbzOuJ9O"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "92QGXrl6wD1Q"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "anSPv4zrLP5G": {"uuid": "KzGdsmTCqalt", "width": 1180, "height": 540, "container": {"__ref": "u2GVO3L3vsx1"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "bgUfLcScXBSM"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ruolXQinaQgK": {"uuid": "y1CJn3iGJYn5", "width": 1180, "height": 540, "container": {"__ref": "3LU6EDyc-oxO"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pLXcb85mRSWS"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "9q2G0sfrnwfW": {"uuid": "3pCrNWrd5stw", "width": 1180, "height": 540, "container": {"__ref": "1HSKl93-5FHz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "cEIRMhDKPhmd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "kCkPkuH4CwxV": {"uuid": "M-nZsYMRRUYq", "width": 1180, "height": 540, "container": {"__ref": "ZhY4OgXRQhNA"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "x2ZuXQtQ7fsO"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "7VLp-7kjEKt1": {"uuid": "ApU0XiGSenHR", "width": 1180, "height": 540, "container": {"__ref": "OFePm73LStpH"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "x2ZuXQtQ7fsO"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Mm_-RO03WHmO": {"uuid": "9dxaiExecHHu", "width": 1180, "height": 540, "container": {"__ref": "DkYYt3rViC1d"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "TMmVv5yI_Gl6"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "_znN_r97IueL": {"uuid": "BZ7nCoBV-6be", "width": 1180, "height": 540, "container": {"__ref": "QlmsPdCVKbxO"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "TMmVv5yI_Gl6"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "MP9F9PgknpXk": {"uuid": "nfc15UNJ7qFT", "width": 1180, "height": 540, "container": {"__ref": "67J3j9YRc9wP"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "boNkne008VC3"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "jpWJhmAA0F1b": {"uuid": "JVlxtEjUNtnJ", "width": 1180, "height": 540, "container": {"__ref": "ofPSFBqIREFT"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "boNkne008VC3"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6cph0cXF5aMu": {"uuid": "f9M9zYKHorl7", "width": 1180, "height": 540, "container": {"__ref": "xUHV25JqwcvM"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8O-Ip7hwZDj3"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Qp3gc7fhPVU8": {"uuid": "kznHy6GaD4As", "width": 1180, "height": 540, "container": {"__ref": "cO-BPUmjpqjK"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8O-Ip7hwZDj3"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "E2GqrUCXaqgT": {"uuid": "fX3CZ8DubeKo", "width": 1180, "height": 540, "container": {"__ref": "nc6SMQAso-Xc"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "r8t_K3NKv6FJ"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "os2TRFCcoj8u": {"uuid": "uxKD9iAe4mGZ", "width": 1180, "height": 540, "container": {"__ref": "L4ojw1FwCGNN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "r8t_K3NKv6FJ"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "EDWPgKX8bevF": {"uuid": "01LxPYHPraUn", "width": 1180, "height": 540, "container": {"__ref": "0NfuRN-jUcvd"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "92QGXrl6wD1Q"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "VvMFOceMTEgB": {"uuid": "AW1cAE9s2qla", "width": 1180, "height": 540, "container": {"__ref": "gx-nJSYF6svo"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "92QGXrl6wD1Q"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "tULF6FFyyylV": {"uuid": "gNqjcmk8L7Zx", "width": 1180, "height": 540, "container": {"__ref": "dcbgvuFJEQ7q"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "bgUfLcScXBSM"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "qP93fjX6DwcD": {"uuid": "whkARpH1zrEB", "width": 1180, "height": 540, "container": {"__ref": "o2qqXKZlOt7z"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "bgUfLcScXBSM"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5B_fhU_FRrHx": {"uuid": "yhPim2o1clUL", "width": 1180, "height": 540, "container": {"__ref": "4W4uVFC5dY-V"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "-4w4Dy07a9cq"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "m6yX4Ij4xVlI": {"uuid": "SvpQtUm9K8uA", "width": 1180, "height": 540, "container": {"__ref": "GofbsMsUN6-A"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "-4w4Dy07a9cq"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "AL28-xv8co9c": {"uuid": "dfvMLN6r_trC", "width": 1180, "height": 540, "container": {"__ref": "shQx11ceWjv1"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "_3z_P-NNtxuv"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "8lJbOj5Eo79S": {"uuid": "d3pTMD-V6Kkp", "width": 1180, "height": 540, "container": {"__ref": "EB2bBL64AUQq"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "_3z_P-NNtxuv"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "wIr6l4iFnfJI": {"uuid": "2k2qC5L3VzRw", "width": 1180, "height": 540, "container": {"__ref": "4FgBB_rOzCA7"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "vHnYd1i6wg4G"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6aVo0AaN2XZ_": {"uuid": "QM9JHXaZeWPf", "width": 1180, "height": 540, "container": {"__ref": "6ZprDfSbDRXv"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "vHnYd1i6wg4G"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "mflnTde2im55": {"uuid": "rZ3odZbDi3YP", "width": 1180, "height": 540, "container": {"__ref": "fzh00raFDmUX"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "M7BJw2PREMmA"}, {"__ref": "lw1r0YN71__P"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "hrlO2Q09zkru": {"uuid": "6H8O55C58Na7", "width": 1180, "height": 540, "container": {"__ref": "br62g4Mt6mnF"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "fiZpOiUmqWUj"}, {"__ref": "M7BJw2PREMmA"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6Gb8FkfMjYKn": {"uuid": "UvH6Vi-6RdLH", "width": 1180, "height": 540, "container": {"__ref": "67LonGK91tse"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pLXcb85mRSWS"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "2goYqXI2rRtT": {"uuid": "pEG2Vpv0OW49", "width": 1180, "height": 540, "container": {"__ref": "fhQlHZNVz5tb"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "pLXcb85mRSWS"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "VhXUuPakbrI0": {"uuid": "v-ktlF4w3D7x", "width": 1180, "height": 540, "container": {"__ref": "q0rX89s9hmvl"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "v558s9LW1FOf"}, {"__ref": "UlUyAvXIG8Ed"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Lla01SvUtiek": {"uuid": "hFjY-EMQdvNw", "width": 1180, "height": 540, "container": {"__ref": "M-g-y2vCoMvQ"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ZGjQKY3nngC6": {"uuid": "ocwlEORDemTy", "width": 1180, "height": 540, "container": {"__ref": "09j22udvsnnY"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "cEIRMhDKPhmd"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "DzlEoJztOW9-": {"uuid": "b1AbA5OC_QO2", "width": 1180, "height": 540, "container": {"__ref": "lN4cfUbGFc3o"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "jyyU9ZyH-Cnl"}, {"__ref": "aMV_xZLTjaiR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "fCbOi3IQNpIK": {"uuid": "Ly9Eqq0vwZhN", "width": 1180, "height": 540, "container": {"__ref": "ZIEWZ5IF_k5P"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "jyyU9ZyH-Cnl"}, {"__ref": "MkyOndX3NfEk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "9PJtc3F4PZYM": {"code": "(`Current User: ${currentUser.email ?? \"Unlogged\"}`)", "fallback": {"__ref": "9-_jDIb3dW7l"}, "__type": "CustomCode"}, "CZu6QWPC5YFv": {"tpl": [{"__ref": "D3Ne3OUm8qUJ"}], "__type": "VirtualRenderExpr"}, "Edv_8E3wxkbD": {"tpl": [{"__ref": "QtI7TeXhWRXr"}], "__type": "RenderExpr"}, "AAltDIpDpumM": {"tpl": [{"__ref": "K38l_9i9oV2I"}], "__type": "VirtualRenderExpr"}, "QamAKdBEybUa": {"interactionName": "Log in", "actionName": "login", "args": [{"__ref": "NkuXIMsjJT-4"}], "condExpr": null, "conditionalMode": "always", "uuid": "YEf0rUR3plKG", "parent": {"__ref": "rLPn6UezMT8a"}, "__type": "Interaction"}, "zQji7syGukay": {"tpl": [{"__ref": "uhnHeGgrbMrn"}], "__type": "VirtualRenderExpr"}, "wHJwsIGE4gE2": {"tpl": [{"__ref": "l4ygYz1yMJZY"}], "__type": "RenderExpr"}, "qEh9ajxkCdUx": {"tpl": [{"__ref": "26RB2Curt0TU"}], "__type": "VirtualRenderExpr"}, "PWcZU2onP1sk": {"interactionName": "Log out", "actionName": "logout", "args": [], "condExpr": null, "conditionalMode": "always", "uuid": "fIjdWaMBjvSD", "parent": {"__ref": "4NdwgWegFCX6"}, "__type": "Interaction"}, "nDzaMRIZQDaD": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {"outerHTML": {"__ref": "LNO115QToSFQ"}}, "rs": {"__ref": "mKxJN9NciYt2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "u31-YFxsjqaa": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "8-FFcJkcdgoz": {"values": {}, "mixins": [], "__type": "RuleSet"}, "-32mf8hS2VvX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "jlprwBjVYMIf": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "yDXpkozHs3bH": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "tUG7i5hwYvB2": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "K0VyFbDe2WxU": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "71eXqU1lNMlo": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "Fmz2FN6xh6tP": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "IqzOQw0IFntP": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "hSlzo4PbkWFp": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "LxZE_ujXrZ_-": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "ts8f637y-5l8": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "eGEUgG-xdZdK": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "Gwrjc-ddI3vs": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {}, "rs": {"__ref": "pGiWTIgnGX0g"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ePUdVby7z3lH"}, "columnsConfig": null, "__type": "VariantSetting"}, "j-XykZrJdkYn": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "htVnZ9ZZjEhW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "0EzueaqEr5pX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "iwLMM6zvEL5M": {"values": {}, "mixins": [], "__type": "RuleSet"}, "H5WqGjpLzT93": {"values": {}, "mixins": [], "__type": "RuleSet"}, "W6CUptXqarsk": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vNrWOjA854eq": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "s4tUr77nl5uC": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "MUw_rs8GqGXA": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "imymkYMtp-MD": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "34oHCw93l941": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "P1iUjTPoy4FJ": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "EHP02hSU45PT": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "_aBY8AcsrJ0l": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "TIRpLibC1bqW": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "ZogLwIbf0-7m": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "YZp6oMMjrc9S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ELRaCNMFmIg-": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "qegt3EluE8fx": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "RYv2hJQXsRRt": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "qmnFdp_XWKXJ": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "nDJqX74DTwXV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Nq8dWL9DB98V": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HMC_nGDcrVv7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2R3FLmr6otQZ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xbeYB-kN53Ua": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "pODT88IfdXvI": {"variants": [{"__ref": "Z3-1oAqJmta8"}], "args": [], "attrs": {"outerHTML": {"__ref": "2ssSsSZO-nOH"}}, "rs": {"__ref": "DwyFgUelzJxY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "h5wjJUxS9VQf": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "9E9ChaFnyK-O": {"values": {}, "mixins": [], "__type": "RuleSet"}, "B0zHxak-PBGy": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "zkvqtMYE9Qjb": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "SSikJv8kUAPq": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "1vbjeo2cukxW": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "SfAf7OYrRM8O": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "4l97bEupybXC": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "-839TO9qHvvH": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "rdOPApWQSuB3": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "yAPaUIDPN6WX": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "agi-LJpehzan": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "Nqjle3ZSPBRs": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "Ll9oOxb73Kl2": {"tpl": [{"__ref": "367Nm3jHHfNS"}], "__type": "VirtualRenderExpr"}, "SPaVerY9KOWq": {"tpl": [{"__ref": "Iio6Ejn3SpB0"}], "__type": "RenderExpr"}, "7wdTVJDyKN1n": {"tpl": [{"__ref": "_6lr-UKTtHhS"}], "__type": "VirtualRenderExpr"}, "SG6s-yNM4JoV": {"interactionName": "Log in", "actionName": "login", "args": [{"__ref": "JPjRtMtstsWL"}], "condExpr": null, "conditionalMode": "always", "uuid": "GSxmA78mWNns", "parent": {"__ref": "KRqswG6nDJHl"}, "__type": "Interaction"}, "HYXlxDa57mdu": {"tpl": [{"__ref": "92OYycAMzof_"}], "__type": "VirtualRenderExpr"}, "OJe6TLQSYSDA": {"tpl": [{"__ref": "keGrQ_qLgMZO"}], "__type": "RenderExpr"}, "E2t9b1FzYudk": {"tpl": [{"__ref": "axRwtUESKRv2"}], "__type": "VirtualRenderExpr"}, "nH_j7F5iOutE": {"interactionName": "Log out", "actionName": "logout", "args": [], "condExpr": null, "conditionalMode": "always", "uuid": "1LX4KuAXCm9b", "parent": {"__ref": "TNtOoFfMzX5-"}, "__type": "Interaction"}, "1jaEfgMFRldb": {"tpl": [{"__ref": "Uxo67P4VgTOH"}], "__type": "VirtualRenderExpr"}, "AAfpXF_gawlO": {"tpl": [{"__ref": "I1T_wYvekhpO"}], "__type": "RenderExpr"}, "toNffJ2jXGpC": {"tpl": [{"__ref": "R6TI0RdkBXt9"}], "__type": "VirtualRenderExpr"}, "wLu1lQUyI4OX": {"interactionName": "Log in", "actionName": "login", "args": [{"__ref": "H1iSYGDkMPeZ"}], "condExpr": null, "conditionalMode": "always", "uuid": "-Rzu3wqD7wPz", "parent": {"__ref": "CgI5PPe4JnHQ"}, "__type": "Interaction"}, "hjXBISELwoVL": {"tpl": [{"__ref": "H6QbCcXldZuA"}], "__type": "VirtualRenderExpr"}, "1017Zcp1o2JI": {"tpl": [{"__ref": "xxAmSLZvSeRV"}], "__type": "RenderExpr"}, "ry5IWxyaVeHv": {"tpl": [{"__ref": "ZJlIYR7DND9d"}], "__type": "VirtualRenderExpr"}, "jcnk-mihIumX": {"interactionName": "Log out", "actionName": "logout", "args": [], "condExpr": null, "conditionalMode": "always", "uuid": "J7fuJIIj9hZy", "parent": {"__ref": "u0KLSO9FxGHG"}, "__type": "Interaction"}, "PM1oklfjONb0": {"name": null, "component": {"__ref": "hSSsIgYX9Ez_"}, "uuid": "GT8Epg-0wClM", "parent": null, "locked": null, "vsettings": [{"__ref": "Ls6_3z1_Eh6v"}], "__type": "TplComponent"}, "8R6K2rmMMbqH": {"name": null, "component": {"__ref": "hSSsIgYX9Ez_"}, "uuid": "Jc7XrLj_QnuL", "parent": null, "locked": null, "vsettings": [{"__ref": "bEUfFyZJLZHU"}], "__type": "TplComponent"}, "U0wGhHNkbqyn": {"name": null, "component": {"__ref": "uhJY0og8JLqe"}, "uuid": "7fG019IsaPfk", "parent": null, "locked": null, "vsettings": [{"__ref": "ehPrRysbP8wm"}], "__type": "TplComponent"}, "P8Av7Vhgtnf5": {"name": null, "component": {"__ref": "uhJY0og8JLqe"}, "uuid": "ltDE7vta4-hk", "parent": null, "locked": null, "vsettings": [{"__ref": "MPzW3iwj2vVE"}], "__type": "TplComponent"}, "PxniD9PaIQb3": {"name": null, "component": {"__ref": "J7HHtq-clz9l"}, "uuid": "EAJPFZILeF3a", "parent": null, "locked": null, "vsettings": [{"__ref": "lWTeBXC-31Td"}], "__type": "TplComponent"}, "sLBMwwJUjhsB": {"name": null, "component": {"__ref": "J7HHtq-clz9l"}, "uuid": "7Bj89UMiSODP", "parent": null, "locked": null, "vsettings": [{"__ref": "CXsedYLbZ8sS"}], "__type": "TplComponent"}, "jW6xqvXbQMjv": {"name": null, "component": {"__ref": "y3Gi1ZRR3zFC"}, "uuid": "LBzo_tkLmifB", "parent": null, "locked": null, "vsettings": [{"__ref": "cKPLSBjmydYO"}], "__type": "TplComponent"}, "nakOsPDPxi79": {"name": null, "component": {"__ref": "y3Gi1ZRR3zFC"}, "uuid": "KWKBS-9o_7uj", "parent": null, "locked": null, "vsettings": [{"__ref": "e9xfQQk3Yxe2"}], "__type": "TplComponent"}, "SQXVzvq5FmQ0": {"name": null, "component": {"__ref": "a8rMN9AF25rn"}, "uuid": "gnFOafuk3Jw3", "parent": null, "locked": null, "vsettings": [{"__ref": "mFC3l4wf40Ks"}], "__type": "TplComponent"}, "7p_903QbTWtQ": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "e-lxwWnGBbzG", "parent": null, "locked": null, "vsettings": [{"__ref": "G84qaTzkw1gC"}], "__type": "TplComponent"}, "l_BEMx_WkOLG": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "H9t7P85Zk1dX", "parent": null, "locked": null, "vsettings": [{"__ref": "BgFH11aLuXtH"}], "__type": "TplComponent"}, "ZE7ivo3so_Yp": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "XYyK9_9Id-eC", "parent": null, "locked": null, "vsettings": [{"__ref": "PMQznpW8rHK3"}], "__type": "TplComponent"}, "APck6flvAD8Y": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "vVSQzOoEcZXk", "parent": null, "locked": null, "vsettings": [{"__ref": "oRN9NopCkbl6"}], "__type": "TplComponent"}, "dcwV03gJkZDv": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "f2UEbS7R3o2f", "parent": null, "locked": null, "vsettings": [{"__ref": "BEF_DjqowqJr"}], "__type": "TplComponent"}, "AZ01cpNzDl-F": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "89tjZSX3HRkY", "parent": null, "locked": null, "vsettings": [{"__ref": "loTjYJk2C8K8"}], "__type": "TplComponent"}, "wIIecC1clAl5": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "2wyKM5brI23H", "parent": null, "locked": null, "vsettings": [{"__ref": "pv0LlB1BQVk5"}], "__type": "TplComponent"}, "PRjzrubuaz9q": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "Ox9PxRxkz5cd", "parent": null, "locked": null, "vsettings": [{"__ref": "WNEulq84U9E_"}], "__type": "TplComponent"}, "uYYYv1en3jWB": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "HzpObS2CDe8O", "parent": null, "locked": null, "vsettings": [{"__ref": "3LagBHlKlBdm"}], "__type": "TplComponent"}, "qFhK14UWLQfS": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "DARaFsy30C7T", "parent": null, "locked": null, "vsettings": [{"__ref": "jZf2lU0ZgotH"}], "__type": "TplComponent"}, "FX0lXWcC2Bwa": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "MGXNOa4z8oou", "parent": null, "locked": null, "vsettings": [{"__ref": "Y8nUswv3RQAx"}], "__type": "TplComponent"}, "nPHbMMaOaVec": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "CgHfTLakux4w", "parent": null, "locked": null, "vsettings": [{"__ref": "0Yo_PgISJ38R"}], "__type": "TplComponent"}, "pomjFihpSeeg": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "ywCsAq3XnB1e", "parent": null, "locked": null, "vsettings": [{"__ref": "y8AUSc_nR8sL"}], "__type": "TplComponent"}, "1WNM10bQK1Ic": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "WgTgPpYO3FR1", "parent": null, "locked": null, "vsettings": [{"__ref": "_FltTGh9TO3a"}], "__type": "TplComponent"}, "tSK27n-ecB2S": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "QgRHn05rZ09L", "parent": null, "locked": null, "vsettings": [{"__ref": "ZQiFdfT-SSVG"}], "__type": "TplComponent"}, "uuS5JEoPSTv1": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "wri0KwE-swa_", "parent": null, "locked": null, "vsettings": [{"__ref": "VeC0Y0A3JNzY"}], "__type": "TplComponent"}, "IYWM45lCREFq": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "YG0N4Mal6ohQ", "parent": null, "locked": null, "vsettings": [{"__ref": "NdD7vCZ3-sSw"}], "__type": "TplComponent"}, "fTNSRu7Y9Bjv": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "fDhTbOGntigu", "parent": null, "locked": null, "vsettings": [{"__ref": "W-guCtDHshOr"}], "__type": "TplComponent"}, "a7KeLXYOsUSF": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "Om-zieUdLn-m", "parent": null, "locked": null, "vsettings": [{"__ref": "PIe8rzJknyVP"}], "__type": "TplComponent"}, "OfEfajgf5rNg": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "95qmi2OoKk2v", "parent": null, "locked": null, "vsettings": [{"__ref": "n71KiHBF7S_6"}], "__type": "TplComponent"}, "AFQzh3qGjCmU": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "q7kkm4mj51cy", "parent": null, "locked": null, "vsettings": [{"__ref": "pF-ZMJ-m0a44"}], "__type": "TplComponent"}, "qkyoMm7cEBRL": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "hLRdcgCs3orU", "parent": null, "locked": null, "vsettings": [{"__ref": "NNG1XSdf705G"}], "__type": "TplComponent"}, "DjVAIbzOuJ9O": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "jHM7SSLZGCVQ", "parent": null, "locked": null, "vsettings": [{"__ref": "eoqup1Ffpf8n"}], "__type": "TplComponent"}, "u2GVO3L3vsx1": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "PJyfCU795hxU", "parent": null, "locked": null, "vsettings": [{"__ref": "BvUdYtjsGD7w"}], "__type": "TplComponent"}, "3LU6EDyc-oxO": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "8ixezrKu02op", "parent": null, "locked": null, "vsettings": [{"__ref": "JRFEjjyWl3OC"}], "__type": "TplComponent"}, "1HSKl93-5FHz": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "uQLru3Uab60s", "parent": null, "locked": null, "vsettings": [{"__ref": "2SbVNNUpHybm"}], "__type": "TplComponent"}, "ZhY4OgXRQhNA": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "_ctB6FzP-YNQ", "parent": null, "locked": null, "vsettings": [{"__ref": "OhVHd-oi8Bpf"}], "__type": "TplComponent"}, "OFePm73LStpH": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "H552JQ4xZyIH", "parent": null, "locked": null, "vsettings": [{"__ref": "pJUd7QcKrjZA"}], "__type": "TplComponent"}, "DkYYt3rViC1d": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "ozksvGX22k2W", "parent": null, "locked": null, "vsettings": [{"__ref": "sY0186-OPb4k"}], "__type": "TplComponent"}, "QlmsPdCVKbxO": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "mH20847yNj41", "parent": null, "locked": null, "vsettings": [{"__ref": "n2ADuu8725eI"}], "__type": "TplComponent"}, "67J3j9YRc9wP": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "rJvr4HwWrWI3", "parent": null, "locked": null, "vsettings": [{"__ref": "iyx3zpkN8QO6"}], "__type": "TplComponent"}, "ofPSFBqIREFT": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "dmoB8Z15cQc2", "parent": null, "locked": null, "vsettings": [{"__ref": "4RtM54kDlHag"}], "__type": "TplComponent"}, "xUHV25JqwcvM": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "3AqQeal0hlq8", "parent": null, "locked": null, "vsettings": [{"__ref": "THRe4vsxHYZO"}], "__type": "TplComponent"}, "cO-BPUmjpqjK": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "6vQSToRbxvEe", "parent": null, "locked": null, "vsettings": [{"__ref": "DPs_AxVUQXq7"}], "__type": "TplComponent"}, "nc6SMQAso-Xc": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "Sz0M8EPZYNjH", "parent": null, "locked": null, "vsettings": [{"__ref": "Q2P5aTz0cgq0"}], "__type": "TplComponent"}, "L4ojw1FwCGNN": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "tBEn77prDkFX", "parent": null, "locked": null, "vsettings": [{"__ref": "W9w_hlS1h3fu"}], "__type": "TplComponent"}, "0NfuRN-jUcvd": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "Lbrcha3deETk", "parent": null, "locked": null, "vsettings": [{"__ref": "XOX3IacyLq6J"}], "__type": "TplComponent"}, "gx-nJSYF6svo": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "DY3wjsq3apy_", "parent": null, "locked": null, "vsettings": [{"__ref": "laf7-xdA43Ru"}], "__type": "TplComponent"}, "dcbgvuFJEQ7q": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "UxRmDwcOfmxw", "parent": null, "locked": null, "vsettings": [{"__ref": "fHREnv7IeAeH"}], "__type": "TplComponent"}, "o2qqXKZlOt7z": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "w44WZKM7l2ms", "parent": null, "locked": null, "vsettings": [{"__ref": "E_Cyn-_h2eav"}], "__type": "TplComponent"}, "4W4uVFC5dY-V": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "-LKBe6PuY_qS", "parent": null, "locked": null, "vsettings": [{"__ref": "XKEfMVY6InVr"}], "__type": "TplComponent"}, "GofbsMsUN6-A": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "0n9tw_u3enay", "parent": null, "locked": null, "vsettings": [{"__ref": "TB2erPIY-UX3"}], "__type": "TplComponent"}, "shQx11ceWjv1": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "avpqIBj-Qlw_", "parent": null, "locked": null, "vsettings": [{"__ref": "TF56BwVRqGF5"}], "__type": "TplComponent"}, "EB2bBL64AUQq": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "ywOiUtu3QqXi", "parent": null, "locked": null, "vsettings": [{"__ref": "TppNdvGB9aUz"}], "__type": "TplComponent"}, "4FgBB_rOzCA7": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "kLzu3b284OXg", "parent": null, "locked": null, "vsettings": [{"__ref": "P-nRd5oBJN57"}], "__type": "TplComponent"}, "6ZprDfSbDRXv": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "6tb81YzJHVo_", "parent": null, "locked": null, "vsettings": [{"__ref": "glTnS52vpAFb"}], "__type": "TplComponent"}, "fzh00raFDmUX": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "IeRXKCgW3EAd", "parent": null, "locked": null, "vsettings": [{"__ref": "scJIhwL9oLWb"}], "__type": "TplComponent"}, "br62g4Mt6mnF": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "V7F4ioi2k6pU", "parent": null, "locked": null, "vsettings": [{"__ref": "YdOAIckWfUwG"}], "__type": "TplComponent"}, "67LonGK91tse": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "5o32jzAslRBu", "parent": null, "locked": null, "vsettings": [{"__ref": "ijWPXTvCjf9F"}], "__type": "TplComponent"}, "fhQlHZNVz5tb": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "z4ZfhOYQpmDt", "parent": null, "locked": null, "vsettings": [{"__ref": "4eiXyWtLzr-G"}], "__type": "TplComponent"}, "q0rX89s9hmvl": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "3AeeYCWqHRZk", "parent": null, "locked": null, "vsettings": [{"__ref": "DHOdK9sn7bni"}], "__type": "TplComponent"}, "M-g-y2vCoMvQ": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "_bKooJvrNaZ3", "parent": null, "locked": null, "vsettings": [{"__ref": "cigA__OlhFNi"}], "__type": "TplComponent"}, "09j22udvsnnY": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "vXO65p8Cww4O", "parent": null, "locked": null, "vsettings": [{"__ref": "_VseiR0rnV7R"}], "__type": "TplComponent"}, "lN4cfUbGFc3o": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "-kUmv4mvbMvy", "parent": null, "locked": null, "vsettings": [{"__ref": "NfFa4-X5pywT"}], "__type": "TplComponent"}, "ZIEWZ5IF_k5P": {"name": null, "component": {"__ref": "zc3muSrtBGCI"}, "uuid": "8SoNZay63XOw", "parent": null, "locked": null, "vsettings": [{"__ref": "BeUKlgycRWVy"}], "__type": "TplComponent"}, "9-_jDIb3dW7l": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "D3Ne3OUm8qUJ": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "ZGPw-pvzD1_B", "parent": {"__ref": "jCSWpHWzU3m7"}, "locked": null, "vsettings": [{"__ref": "ysa4mFYHsu0-"}], "__type": "TplTag"}, "QtI7TeXhWRXr": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "u5MLE-ASUMCh", "parent": {"__ref": "jCSWpHWzU3m7"}, "locked": null, "vsettings": [{"__ref": "8N-dG5OopVx3"}], "__type": "TplTag"}, "K38l_9i9oV2I": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "ZnJ51uZd3RIE", "parent": {"__ref": "jCSWpHWzU3m7"}, "locked": null, "vsettings": [{"__ref": "CHCjavZP9MA0"}], "__type": "TplTag"}, "NkuXIMsjJT-4": {"name": "continueTo", "expr": {"__ref": "bu_D1Yd2gxXG"}, "__type": "NameArg"}, "uhnHeGgrbMrn": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "TajKvorzAn_7", "parent": {"__ref": "Og8X1GYkG4Mw"}, "locked": null, "vsettings": [{"__ref": "g3wMLg3cYMxm"}], "__type": "TplTag"}, "l4ygYz1yMJZY": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "fnJB7XrHoTvo", "parent": {"__ref": "Og8X1GYkG4Mw"}, "locked": null, "vsettings": [{"__ref": "EKlh0r3KRHCq"}], "__type": "TplTag"}, "26RB2Curt0TU": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "9zzltYtfz5cN", "parent": {"__ref": "Og8X1GYkG4Mw"}, "locked": null, "vsettings": [{"__ref": "pbtnyHbjRFaT"}], "__type": "TplTag"}, "LNO115QToSFQ": {"asset": {"__ref": "xevvXe89XAHn"}, "__type": "ImageAssetRef"}, "mKxJN9NciYt2": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "pGiWTIgnGX0g": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ePUdVby7z3lH": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "2ssSsSZO-nOH": {"asset": {"__ref": "i9xJcsRFqi76"}, "__type": "ImageAssetRef"}, "DwyFgUelzJxY": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "367Nm3jHHfNS": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "zgLcEmpTl_6i", "parent": {"__ref": "huPurRLsIC7Q"}, "locked": null, "vsettings": [{"__ref": "4WgOnMoYx4s4"}], "__type": "TplTag"}, "Iio6Ejn3SpB0": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "LnybWi7Re2_J", "parent": {"__ref": "huPurRLsIC7Q"}, "locked": null, "vsettings": [{"__ref": "C15S7qYHm2CF"}], "__type": "TplTag"}, "_6lr-UKTtHhS": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "f_IZkEiSADX3", "parent": {"__ref": "huPurRLsIC7Q"}, "locked": null, "vsettings": [{"__ref": "DJgsPEkzrLCF"}], "__type": "TplTag"}, "JPjRtMtstsWL": {"name": "continueTo", "expr": {"__ref": "dezVtJC0Vo7t"}, "__type": "NameArg"}, "92OYycAMzof_": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "AOtKF0XcsDBj", "parent": {"__ref": "OueZnU6ai0mQ"}, "locked": null, "vsettings": [{"__ref": "w8N6d22MPfi9"}], "__type": "TplTag"}, "keGrQ_qLgMZO": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "XCi7NwSJTyku", "parent": {"__ref": "OueZnU6ai0mQ"}, "locked": null, "vsettings": [{"__ref": "sTGXW28bgvSs"}], "__type": "TplTag"}, "axRwtUESKRv2": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "fNg35gosc2V_", "parent": {"__ref": "OueZnU6ai0mQ"}, "locked": null, "vsettings": [{"__ref": "yVLb-RwFH1d-"}], "__type": "TplTag"}, "Uxo67P4VgTOH": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "qv47H-t0z-oG", "parent": {"__ref": "shm24WXzWsGX"}, "locked": null, "vsettings": [{"__ref": "kO8lf9PF2vNJ"}], "__type": "TplTag"}, "I1T_wYvekhpO": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "NNDgGP1ljYEQ", "parent": {"__ref": "shm24WXzWsGX"}, "locked": null, "vsettings": [{"__ref": "90heuixUYSRq"}], "__type": "TplTag"}, "R6TI0RdkBXt9": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "QxvT_82SnQax", "parent": {"__ref": "shm24WXzWsGX"}, "locked": null, "vsettings": [{"__ref": "jKcR6c55QwwU"}], "__type": "TplTag"}, "H1iSYGDkMPeZ": {"name": "continueTo", "expr": {"__ref": "KmPhOJQ7WOPA"}, "__type": "NameArg"}, "H6QbCcXldZuA": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "4pCwC2-qR_8F", "parent": {"__ref": "T_DgB2IOuVM6"}, "locked": null, "vsettings": [{"__ref": "OD4ygA56ibQC"}], "__type": "TplTag"}, "xxAmSLZvSeRV": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "InayZmEWZ6YJ", "parent": {"__ref": "T_DgB2IOuVM6"}, "locked": null, "vsettings": [{"__ref": "5qyaAXxxHKg_"}], "__type": "TplTag"}, "ZJlIYR7DND9d": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "D5sWRAXYA9b5", "parent": {"__ref": "T_DgB2IOuVM6"}, "locked": null, "vsettings": [{"__ref": "bBrgPJBZediV"}], "__type": "TplTag"}, "Ls6_3z1_Eh6v": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "0zvVJziIWo_M"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bEUfFyZJLZHU": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "Oj0tvDuCYe8O"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ehPrRysbP8wm": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "9jtdWDUppNeD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MPzW3iwj2vVE": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "Ge4i6L_tEtvW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lWTeBXC-31Td": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "U5j2BpBbFvlx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CXsedYLbZ8sS": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "IXZHQqjRE9AS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cKPLSBjmydYO": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "fLjBD2Qd6m4E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "e9xfQQk3Yxe2": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "kxUvsrx1hEeE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mFC3l4wf40Ks": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "sYvda40KhTM0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "G84qaTzkw1gC": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "DiK-T_CICbyt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BgFH11aLuXtH": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "ef3uCp9kyKoD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PMQznpW8rHK3": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "-fsHWeCjo8y2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oRN9NopCkbl6": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "pTJoR_bDGd__"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BEF_DjqowqJr": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "QAHPu1rN36Ar"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "loTjYJk2C8K8": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "VYOu99cR_2ry"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pv0LlB1BQVk5": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "yFCw4MDfV4IL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "WNEulq84U9E_": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "XKquaqR_ciqM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3LagBHlKlBdm": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "VjJTbojacP-a"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jZf2lU0ZgotH": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "adXCoMI2BbvI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Y8nUswv3RQAx": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "GEFEtpReqSoP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0Yo_PgISJ38R": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "695hCL7F9uE-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "y8AUSc_nR8sL": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "SaRCABAxtT1X"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_FltTGh9TO3a": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "lh64M7Bg6UNh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZQiFdfT-SSVG": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "Jl1vuuBUWjhm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VeC0Y0A3JNzY": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "kG6u-uCvtePt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NdD7vCZ3-sSw": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "V42znGsUx5jf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "W-guCtDHshOr": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "0ytYQ9DFMFk4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PIe8rzJknyVP": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "PV9uMWyu3IOu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "n71KiHBF7S_6": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "w5FnfAypn4EV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pF-ZMJ-m0a44": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "LCyR7HVxSSPg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NNG1XSdf705G": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "82BHB4xqgjYJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eoqup1Ffpf8n": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "OEo3JEROg7so"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BvUdYtjsGD7w": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "tm2Sc82od3AB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JRFEjjyWl3OC": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "kxNcq76EFuLs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2SbVNNUpHybm": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "YaN8JMIgCI12"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OhVHd-oi8Bpf": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "eh7X0jIaHuk3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pJUd7QcKrjZA": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "A7m-mA8Z_FmS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sY0186-OPb4k": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "6d1osGWn5Vbs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "n2ADuu8725eI": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "uD6H8Imf0GrN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iyx3zpkN8QO6": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "WagzlacNqVY8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4RtM54kDlHag": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "aX63Wubj_YB4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "THRe4vsxHYZO": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "lu_S8kXuPivd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DPs_AxVUQXq7": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "nTzDIL_NAWT0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Q2P5aTz0cgq0": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "Z42L7WaJOxQG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "W9w_hlS1h3fu": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "CpdEFFViokwe"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XOX3IacyLq6J": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "4jSzOwgFJ7lq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "laf7-xdA43Ru": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "u8Blm-wMfLtP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fHREnv7IeAeH": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "EdBR9j5NIsdQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "E_Cyn-_h2eav": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "VnnC_MTZfZya"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XKEfMVY6InVr": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "lyokjcawhxfE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TB2erPIY-UX3": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "UUxETy4dk2bm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TF56BwVRqGF5": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "6xuNtTrmyv5s"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TppNdvGB9aUz": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "cWpiEviUqKMJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "P-nRd5oBJN57": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "xpKTxOq686Ct"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "glTnS52vpAFb": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "9-DD3LuB7zz0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "scJIhwL9oLWb": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "4iWstIUqCV0m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YdOAIckWfUwG": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "M-cuRYEXkYUm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ijWPXTvCjf9F": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "aitMN86HMZSL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4eiXyWtLzr-G": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "hcu3D6p430DU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DHOdK9sn7bni": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "q8O3mA3asG-6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cigA__OlhFNi": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "gX9G2jNlx3Ak"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_VseiR0rnV7R": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "cppVcff3CkYi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NfFa4-X5pywT": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "lAx8xuCAgwFb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BeUKlgycRWVy": {"variants": [{"__ref": "pTY6zCfBeZCi"}], "args": [], "attrs": {}, "rs": {"__ref": "QLYkGu4c902Z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ysa4mFYHsu0-": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {"outerHTML": {"__ref": "PaCkQCrf1-2C"}}, "rs": {"__ref": "anwkPYEnwEYQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8N-dG5OopVx3": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {}, "rs": {"__ref": "Nv-rKo4m0grN"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ZsIg94iQ5r_e"}, "columnsConfig": null, "__type": "VariantSetting"}, "CHCjavZP9MA0": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {"outerHTML": {"__ref": "dGCEy58lvGyt"}}, "rs": {"__ref": "GtNpqTvTMgFW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bu_D1Yd2gxXG": {"page": {"__ref": "J7HHtq-clz9l"}, "params": {}, "__type": "PageHref"}, "g3wMLg3cYMxm": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {"outerHTML": {"__ref": "toFE1spjHrup"}}, "rs": {"__ref": "6MMp34qKb6DE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EKlh0r3KRHCq": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {}, "rs": {"__ref": "LjRGHmlRsIIg"}, "dataCond": null, "dataRep": null, "text": {"__ref": "MSRM6uPFiS8m"}, "columnsConfig": null, "__type": "VariantSetting"}, "pbtnyHbjRFaT": {"variants": [{"__ref": "AALHp7VW3AZi"}], "args": [], "attrs": {"outerHTML": {"__ref": "7R5GFgyI1-Me"}}, "rs": {"__ref": "1si9uB8AD1bZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4WgOnMoYx4s4": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {"outerHTML": {"__ref": "fs-j6LQLnYF6"}}, "rs": {"__ref": "WSAPzlhdrwEg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "C15S7qYHm2CF": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {}, "rs": {"__ref": "2VSFMNb6kvru"}, "dataCond": null, "dataRep": null, "text": {"__ref": "QiL_JC6oR4PY"}, "columnsConfig": null, "__type": "VariantSetting"}, "DJgsPEkzrLCF": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {"outerHTML": {"__ref": "wuGgf8bcnbHl"}}, "rs": {"__ref": "CYMc_N5VmGsg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dezVtJC0Vo7t": {"page": {"__ref": "y3Gi1ZRR3zFC"}, "params": {}, "__type": "PageHref"}, "w8N6d22MPfi9": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {"outerHTML": {"__ref": "KbBu_fzT1y8z"}}, "rs": {"__ref": "w_LUBRSLCsUf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sTGXW28bgvSs": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {}, "rs": {"__ref": "x6Z08aD8cfoY"}, "dataCond": null, "dataRep": null, "text": {"__ref": "acd_n2MRJx41"}, "columnsConfig": null, "__type": "VariantSetting"}, "yVLb-RwFH1d-": {"variants": [{"__ref": "jY5r_aRO-4Eb"}], "args": [], "attrs": {"outerHTML": {"__ref": "MWhtElSS6KIl"}}, "rs": {"__ref": "7ieYzqaFPDYx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kO8lf9PF2vNJ": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {"outerHTML": {"__ref": "vqUfwyUeCuc2"}}, "rs": {"__ref": "1mrrbxE9Oqb3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "90heuixUYSRq": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {}, "rs": {"__ref": "9eTMP2oBMsqA"}, "dataCond": null, "dataRep": null, "text": {"__ref": "u0jybCRm5jpg"}, "columnsConfig": null, "__type": "VariantSetting"}, "jKcR6c55QwwU": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {"outerHTML": {"__ref": "zVTV6fEraC5g"}}, "rs": {"__ref": "Y5tokCrDRG1T"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KmPhOJQ7WOPA": {"page": {"__ref": "uhJY0og8JLqe"}, "params": {}, "__type": "PageHref"}, "OD4ygA56ibQC": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {"outerHTML": {"__ref": "LrYgOLPiqknU"}}, "rs": {"__ref": "kIHUNFoZIAaD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5qyaAXxxHKg_": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {}, "rs": {"__ref": "jKr08sseIhtd"}, "dataCond": null, "dataRep": null, "text": {"__ref": "4gbbC05t-Us8"}, "columnsConfig": null, "__type": "VariantSetting"}, "bBrgPJBZediV": {"variants": [{"__ref": "1sj2PKIZW7Yv"}], "args": [], "attrs": {"outerHTML": {"__ref": "CWapNRV6QkKV"}}, "rs": {"__ref": "jQ9K5yll1vIx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0zvVJziIWo_M": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Oj0tvDuCYe8O": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9jtdWDUppNeD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Ge4i6L_tEtvW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "U5j2BpBbFvlx": {"values": {}, "mixins": [], "__type": "RuleSet"}, "IXZHQqjRE9AS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "fLjBD2Qd6m4E": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kxUvsrx1hEeE": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sYvda40KhTM0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "DiK-T_CICbyt": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ef3uCp9kyKoD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "-fsHWeCjo8y2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pTJoR_bDGd__": {"values": {}, "mixins": [], "__type": "RuleSet"}, "QAHPu1rN36Ar": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VYOu99cR_2ry": {"values": {}, "mixins": [], "__type": "RuleSet"}, "yFCw4MDfV4IL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XKquaqR_ciqM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VjJTbojacP-a": {"values": {}, "mixins": [], "__type": "RuleSet"}, "adXCoMI2BbvI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "GEFEtpReqSoP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "695hCL7F9uE-": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SaRCABAxtT1X": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lh64M7Bg6UNh": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Jl1vuuBUWjhm": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kG6u-uCvtePt": {"values": {}, "mixins": [], "__type": "RuleSet"}, "V42znGsUx5jf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "0ytYQ9DFMFk4": {"values": {}, "mixins": [], "__type": "RuleSet"}, "PV9uMWyu3IOu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "w5FnfAypn4EV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LCyR7HVxSSPg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "82BHB4xqgjYJ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OEo3JEROg7so": {"values": {}, "mixins": [], "__type": "RuleSet"}, "tm2Sc82od3AB": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kxNcq76EFuLs": {"values": {}, "mixins": [], "__type": "RuleSet"}, "YaN8JMIgCI12": {"values": {}, "mixins": [], "__type": "RuleSet"}, "eh7X0jIaHuk3": {"values": {}, "mixins": [], "__type": "RuleSet"}, "A7m-mA8Z_FmS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6d1osGWn5Vbs": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uD6H8Imf0GrN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "WagzlacNqVY8": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aX63Wubj_YB4": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lu_S8kXuPivd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "nTzDIL_NAWT0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Z42L7WaJOxQG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CpdEFFViokwe": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4jSzOwgFJ7lq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "u8Blm-wMfLtP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EdBR9j5NIsdQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VnnC_MTZfZya": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lyokjcawhxfE": {"values": {}, "mixins": [], "__type": "RuleSet"}, "UUxETy4dk2bm": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6xuNtTrmyv5s": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cWpiEviUqKMJ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xpKTxOq686Ct": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9-DD3LuB7zz0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4iWstIUqCV0m": {"values": {}, "mixins": [], "__type": "RuleSet"}, "M-cuRYEXkYUm": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aitMN86HMZSL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "hcu3D6p430DU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "q8O3mA3asG-6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gX9G2jNlx3Ak": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cppVcff3CkYi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lAx8xuCAgwFb": {"values": {}, "mixins": [], "__type": "RuleSet"}, "QLYkGu4c902Z": {"values": {}, "mixins": [], "__type": "RuleSet"}, "PaCkQCrf1-2C": {"asset": {"__ref": "xevvXe89XAHn"}, "__type": "ImageAssetRef"}, "anwkPYEnwEYQ": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "Nv-rKo4m0grN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZsIg94iQ5r_e": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "dGCEy58lvGyt": {"asset": {"__ref": "i9xJcsRFqi76"}, "__type": "ImageAssetRef"}, "GtNpqTvTMgFW": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "toFE1spjHrup": {"asset": {"__ref": "xevvXe89XAHn"}, "__type": "ImageAssetRef"}, "6MMp34qKb6DE": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "LjRGHmlRsIIg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MSRM6uPFiS8m": {"markers": [], "text": "Logout", "__type": "RawText"}, "7R5GFgyI1-Me": {"asset": {"__ref": "i9xJcsRFqi76"}, "__type": "ImageAssetRef"}, "1si9uB8AD1bZ": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "fs-j6LQLnYF6": {"asset": {"__ref": "xevvXe89XAHn"}, "__type": "ImageAssetRef"}, "WSAPzlhdrwEg": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "2VSFMNb6kvru": {"values": {}, "mixins": [], "__type": "RuleSet"}, "QiL_JC6oR4PY": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "wuGgf8bcnbHl": {"asset": {"__ref": "i9xJcsRFqi76"}, "__type": "ImageAssetRef"}, "CYMc_N5VmGsg": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "KbBu_fzT1y8z": {"asset": {"__ref": "xevvXe89XAHn"}, "__type": "ImageAssetRef"}, "w_LUBRSLCsUf": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "x6Z08aD8cfoY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "acd_n2MRJx41": {"markers": [], "text": "Logout", "__type": "RawText"}, "MWhtElSS6KIl": {"asset": {"__ref": "i9xJcsRFqi76"}, "__type": "ImageAssetRef"}, "7ieYzqaFPDYx": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "vqUfwyUeCuc2": {"asset": {"__ref": "xevvXe89XAHn"}, "__type": "ImageAssetRef"}, "1mrrbxE9Oqb3": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "9eTMP2oBMsqA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "u0jybCRm5jpg": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "zVTV6fEraC5g": {"asset": {"__ref": "i9xJcsRFqi76"}, "__type": "ImageAssetRef"}, "Y5tokCrDRG1T": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "LrYgOLPiqknU": {"asset": {"__ref": "xevvXe89XAHn"}, "__type": "ImageAssetRef"}, "kIHUNFoZIAaD": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "jKr08sseIhtd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4gbbC05t-Us8": {"markers": [], "text": "Logout", "__type": "RawText"}, "CWapNRV6QkKV": {"asset": {"__ref": "i9xJcsRFqi76"}, "__type": "ImageAssetRef"}, "jQ9K5yll1vIx": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "246-add-component-updated-at"}]]