[["a3a6a4ab-6ef7-466e-8589-a26854d0253c", {"root": "tLUQCVQ8f-RO", "map": {"kOI6y2UkeiJX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vC7ZNDVPHgBS": {"name": "Default Typography", "rs": {"__ref": "kOI6y2UkeiJX"}, "preview": null, "uuid": "bNHnCplnu2s_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "paQKLfuBhpyd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "COSiiltazPgR": {"rs": {"__ref": "paQKLfuBhpyd"}, "__type": "ThemeLayoutSettings"}, "5zRXnYeNoXlY": {"defaultStyle": {"__ref": "vC7ZNDVPHgBS"}, "styles": [], "layout": {"__ref": "COSiiltazPgR"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "cszq9QSLKRwZ": {"uuid": "Ef4k0RTOUSvo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kzXdk6knZEiT": {"components": [{"__ref": "WVmeexY8nJIT"}, {"__ref": "fz5LTr5PlOfD"}, {"__ref": "fwyWe8zEDO_x"}, {"__ref": "Z2qUmJ8ucwKD"}, {"__ref": "eIbHc8zKY3CT"}, {"__ref": "awAmbZVAiMcx"}, {"__ref": "SLyd6sgbmCFe"}], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "cszq9QSLKRwZ"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "5zRXnYeNoXlY"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "VYPp4alMq2ZJ"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "WVmeexY8nJIT": {"uuid": "CORnFSshfBnH", "name": "hostless-iframe", "params": [{"__ref": "-QDuc23__xpC"}, {"__ref": "CgfS5K7t444W"}], "states": [], "tplTree": {"__ref": "ztfOR_Bstte-"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "eqECLHDXJi-_"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "VukX7aoATnkP"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "fz5LTr5PlOfD": {"uuid": "8PMlkKXJz9uU", "name": "hostless-html-video", "params": [{"__ref": "WUDj0RM3vV48"}, {"__ref": "s2ia8u3SiICl"}, {"__ref": "qBfPN_kVPIEa"}, {"__ref": "hQygJfrEbd19"}, {"__ref": "FU54w9zS_qtf"}, {"__ref": "fyXp8nOg15_s"}, {"__ref": "aC4Hhmn8O8RR"}, {"__ref": "5ej7BhA3dPeK"}], "states": [], "tplTree": {"__ref": "y_azsFA64JXq"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "pC4wxDUkmYBt"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "SFovWevvYqpH"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "fwyWe8zEDO_x": {"uuid": "gLLzhPLbjvKg", "name": "hostless-embed", "params": [{"__ref": "CKBtARqpD7tj"}, {"__ref": "fSA3x_hOrHCa"}], "states": [], "tplTree": {"__ref": "K7SUiBaXG1Wz"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "gbZC2yKzz90c"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "7CflaUCfkCqK"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "Z2qUmJ8ucwKD": {"uuid": "vm3I0dJshQph", "name": "hostless-data-provider", "params": [{"__ref": "VDTDvYhGib6J"}, {"__ref": "3MSN4sZjigiL"}, {"__ref": "tIyGxWuCQx-X"}], "states": [], "tplTree": {"__ref": "02shWaU0xxIs"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "n6v7U7i357dR"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "gPaPieigYdSJ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "eIbHc8zKY3CT": {"uuid": "jrquTAh5W_L3", "name": "hostless-condition-guard", "params": [{"__ref": "sNx5Qldg9Nab"}, {"__ref": "6gueoazfR5Bc"}, {"__ref": "35zv2X6_YXz2"}, {"__ref": "eUokKTJCvEAq"}], "states": [], "tplTree": {"__ref": "XSxr40-Oar_G"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "xR3fDPHHtPSX"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "uRN3sgohWc6E"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "awAmbZVAiMcx": {"uuid": "TuVYJijKINpK", "name": "hostless-side-effect", "params": [{"__ref": "kkpcXDSfHMg7"}, {"__ref": "j9s90evyeDXG"}, {"__ref": "KPz0lFImyqOj"}], "states": [], "tplTree": {"__ref": "aKfY6Ev5dyvY"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "AAE5bDarv7vW"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "8AK8wfAtI01Y"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "-QDuc23__xpC": {"type": {"__ref": "Y8jIe067p742"}, "variable": {"__ref": "Dq-O_5-MpDWf"}, "uuid": "Jjo3UxDrehSD", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "u5IKQT5XfI03"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "CgfS5K7t444W": {"type": {"__ref": "rhruYyYQsnlq"}, "variable": {"__ref": "7YsVb7V3juw-"}, "uuid": "ykGeVH7xTNow", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "Load the iframe while editing in Plasmic Studio", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "ztfOR_Bstte-": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8O7N5n-IAdqB", "parent": null, "locked": null, "vsettings": [{"__ref": "tFBnlJN2sZvF"}], "__type": "TplTag"}, "eqECLHDXJi-_": {"uuid": "7R4EO7vqER-v", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "VukX7aoATnkP": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "<PERSON><PERSON><PERSON>", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "2JucG_Ld3Zdi"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "WUDj0RM3vV48": {"type": {"__ref": "1KaJxutOa_J5"}, "variable": {"__ref": "yDqOHhcTPkrk"}, "uuid": "zwLF6ueKwpDH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "1rvA4-CyKSKL"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Source URL", "about": "URL to a video file.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "s2ia8u3SiICl": {"type": {"__ref": "9CuX_0ERe5D9"}, "variable": {"__ref": "dfhZIQT0WyE2"}, "uuid": "MXkpj4_dN9wo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Auto Play", "about": "Whether the video show automatically start playing when the player loads. Chrome and other browsers require 'muted' to also be set for 'autoplay' to work.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "qBfPN_kVPIEa": {"type": {"__ref": "X_CE2_z_skvD"}, "variable": {"__ref": "xiPQXxlLkNxa"}, "uuid": "aifBy1AxbJF3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "PLUvxrncZSbn"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Show Controls", "about": "Whether the video player controls should be displayed", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "hQygJfrEbd19": {"type": {"__ref": "eEX3KkUpiZoN"}, "variable": {"__ref": "K04pD1gRqFxa"}, "uuid": "A37KTt6Rx69B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Plays inline", "about": "Usually on mobile, when tilted landscape, videos can play fullscreen. Turn this on to prevent that.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "FU54w9zS_qtf": {"type": {"__ref": "XPAc7xK50gll"}, "variable": {"__ref": "rRhuujaXUbC1"}, "uuid": "4NkGpb7WBifO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Loop", "about": "Whether the video should be played again after it finishes", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "fyXp8nOg15_s": {"type": {"__ref": "9-aaxCeb77xA"}, "variable": {"__ref": "D_c68ESCsyBD"}, "uuid": "QX4WACngvoQk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Muted", "about": "Whether audio should be muted", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "aC4Hhmn8O8RR": {"type": {"__ref": "O2t9x6luhEsX"}, "variable": {"__ref": "ft8iWCVnGCzO"}, "uuid": "JF0TXVNoF-HA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Poster (placeholder) image", "about": "Image to show while video is downloading", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "5ej7BhA3dPeK": {"type": {"__ref": "SwG3kQa0_DqH"}, "variable": {"__ref": "v0qIwzXt4IjG"}, "uuid": "XQ1y2IQ6y1uG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Preload", "about": "Whether to preload nothing, metadata only, or the full video", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "y_azsFA64JXq": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "xThPd4P7Gm9L", "parent": null, "locked": null, "vsettings": [{"__ref": "DFm1COCpsUDh"}], "__type": "TplTag"}, "pC4wxDUkmYBt": {"uuid": "w-jbdHfz1xgY", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "SFovWevvYqpH": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "HTML Video", "importName": "Video", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "dvYZupE8h_8H"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "CKBtARqpD7tj": {"type": {"__ref": "HCAXUQT2TgYW"}, "variable": {"__ref": "_v_a6xvzfUj-"}, "uuid": "kLG8DBnP1WMm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "The HTML code to be embedded", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "fSA3x_hOrHCa": {"type": {"__ref": "IAbJSweZNgdw"}, "variable": {"__ref": "wwf68HUEpi35"}, "uuid": "8EcIQtoHGsmu", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "<PERSON>de in editor", "about": "Disable running the code while editing in Plasmic Studio (may require reload)", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "K7SUiBaXG1Wz": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7kiPl2mWe7qt", "parent": null, "locked": null, "vsettings": [{"__ref": "S7XmE7-SUMzf"}], "__type": "TplTag"}, "gbZC2yKzz90c": {"uuid": "IFoVJ1Z3Sdi6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "7CflaUCfkCqK": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Embed HTML", "importName": "Embed", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "PJwtbpUt3gc7"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "VDTDvYhGib6J": {"type": {"__ref": "eEHvH4elOi1R"}, "variable": {"__ref": "WVXhpCiCpeB1"}, "uuid": "3qC5H4Nse5TT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "uE68UTRhDDj_"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "The name of the variable to store the data in", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "3MSN4sZjigiL": {"type": {"__ref": "QBfrHrPBicu1"}, "variable": {"__ref": "4zP1ZpIf0uX0"}, "uuid": "0JPe1eQtDq55", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "l9UUWhaSZqPH"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "tIyGxWuCQx-X": {"type": {"__ref": "73rL38D_jMBO"}, "tplSlot": {"__ref": "ya7q0M-zYGn7"}, "variable": {"__ref": "XwkHUh50q0_3"}, "uuid": "byLkvLl-9vc4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "02shWaU0xxIs": {"tag": "div", "name": null, "children": [{"__ref": "ya7q0M-zYGn7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "XUfyHtiXTLq8", "parent": null, "locked": null, "vsettings": [{"__ref": "o9mzeW3uOTug"}], "__type": "TplTag"}, "n6v7U7i357dR": {"uuid": "2V7KRwPwtnlM", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gPaPieigYdSJ": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Data Provider", "importName": "DataProvider", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": true, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "sNx5Qldg9Nab": {"type": {"__ref": "g_UMhhObU_hY"}, "tplSlot": {"__ref": "13DCsga530Nj"}, "variable": {"__ref": "yCxlvsxUw36s"}, "uuid": "pwnPNk6E5nU_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "6gueoazfR5Bc": {"type": {"__ref": "DIZ9uHGSzMGR"}, "variable": {"__ref": "wJNyz2hMlwLR"}, "uuid": "DtXLVpcfi0Wo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "IUgHs5QhxQcg"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Condition", "about": "The condition to guard against", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "35zv2X6_YXz2": {"type": {"__ref": "lAVKVha7dFve"}, "variable": {"__ref": "iZBkjlzl1CLP"}, "uuid": "y-wpt9_6EhGY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "On condition false", "about": "The action to run when the condition is not satisfied", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "eUokKTJCvEAq": {"type": {"__ref": "FxbB5L7JBYQ4"}, "variable": {"__ref": "z-pQaJfMsQra"}, "uuid": "bxNyxYFwQoiV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Skip Paths", "about": "Paths that the action should not run", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "XSxr40-Oar_G": {"tag": "div", "name": null, "children": [{"__ref": "13DCsga530Nj"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "zkOGflIShjiG", "parent": null, "locked": null, "vsettings": [{"__ref": "E7OWBOPP9TWv"}], "__type": "TplTag"}, "xR3fDPHHtPSX": {"uuid": "Pk1zb_JiBYOc", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "uRN3sgohWc6E": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Condition Guard", "importName": "Condition<PERSON><PERSON>", "description": "Ensure some condition, or else run an interaction. Examples: ensure all users have a database row, or require new users to setup a profile.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "kkpcXDSfHMg7": {"type": {"__ref": "MLvTIts4j9G6"}, "variable": {"__ref": "V_xUzNgAVJUF"}, "uuid": "0_iWDmVLrchr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "On load", "about": "Actions to run when this Side Effect component is mounted.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "j9s90evyeDXG": {"type": {"__ref": "VzePEaeEK4XE"}, "variable": {"__ref": "itfqJCNLlIU9"}, "uuid": "f8d599BRrvFJ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "On unload", "about": "Actions to run when this Side Effect component is unmounted.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "KPz0lFImyqOj": {"type": {"__ref": "eTixqbVawOoW"}, "variable": {"__ref": "rFWD7ixcrxCh"}, "uuid": "IKxLv-tB6rvS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "When data changes", "about": "List of values which should trigger a re-run of the actions if changed.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "aKfY6Ev5dyvY": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E9WrO_6lJdSZ", "parent": null, "locked": null, "vsettings": [{"__ref": "XJuIlBKXwFLU"}], "__type": "TplTag"}, "AAE5bDarv7vW": {"uuid": "nif9W-sOFgbT", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8AK8wfAtI01Y": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Side Effect", "importName": "SideEffect", "description": "Run actions on load, unload, and when data changes.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "Y8jIe067p742": {"name": "text", "__type": "Text"}, "Dq-O_5-MpDWf": {"name": "src", "uuid": "EnwNyUa_UoVD", "__type": "Var"}, "u5IKQT5XfI03": {"code": "\"https://www.example.com\"", "fallback": null, "__type": "CustomCode"}, "rhruYyYQsnlq": {"name": "bool", "__type": "BoolType"}, "7YsVb7V3juw-": {"name": "preview", "uuid": "mbLQ0pFLssZs", "__type": "Var"}, "tFBnlJN2sZvF": {"variants": [{"__ref": "eqECLHDXJi-_"}], "args": [], "attrs": {}, "rs": {"__ref": "jmzIpamEAlya"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2JucG_Ld3Zdi": {"values": {"width": "300px", "height": "150px", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "1KaJxutOa_J5": {"name": "text", "__type": "Text"}, "yDqOHhcTPkrk": {"name": "src", "uuid": "ODMFL1xR0b-s", "__type": "Var"}, "1rvA4-CyKSKL": {"code": "\"https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.webm\"", "fallback": null, "__type": "CustomCode"}, "9CuX_0ERe5D9": {"name": "bool", "__type": "BoolType"}, "dfhZIQT0WyE2": {"name": "autoPlay", "uuid": "5g3gUjFE1xpi", "__type": "Var"}, "X_CE2_z_skvD": {"name": "bool", "__type": "BoolType"}, "xiPQXxlLkNxa": {"name": "controls", "uuid": "Nix3maVpomcj", "__type": "Var"}, "PLUvxrncZSbn": {"code": "true", "fallback": null, "__type": "CustomCode"}, "eEX3KkUpiZoN": {"name": "bool", "__type": "BoolType"}, "K04pD1gRqFxa": {"name": "playsInline", "uuid": "m_cTLoC3yrTj", "__type": "Var"}, "XPAc7xK50gll": {"name": "bool", "__type": "BoolType"}, "rRhuujaXUbC1": {"name": "loop", "uuid": "8-09WIxSihS7", "__type": "Var"}, "9-aaxCeb77xA": {"name": "bool", "__type": "BoolType"}, "D_c68ESCsyBD": {"name": "muted", "uuid": "9roPvdzVWVVx", "__type": "Var"}, "O2t9x6luhEsX": {"name": "img", "__type": "Img"}, "ft8iWCVnGCzO": {"name": "poster", "uuid": "k1voqXwXhdi7", "__type": "Var"}, "SwG3kQa0_DqH": {"name": "choice", "options": ["none", "metadata", "auto"], "__type": "Choice"}, "v0qIwzXt4IjG": {"name": "preload", "uuid": "uOTv5BPIMbeB", "__type": "Var"}, "DFm1COCpsUDh": {"variants": [{"__ref": "pC4wxDUkmYBt"}], "args": [], "attrs": {}, "rs": {"__ref": "H6VYLKd6P61a"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dvYZupE8h_8H": {"values": {"width": "640px", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "HCAXUQT2TgYW": {"name": "text", "__type": "Text"}, "_v_a6xvzfUj-": {"name": "code", "uuid": "rVw8snbCMzSr", "__type": "Var"}, "IAbJSweZNgdw": {"name": "bool", "__type": "BoolType"}, "wwf68HUEpi35": {"name": "hideInEditor", "uuid": "8VoFSy1H_96i", "__type": "Var"}, "S7XmE7-SUMzf": {"variants": [{"__ref": "gbZC2yKzz90c"}], "args": [], "attrs": {}, "rs": {"__ref": "GieRiZn61xQ3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PJwtbpUt3gc7": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "eEHvH4elOi1R": {"name": "text", "__type": "Text"}, "WVXhpCiCpeB1": {"name": "name", "uuid": "f4u3TOjjkA82", "__type": "Var"}, "uE68UTRhDDj_": {"code": "\"celebrities\"", "fallback": null, "__type": "CustomCode"}, "QBfrHrPBicu1": {"name": "any", "__type": "AnyType"}, "4zP1ZpIf0uX0": {"name": "data", "uuid": "qZ7q4YtwPiFf", "__type": "Var"}, "l9UUWhaSZqPH": {"code": "[{\"name\":\"<PERSON><PERSON> <PERSON>\",\"birthYear\":1950,\"profilePicture\":[\"https://www.fillmurray.com/200/300\"]},{\"name\":\"<PERSON> Cage\",\"birthYear\":1950,\"profilePicture\":[\"https://www.placecage.com/200/300\"]}]", "fallback": null, "__type": "CustomCode"}, "73rL38D_jMBO": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "XwkHUh50q0_3": {"name": "children", "uuid": "ZbJSDWTlV5Qn", "__type": "Var"}, "ya7q0M-zYGn7": {"param": {"__ref": "tIyGxWuCQx-X"}, "defaultContents": [], "uuid": "xhNyJjV48NS1", "parent": {"__ref": "02shWaU0xxIs"}, "locked": null, "vsettings": [{"__ref": "pC5PEAJYn_ea"}], "__type": "TplSlot"}, "o9mzeW3uOTug": {"variants": [{"__ref": "n6v7U7i357dR"}], "args": [], "attrs": {}, "rs": {"__ref": "PlYTf4_K8j_R"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "g_UMhhObU_hY": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "yCxlvsxUw36s": {"name": "children", "uuid": "zLcYcRlKYOIo", "__type": "Var"}, "DIZ9uHGSzMGR": {"name": "bool", "__type": "BoolType"}, "wJNyz2hMlwLR": {"name": "condition", "uuid": "RgzMdvAJ6GDb", "__type": "Var"}, "IUgHs5QhxQcg": {"code": "true", "fallback": null, "__type": "CustomCode"}, "lAVKVha7dFve": {"name": "func", "params": [], "__type": "FunctionType"}, "iZBkjlzl1CLP": {"name": "onNotSatisfied", "uuid": "-xZdEhw1soHx", "__type": "Var"}, "FxbB5L7JBYQ4": {"name": "any", "__type": "AnyType"}, "z-pQaJfMsQra": {"name": "skipPaths", "uuid": "wmQA6WpHEzM7", "__type": "Var"}, "13DCsga530Nj": {"param": {"__ref": "sNx5Qldg9Nab"}, "defaultContents": [], "uuid": "L6NYQ2EvOEyf", "parent": {"__ref": "XSxr40-Oar_G"}, "locked": null, "vsettings": [{"__ref": "Ub7p4R9vh1Pe"}], "__type": "TplSlot"}, "E7OWBOPP9TWv": {"variants": [{"__ref": "xR3fDPHHtPSX"}], "args": [], "attrs": {}, "rs": {"__ref": "pdPGuS5XJWiN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MLvTIts4j9G6": {"name": "func", "params": [], "__type": "FunctionType"}, "V_xUzNgAVJUF": {"name": "onMount", "uuid": "tJZPJvrd_vQb", "__type": "Var"}, "VzePEaeEK4XE": {"name": "func", "params": [], "__type": "FunctionType"}, "itfqJCNLlIU9": {"name": "onUnmount", "uuid": "WS1vfp8SlANU", "__type": "Var"}, "eTixqbVawOoW": {"name": "any", "__type": "AnyType"}, "rFWD7ixcrxCh": {"name": "deps", "uuid": "5ag_oHK0DWGc", "__type": "Var"}, "XJuIlBKXwFLU": {"variants": [{"__ref": "AAE5bDarv7vW"}], "args": [], "attrs": {}, "rs": {"__ref": "eg3LE_sDoa24"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jmzIpamEAlya": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "H6VYLKd6P61a": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "GieRiZn61xQ3": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "pC5PEAJYn_ea": {"variants": [{"__ref": "n6v7U7i357dR"}], "args": [], "attrs": {}, "rs": {"__ref": "QmTUx2yhLFMo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PlYTf4_K8j_R": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "Ub7p4R9vh1Pe": {"variants": [{"__ref": "xR3fDPHHtPSX"}], "args": [], "attrs": {}, "rs": {"__ref": "t4ou1w9dK3kD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pdPGuS5XJWiN": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "eg3LE_sDoa24": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "QmTUx2yhLFMo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "t4ou1w9dK3kD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SLyd6sgbmCFe": {"uuid": "IzYmqWf7r_Lf", "name": "hostless-timer", "params": [{"__ref": "02FCmET1mnT7"}, {"__ref": "OO-7SJp1Y0tQ"}, {"__ref": "_7PzVYmZcrwV"}], "states": [], "tplTree": {"__ref": "uBqI0VWCOIRt"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "7J4sKBiziKJ8"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "7FDA2WlplvPH"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "02FCmET1mnT7": {"type": {"__ref": "jEpoTKUq5x4p"}, "variable": {"__ref": "K9_ceW0ksh3C"}, "uuid": "TGUhuwpsKqzH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Run periodically", "about": "Actions to run periodically", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "OO-7SJp1Y0tQ": {"type": {"__ref": "FaN7B5B32vMh"}, "variable": {"__ref": "HFQ3MtYYS7md"}, "uuid": "2fxZTAQjVQlr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Seconds", "about": "Interval in seconds", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "uBqI0VWCOIRt": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "CqHHWhaj7YzG", "parent": null, "locked": null, "vsettings": [{"__ref": "ej-rqYYPaXvx"}], "__type": "TplTag"}, "7J4sKBiziKJ8": {"uuid": "JPvA2Kzna-oI", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "7FDA2WlplvPH": {"importPath": "@plasmicpkgs/plasmic-basic-components", "defaultExport": false, "displayName": "Timer", "importName": "Timer", "description": "Run something periodically", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "jEpoTKUq5x4p": {"name": "func", "params": [], "__type": "FunctionType"}, "K9_ceW0ksh3C": {"name": "onTick", "uuid": "OGqSPYPaU2MW", "__type": "Var"}, "FaN7B5B32vMh": {"name": "num", "__type": "<PERSON><PERSON>"}, "HFQ3MtYYS7md": {"name": "intervalSeconds", "uuid": "xKkKDFGQvDvV", "__type": "Var"}, "ej-rqYYPaXvx": {"variants": [{"__ref": "7J4sKBiziKJ8"}], "args": [], "attrs": {}, "rs": {"__ref": "tkuZgtYlUtB6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tkuZgtYlUtB6": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "_7PzVYmZcrwV": {"type": {"__ref": "BXNbTTOzbyb4"}, "variable": {"__ref": "LpwLvSm3yEvC"}, "uuid": "kRvq5eB51PJL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Is Running?", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "VYPp4alMq2ZJ": {"name": "plasmic-basic-components", "npmPkg": ["@plasmicpkgs/plasmic-basic-components"], "cssImport": [], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "BXNbTTOzbyb4": {"name": "bool", "__type": "BoolType"}, "LpwLvSm3yEvC": {"name": "isRunning", "uuid": "eaRIkCSjmBwQ", "__type": "Var"}, "tLUQCVQ8f-RO": {"uuid": "-JhLYD_BNZLX", "pkgId": "7a22955d-2d2c-47b8-b363-233729ec6287", "projectId": "7MTac4YvkUci2fyWritrVZ", "version": "1.0.21", "name": "plasmic-basic-components hostless", "site": {"__ref": "kzXdk6knZEiT"}, "__type": "ProjectDependency"}}, "deps": [], "version": "246-add-component-updated-at"}], ["iCCjr8rqPwuFmdvWiW18yE", {"root": "Fnz_G6HMnUr8", "map": {"LpIO5_Qfg6Ai": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "JYNFLiEJHagr": {"name": "Default Typography", "rs": {"__ref": "LpIO5_Qfg6Ai"}, "preview": null, "uuid": "lFf6RJDxfLhp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "lufHJZX4VQZO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xQeBO3UhLaTD": {"rs": {"__ref": "lufHJZX4VQZO"}, "__type": "ThemeLayoutSettings"}, "GnvPnkeBINkn": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "uXaGB5hdEQRd": {"name": "Default \"h1\"", "rs": {"__ref": "GnvPnkeBINkn"}, "preview": null, "uuid": "xBeRYPKdmpwH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Mrcdwiten17M": {"selector": "h1", "style": {"__ref": "uXaGB5hdEQRd"}, "__type": "ThemeStyle"}, "7uz142UodDYg": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "SzCkoj_t4M5q": {"name": "Default \"h2\"", "rs": {"__ref": "7uz142UodDYg"}, "preview": null, "uuid": "s5ofLVI9qEtD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "DSwWfvR5MuYW": {"selector": "h2", "style": {"__ref": "SzCkoj_t4M5q"}, "__type": "ThemeStyle"}, "Q6WPDRelM5QG": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "9sFe0msHF6ir": {"name": "Default \"h3\"", "rs": {"__ref": "Q6WPDRelM5QG"}, "preview": null, "uuid": "N4DWlxvG84od", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "v22bNRU51V_O": {"selector": "h3", "style": {"__ref": "9sFe0msHF6ir"}, "__type": "ThemeStyle"}, "5KBW6qI2cqGj": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "zxMMG-CHjK8C": {"name": "Default \"h4\"", "rs": {"__ref": "5KBW6qI2cqGj"}, "preview": null, "uuid": "O_suf6H6aPjG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "woKRdd6n6uhX": {"selector": "h4", "style": {"__ref": "zxMMG-CHjK8C"}, "__type": "ThemeStyle"}, "dMH4a1JbjaYv": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "Ba--MeuHbXMo": {"name": "Default \"h5\"", "rs": {"__ref": "dMH4a1JbjaYv"}, "preview": null, "uuid": "uHvM-YXa2eLu", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "-uMB_VaQZiS3": {"selector": "h5", "style": {"__ref": "Ba--MeuHbXMo"}, "__type": "ThemeStyle"}, "F7yja1h4pdt2": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "GkFDMfsKPvZe": {"name": "Default \"h6\"", "rs": {"__ref": "F7yja1h4pdt2"}, "preview": null, "uuid": "j1ERrie_7zoa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "byQslVmRHIZW": {"selector": "h6", "style": {"__ref": "GkFDMfsKPvZe"}, "__type": "ThemeStyle"}, "DIThBbGo7Bsx": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "jvi7OehfZlM0": {"name": "Default \"a\"", "rs": {"__ref": "DIThBbGo7Bsx"}, "preview": null, "uuid": "sU1dfPM9XgYC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "w6x-OG97ogjV": {"selector": "a", "style": {"__ref": "jvi7OehfZlM0"}, "__type": "ThemeStyle"}, "QQTQNbzzFQ69": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "wnE3fFcRXCaK": {"name": "Default \"a:hover\"", "rs": {"__ref": "QQTQNbzzFQ69"}, "preview": null, "uuid": "poloVl4rFhPZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "2lVMKOuT6mkQ": {"selector": "a:hover", "style": {"__ref": "wnE3fFcRXCaK"}, "__type": "ThemeStyle"}, "7SjFVLJJpIKR": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "YndzrkafDwy3": {"name": "Default \"blockquote\"", "rs": {"__ref": "7SjFVLJJpIKR"}, "preview": null, "uuid": "5UAoBGKLyOM1", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "UFf1K6hsMnmm": {"selector": "blockquote", "style": {"__ref": "YndzrkafDwy3"}, "__type": "ThemeStyle"}, "Opjkfqlo3bXO": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "rOM0VI_sxYt-": {"name": "Default \"code\"", "rs": {"__ref": "Opjkfqlo3bXO"}, "preview": null, "uuid": "iQQDSomc7mBG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7DbMXqPkA7Wb": {"selector": "code", "style": {"__ref": "rOM0VI_sxYt-"}, "__type": "ThemeStyle"}, "5xUl1Hjym-id": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "-18cRDsRGXJI": {"name": "Default \"pre\"", "rs": {"__ref": "5xUl1Hjym-id"}, "preview": null, "uuid": "jP69JsTM-NyM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "tBezVvaqIhW6": {"selector": "pre", "style": {"__ref": "-18cRDsRGXJI"}, "__type": "ThemeStyle"}, "blm3LGhSCElh": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "McmyN-xdROya": {"name": "Default \"ol\"", "rs": {"__ref": "blm3LGhSCElh"}, "preview": null, "uuid": "TaK98kKwBJIL", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "e6PHsV3PJxG3": {"selector": "ol", "style": {"__ref": "McmyN-xdROya"}, "__type": "ThemeStyle"}, "TaKibTVjYpGm": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "0BRSLGUkdQzK": {"name": "Default \"ul\"", "rs": {"__ref": "TaKibTVjYpGm"}, "preview": null, "uuid": "v3fD3EwiRxhU", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "JUPHVla_AGqM": {"selector": "ul", "style": {"__ref": "0BRSLGUkdQzK"}, "__type": "ThemeStyle"}, "1-xA_R_qaU-3": {"defaultStyle": {"__ref": "JYNFLiEJHagr"}, "styles": [{"__ref": "Mrcdwiten17M"}, {"__ref": "DSwWfvR5MuYW"}, {"__ref": "v22bNRU51V_O"}, {"__ref": "woKRdd6n6uhX"}, {"__ref": "-uMB_VaQZiS3"}, {"__ref": "byQslVmRHIZW"}, {"__ref": "w6x-OG97ogjV"}, {"__ref": "2lVMKOuT6mkQ"}, {"__ref": "UFf1K6hsMnmm"}, {"__ref": "7DbMXqPkA7Wb"}, {"__ref": "tBezVvaqIhW6"}, {"__ref": "e6PHsV3PJxG3"}, {"__ref": "JUPHVla_AGqM"}], "layout": {"__ref": "xQeBO3UhLaTD"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "ahAn10eFjpYS": {"name": "text", "__type": "Text"}, "mxv9WV8x0hcC": {"name": "Screen", "uuid": "67HN45HOBxGf", "__type": "Var"}, "5xxReGEMqGys": {"type": {"__ref": "ahAn10eFjpYS"}, "variable": {"__ref": "mxv9WV8x0hcC"}, "uuid": "684lCiMqIpSi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "5aFY0OymwIIh": {"type": "global-screen", "param": {"__ref": "5xxReGEMqGys"}, "uuid": "qlYRqC2DyhpX", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "VIosIrbQRYdm": {"uuid": "AZbZB8Dkxs9C", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Fnz_G6HMnUr8": {"components": [{"__ref": "tZqG4OfmugDb"}, {"__ref": "_508Rzy9eVKN"}, {"__ref": "e13a_ukefuzg"}, {"__ref": "M3D_20Qsreym"}, {"__ref": "SxIYAk_gqRFL"}], "arenas": [{"__ref": "qXJ2nt0vvAt2"}], "pageArenas": [{"__ref": "TuIXVbeBQoYs"}], "componentArenas": [{"__ref": "ZZy-gqT9jwN8"}, {"__ref": "i2_aBIwJZ9Cw"}], "globalVariantGroups": [{"__ref": "5aFY0OymwIIh"}], "userManagedFonts": [], "globalVariant": {"__ref": "VIosIrbQRYdm"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "1-xA_R_qaU-3"}], "activeTheme": {"__ref": "1-xA_R_qaU-3"}, "imageAssets": [{"__ref": "QRd8r00wuTBP"}, {"__ref": "gGwu7LpANEdW"}, {"__ref": "NyHtd6BFEt5h"}], "projectDependencies": [{"__xref": {"uuid": "a3a6a4ab-6ef7-466e-8589-a26854d0253c", "iid": "tLUQCVQ8f-RO"}}], "activeScreenVariantGroup": {"__ref": "5aFY0OymwIIh"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"button": {"__ref": "M3D_20Qsreym"}, "text-input": {"__ref": "SxIYAk_gqRFL"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site"}, "qXJ2nt0vvAt2": {"name": "Custom arena 1", "children": [{"__ref": "wp7l7GNlS5a2"}], "__type": "Arena"}, "tZqG4OfmugDb": {"uuid": "tl3SQwLzRCwL", "name": "hostless-plasmic-head", "params": [{"__ref": "6E-BG1PCIlgW"}, {"__ref": "xYw7ij8R3WbD"}, {"__ref": "usmiXTsXCB7B"}, {"__ref": "592xGhbtXFTb"}], "states": [], "tplTree": {"__ref": "aGuy4aMFf9bG"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "mFsWkPfLyUrJ"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "86TTaV6SoCCA"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "_508Rzy9eVKN": {"uuid": "bNhFrmCctfMq", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "uUDZiFhtjqZb"}, {"__ref": "Pvg0w0s8e970"}, {"__ref": "cEJBvNAqM_a6"}, {"__ref": "FJwRtb8qlgPz"}, {"__ref": "kZpgFumxZGFu"}], "states": [], "tplTree": {"__ref": "0jsog_rpACPt"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "w32Pd3g0rV3g"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "iyXW4jDxhsN6"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "6E-BG1PCIlgW": {"type": {"__ref": "1vuvbD0cJ4Up"}, "variable": {"__ref": "SHDlsS19Tf72"}, "uuid": "UoBE8IHfGogG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "xYw7ij8R3WbD": {"type": {"__ref": "VOWmivD1hNav"}, "variable": {"__ref": "eSBPqgNSr_X3"}, "uuid": "UYntsGZqc9iP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "usmiXTsXCB7B": {"type": {"__ref": "uORcaQQuG_ys"}, "variable": {"__ref": "HKp-bdg2U4lb"}, "uuid": "y2iva460Cd7l", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "592xGhbtXFTb": {"type": {"__ref": "H9WFM3Ln9Y6U"}, "variable": {"__ref": "YBLJT2zpmogy"}, "uuid": "ojqlOeDKKJ7x", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "aGuy4aMFf9bG": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "m7mdyIBbpgQh", "parent": null, "locked": null, "vsettings": [{"__ref": "JwwBTncKBpZi"}], "__type": "TplTag"}, "mFsWkPfLyUrJ": {"uuid": "Tv1F26OVoyxw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "86TTaV6SoCCA": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "uUDZiFhtjqZb": {"type": {"__ref": "yPVxoY4xuAC4"}, "variable": {"__ref": "C-P6cOgZ0fD_"}, "uuid": "har6o8XZeD9V", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "Pvg0w0s8e970": {"type": {"__ref": "8IYfMkl5IO09"}, "variable": {"__ref": "Rv8YILbxAyOM"}, "uuid": "ms26fHf95Mbr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "cEJBvNAqM_a6": {"type": {"__ref": "7VLR_pCxK2wk"}, "tplSlot": {"__ref": "HLQO9MhH2hLb"}, "variable": {"__ref": "TYsbvENlBFAb"}, "uuid": "DuKtDsN5zLFV", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "FJwRtb8qlgPz": {"type": {"__ref": "g9AvKpfo73Jf"}, "variable": {"__ref": "n8h7_CRHwcgK"}, "uuid": "WDd7h6kCl4lx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "kZpgFumxZGFu": {"type": {"__ref": "ksFIQ6Ok0Vdk"}, "variable": {"__ref": "SnGAi7SgcAx3"}, "uuid": "YKr5IJTC5STX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "0jsog_rpACPt": {"tag": "div", "name": null, "children": [{"__ref": "HLQO9MhH2hLb"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "GF_jLucM_583", "parent": null, "locked": null, "vsettings": [{"__ref": "vsTlsKOJ7YJT"}], "__type": "TplTag"}, "w32Pd3g0rV3g": {"uuid": "p7HJgVhL-Fe0", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "iyXW4jDxhsN6": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "1vuvbD0cJ4Up": {"name": "text", "__type": "Text"}, "SHDlsS19Tf72": {"name": "title", "uuid": "cf_w2kP1UTvf", "__type": "Var"}, "VOWmivD1hNav": {"name": "text", "__type": "Text"}, "eSBPqgNSr_X3": {"name": "description", "uuid": "jcKRZx5rv2q5", "__type": "Var"}, "uORcaQQuG_ys": {"name": "img", "__type": "Img"}, "HKp-bdg2U4lb": {"name": "image", "uuid": "rkEHI6CknslM", "__type": "Var"}, "H9WFM3Ln9Y6U": {"name": "text", "__type": "Text"}, "YBLJT2zpmogy": {"name": "canonical", "uuid": "pPBEzJB9JHSm", "__type": "Var"}, "JwwBTncKBpZi": {"variants": [{"__ref": "mFsWkPfLyUrJ"}], "args": [], "attrs": {}, "rs": {"__ref": "46WxjVD9AmNi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yPVxoY4xuAC4": {"name": "any", "__type": "AnyType"}, "C-P6cOgZ0fD_": {"name": "dataOp", "uuid": "YYeTtt5nZJbw", "__type": "Var"}, "8IYfMkl5IO09": {"name": "text", "__type": "Text"}, "Rv8YILbxAyOM": {"name": "name", "uuid": "RpM3Oioi7ef6", "__type": "Var"}, "7VLR_pCxK2wk": {"name": "renderFunc", "params": [{"__ref": "Hh3UX8UesF4o"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "TYsbvENlBFAb": {"name": "children", "uuid": "UGh0eATHOKGD", "__type": "Var"}, "g9AvKpfo73Jf": {"name": "num", "__type": "<PERSON><PERSON>"}, "n8h7_CRHwcgK": {"name": "pageSize", "uuid": "mn38YlGXPjrQ", "__type": "Var"}, "ksFIQ6Ok0Vdk": {"name": "num", "__type": "<PERSON><PERSON>"}, "SnGAi7SgcAx3": {"name": "pageIndex", "uuid": "V3XYoSg0cFvL", "__type": "Var"}, "HLQO9MhH2hLb": {"param": {"__ref": "cEJBvNAqM_a6"}, "defaultContents": [], "uuid": "2ThoPRDpVFf1", "parent": {"__ref": "0jsog_rpACPt"}, "locked": null, "vsettings": [{"__ref": "EGmS1NhNsIv9"}], "__type": "TplSlot"}, "vsTlsKOJ7YJT": {"variants": [{"__ref": "w32Pd3g0rV3g"}], "args": [], "attrs": {}, "rs": {"__ref": "-SemX9G2iwvT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "46WxjVD9AmNi": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "Hh3UX8UesF4o": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "vwvvigt38BFT"}, "__type": "ArgType"}, "EGmS1NhNsIv9": {"variants": [{"__ref": "w32Pd3g0rV3g"}], "args": [], "attrs": {}, "rs": {"__ref": "2HCEY9LaVu22"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-SemX9G2iwvT": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "vwvvigt38BFT": {"name": "any", "__type": "AnyType"}, "2HCEY9LaVu22": {"values": {}, "mixins": [], "__type": "RuleSet"}, "e13a_ukefuzg": {"uuid": "r76LI_BGkTwO", "name": "timer-test", "params": [{"__ref": "aERZn6kZAKWt"}, {"__ref": "WLoJoMRB2EpP"}, {"__ref": "OchNuHXqMdub"}, {"__ref": "6OsrOJ6N6WFL"}, {"__ref": "5GGxOT7fD_P3"}, {"__ref": "vlk7b-ofyDUi"}, {"__ref": "_rc4bseF3ywA"}, {"__ref": "aF6PWOvrgEb0"}], "states": [{"__ref": "XW2TiUsYm85q"}, {"__ref": "NKuXf5GFanNB"}, {"__ref": "pu9Wxw_mOInY"}, {"__ref": "l6S6hzREslsO"}], "tplTree": {"__ref": "-e-kDsxR2GzL"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "8fMTSV9zRIab"}], "variantGroups": [], "pageMeta": {"__ref": "wILNcnpZcQal"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "TuIXVbeBQoYs": {"component": {"__ref": "e13a_ukefuzg"}, "matrix": {"__ref": "Mf2EzMBxI5rZ"}, "customMatrix": {"__ref": "p5iLNAUAZioZ"}, "__type": "PageArena"}, "wp7l7GNlS5a2": {"uuid": "3pGtn2hssm6g", "width": 800, "height": 800, "container": {"__ref": "QXLnouVnQIu5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8fMTSV9zRIab"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": 61, "left": -136, "__type": "ArenaFrame"}, "-e-kDsxR2GzL": {"tag": "div", "name": null, "children": [{"__ref": "2jLXJd5sNBOP"}, {"__ref": "OMPaGxj9wtEK"}, {"__ref": "cjMIn7vVuo6Z"}, {"__ref": "gd-yb2DGhEn2"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SxuS-LPHQ7mL", "parent": null, "locked": null, "vsettings": [{"__ref": "uU0zAkgMz1qf"}], "__type": "TplTag"}, "8fMTSV9zRIab": {"uuid": "9l_e3RLzqYCO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "wILNcnpZcQal": {"path": "/timer-test", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "Mf2EzMBxI5rZ": {"rows": [{"__ref": "PpMFMgCVQUMm"}], "__type": "ArenaFrameGrid"}, "p5iLNAUAZioZ": {"rows": [{"__ref": "SgB4_qjOzYuj"}], "__type": "ArenaFrameGrid"}, "QXLnouVnQIu5": {"name": null, "component": {"__ref": "e13a_ukefuzg"}, "uuid": "jlu_9ZAU7DKy", "parent": null, "locked": null, "vsettings": [{"__ref": "xSXkvgaqcjsU"}], "__type": "TplComponent"}, "uU0zAkgMz1qf": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {}, "rs": {"__ref": "TDR_CyZHqfgj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PpMFMgCVQUMm": {"cols": [{"__ref": "_1lD7CzSbAHI"}, {"__ref": "la9-hCblxLKI"}], "rowKey": {"__ref": "8fMTSV9zRIab"}, "__type": "ArenaFrameRow"}, "SgB4_qjOzYuj": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "xSXkvgaqcjsU": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "HuLGsQqH7eq4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TDR_CyZHqfgj": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "_1lD7CzSbAHI": {"frame": {"__ref": "sce3KAbPQZFQ"}, "cellKey": null, "__type": "ArenaFrameCell"}, "la9-hCblxLKI": {"frame": {"__ref": "fdZnu-d3M465"}, "cellKey": null, "__type": "ArenaFrameCell"}, "HuLGsQqH7eq4": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sce3KAbPQZFQ": {"uuid": "1uBSY4qmrkeT", "width": 1366, "height": 768, "container": {"__ref": "UxFMQTjRZXag"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8fMTSV9zRIab"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "fdZnu-d3M465": {"uuid": "_fMTB25MSupi", "width": 414, "height": 736, "container": {"__ref": "jDc5ugSowmbR"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8fMTSV9zRIab"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "UxFMQTjRZXag": {"name": null, "component": {"__ref": "e13a_ukefuzg"}, "uuid": "tUb7wu51fpXm", "parent": null, "locked": null, "vsettings": [{"__ref": "12uK4MqbKNCX"}], "__type": "TplComponent"}, "jDc5ugSowmbR": {"name": null, "component": {"__ref": "e13a_ukefuzg"}, "uuid": "b94-GNl-t5z3", "parent": null, "locked": null, "vsettings": [{"__ref": "bAFw64irq0zD"}], "__type": "TplComponent"}, "12uK4MqbKNCX": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "SMvnx1VLVYkb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bAFw64irq0zD": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "CYhV_3r7uI_M"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SMvnx1VLVYkb": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CYhV_3r7uI_M": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OMPaGxj9wtEK": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "o5XpkdHJHitN", "parent": {"__ref": "-e-kDsxR2GzL"}, "locked": null, "vsettings": [{"__ref": "AjO09twwFaHw"}], "__type": "TplTag"}, "AjO09twwFaHw": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {"id": {"__ref": "eijBcVWYGtVg"}}, "rs": {"__ref": "HmiscZ2bu9JP"}, "dataCond": null, "dataRep": null, "text": {"__ref": "xrvo5giyNP-U"}, "columnsConfig": null, "__type": "VariantSetting"}, "HmiscZ2bu9JP": {"values": {"position": "relative", "font-size": "10em"}, "mixins": [], "__type": "RuleSet"}, "gd-yb2DGhEn2": {"tag": "div", "name": null, "children": [{"__ref": "38N9M1uylUjX"}, {"__ref": "8DN-CfwZDomb"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "aMJJsmw_grfp", "parent": {"__ref": "-e-kDsxR2GzL"}, "locked": null, "vsettings": [{"__ref": "KWbx12W_U5V0"}], "__type": "TplTag"}, "KWbx12W_U5V0": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {}, "rs": {"__ref": "-ZZYg2eEXfKF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-ZZYg2eEXfKF": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "QRd8r00wuTBP": {"uuid": "puspbJrN_udM", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "gGwu7LpANEdW": {"uuid": "zqJb2y916R08", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "M3D_20Qsreym": {"uuid": "MLSPTET2L_gc", "name": "<PERSON><PERSON>", "params": [{"__ref": "soQ0vUU8_0Gw"}, {"__ref": "FLCzirD8MdMJ"}, {"__ref": "6br7NRgyu1cj"}, {"__ref": "cP-XNqw5CqOP"}, {"__ref": "vdXCJxTWWPks"}, {"__ref": "wRx09KricQ1w"}, {"__ref": "4pfpOTm25Avq"}, {"__ref": "sXaAWJIME8VU"}, {"__ref": "rpU0QXsA7TR1"}, {"__ref": "rE0ER52km5Od"}, {"__ref": "hjbYKVqGTIEv"}, {"__ref": "A8JFFLZ_JeXi"}, {"__ref": "uGW_Wj9bS-dR"}, {"__ref": "S4Lxm_zlTnU0"}, {"__ref": "OVjQhRWjR8BI"}, {"__ref": "xUy4isuLc-fA"}, {"__ref": "LeEFzD5rjZRA"}, {"__ref": "EA7yX3DSmeHC"}], "states": [{"__ref": "ari-MQpsgy-6"}, {"__ref": "KHlHd3uCeh3F"}, {"__ref": "3NNrjRQ8KVqO"}, {"__ref": "aTB05hAixkdT"}, {"__ref": "1zkACrfYqGNl"}, {"__ref": "L0ctXHKAc7v1"}], "tplTree": {"__ref": "Te-CE6Dd6rGs"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "klo4wGsplYfu"}, {"__ref": "Lm_Y4jYRxbu0"}, {"__ref": "fLNraCYnhsPw"}, {"__ref": "rXS3ClnNzXCb"}, {"__ref": "QClRYrh3WRQd"}], "variantGroups": [{"__ref": "i-hQCl_7TWIx"}, {"__ref": "2hf86NcLQ4N2"}, {"__ref": "ABhERiaFKJty"}, {"__ref": "RiJHlchMNNjf"}, {"__ref": "FNXqANmtGzgi"}, {"__ref": "XVHFUpOQXFl5"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "oshTiZR9kfnA"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true, "__type": "Component", "updatedAt": null}, "ZZy-gqT9jwN8": {"component": {"__ref": "M3D_20Qsreym"}, "matrix": {"__ref": "2xV75X0girrC"}, "customMatrix": {"__ref": "TNczPUjc5Sb2"}, "__type": "ComponentArena"}, "38N9M1uylUjX": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "z7YGz3nZWd3t", "parent": {"__ref": "gd-yb2DGhEn2"}, "locked": null, "vsettings": [{"__ref": "lMkbeIUzhe-I"}], "__type": "TplComponent"}, "soQ0vUU8_0Gw": {"type": {"__ref": "CZic9HAuZ6eO"}, "tplSlot": {"__ref": "XRp9gcLa06VR"}, "variable": {"__ref": "fXFoV5D1A77W"}, "uuid": "V5mokc7xAeD2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false, "__type": "SlotParam"}, "FLCzirD8MdMJ": {"type": {"__ref": "e-xSmRdTYdkf"}, "state": {"__ref": "ari-MQpsgy-6"}, "variable": {"__ref": "7CDX-ycF7gKk"}, "uuid": "qa22ZUKlZlJc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "6br7NRgyu1cj": {"type": {"__ref": "7mAv0ZQrl5TE"}, "state": {"__ref": "KHlHd3uCeh3F"}, "variable": {"__ref": "dDjn87bPCWFb"}, "uuid": "EWQUNYzZd2oT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "cP-XNqw5CqOP": {"type": {"__ref": "Od8vjdBmlqnU"}, "tplSlot": {"__ref": "SAt7F884ywWJ"}, "variable": {"__ref": "rimoV9sUjLkz"}, "uuid": "2V-pU25OyDnn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "vdXCJxTWWPks": {"type": {"__ref": "tW4AKuMmIgCR"}, "tplSlot": {"__ref": "q3kejXe_YdDY"}, "variable": {"__ref": "ppZZ0v_7c4mL"}, "uuid": "FFvNIv1Wuso5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "wRx09KricQ1w": {"type": {"__ref": "_ULR6fFcpQRI"}, "state": {"__ref": "3NNrjRQ8KVqO"}, "variable": {"__ref": "gYU_niuKWiRH"}, "uuid": "g5UdKMEa9nRw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "4pfpOTm25Avq": {"type": {"__ref": "Cpeg8eWIr1Km"}, "variable": {"__ref": "JBWAt0KBd9us"}, "uuid": "dt1ZrSv_2l1Z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "sXaAWJIME8VU": {"type": {"__ref": "cU-8jKHXfI8a"}, "state": {"__ref": "L0ctXHKAc7v1"}, "variable": {"__ref": "w-mtZM9zsAs3"}, "uuid": "ucpbyeG7FlG9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "rpU0QXsA7TR1": {"type": {"__ref": "knezJtQPuHD3"}, "state": {"__ref": "1zkACrfYqGNl"}, "variable": {"__ref": "wSoB3Tv8IxMh"}, "uuid": "WG-0fSMpLIH3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "rE0ER52km5Od": {"type": {"__ref": "zBebPD3l2Xh7"}, "state": {"__ref": "aTB05hAixkdT"}, "variable": {"__ref": "Eg49s_aZVHLW"}, "uuid": "Y4tLlKEy8iUh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "hjbYKVqGTIEv": {"type": {"__ref": "PnrFS6dF8wZE"}, "variable": {"__ref": "PIPpfB0RMN9V"}, "uuid": "r1NRedYwKU_C", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Submits form?", "about": "Whether clicking on this button submits the enclosing form or not", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "A8JFFLZ_JeXi": {"type": {"__ref": "QFoGLuadKoxY"}, "variable": {"__ref": "og54Q9GKUy7v"}, "uuid": "BjrYTAIRemqu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Open in new tab?", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "uGW_Wj9bS-dR": {"type": {"__ref": "D4uyikVcLiva"}, "state": {"__ref": "ari-MQpsgy-6"}, "variable": {"__ref": "E1q9ktC5c0w2"}, "uuid": "DhhKw5yvn6Gk", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "S4Lxm_zlTnU0": {"type": {"__ref": "kEXU6jVOo_0R"}, "state": {"__ref": "KHlHd3uCeh3F"}, "variable": {"__ref": "O546ypOwzHv9"}, "uuid": "FWNzvqFcCVti", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "OVjQhRWjR8BI": {"type": {"__ref": "JhBenvEDc0wQ"}, "state": {"__ref": "3NNrjRQ8KVqO"}, "variable": {"__ref": "aLP5cHlQi9pq"}, "uuid": "qUo2L7dPkwpI", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "xUy4isuLc-fA": {"type": {"__ref": "542VJo_qD-9n"}, "state": {"__ref": "aTB05hAixkdT"}, "variable": {"__ref": "LqfRny5wCYAu"}, "uuid": "grbLcI2m2B0t", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "LeEFzD5rjZRA": {"type": {"__ref": "YzNyMYnDA85V"}, "state": {"__ref": "1zkACrfYqGNl"}, "variable": {"__ref": "P1UyQTCbuVjc"}, "uuid": "ItiGaB5Lbio5", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "EA7yX3DSmeHC": {"type": {"__ref": "PUvbCkr33iYu"}, "state": {"__ref": "L0ctXHKAc7v1"}, "variable": {"__ref": "5IueJwfE8d9Q"}, "uuid": "AvQKbIDv5MQu", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "ari-MQpsgy-6": {"variantGroup": {"__ref": "i-hQCl_7TWIx"}, "param": {"__ref": "FLCzirD8MdMJ"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "uGW_Wj9bS-dR"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "KHlHd3uCeh3F": {"variantGroup": {"__ref": "2hf86NcLQ4N2"}, "param": {"__ref": "6br7NRgyu1cj"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "S4Lxm_zlTnU0"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "3NNrjRQ8KVqO": {"variantGroup": {"__ref": "ABhERiaFKJty"}, "param": {"__ref": "wRx09KricQ1w"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "OVjQhRWjR8BI"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "aTB05hAixkdT": {"variantGroup": {"__ref": "RiJHlchMNNjf"}, "param": {"__ref": "rE0ER52km5Od"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "xUy4isuLc-fA"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "1zkACrfYqGNl": {"variantGroup": {"__ref": "FNXqANmtGzgi"}, "param": {"__ref": "rpU0QXsA7TR1"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "LeEFzD5rjZRA"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "L0ctXHKAc7v1": {"variantGroup": {"__ref": "XVHFUpOQXFl5"}, "param": {"__ref": "sXaAWJIME8VU"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "EA7yX3DSmeHC"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "Te-CE6Dd6rGs": {"tag": "button", "name": null, "children": [{"__ref": "JOKcGb4_m0OX"}, {"__ref": "fpxFhcCeFsZX"}, {"__ref": "GWzO8WeSs9Zi"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "4jA8dG7R57xG", "parent": null, "locked": null, "vsettings": [{"__ref": "74_Wnr1IcWR2"}, {"__ref": "Vj3DRe4G67ps"}, {"__ref": "WrmLdw--5Ka2"}, {"__ref": "vrm9qQVu0cBI"}, {"__ref": "NQoJpesOeaIN"}, {"__ref": "4V9S5Z3dJBO2"}, {"__ref": "LYa7VgZIVxGy"}, {"__ref": "VIJi8BBuZ9UP"}, {"__ref": "hF25BcLilros"}, {"__ref": "DZTi-vZ4Gb4R"}, {"__ref": "wk-SgJj2YzUV"}, {"__ref": "6pZQ_l3ijFuH"}, {"__ref": "iVqEjcA4dyDJ"}, {"__ref": "fegdlLksxpve"}, {"__ref": "NG_iwa4SCj-A"}, {"__ref": "M5ECjJU9pr4u"}, {"__ref": "_wBfhxOj_RYl"}, {"__ref": "HXlJ8lqLHSQi"}, {"__ref": "aD52FjZFeND3"}, {"__ref": "crWTUgXCpd3U"}, {"__ref": "mJNbGjytWzxf"}, {"__ref": "zJ4Gia-Wixmg"}, {"__ref": "nMFG4vFAHGtN"}, {"__ref": "3Qhhfvhh4qN3"}, {"__ref": "wDXvZVkVPmVB"}, {"__ref": "XwFVj6em50q_"}, {"__ref": "ewN4vVuUSWyg"}, {"__ref": "pLx44GFh8kD9"}, {"__ref": "O5betd26YGM-"}, {"__ref": "geH82EgeMY5t"}, {"__ref": "pq-N-eMeFfYY"}, {"__ref": "6pXRjRuDsGfd"}, {"__ref": "8ure1kXbkLuq"}, {"__ref": "SSiLvurQ9cxj"}, {"__ref": "Bi55juR7Zavy"}, {"__ref": "mgqk5XyARGmk"}, {"__ref": "wOhFiEmF-Gie"}, {"__ref": "dvSwTzCEDdzK"}, {"__ref": "4DbWjPM4e98Q"}, {"__ref": "Yc1-3Ob9ukeS"}, {"__ref": "-pev2hKiO5B4"}, {"__ref": "pTvkuUsI_Lbp"}, {"__ref": "Xp9BHHguDv8_"}, {"__ref": "vEQBrE9BQ28g"}, {"__ref": "htZEQ9j_w2NH"}, {"__ref": "_F8WatVKAS04"}, {"__ref": "GlB5JG2514da"}, {"__ref": "LyJTPiOhAkCk"}, {"__ref": "hnNvwuilROZV"}, {"__ref": "OBngB8he42tY"}, {"__ref": "pShnCGXD0IfV"}, {"__ref": "zfzc0HJuRYjJ"}, {"__ref": "uzuLPLTNfIXM"}, {"__ref": "kNVMUf1hbqbt"}, {"__ref": "0qfE7-xk5kmR"}, {"__ref": "HcgMI5Ecpv44"}, {"__ref": "KIPlYCloBNcI"}, {"__ref": "euOKTNtVv80J"}, {"__ref": "eWEhtboTU9Rr"}, {"__ref": "LxPGGKtN68Br"}], "__type": "TplTag"}, "klo4wGsplYfu": {"uuid": "nWJR4WCeEMGi", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Lm_Y4jYRxbu0": {"uuid": "bFZTfOcZ4Zwu", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "fLNraCYnhsPw": {"uuid": "zmjVSYv4gIDf", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rXS3ClnNzXCb": {"uuid": "LZX7QRpWwuVe", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "QClRYrh3WRQd": {"uuid": "yYIg_CDIuTQT", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "i-hQCl_7TWIx": {"type": "component", "param": {"__ref": "FLCzirD8MdMJ"}, "linkedState": {"__ref": "ari-MQpsgy-6"}, "uuid": "QmglhAQKNr4E", "variants": [{"__ref": "Qhvd21RblTS6"}], "multi": false, "__type": "ComponentVariantGroup"}, "2hf86NcLQ4N2": {"type": "component", "param": {"__ref": "6br7NRgyu1cj"}, "linkedState": {"__ref": "KHlHd3uCeh3F"}, "uuid": "Q2Cqioe62ybK", "variants": [{"__ref": "yq9QffPpyM2J"}], "multi": false, "__type": "ComponentVariantGroup"}, "ABhERiaFKJty": {"type": "component", "param": {"__ref": "wRx09KricQ1w"}, "linkedState": {"__ref": "3NNrjRQ8KVqO"}, "uuid": "DRQAp_JAg66M", "variants": [{"__ref": "zcO4mBn4V1wT"}], "multi": false, "__type": "ComponentVariantGroup"}, "RiJHlchMNNjf": {"type": "component", "param": {"__ref": "rE0ER52km5Od"}, "linkedState": {"__ref": "aTB05hAixkdT"}, "uuid": "9F8WOfqoVvZC", "variants": [{"__ref": "iUI-B46ZLwtT"}, {"__ref": "-vX-qimNWhqd"}, {"__ref": "n27l-fRMsuCS"}], "multi": false, "__type": "ComponentVariantGroup"}, "FNXqANmtGzgi": {"type": "component", "param": {"__ref": "rpU0QXsA7TR1"}, "linkedState": {"__ref": "1zkACrfYqGNl"}, "uuid": "97a2LqkajaMt", "variants": [{"__ref": "RiiM8z4glg_B"}, {"__ref": "UMpGQ1nwhNff"}], "multi": false, "__type": "ComponentVariantGroup"}, "XVHFUpOQXFl5": {"type": "component", "param": {"__ref": "sXaAWJIME8VU"}, "linkedState": {"__ref": "L0ctXHKAc7v1"}, "uuid": "E61uq5EfQxGD", "variants": [{"__ref": "Q_bQVPxXY6kG"}, {"__ref": "QNx57Blo8rTr"}, {"__ref": "kpUxY5ieaAQf"}, {"__ref": "lomZRpMcCROC"}, {"__ref": "YWeDO5D2a9V4"}, {"__ref": "1hbJwXKAevLS"}, {"__ref": "rozacXTR5145"}, {"__ref": "wlgMCJ5ZrEuD"}, {"__ref": "KY5faWPazsCy"}, {"__ref": "Ne7YQqMc4g89"}, {"__ref": "tYFUx8VxDxot"}, {"__ref": "YPpaIyQG98LD"}, {"__ref": "0FdCSZaRreQX"}], "multi": false, "__type": "ComponentVariantGroup"}, "oshTiZR9kfnA": {"type": "button", "__type": "PlumeInfo"}, "2xV75X0girrC": {"rows": [{"__ref": "MkbbDY55eCrT"}, {"__ref": "eeNwON9B1OBt"}, {"__ref": "7Oanymkda4PN"}, {"__ref": "vD7lmg-fziVC"}, {"__ref": "yGeokpaddnEZ"}, {"__ref": "L9g4zon5hnDw"}, {"__ref": "O9PKH166GEkN"}], "__type": "ArenaFrameGrid"}, "TNczPUjc5Sb2": {"rows": [{"__ref": "-uAcpOkxASTl"}], "__type": "ArenaFrameGrid"}, "lMkbeIUzhe-I": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [{"__ref": "mQk9lErO9pb2"}, {"__ref": "aND-LTWXRE4C"}, {"__ref": "22QGRBvF2AFc"}], "attrs": {"onClick": {"__ref": "49p4EKOX7Dnq"}}, "rs": {"__ref": "hnc0EEvBcGDt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CZic9HAuZ6eO": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "fXFoV5D1A77W": {"name": "children", "uuid": "uUIoDh29leu7", "__type": "Var"}, "e-xSmRdTYdkf": {"name": "any", "__type": "AnyType"}, "7CDX-ycF7gKk": {"name": "Show Start Icon", "uuid": "L73Hxob9Yg4f", "__type": "Var"}, "7mAv0ZQrl5TE": {"name": "any", "__type": "AnyType"}, "dDjn87bPCWFb": {"name": "Show End Icon", "uuid": "DA3FLCrlTrOx", "__type": "Var"}, "Od8vjdBmlqnU": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "rimoV9sUjLkz": {"name": "start icon", "uuid": "cnoLstYYxyrN", "__type": "Var"}, "tW4AKuMmIgCR": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "ppZZ0v_7c4mL": {"name": "end icon", "uuid": "HnXzC6tT9ZCM", "__type": "Var"}, "_ULR6fFcpQRI": {"name": "any", "__type": "AnyType"}, "gYU_niuKWiRH": {"name": "Is Disabled", "uuid": "-8Xl8TxXwPV3", "__type": "Var"}, "Cpeg8eWIr1Km": {"name": "href", "__type": "HrefType"}, "JBWAt0KBd9us": {"name": "link", "uuid": "yuKsXZ6q0azw", "__type": "Var"}, "cU-8jKHXfI8a": {"name": "any", "__type": "AnyType"}, "w-mtZM9zsAs3": {"name": "Color", "uuid": "7ApH9zi33v70", "__type": "Var"}, "knezJtQPuHD3": {"name": "any", "__type": "AnyType"}, "wSoB3Tv8IxMh": {"name": "Size", "uuid": "ahd7yOpenViJ", "__type": "Var"}, "zBebPD3l2Xh7": {"name": "any", "__type": "AnyType"}, "Eg49s_aZVHLW": {"name": "<PERSON><PERSON><PERSON>", "uuid": "WZIvz2dIuBd4", "__type": "Var"}, "PnrFS6dF8wZE": {"name": "bool", "__type": "BoolType"}, "PIPpfB0RMN9V": {"name": "submitsForm", "uuid": "pjnrOuYF0Ow4", "__type": "Var"}, "QFoGLuadKoxY": {"name": "bool", "__type": "BoolType"}, "og54Q9GKUy7v": {"name": "target", "uuid": "sfviZ7hGnjOK", "__type": "Var"}, "D4uyikVcLiva": {"name": "func", "params": [{"__ref": "zfd4KpfWs3CO"}], "__type": "FunctionType"}, "E1q9ktC5c0w2": {"name": "On Show Start Icon change", "uuid": "fRE-KN0KQ_bC", "__type": "Var"}, "kEXU6jVOo_0R": {"name": "func", "params": [{"__ref": "msoLnKsBCycN"}], "__type": "FunctionType"}, "O546ypOwzHv9": {"name": "On Show End Icon change", "uuid": "xObkNSYsWqy9", "__type": "Var"}, "JhBenvEDc0wQ": {"name": "func", "params": [{"__ref": "qYSb-YCVCqw8"}], "__type": "FunctionType"}, "aLP5cHlQi9pq": {"name": "On Is Disabled change", "uuid": "hDGg6ky28jFf", "__type": "Var"}, "542VJo_qD-9n": {"name": "func", "params": [{"__ref": "kkfIHTOuXRJ2"}], "__type": "FunctionType"}, "LqfRny5wCYAu": {"name": "On Shape change", "uuid": "HCTjsv89drVR", "__type": "Var"}, "YzNyMYnDA85V": {"name": "func", "params": [{"__ref": "Jchfc8_oZqy6"}], "__type": "FunctionType"}, "P1UyQTCbuVjc": {"name": "On Size change", "uuid": "5JLYoc9IYQ57", "__type": "Var"}, "PUvbCkr33iYu": {"name": "func", "params": [{"__ref": "Shw_1eClXa0t"}], "__type": "FunctionType"}, "5IueJwfE8d9Q": {"name": "On Color change", "uuid": "h4G_r4mWDcXg", "__type": "Var"}, "JOKcGb4_m0OX": {"tag": "div", "name": "start icon container", "children": [{"__ref": "SAt7F884ywWJ"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "1rqqwyPw3FCR", "parent": {"__ref": "Te-CE6Dd6rGs"}, "locked": null, "vsettings": [{"__ref": "Cp_cKYtPiiHf"}, {"__ref": "JrDaB1UMBcIH"}, {"__ref": "YKWZXSHX_P-d"}, {"__ref": "HHfwwNPQGd0D"}], "__type": "TplTag"}, "fpxFhcCeFsZX": {"tag": "div", "name": "content container", "children": [{"__ref": "XRp9gcLa06VR"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Ls4kVAKz0G3s", "parent": {"__ref": "Te-CE6Dd6rGs"}, "locked": null, "vsettings": [{"__ref": "GPXsxS-IgXgd"}, {"__ref": "m9tlzwluWKlX"}, {"__ref": "g5-SeQJHx2aQ"}, {"__ref": "MQ1alB1-wSkD"}, {"__ref": "uubhk0NQzLVE"}], "__type": "TplTag"}, "GWzO8WeSs9Zi": {"tag": "div", "name": "end icon container", "children": [{"__ref": "q3kejXe_YdDY"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DqX_kGsKdysG", "parent": {"__ref": "Te-CE6Dd6rGs"}, "locked": null, "vsettings": [{"__ref": "LzCpfTNu8KGk"}, {"__ref": "f4boOsLGrVQh"}, {"__ref": "LpOhc794kTTU"}, {"__ref": "tSUo3UB2U4c5"}], "__type": "TplTag"}, "74_Wnr1IcWR2": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "umV0DYaQtm9b"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Vj3DRe4G67ps": {"variants": [{"__ref": "Lm_Y4jYRxbu0"}], "args": [], "attrs": {}, "rs": {"__ref": "BxKh20Cj4HrT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "WrmLdw--5Ka2": {"variants": [{"__ref": "fLNraCYnhsPw"}], "args": [], "attrs": {}, "rs": {"__ref": "vNreHcx61A2E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vrm9qQVu0cBI": {"variants": [{"__ref": "zcO4mBn4V1wT"}], "args": [], "attrs": {}, "rs": {"__ref": "vZx8LE9EtF3O"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NQoJpesOeaIN": {"variants": [{"__ref": "yq9QffPpyM2J"}], "args": [], "attrs": {}, "rs": {"__ref": "9rXE08axqVeC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4V9S5Z3dJBO2": {"variants": [{"__ref": "Qhvd21RblTS6"}], "args": [], "attrs": {}, "rs": {"__ref": "dLul3ShlR8Jd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LYa7VgZIVxGy": {"variants": [{"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "Sg3JNt57giZm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VIJi8BBuZ9UP": {"variants": [{"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "-ZGTYjMNiW3R"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hF25BcLilros": {"variants": [{"__ref": "rozacXTR5145"}], "args": [], "attrs": {}, "rs": {"__ref": "dfz1zspQ7bGY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DZTi-vZ4Gb4R": {"variants": [{"__ref": "rozacXTR5145"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "eO6dwsfvV7jg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wk-SgJj2YzUV": {"variants": [{"__ref": "rozacXTR5145"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "Npam1Va72FJC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6pZQ_l3ijFuH": {"variants": [{"__ref": "wlgMCJ5ZrEuD"}], "args": [], "attrs": {}, "rs": {"__ref": "xzndQK-7ZuvC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iVqEjcA4dyDJ": {"variants": [{"__ref": "wlgMCJ5ZrEuD"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "YYC17xHNJIMC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fegdlLksxpve": {"variants": [{"__ref": "wlgMCJ5ZrEuD"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "1msdKLX9oMU8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NG_iwa4SCj-A": {"variants": [{"__ref": "kpUxY5ieaAQf"}], "args": [], "attrs": {}, "rs": {"__ref": "HEDJx4gcTbF6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "M5ECjJU9pr4u": {"variants": [{"__ref": "kpUxY5ieaAQf"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "PxZbci50L2lh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_wBfhxOj_RYl": {"variants": [{"__ref": "kpUxY5ieaAQf"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "W5dJ3tY1hx7W"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HXlJ8lqLHSQi": {"variants": [{"__ref": "lomZRpMcCROC"}], "args": [], "attrs": {}, "rs": {"__ref": "WqeuBtEK-6-m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aD52FjZFeND3": {"variants": [{"__ref": "lomZRpMcCROC"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "0WOF-NdBQk3q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "crWTUgXCpd3U": {"variants": [{"__ref": "lomZRpMcCROC"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "YzUSH2HQEelT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mJNbGjytWzxf": {"variants": [{"__ref": "KY5faWPazsCy"}], "args": [], "attrs": {}, "rs": {"__ref": "fZ-hkNV689w8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zJ4Gia-Wixmg": {"variants": [{"__ref": "KY5faWPazsCy"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "28EO4TKmU-xN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nMFG4vFAHGtN": {"variants": [{"__ref": "KY5faWPazsCy"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "d3HjVvl0B_So"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3Qhhfvhh4qN3": {"variants": [{"__ref": "Ne7YQqMc4g89"}], "args": [], "attrs": {}, "rs": {"__ref": "kbUJBOrW3wYs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wDXvZVkVPmVB": {"variants": [{"__ref": "Ne7YQqMc4g89"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "JYZbAzhAgI5f"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XwFVj6em50q_": {"variants": [{"__ref": "Ne7YQqMc4g89"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "397Ug6TJK4X6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ewN4vVuUSWyg": {"variants": [{"__ref": "tYFUx8VxDxot"}], "args": [], "attrs": {}, "rs": {"__ref": "QPVpOsF5q678"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pLx44GFh8kD9": {"variants": [{"__ref": "tYFUx8VxDxot"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "gRuxevoEQrxj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "O5betd26YGM-": {"variants": [{"__ref": "tYFUx8VxDxot"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "nPwIOKm3sC0E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "geH82EgeMY5t": {"variants": [{"__ref": "Q_bQVPxXY6kG"}], "args": [], "attrs": {}, "rs": {"__ref": "xpezIvmqjJOS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pq-N-eMeFfYY": {"variants": [{"__ref": "Q_bQVPxXY6kG"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "4epK5YTJuysT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6pXRjRuDsGfd": {"variants": [{"__ref": "Q_bQVPxXY6kG"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "StTIBRX6S70N"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8ure1kXbkLuq": {"variants": [{"__ref": "QNx57Blo8rTr"}], "args": [], "attrs": {}, "rs": {"__ref": "kP6Ko_gCNh4F"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SSiLvurQ9cxj": {"variants": [{"__ref": "QNx57Blo8rTr"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "4db707O2etjy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Bi55juR7Zavy": {"variants": [{"__ref": "QNx57Blo8rTr"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "Q14iaWIh66y7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mgqk5XyARGmk": {"variants": [{"__ref": "YWeDO5D2a9V4"}], "args": [], "attrs": {}, "rs": {"__ref": "xSTTl3cvNKqS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wOhFiEmF-Gie": {"variants": [{"__ref": "YWeDO5D2a9V4"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "Zc0iuvwxUU4W"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dvSwTzCEDdzK": {"variants": [{"__ref": "YWeDO5D2a9V4"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "tBqezUaC_U_i"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4DbWjPM4e98Q": {"variants": [{"__ref": "RiiM8z4glg_B"}], "args": [], "attrs": {}, "rs": {"__ref": "n_KfulLq1BHX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Yc1-3Ob9ukeS": {"variants": [{"__ref": "RiiM8z4glg_B"}, {"__ref": "Qhvd21RblTS6"}], "args": [], "attrs": {}, "rs": {"__ref": "UMMYroodjXfz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-pev2hKiO5B4": {"variants": [{"__ref": "RiiM8z4glg_B"}, {"__ref": "Qhvd21RblTS6"}, {"__ref": "yq9QffPpyM2J"}], "args": [], "attrs": {}, "rs": {"__ref": "bMWP9RHnj0-V"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pTvkuUsI_Lbp": {"variants": [{"__ref": "RiiM8z4glg_B"}, {"__ref": "yq9QffPpyM2J"}], "args": [], "attrs": {}, "rs": {"__ref": "ZQVrR6tZM6Kg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Xp9BHHguDv8_": {"variants": [{"__ref": "iUI-B46ZLwtT"}], "args": [], "attrs": {}, "rs": {"__ref": "Gl_UeXQQyKqo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vEQBrE9BQ28g": {"variants": [{"__ref": "iUI-B46ZLwtT"}, {"__ref": "Qhvd21RblTS6"}], "args": [], "attrs": {}, "rs": {"__ref": "iMvB_1CoAdKK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "htZEQ9j_w2NH": {"variants": [{"__ref": "yq9QffPpyM2J"}, {"__ref": "iUI-B46ZLwtT"}], "args": [], "attrs": {}, "rs": {"__ref": "AHtvuslCJilF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_F8WatVKAS04": {"variants": [{"__ref": "RiiM8z4glg_B"}, {"__ref": "iUI-B46ZLwtT"}], "args": [], "attrs": {}, "rs": {"__ref": "inxSjISxJ1yQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GlB5JG2514da": {"variants": [{"__ref": "YPpaIyQG98LD"}], "args": [], "attrs": {}, "rs": {"__ref": "gJkpptPNCNiL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LyJTPiOhAkCk": {"variants": [{"__ref": "YPpaIyQG98LD"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "FXLjr1gHs7Oo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hnNvwuilROZV": {"variants": [{"__ref": "YPpaIyQG98LD"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "2ZCtbrP5ttRv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OBngB8he42tY": {"variants": [{"__ref": "-vX-qimNWhqd"}], "args": [], "attrs": {}, "rs": {"__ref": "-XN2UGBJMG3-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pShnCGXD0IfV": {"variants": [{"__ref": "-vX-qimNWhqd"}, {"__ref": "RiiM8z4glg_B"}], "args": [], "attrs": {}, "rs": {"__ref": "6Qpide-ufhgA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zfzc0HJuRYjJ": {"variants": [{"__ref": "0FdCSZaRreQX"}], "args": [], "attrs": {}, "rs": {"__ref": "1Ck7ii7RcvKM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uzuLPLTNfIXM": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "oUiV8JBb-rI-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kNVMUf1hbqbt": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "4qj6MUsmSeTy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0qfE7-xk5kmR": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "UMpGQ1nwhNff"}], "args": [], "attrs": {}, "rs": {"__ref": "TbRXmrDERT_-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HcgMI5Ecpv44": {"variants": [{"__ref": "UMpGQ1nwhNff"}], "args": [], "attrs": {}, "rs": {"__ref": "O6l06pAagvr3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KIPlYCloBNcI": {"variants": [{"__ref": "1hbJwXKAevLS"}], "args": [], "attrs": {}, "rs": {"__ref": "nDDMR2zMqbyp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "euOKTNtVv80J": {"variants": [{"__ref": "1hbJwXKAevLS"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "2DyDWsk5IUNd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eWEhtboTU9Rr": {"variants": [{"__ref": "1hbJwXKAevLS"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "qBl58MwXqilH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LxPGGKtN68Br": {"variants": [{"__ref": "n27l-fRMsuCS"}], "args": [], "attrs": {}, "rs": {"__ref": "AZrLNj9cKKTQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Qhvd21RblTS6": {"uuid": "4xpRDuAC0x4T", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "i-hQCl_7TWIx"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "yq9QffPpyM2J": {"uuid": "SzLWNzA-FLQi", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "2hf86NcLQ4N2"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zcO4mBn4V1wT": {"uuid": "6BCIiYLRy3Cl", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "ABhERiaFKJty"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "iUI-B46ZLwtT": {"uuid": "vVZfJeIc6o5I", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "RiJHlchMNNjf"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "-vX-qimNWhqd": {"uuid": "xqSTxEzGVyGf", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "RiJHlchMNNjf"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "n27l-fRMsuCS": {"uuid": "ahzPqDY4TSKe", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "RiJHlchMNNjf"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "RiiM8z4glg_B": {"uuid": "D57gS_UWUYOY", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "FNXqANmtGzgi"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "UMpGQ1nwhNff": {"uuid": "mvenRT-6peMr", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "FNXqANmtGzgi"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Q_bQVPxXY6kG": {"uuid": "cUaOualvKu8C", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "QNx57Blo8rTr": {"uuid": "cPw_NaR2b6iW", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kpUxY5ieaAQf": {"uuid": "8z4nnfMtYz2D", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "lomZRpMcCROC": {"uuid": "M6utl0BrSKzV", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "YWeDO5D2a9V4": {"uuid": "e9PQGOBEB5i_", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "1hbJwXKAevLS": {"uuid": "72bipNh4D1xA", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "rozacXTR5145": {"uuid": "6K3qaau_zR53", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "wlgMCJ5ZrEuD": {"uuid": "8Y8NMDnTadON", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "KY5faWPazsCy": {"uuid": "IUlfafPgeNSB", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Ne7YQqMc4g89": {"uuid": "Yh7NytQL3D24", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "tYFUx8VxDxot": {"uuid": "ICSPFbTCKfh8", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "YPpaIyQG98LD": {"uuid": "uXFID5DR9Egd", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "0FdCSZaRreQX": {"uuid": "5gz0ksY_DGXY", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "XVHFUpOQXFl5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "MkbbDY55eCrT": {"cols": [{"__ref": "YHMvvp4nH9sK"}, {"__ref": "g_GuCsIFsevq"}, {"__ref": "XGy9bo4BpSyy"}, {"__ref": "_pnlZIl-sVPu"}, {"__ref": "_yiD-ymRV2yw"}], "rowKey": null, "__type": "ArenaFrameRow"}, "eeNwON9B1OBt": {"cols": [{"__ref": "ueGRa06H1H0D"}], "rowKey": {"__ref": "i-hQCl_7TWIx"}, "__type": "ArenaFrameRow"}, "7Oanymkda4PN": {"cols": [{"__ref": "xQEDNGSciseg"}], "rowKey": {"__ref": "2hf86NcLQ4N2"}, "__type": "ArenaFrameRow"}, "vD7lmg-fziVC": {"cols": [{"__ref": "guqDovBm8Yv7"}], "rowKey": {"__ref": "ABhERiaFKJty"}, "__type": "ArenaFrameRow"}, "yGeokpaddnEZ": {"cols": [{"__ref": "X8jA4qT6qASw"}, {"__ref": "B9c6t1EL5FKQ"}, {"__ref": "1wDDNrz_6Md-"}], "rowKey": {"__ref": "RiJHlchMNNjf"}, "__type": "ArenaFrameRow"}, "L9g4zon5hnDw": {"cols": [{"__ref": "P-FwUmxjt_Nv"}, {"__ref": "aZJbsm4Kuwll"}], "rowKey": {"__ref": "FNXqANmtGzgi"}, "__type": "ArenaFrameRow"}, "O9PKH166GEkN": {"cols": [{"__ref": "YT9khuxMJTom"}, {"__ref": "eazBpgja9XWs"}, {"__ref": "-kP0hUleWg4Z"}, {"__ref": "Z7TF9S0TUlhf"}, {"__ref": "Y6LTqqHb0Xxm"}, {"__ref": "2orFMzNyXpr4"}, {"__ref": "CKoU7c7y7qfQ"}, {"__ref": "47eOyYJ9twkL"}, {"__ref": "VqrHXLFAvB8O"}, {"__ref": "0CoZL-CH5nLb"}, {"__ref": "Fe2Ypy409Eiy"}, {"__ref": "xm1UyaPbYYe2"}, {"__ref": "G6aZ9IWwm8GW"}], "rowKey": {"__ref": "XVHFUpOQXFl5"}, "__type": "ArenaFrameRow"}, "-uAcpOkxASTl": {"cols": [{"__ref": "sVH7W8M5nFX7"}, {"__ref": "MeCyM6NXpuKS"}, {"__ref": "vqc-Q4fCO-xn"}, {"__ref": "L-tuniS03rK0"}, {"__ref": "fiA7qxl-2GLK"}, {"__ref": "ApBU400dNvUF"}, {"__ref": "sE5KhdydxAr1"}, {"__ref": "bwbPPbZ1tcDG"}, {"__ref": "4Fsv11R-0UKd"}, {"__ref": "PhVzld63nuQg"}, {"__ref": "SyrKzgOraGYC"}, {"__ref": "uloIB0rWGUYM"}, {"__ref": "KQp4ruR0Wy1k"}, {"__ref": "dMO5c5rQdBzU"}, {"__ref": "OLeuUJISteqj"}, {"__ref": "sATJD1rTe1pX"}, {"__ref": "bulVEVOIGP36"}, {"__ref": "yBJVJn69ZHdE"}, {"__ref": "YS3C0c8R-MwG"}, {"__ref": "TJw2bPGrKKxW"}, {"__ref": "MHpfK_zxHGpw"}, {"__ref": "lrQFCd1lScgU"}, {"__ref": "AV1smiYk6-wL"}, {"__ref": "ok7H3CtwX6kl"}, {"__ref": "phF7kSCgi9LC"}, {"__ref": "09DvPD-pv6P5"}, {"__ref": "ubU3e5CfFFho"}, {"__ref": "u8gzuONF8FnV"}, {"__ref": "GxHB29fgDTgs"}], "rowKey": null, "__type": "ArenaFrameRow"}, "mQk9lErO9pb2": {"param": {"__ref": "cP-XNqw5CqOP"}, "expr": {"__ref": "16_muSbc3QTM"}, "__type": "Arg"}, "aND-LTWXRE4C": {"param": {"__ref": "soQ0vUU8_0Gw"}, "expr": {"__ref": "YCRVce7q8h5_"}, "__type": "Arg"}, "22QGRBvF2AFc": {"param": {"__ref": "vdXCJxTWWPks"}, "expr": {"__ref": "0naQG3WVPP8H"}, "__type": "Arg"}, "hnc0EEvBcGDt": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "zfd4KpfWs3CO": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "gh9CUIBKHuYi"}, "__type": "ArgType"}, "msoLnKsBCycN": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "ImruPbptQkr9"}, "__type": "ArgType"}, "qYSb-YCVCqw8": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "OLKLKA7us4XK"}, "__type": "ArgType"}, "kkfIHTOuXRJ2": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "1SgRAcXMXzLw"}, "__type": "ArgType"}, "Jchfc8_oZqy6": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "RSBNLUFDCLt3"}, "__type": "ArgType"}, "Shw_1eClXa0t": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "YV7b8FF6uL_U"}, "__type": "ArgType"}, "SAt7F884ywWJ": {"param": {"__ref": "cP-XNqw5CqOP"}, "defaultContents": [{"__ref": "Vg0ybsvMtnhx"}], "uuid": "xPtNP6NjDYHZ", "parent": {"__ref": "JOKcGb4_m0OX"}, "locked": null, "vsettings": [{"__ref": "YiQvU6D8a6aM"}, {"__ref": "J6MNkM83z5cX"}, {"__ref": "oY1yI3gLlHlh"}, {"__ref": "AK1kCGwVAHV5"}, {"__ref": "d7kMyr4qJiuO"}, {"__ref": "7__AME9qPO57"}, {"__ref": "cEuhVGS7q4-V"}, {"__ref": "IsDQzUtlnQrx"}, {"__ref": "Z221_y3-a7d8"}, {"__ref": "iwb2zBu3azLO"}, {"__ref": "jjMoBFqSwRwH"}, {"__ref": "vct4IjM5aATd"}, {"__ref": "MGFC-igDyj9_"}, {"__ref": "DJLJv6dSl0Wy"}], "__type": "TplSlot"}, "Cp_cKYtPiiHf": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "vppm5lFqxU0x"}, "dataCond": {"__ref": "Bla7L1sNu7Ri"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JrDaB1UMBcIH": {"variants": [{"__ref": "Qhvd21RblTS6"}], "args": [], "attrs": {}, "rs": {"__ref": "zBsCM4YA-Y_n"}, "dataCond": {"__ref": "Nq_rnJTDwVoF"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YKWZXSHX_P-d": {"variants": [{"__ref": "Q_bQVPxXY6kG"}], "args": [], "attrs": {}, "rs": {"__ref": "ICgJ9N4b3UqY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HHfwwNPQGd0D": {"variants": [{"__ref": "iUI-B46ZLwtT"}, {"__ref": "Qhvd21RblTS6"}], "args": [], "attrs": {}, "rs": {"__ref": "ZuTBUHw_qRVi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XRp9gcLa06VR": {"param": {"__ref": "soQ0vUU8_0Gw"}, "defaultContents": [{"__ref": "W760yHkZP_2O"}], "uuid": "oUjPt2CrojoE", "parent": {"__ref": "fpxFhcCeFsZX"}, "locked": null, "vsettings": [{"__ref": "ZSAL7lGr9efc"}, {"__ref": "z4Hpw71jQmyq"}, {"__ref": "AL8dZ3KyD2dC"}, {"__ref": "Z8KKKDB18XUh"}, {"__ref": "ZRsZl7RT5SnG"}, {"__ref": "mfeZk2buVBuC"}, {"__ref": "5KpNc-MVjc1Y"}, {"__ref": "i8miaDDeGa9V"}, {"__ref": "GkW8I6ybtVdR"}, {"__ref": "chAWEm_HFmHJ"}, {"__ref": "2ieBqgXIoXZD"}, {"__ref": "KaSurp3zcB2S"}, {"__ref": "y1K8ojFhe-Re"}, {"__ref": "ufFVbC2Ve2cA"}, {"__ref": "bld8lKYGPz6E"}, {"__ref": "4e2aumwf8kCa"}, {"__ref": "W5VMURE8ZYP_"}, {"__ref": "wca_tGJrjcNd"}, {"__ref": "saowtRV5OyZ9"}, {"__ref": "fB1XCwKHROLQ"}, {"__ref": "ijaUtbCi-ILU"}, {"__ref": "h7pPrnL1q4Qq"}, {"__ref": "KK2kDO5usXbn"}, {"__ref": "jw6NBOY_aTEF"}, {"__ref": "x9ClJSxH5hCZ"}, {"__ref": "SxbFnA58amIx"}], "__type": "TplSlot"}, "GPXsxS-IgXgd": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "nzX6fo5sL5kA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "m9tlzwluWKlX": {"variants": [{"__ref": "zcO4mBn4V1wT"}], "args": [], "attrs": {}, "rs": {"__ref": "TDWEU5-Zd-NA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "g5-SeQJHx2aQ": {"variants": [{"__ref": "yq9QffPpyM2J"}], "args": [], "attrs": {}, "rs": {"__ref": "0pWh94wz8vV4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MQ1alB1-wSkD": {"variants": [{"__ref": "Lm_Y4jYRxbu0"}], "args": [], "attrs": {}, "rs": {"__ref": "K0kj74ECRGcS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uubhk0NQzLVE": {"variants": [{"__ref": "iUI-B46ZLwtT"}], "args": [], "attrs": {}, "rs": {"__ref": "Hw62U55tbQjf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "q3kejXe_YdDY": {"param": {"__ref": "vdXCJxTWWPks"}, "defaultContents": [{"__ref": "2RJn04L0Mhrs"}], "uuid": "Th08Rx78v5NQ", "parent": {"__ref": "GWzO8WeSs9Zi"}, "locked": null, "vsettings": [{"__ref": "ySzJPZm8Itx5"}, {"__ref": "5U6dHXA-5eL8"}, {"__ref": "MwvJtn9UgszC"}, {"__ref": "ARb5frtzjZmV"}, {"__ref": "waiflT0JsdjE"}, {"__ref": "5OB-ZuizWduC"}, {"__ref": "Twj5zWIFfKAb"}, {"__ref": "Q9sB9wLVXW_p"}, {"__ref": "bxpC0BG_ldpk"}, {"__ref": "hO7GK9JxBJp5"}, {"__ref": "LnqSvD8hA_7p"}, {"__ref": "nHNhEGgrC-wh"}, {"__ref": "T70HHsGqUajN"}], "__type": "TplSlot"}, "LzCpfTNu8KGk": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "wBvdNIyUyHDU"}, "dataCond": {"__ref": "UoDRhsxJ7D-s"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "f4boOsLGrVQh": {"variants": [{"__ref": "yq9QffPpyM2J"}], "args": [], "attrs": {}, "rs": {"__ref": "8PPC8DLBwray"}, "dataCond": {"__ref": "NHlD0fONhp43"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LpOhc794kTTU": {"variants": [{"__ref": "kpUxY5ieaAQf"}], "args": [], "attrs": {}, "rs": {"__ref": "bOwC_Uci9AUM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tSUo3UB2U4c5": {"variants": [{"__ref": "1hbJwXKAevLS"}], "args": [], "attrs": {}, "rs": {"__ref": "S964-vJRTDLw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "umV0DYaQtm9b": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "flex-column-gap": "8px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px"}, "mixins": [], "__type": "RuleSet"}, "BxKh20Cj4HrT": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "vNreHcx61A2E": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "vZx8LE9EtF3O": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "9rXE08axqVeC": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "dLul3ShlR8Jd": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "Sg3JNt57giZm": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "-ZGTYjMNiW3R": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "dfz1zspQ7bGY": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "eO6dwsfvV7jg": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "Npam1Va72FJC": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "xzndQK-7ZuvC": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "YYC17xHNJIMC": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "1msdKLX9oMU8": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "HEDJx4gcTbF6": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "PxZbci50L2lh": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "W5dJ3tY1hx7W": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "WqeuBtEK-6-m": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "0WOF-NdBQk3q": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "YzUSH2HQEelT": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "fZ-hkNV689w8": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "28EO4TKmU-xN": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "d3HjVvl0B_So": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "kbUJBOrW3wYs": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "JYZbAzhAgI5f": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "397Ug6TJK4X6": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "QPVpOsF5q678": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "gRuxevoEQrxj": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "nPwIOKm3sC0E": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "xpezIvmqjJOS": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "4epK5YTJuysT": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "StTIBRX6S70N": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "kP6Ko_gCNh4F": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "4db707O2etjy": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "Q14iaWIh66y7": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "xSTTl3cvNKqS": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "Zc0iuvwxUU4W": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "tBqezUaC_U_i": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "n_KfulLq1BHX": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "UMMYroodjXfz": {"values": {}, "mixins": [], "__type": "RuleSet"}, "bMWP9RHnj0-V": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZQVrR6tZM6Kg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Gl_UeXQQyKqo": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "iMvB_1CoAdKK": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "AHtvuslCJilF": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "inxSjISxJ1yQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gJkpptPNCNiL": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "FXLjr1gHs7Oo": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "2ZCtbrP5ttRv": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "-XN2UGBJMG3-": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "6Qpide-ufhgA": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "1Ck7ii7RcvKM": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "oUiV8JBb-rI-": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "4qj6MUsmSeTy": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "TbRXmrDERT_-": {"values": {}, "mixins": [], "__type": "RuleSet"}, "O6l06pAagvr3": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "nDDMR2zMqbyp": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "2DyDWsk5IUNd": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "qBl58MwXqilH": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "AZrLNj9cKKTQ": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "YHMvvp4nH9sK": {"frame": {"__ref": "R34uJ73FqFfQ"}, "cellKey": {"__ref": "klo4wGsplYfu"}, "__type": "ArenaFrameCell"}, "g_GuCsIFsevq": {"frame": {"__ref": "Qa1Mgt5uuZ1s"}, "cellKey": {"__ref": "Lm_Y4jYRxbu0"}, "__type": "ArenaFrameCell"}, "XGy9bo4BpSyy": {"frame": {"__ref": "y9OjNOrVWJL4"}, "cellKey": {"__ref": "fLNraCYnhsPw"}, "__type": "ArenaFrameCell"}, "_pnlZIl-sVPu": {"frame": {"__ref": "eqIq2pPguj7u"}, "cellKey": {"__ref": "rXS3ClnNzXCb"}, "__type": "ArenaFrameCell"}, "_yiD-ymRV2yw": {"frame": {"__ref": "yZ7HarU4ZRbU"}, "cellKey": {"__ref": "QClRYrh3WRQd"}, "__type": "ArenaFrameCell"}, "ueGRa06H1H0D": {"frame": {"__ref": "Q47NhsxQSRPo"}, "cellKey": {"__ref": "Qhvd21RblTS6"}, "__type": "ArenaFrameCell"}, "xQEDNGSciseg": {"frame": {"__ref": "VvLeLeIqvTvo"}, "cellKey": {"__ref": "yq9QffPpyM2J"}, "__type": "ArenaFrameCell"}, "guqDovBm8Yv7": {"frame": {"__ref": "PPofG1q03huG"}, "cellKey": {"__ref": "zcO4mBn4V1wT"}, "__type": "ArenaFrameCell"}, "X8jA4qT6qASw": {"frame": {"__ref": "JJuQxCpzaJXF"}, "cellKey": {"__ref": "iUI-B46ZLwtT"}, "__type": "ArenaFrameCell"}, "B9c6t1EL5FKQ": {"frame": {"__ref": "SfxxuPIkdkwD"}, "cellKey": {"__ref": "-vX-qimNWhqd"}, "__type": "ArenaFrameCell"}, "1wDDNrz_6Md-": {"frame": {"__ref": "BDafxlQ1_oSa"}, "cellKey": {"__ref": "n27l-fRMsuCS"}, "__type": "ArenaFrameCell"}, "P-FwUmxjt_Nv": {"frame": {"__ref": "AT3BAXAcMRkP"}, "cellKey": {"__ref": "RiiM8z4glg_B"}, "__type": "ArenaFrameCell"}, "aZJbsm4Kuwll": {"frame": {"__ref": "BaiV6EvGZ5lF"}, "cellKey": {"__ref": "UMpGQ1nwhNff"}, "__type": "ArenaFrameCell"}, "YT9khuxMJTom": {"frame": {"__ref": "hr9DwU4_QcZK"}, "cellKey": {"__ref": "Q_bQVPxXY6kG"}, "__type": "ArenaFrameCell"}, "eazBpgja9XWs": {"frame": {"__ref": "CKwm1z2iwu3H"}, "cellKey": {"__ref": "QNx57Blo8rTr"}, "__type": "ArenaFrameCell"}, "-kP0hUleWg4Z": {"frame": {"__ref": "teCQA_ZMpYj7"}, "cellKey": {"__ref": "kpUxY5ieaAQf"}, "__type": "ArenaFrameCell"}, "Z7TF9S0TUlhf": {"frame": {"__ref": "cL7mreqqAlgq"}, "cellKey": {"__ref": "lomZRpMcCROC"}, "__type": "ArenaFrameCell"}, "Y6LTqqHb0Xxm": {"frame": {"__ref": "0bOD8i9yNyRE"}, "cellKey": {"__ref": "YWeDO5D2a9V4"}, "__type": "ArenaFrameCell"}, "2orFMzNyXpr4": {"frame": {"__ref": "cz3VaiaceDxH"}, "cellKey": {"__ref": "1hbJwXKAevLS"}, "__type": "ArenaFrameCell"}, "CKoU7c7y7qfQ": {"frame": {"__ref": "t5qZNNt7Buxs"}, "cellKey": {"__ref": "rozacXTR5145"}, "__type": "ArenaFrameCell"}, "47eOyYJ9twkL": {"frame": {"__ref": "xMiroFzQ3as2"}, "cellKey": {"__ref": "wlgMCJ5ZrEuD"}, "__type": "ArenaFrameCell"}, "VqrHXLFAvB8O": {"frame": {"__ref": "ibrx53VmTtJg"}, "cellKey": {"__ref": "KY5faWPazsCy"}, "__type": "ArenaFrameCell"}, "0CoZL-CH5nLb": {"frame": {"__ref": "wfsD-169CRXR"}, "cellKey": {"__ref": "Ne7YQqMc4g89"}, "__type": "ArenaFrameCell"}, "Fe2Ypy409Eiy": {"frame": {"__ref": "H0bH34uyiEXF"}, "cellKey": {"__ref": "tYFUx8VxDxot"}, "__type": "ArenaFrameCell"}, "xm1UyaPbYYe2": {"frame": {"__ref": "FxdMW3rlhaSq"}, "cellKey": {"__ref": "YPpaIyQG98LD"}, "__type": "ArenaFrameCell"}, "G6aZ9IWwm8GW": {"frame": {"__ref": "TojuGP2XgaFy"}, "cellKey": {"__ref": "0FdCSZaRreQX"}, "__type": "ArenaFrameCell"}, "sVH7W8M5nFX7": {"frame": {"__ref": "wJvdDEdodCQe"}, "cellKey": [{"__ref": "rozacXTR5145"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "MeCyM6NXpuKS": {"frame": {"__ref": "8XVoTM0ZkY7w"}, "cellKey": [{"__ref": "rozacXTR5145"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "vqc-Q4fCO-xn": {"frame": {"__ref": "6jQrCrKdWqN-"}, "cellKey": [{"__ref": "wlgMCJ5ZrEuD"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "L-tuniS03rK0": {"frame": {"__ref": "WB8B4xVeiotA"}, "cellKey": [{"__ref": "wlgMCJ5ZrEuD"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "fiA7qxl-2GLK": {"frame": {"__ref": "oyA_pVts7E81"}, "cellKey": [{"__ref": "kpUxY5ieaAQf"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "ApBU400dNvUF": {"frame": {"__ref": "Na6pB5co4pVb"}, "cellKey": [{"__ref": "kpUxY5ieaAQf"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "sE5KhdydxAr1": {"frame": {"__ref": "WljrPUQpR1-b"}, "cellKey": [{"__ref": "lomZRpMcCROC"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "bwbPPbZ1tcDG": {"frame": {"__ref": "kJKBuRAWemVG"}, "cellKey": [{"__ref": "lomZRpMcCROC"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "4Fsv11R-0UKd": {"frame": {"__ref": "Ue3QJ4_I9NYK"}, "cellKey": [{"__ref": "KY5faWPazsCy"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "PhVzld63nuQg": {"frame": {"__ref": "XD6fx6EcQHaH"}, "cellKey": [{"__ref": "KY5faWPazsCy"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "SyrKzgOraGYC": {"frame": {"__ref": "eaXBav7geqiO"}, "cellKey": [{"__ref": "Ne7YQqMc4g89"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "uloIB0rWGUYM": {"frame": {"__ref": "pVBwoIYLOqzJ"}, "cellKey": [{"__ref": "Ne7YQqMc4g89"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "KQp4ruR0Wy1k": {"frame": {"__ref": "9fBEG8UF15bk"}, "cellKey": [{"__ref": "tYFUx8VxDxot"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "dMO5c5rQdBzU": {"frame": {"__ref": "iya3s9waU7Za"}, "cellKey": [{"__ref": "tYFUx8VxDxot"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "OLeuUJISteqj": {"frame": {"__ref": "BEK7LrGU1mpD"}, "cellKey": [{"__ref": "Q_bQVPxXY6kG"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "sATJD1rTe1pX": {"frame": {"__ref": "qyuK60y27VxV"}, "cellKey": [{"__ref": "Q_bQVPxXY6kG"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "bulVEVOIGP36": {"frame": {"__ref": "3T0eaVuJZFQh"}, "cellKey": [{"__ref": "QNx57Blo8rTr"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "yBJVJn69ZHdE": {"frame": {"__ref": "7ygb5ILn31OX"}, "cellKey": [{"__ref": "QNx57Blo8rTr"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "YS3C0c8R-MwG": {"frame": {"__ref": "hwxime_Ej4BU"}, "cellKey": [{"__ref": "YWeDO5D2a9V4"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "TJw2bPGrKKxW": {"frame": {"__ref": "vOXpoAKWfUyh"}, "cellKey": [{"__ref": "YWeDO5D2a9V4"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "MHpfK_zxHGpw": {"frame": {"__ref": "a91G_ytz_mea"}, "cellKey": [{"__ref": "iUI-B46ZLwtT"}, {"__ref": "Qhvd21RblTS6"}], "__type": "ArenaFrameCell"}, "lrQFCd1lScgU": {"frame": {"__ref": "MEyxUDNPs07f"}, "cellKey": [{"__ref": "yq9QffPpyM2J"}, {"__ref": "iUI-B46ZLwtT"}], "__type": "ArenaFrameCell"}, "AV1smiYk6-wL": {"frame": {"__ref": "nUmMFOJZz9JU"}, "cellKey": [{"__ref": "YPpaIyQG98LD"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "ok7H3CtwX6kl": {"frame": {"__ref": "Ui6bMRP4yCj-"}, "cellKey": [{"__ref": "YPpaIyQG98LD"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "phF7kSCgi9LC": {"frame": {"__ref": "TY-jB_9S0-xS"}, "cellKey": [{"__ref": "-vX-qimNWhqd"}, {"__ref": "RiiM8z4glg_B"}], "__type": "ArenaFrameCell"}, "09DvPD-pv6P5": {"frame": {"__ref": "ULg3wVCLCO5G"}, "cellKey": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "ubU3e5CfFFho": {"frame": {"__ref": "E84SiAEFxCdc"}, "cellKey": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "u8gzuONF8FnV": {"frame": {"__ref": "6HJVW-qtSzFv"}, "cellKey": [{"__ref": "1hbJwXKAevLS"}, {"__ref": "rXS3ClnNzXCb"}], "__type": "ArenaFrameCell"}, "GxHB29fgDTgs": {"frame": {"__ref": "aG1i8KvsjvI_"}, "cellKey": [{"__ref": "1hbJwXKAevLS"}, {"__ref": "QClRYrh3WRQd"}], "__type": "ArenaFrameCell"}, "16_muSbc3QTM": {"tpl": [{"__ref": "4_vdINEFdcdY"}], "__type": "VirtualRenderExpr"}, "0naQG3WVPP8H": {"tpl": [{"__ref": "O01nYdmLXuSO"}], "__type": "VirtualRenderExpr"}, "gh9CUIBKHuYi": {"name": "any", "__type": "AnyType"}, "ImruPbptQkr9": {"name": "any", "__type": "AnyType"}, "OLKLKA7us4XK": {"name": "any", "__type": "AnyType"}, "1SgRAcXMXzLw": {"name": "any", "__type": "AnyType"}, "RSBNLUFDCLt3": {"name": "any", "__type": "AnyType"}, "YV7b8FF6uL_U": {"name": "any", "__type": "AnyType"}, "Vg0ybsvMtnhx": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "kWwVM0P3tXDI", "parent": {"__ref": "SAt7F884ywWJ"}, "locked": null, "vsettings": [{"__ref": "gASeqPVtiCf2"}], "__type": "TplTag"}, "YiQvU6D8a6aM": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "D6C5eyxz2Cld"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "J6MNkM83z5cX": {"variants": [{"__ref": "Qhvd21RblTS6"}], "args": [], "attrs": {}, "rs": {"__ref": "APPDmeQYeCFR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oY1yI3gLlHlh": {"variants": [{"__ref": "Q_bQVPxXY6kG"}], "args": [], "attrs": {}, "rs": {"__ref": "b63X3-hkunEK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AK1kCGwVAHV5": {"variants": [{"__ref": "rozacXTR5145"}], "args": [], "attrs": {}, "rs": {"__ref": "2xOtldj1J0G0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "d7kMyr4qJiuO": {"variants": [{"__ref": "wlgMCJ5ZrEuD"}], "args": [], "attrs": {}, "rs": {"__ref": "FgR79I2qi1K3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7__AME9qPO57": {"variants": [{"__ref": "KY5faWPazsCy"}], "args": [], "attrs": {}, "rs": {"__ref": "5tZ9uXWfKSJt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cEuhVGS7q4-V": {"variants": [{"__ref": "Ne7YQqMc4g89"}], "args": [], "attrs": {}, "rs": {"__ref": "oDV5DRODXxmk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IsDQzUtlnQrx": {"variants": [{"__ref": "tYFUx8VxDxot"}], "args": [], "attrs": {}, "rs": {"__ref": "3ddLWWCtmaSO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Z221_y3-a7d8": {"variants": [{"__ref": "kpUxY5ieaAQf"}], "args": [], "attrs": {}, "rs": {"__ref": "YSjo7RPJ6A6j"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iwb2zBu3azLO": {"variants": [{"__ref": "0FdCSZaRreQX"}], "args": [], "attrs": {}, "rs": {"__ref": "pzI6dOwt3kyL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jjMoBFqSwRwH": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "oIAHe3cJmTbU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vct4IjM5aATd": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "czQzi1rAtRXk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MGFC-igDyj9_": {"variants": [{"__ref": "YPpaIyQG98LD"}], "args": [], "attrs": {}, "rs": {"__ref": "9rTUyOcYKv2J"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DJLJv6dSl0Wy": {"variants": [{"__ref": "1hbJwXKAevLS"}], "args": [], "attrs": {}, "rs": {"__ref": "lLcp2_Br3VQJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vppm5lFqxU0x": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "Bla7L1sNu7Ri": {"code": "false", "fallback": null, "__type": "CustomCode"}, "zBsCM4YA-Y_n": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "Nq_rnJTDwVoF": {"code": "true", "fallback": null, "__type": "CustomCode"}, "ICgJ9N4b3UqY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZuTBUHw_qRVi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "W760yHkZP_2O": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "3003JXzASy1W", "parent": {"__ref": "XRp9gcLa06VR"}, "locked": null, "vsettings": [{"__ref": "8Veg8ivE3WNm"}], "__type": "TplTag"}, "ZSAL7lGr9efc": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "QBHwwp42jibN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "z4Hpw71jQmyq": {"variants": [{"__ref": "fLNraCYnhsPw"}], "args": [], "attrs": {}, "rs": {"__ref": "kuYRZLrPt3Z2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AL8dZ3KyD2dC": {"variants": [{"__ref": "Lm_Y4jYRxbu0"}], "args": [], "attrs": {}, "rs": {"__ref": "_RwvcvkODArI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Z8KKKDB18XUh": {"variants": [{"__ref": "Qhvd21RblTS6"}], "args": [], "attrs": {}, "rs": {"__ref": "dfY1QDywQZJ1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZRsZl7RT5SnG": {"variants": [{"__ref": "yq9QffPpyM2J"}], "args": [], "attrs": {}, "rs": {"__ref": "gk8WtrgIc3eD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mfeZk2buVBuC": {"variants": [{"__ref": "zcO4mBn4V1wT"}], "args": [], "attrs": {}, "rs": {"__ref": "OmZazJrlJKpI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5KpNc-MVjc1Y": {"variants": [{"__ref": "rozacXTR5145"}], "args": [], "attrs": {}, "rs": {"__ref": "EbJmxOnTszIj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "i8miaDDeGa9V": {"variants": [{"__ref": "wlgMCJ5ZrEuD"}], "args": [], "attrs": {}, "rs": {"__ref": "nq1wRG9ziw96"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GkW8I6ybtVdR": {"variants": [{"__ref": "kpUxY5ieaAQf"}], "args": [], "attrs": {}, "rs": {"__ref": "Cnh594_eUbtS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "chAWEm_HFmHJ": {"variants": [{"__ref": "KY5faWPazsCy"}], "args": [], "attrs": {}, "rs": {"__ref": "iEcC8cASSzSZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2ieBqgXIoXZD": {"variants": [{"__ref": "Ne7YQqMc4g89"}], "args": [], "attrs": {}, "rs": {"__ref": "yfHXueV0061Q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KaSurp3zcB2S": {"variants": [{"__ref": "tYFUx8VxDxot"}], "args": [], "attrs": {}, "rs": {"__ref": "Bcu_7ASDf3rA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "y1K8ojFhe-Re": {"variants": [{"__ref": "Q_bQVPxXY6kG"}], "args": [], "attrs": {}, "rs": {"__ref": "WC5LF4nNogD4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ufFVbC2Ve2cA": {"variants": [{"__ref": "QNx57Blo8rTr"}], "args": [], "attrs": {}, "rs": {"__ref": "n1FclRCZGr53"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bld8lKYGPz6E": {"variants": [{"__ref": "YWeDO5D2a9V4"}], "args": [], "attrs": {}, "rs": {"__ref": "c2HpqBNZheZI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4e2aumwf8kCa": {"variants": [{"__ref": "lomZRpMcCROC"}], "args": [], "attrs": {}, "rs": {"__ref": "k_tzepIWpO06"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "W5VMURE8ZYP_": {"variants": [{"__ref": "iUI-B46ZLwtT"}], "args": [], "attrs": {}, "rs": {"__ref": "uhLcg-jIaByl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wca_tGJrjcNd": {"variants": [{"__ref": "YPpaIyQG98LD"}], "args": [], "attrs": {}, "rs": {"__ref": "kdEIOUfKDvBc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "saowtRV5OyZ9": {"variants": [{"__ref": "0FdCSZaRreQX"}], "args": [], "attrs": {}, "rs": {"__ref": "_PgOj33E80bY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fB1XCwKHROLQ": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "1gdJTGXQ8gm4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ijaUtbCi-ILU": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "eST7G1mxo7Qc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "h7pPrnL1q4Qq": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "UMpGQ1nwhNff"}], "args": [], "attrs": {}, "rs": {"__ref": "UzcUeaMzqSv4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KK2kDO5usXbn": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "UMpGQ1nwhNff"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "lGe25FMt7G7z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jw6NBOY_aTEF": {"variants": [{"__ref": "UMpGQ1nwhNff"}], "args": [], "attrs": {}, "rs": {"__ref": "0UOU0-aMm8Lh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x9ClJSxH5hCZ": {"variants": [{"__ref": "UMpGQ1nwhNff"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "pzkd3GMKzJpl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SxbFnA58amIx": {"variants": [{"__ref": "1hbJwXKAevLS"}], "args": [], "attrs": {}, "rs": {"__ref": "AiyG2XL79O1Z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nzX6fo5sL5kA": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "TDWEU5-Zd-NA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "0pWh94wz8vV4": {"values": {}, "mixins": [], "__type": "RuleSet"}, "K0kj74ECRGcS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Hw62U55tbQjf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2RJn04L0Mhrs": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Wdv9PRVH_Jpe", "parent": {"__ref": "q3kejXe_YdDY"}, "locked": null, "vsettings": [{"__ref": "EYjZ3V511jD9"}], "__type": "TplTag"}, "ySzJPZm8Itx5": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "bkkmhtOkcIP4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5U6dHXA-5eL8": {"variants": [{"__ref": "yq9QffPpyM2J"}], "args": [], "attrs": {}, "rs": {"__ref": "xnIG1AKksifV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MwvJtn9UgszC": {"variants": [{"__ref": "rozacXTR5145"}], "args": [], "attrs": {}, "rs": {"__ref": "tk3FkaJHsqyV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ARb5frtzjZmV": {"variants": [{"__ref": "wlgMCJ5ZrEuD"}], "args": [], "attrs": {}, "rs": {"__ref": "5Fg3uZpVL8g3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "waiflT0JsdjE": {"variants": [{"__ref": "KY5faWPazsCy"}], "args": [], "attrs": {}, "rs": {"__ref": "pbJZzuA0KsRv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5OB-ZuizWduC": {"variants": [{"__ref": "Ne7YQqMc4g89"}], "args": [], "attrs": {}, "rs": {"__ref": "pZ_ETmslqiYG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Twj5zWIFfKAb": {"variants": [{"__ref": "tYFUx8VxDxot"}], "args": [], "attrs": {}, "rs": {"__ref": "IAHClUNgZh9u"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Q9sB9wLVXW_p": {"variants": [{"__ref": "kpUxY5ieaAQf"}], "args": [], "attrs": {}, "rs": {"__ref": "FRq_fy7mTbev"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bxpC0BG_ldpk": {"variants": [{"__ref": "0FdCSZaRreQX"}], "args": [], "attrs": {}, "rs": {"__ref": "EglxlrLTzNPe"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hO7GK9JxBJp5": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "rXS3ClnNzXCb"}], "args": [], "attrs": {}, "rs": {"__ref": "Jm4QhuXHZOUh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LnqSvD8hA_7p": {"variants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "QClRYrh3WRQd"}], "args": [], "attrs": {}, "rs": {"__ref": "k2pZb9F2jB27"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nHNhEGgrC-wh": {"variants": [{"__ref": "YPpaIyQG98LD"}], "args": [], "attrs": {}, "rs": {"__ref": "-_pKVRjS6Xcs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "T70HHsGqUajN": {"variants": [{"__ref": "1hbJwXKAevLS"}], "args": [], "attrs": {}, "rs": {"__ref": "9V2kPMNb84yE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wBvdNIyUyHDU": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "UoDRhsxJ7D-s": {"code": "false", "fallback": null, "__type": "CustomCode"}, "8PPC8DLBwray": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "NHlD0fONhp43": {"code": "true", "fallback": null, "__type": "CustomCode"}, "bOwC_Uci9AUM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "S964-vJRTDLw": {"values": {}, "mixins": [], "__type": "RuleSet"}, "R34uJ73FqFfQ": {"uuid": "wo__S6XTY_GH", "width": 1180, "height": 540, "container": {"__ref": "pD2poQKwDiyN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "klo4wGsplYfu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Qa1Mgt5uuZ1s": {"uuid": "gQ0v9sQ3o25F", "width": 1180, "height": 540, "container": {"__ref": "yCf6c-GrASnc"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Lm_Y4jYRxbu0"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "y9OjNOrVWJL4": {"uuid": "h9sEzrezIIZY", "width": 1180, "height": 540, "container": {"__ref": "GjfkYmTGLqat"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "fLNraCYnhsPw"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eqIq2pPguj7u": {"uuid": "-k3Q3XNLDFp2", "width": 1180, "height": 540, "container": {"__ref": "3l1uyg5Xok3w"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "yZ7HarU4ZRbU": {"uuid": "fehgL1VU3xWd", "width": 1180, "height": 540, "container": {"__ref": "nMNExdS07lLe"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Q47NhsxQSRPo": {"uuid": "IMjlowAXlSVA", "width": 1180, "height": 540, "container": {"__ref": "Kml1YuxavBXB"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Qhvd21RblTS6"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "VvLeLeIqvTvo": {"uuid": "FyxHScXYdTrV", "width": 1180, "height": 540, "container": {"__ref": "n1TDWCDooRSo"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "yq9QffPpyM2J"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "PPofG1q03huG": {"uuid": "vaoTnJtG-oP-", "width": 1180, "height": 540, "container": {"__ref": "vVBxyvSty16K"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "zcO4mBn4V1wT"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "JJuQxCpzaJXF": {"uuid": "SHj_zig7rpuq", "width": 1180, "height": 540, "container": {"__ref": "RqlUlzU-dXVX"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "iUI-B46ZLwtT"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "SfxxuPIkdkwD": {"uuid": "iTh25F6JF4Oa", "width": 1180, "height": 540, "container": {"__ref": "sGO1ld4kt7O2"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "-vX-qimNWhqd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "BDafxlQ1_oSa": {"uuid": "XJm2cpgenXMJ", "width": 1180, "height": 540, "container": {"__ref": "PgRioGxpCpRC"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "n27l-fRMsuCS"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "AT3BAXAcMRkP": {"uuid": "XM74IJnZATDr", "width": 1180, "height": 540, "container": {"__ref": "9qGv4Td6Q3LG"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "RiiM8z4glg_B"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "BaiV6EvGZ5lF": {"uuid": "YpLBFupWijoC", "width": 1180, "height": 540, "container": {"__ref": "2jKegzjPcpbw"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "UMpGQ1nwhNff"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "hr9DwU4_QcZK": {"uuid": "0sZGOVqdGTTf", "width": 1180, "height": 540, "container": {"__ref": "B1PGCfR-bVo7"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Q_bQVPxXY6kG"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "CKwm1z2iwu3H": {"uuid": "1zDTcsJ7wRAt", "width": 1180, "height": 540, "container": {"__ref": "O42HGJJ5ZwY3"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QNx57Blo8rTr"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "teCQA_ZMpYj7": {"uuid": "iqwduN5tnkjx", "width": 1180, "height": 540, "container": {"__ref": "3AsmLZwOy5L0"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "kpUxY5ieaAQf"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "cL7mreqqAlgq": {"uuid": "NQdy_zjYNQc-", "width": 1180, "height": 540, "container": {"__ref": "2SG_W-Y94ZYS"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "lomZRpMcCROC"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "0bOD8i9yNyRE": {"uuid": "yl2FZhkAh4Ey", "width": 1180, "height": 540, "container": {"__ref": "DQ0AgzoLLCQB"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YWeDO5D2a9V4"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "cz3VaiaceDxH": {"uuid": "vwg8La9YHYoo", "width": 1180, "height": 540, "container": {"__ref": "Wa4j_rtO0B8v"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1hbJwXKAevLS"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "t5qZNNt7Buxs": {"uuid": "C0bBwFOD0a-Z", "width": 1180, "height": 540, "container": {"__ref": "JxNRRwxFCk82"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "rozacXTR5145"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "xMiroFzQ3as2": {"uuid": "k9w_HqXIk-ir", "width": 1180, "height": 540, "container": {"__ref": "SEWQSBAeLJUQ"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "wlgMCJ5ZrEuD"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ibrx53VmTtJg": {"uuid": "pMMljK02FwV1", "width": 1180, "height": 540, "container": {"__ref": "DFJRcTW5mJvn"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "KY5faWPazsCy"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "wfsD-169CRXR": {"uuid": "K0yM29WSeiN1", "width": 1180, "height": 540, "container": {"__ref": "LTZf2tFfahoA"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Ne7YQqMc4g89"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "H0bH34uyiEXF": {"uuid": "-I3vpZEF6gMX", "width": 1180, "height": 540, "container": {"__ref": "HhpTQF17TMUz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tYFUx8VxDxot"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "FxdMW3rlhaSq": {"uuid": "eSAoZX2a0kUM", "width": 1180, "height": 540, "container": {"__ref": "8b0VQAmXeLAR"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YPpaIyQG98LD"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "TojuGP2XgaFy": {"uuid": "hj_xxqIYn-Zb", "width": 1180, "height": 540, "container": {"__ref": "vlP7CLC-_zsN"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "0FdCSZaRreQX"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "wJvdDEdodCQe": {"uuid": "3Hyu7vLO-vZv", "width": 1180, "height": 540, "container": {"__ref": "kfX-ww4IHzhs"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "rozacXTR5145"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "8XVoTM0ZkY7w": {"uuid": "-U7MRvKi9K64", "width": 1180, "height": 540, "container": {"__ref": "CkD5NmdM3OCZ"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "rozacXTR5145"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6jQrCrKdWqN-": {"uuid": "eoyxp0OO54Bv", "width": 1180, "height": 540, "container": {"__ref": "LN_kf8lYZsNs"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "wlgMCJ5ZrEuD"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "WB8B4xVeiotA": {"uuid": "Z9sZ_sG8M8zX", "width": 1180, "height": 540, "container": {"__ref": "b_4kmgZSFDFt"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "wlgMCJ5ZrEuD"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "oyA_pVts7E81": {"uuid": "8tF_YJgR3Df3", "width": 1180, "height": 540, "container": {"__ref": "CdcNu8EKxGdT"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "kpUxY5ieaAQf"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Na6pB5co4pVb": {"uuid": "7T1NyDv4ze9n", "width": 1180, "height": 540, "container": {"__ref": "Jhi4L6YOd5zX"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "kpUxY5ieaAQf"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "WljrPUQpR1-b": {"uuid": "QflycCU2VJx8", "width": 1180, "height": 540, "container": {"__ref": "vqkkGjDLavJx"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "lomZRpMcCROC"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "kJKBuRAWemVG": {"uuid": "8YCu9soFKbJ5", "width": 1180, "height": 540, "container": {"__ref": "MQ66qh2BWgts"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "lomZRpMcCROC"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Ue3QJ4_I9NYK": {"uuid": "v1f0DOrnD3c-", "width": 1180, "height": 540, "container": {"__ref": "zoALi9Z6JKyV"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "KY5faWPazsCy"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "XD6fx6EcQHaH": {"uuid": "VfyN7HeBQy-x", "width": 1180, "height": 540, "container": {"__ref": "94ZUXjmsf-k2"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "KY5faWPazsCy"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eaXBav7geqiO": {"uuid": "Op835xtdd8GB", "width": 1180, "height": 540, "container": {"__ref": "MjsUAeLQzHM5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Ne7YQqMc4g89"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "pVBwoIYLOqzJ": {"uuid": "QithXKyu0WxW", "width": 1180, "height": 540, "container": {"__ref": "6CuKyE62f44F"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Ne7YQqMc4g89"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "9fBEG8UF15bk": {"uuid": "fk7zZ8NrZQKS", "width": 1180, "height": 540, "container": {"__ref": "qmuK6kDWgE7H"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tYFUx8VxDxot"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "iya3s9waU7Za": {"uuid": "djnu0De0g83Z", "width": 1180, "height": 540, "container": {"__ref": "HPcMXTcK1OoE"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tYFUx8VxDxot"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "BEK7LrGU1mpD": {"uuid": "CE1mGtVgIil0", "width": 1180, "height": 540, "container": {"__ref": "CJgNcbPn5y5c"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Q_bQVPxXY6kG"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "qyuK60y27VxV": {"uuid": "sDJ3nV1njofK", "width": 1180, "height": 540, "container": {"__ref": "OL_gJa72Dx9k"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Q_bQVPxXY6kG"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "3T0eaVuJZFQh": {"uuid": "QwvuyN8b2EGg", "width": 1180, "height": 540, "container": {"__ref": "MdMjsJ2S4mzz"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QNx57Blo8rTr"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "7ygb5ILn31OX": {"uuid": "9qgnS-qD6koC", "width": 1180, "height": 540, "container": {"__ref": "-yGSZMLam63k"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QNx57Blo8rTr"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "hwxime_Ej4BU": {"uuid": "KH8mwFnZ4yee", "width": 1180, "height": 540, "container": {"__ref": "kUUE6tJlhOng"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YWeDO5D2a9V4"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "vOXpoAKWfUyh": {"uuid": "NhH4mZ51LOOO", "width": 1180, "height": 540, "container": {"__ref": "_6lZMITxCP4V"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YWeDO5D2a9V4"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "a91G_ytz_mea": {"uuid": "N4eNM1MDsmVM", "width": 1180, "height": 540, "container": {"__ref": "HwjjD8NxJ0hl"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "iUI-B46ZLwtT"}, {"__ref": "Qhvd21RblTS6"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "MEyxUDNPs07f": {"uuid": "QPIPc0v3MFHJ", "width": 1180, "height": 540, "container": {"__ref": "9po8muiSBww1"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "yq9QffPpyM2J"}, {"__ref": "iUI-B46ZLwtT"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "nUmMFOJZz9JU": {"uuid": "i1ZuSod397z6", "width": 1180, "height": 540, "container": {"__ref": "BVYoTFzST1y6"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YPpaIyQG98LD"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Ui6bMRP4yCj-": {"uuid": "iCiiZFsYS1tT", "width": 1180, "height": 540, "container": {"__ref": "w9yYxu4cVYhg"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YPpaIyQG98LD"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "TY-jB_9S0-xS": {"uuid": "1eUvacJbVS9P", "width": 1180, "height": 540, "container": {"__ref": "QlWUNwDB5zIA"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "-vX-qimNWhqd"}, {"__ref": "RiiM8z4glg_B"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ULg3wVCLCO5G": {"uuid": "i9WYmz0RD48A", "width": 1180, "height": 540, "container": {"__ref": "dMqCF4SKpORR"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "E84SiAEFxCdc": {"uuid": "gflMHs5FVuZV", "width": 1180, "height": 540, "container": {"__ref": "uXfSXhoz-uTr"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "0FdCSZaRreQX"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6HJVW-qtSzFv": {"uuid": "hVbXZYJiFhxf", "width": 1180, "height": 540, "container": {"__ref": "gb-Meo9Kcr0r"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1hbJwXKAevLS"}, {"__ref": "rXS3ClnNzXCb"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "aG1i8KvsjvI_": {"uuid": "A6dQBJ9y4Rmu", "width": 1180, "height": 540, "container": {"__ref": "UQD2OlbHYLEI"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1hbJwXKAevLS"}, {"__ref": "QClRYrh3WRQd"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "4_vdINEFdcdY": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "_WeEwmwp8R8u", "parent": {"__ref": "38N9M1uylUjX"}, "locked": null, "vsettings": [{"__ref": "3PhcfQgDAWo8"}], "__type": "TplTag"}, "xrHQgmA-tzMG": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "g2NoYDUvWfSF", "parent": {"__ref": "38N9M1uylUjX"}, "locked": null, "vsettings": [{"__ref": "UmkJQHlu0Xzq"}], "__type": "TplTag"}, "O01nYdmLXuSO": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "5v01k4EHK8Nm", "parent": {"__ref": "38N9M1uylUjX"}, "locked": null, "vsettings": [{"__ref": "vFFl9zwDW2BW"}], "__type": "TplTag"}, "gASeqPVtiCf2": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {"outerHTML": {"__ref": "gC1jNr_6z5el"}}, "rs": {"__ref": "IytuzLxHlgs9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "D6C5eyxz2Cld": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "APPDmeQYeCFR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "b63X3-hkunEK": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2xOtldj1J0G0": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "FgR79I2qi1K3": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "5tZ9uXWfKSJt": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "oDV5DRODXxmk": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "3ddLWWCtmaSO": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "YSjo7RPJ6A6j": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "pzI6dOwt3kyL": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "oIAHe3cJmTbU": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "czQzi1rAtRXk": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "9rTUyOcYKv2J": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "lLcp2_Br3VQJ": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "8Veg8ivE3WNm": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {}, "rs": {"__ref": "wF2kvmVcKD4L"}, "dataCond": null, "dataRep": null, "text": {"__ref": "4yQ96WTp85wj"}, "columnsConfig": null, "__type": "VariantSetting"}, "QBHwwp42jibN": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "kuYRZLrPt3Z2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "_RwvcvkODArI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dfY1QDywQZJ1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gk8WtrgIc3eD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OmZazJrlJKpI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EbJmxOnTszIj": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "nq1wRG9ziw96": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "Cnh594_eUbtS": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "iEcC8cASSzSZ": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "yfHXueV0061Q": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "Bcu_7ASDf3rA": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "WC5LF4nNogD4": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "n1FclRCZGr53": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "c2HpqBNZheZI": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "k_tzepIWpO06": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "uhLcg-jIaByl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kdEIOUfKDvBc": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "_PgOj33E80bY": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "1gdJTGXQ8gm4": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "eST7G1mxo7Qc": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "UzcUeaMzqSv4": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lGe25FMt7G7z": {"values": {}, "mixins": [], "__type": "RuleSet"}, "0UOU0-aMm8Lh": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pzkd3GMKzJpl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "AiyG2XL79O1Z": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "EYjZ3V511jD9": {"variants": [{"__ref": "klo4wGsplYfu"}], "args": [], "attrs": {"outerHTML": {"__ref": "SdaOAYnnKkNb"}}, "rs": {"__ref": "aQuH6AmLvq6S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bkkmhtOkcIP4": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "xnIG1AKksifV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "tk3FkaJHsqyV": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "5Fg3uZpVL8g3": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "pbJZzuA0KsRv": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "pZ_ETmslqiYG": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "IAHClUNgZh9u": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "FRq_fy7mTbev": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "EglxlrLTzNPe": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "Jm4QhuXHZOUh": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "k2pZb9F2jB27": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "-_pKVRjS6Xcs": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "9V2kPMNb84yE": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "pD2poQKwDiyN": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "4yNwWY89corw", "parent": null, "locked": null, "vsettings": [{"__ref": "xoTVK2WE7BEO"}], "__type": "TplComponent"}, "yCf6c-GrASnc": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "PT8cXVk4r06p", "parent": null, "locked": null, "vsettings": [{"__ref": "4Swx55wXZJ3h"}], "__type": "TplComponent"}, "GjfkYmTGLqat": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "75xJd208W0yO", "parent": null, "locked": null, "vsettings": [{"__ref": "LlYD-nJQHXvD"}], "__type": "TplComponent"}, "3l1uyg5Xok3w": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "bf3Wt9sRP4YL", "parent": null, "locked": null, "vsettings": [{"__ref": "7L4tD1nx7hyZ"}], "__type": "TplComponent"}, "nMNExdS07lLe": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "7Xi7T_a2LZ3_", "parent": null, "locked": null, "vsettings": [{"__ref": "j5zIHrK5LB4Y"}], "__type": "TplComponent"}, "Kml1YuxavBXB": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "DSgsI9HPjj33", "parent": null, "locked": null, "vsettings": [{"__ref": "4OjAfXH33QL_"}], "__type": "TplComponent"}, "n1TDWCDooRSo": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "sT79Szagg6nN", "parent": null, "locked": null, "vsettings": [{"__ref": "svaS4Kl7nUaf"}], "__type": "TplComponent"}, "vVBxyvSty16K": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "UpXQhBefXq-z", "parent": null, "locked": null, "vsettings": [{"__ref": "5LMx1aDRviCH"}], "__type": "TplComponent"}, "RqlUlzU-dXVX": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "ezU3uadroeCx", "parent": null, "locked": null, "vsettings": [{"__ref": "JxFP9WDt6DxF"}], "__type": "TplComponent"}, "sGO1ld4kt7O2": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "_LTfPJwbTkpu", "parent": null, "locked": null, "vsettings": [{"__ref": "96B_74j8ROOO"}], "__type": "TplComponent"}, "PgRioGxpCpRC": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "MMqkSsEDB79s", "parent": null, "locked": null, "vsettings": [{"__ref": "VbgGK_juEk7E"}], "__type": "TplComponent"}, "9qGv4Td6Q3LG": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "zCDF4ZYau8GQ", "parent": null, "locked": null, "vsettings": [{"__ref": "Al2wQvkrzk0l"}], "__type": "TplComponent"}, "2jKegzjPcpbw": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "BnnhCmdapvKP", "parent": null, "locked": null, "vsettings": [{"__ref": "DbRUPxROJJC9"}], "__type": "TplComponent"}, "B1PGCfR-bVo7": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "xrz8KTUN9bhv", "parent": null, "locked": null, "vsettings": [{"__ref": "PcO4q223qCf_"}], "__type": "TplComponent"}, "O42HGJJ5ZwY3": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "VrBcDIWdZ2Mn", "parent": null, "locked": null, "vsettings": [{"__ref": "fRml2nmHyKGR"}], "__type": "TplComponent"}, "3AsmLZwOy5L0": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "KC2PzO9T4S_y", "parent": null, "locked": null, "vsettings": [{"__ref": "MdAuh4ZYNy_0"}], "__type": "TplComponent"}, "2SG_W-Y94ZYS": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "_gadDl4ZA8p1", "parent": null, "locked": null, "vsettings": [{"__ref": "OEmBFBByx54x"}], "__type": "TplComponent"}, "DQ0AgzoLLCQB": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "nJu7coszJJDO", "parent": null, "locked": null, "vsettings": [{"__ref": "PALxHZ1gcI7S"}], "__type": "TplComponent"}, "Wa4j_rtO0B8v": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "7J7UcaF__eko", "parent": null, "locked": null, "vsettings": [{"__ref": "RD7fx1LjExVh"}], "__type": "TplComponent"}, "JxNRRwxFCk82": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "gVo4TwI0Rr02", "parent": null, "locked": null, "vsettings": [{"__ref": "VCOA0nKU04lI"}], "__type": "TplComponent"}, "SEWQSBAeLJUQ": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "eZC1hvlyLzU7", "parent": null, "locked": null, "vsettings": [{"__ref": "bpPd_Rh4QRWY"}], "__type": "TplComponent"}, "DFJRcTW5mJvn": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "tlkxLXXSOZeK", "parent": null, "locked": null, "vsettings": [{"__ref": "SciUwKtc5skW"}], "__type": "TplComponent"}, "LTZf2tFfahoA": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "T1kkMpZ208x8", "parent": null, "locked": null, "vsettings": [{"__ref": "1PdhAzv2hTfQ"}], "__type": "TplComponent"}, "HhpTQF17TMUz": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "NmSQMy4vcOue", "parent": null, "locked": null, "vsettings": [{"__ref": "JEkjtymogTbd"}], "__type": "TplComponent"}, "8b0VQAmXeLAR": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "AK8N31yGKcML", "parent": null, "locked": null, "vsettings": [{"__ref": "FbKFswDHeJpy"}], "__type": "TplComponent"}, "vlP7CLC-_zsN": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "EGTD-R8QeBjJ", "parent": null, "locked": null, "vsettings": [{"__ref": "pYcbu8-nS_Je"}], "__type": "TplComponent"}, "kfX-ww4IHzhs": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "c4-wPqAZj_4l", "parent": null, "locked": null, "vsettings": [{"__ref": "p2jjZk5ZND61"}], "__type": "TplComponent"}, "CkD5NmdM3OCZ": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "DrhGyau4-PUO", "parent": null, "locked": null, "vsettings": [{"__ref": "yWaO2C9t3_jg"}], "__type": "TplComponent"}, "LN_kf8lYZsNs": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "jdf9keiNDKI3", "parent": null, "locked": null, "vsettings": [{"__ref": "HEaXROruwu_s"}], "__type": "TplComponent"}, "b_4kmgZSFDFt": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "NI-OaPDFCvxz", "parent": null, "locked": null, "vsettings": [{"__ref": "yXyHqyMbD_lO"}], "__type": "TplComponent"}, "CdcNu8EKxGdT": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "MPm7ZR7Dhxsy", "parent": null, "locked": null, "vsettings": [{"__ref": "B8Ia_gV66CQ9"}], "__type": "TplComponent"}, "Jhi4L6YOd5zX": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "0PpbSqd7NX5Y", "parent": null, "locked": null, "vsettings": [{"__ref": "2TFS3KlGdfAx"}], "__type": "TplComponent"}, "vqkkGjDLavJx": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "a3YwXqZAYXHO", "parent": null, "locked": null, "vsettings": [{"__ref": "qDESGa-raRP6"}], "__type": "TplComponent"}, "MQ66qh2BWgts": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "-KNVDuztAWoG", "parent": null, "locked": null, "vsettings": [{"__ref": "dJZHbIC3zwYI"}], "__type": "TplComponent"}, "zoALi9Z6JKyV": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "WYDJ0TJoimil", "parent": null, "locked": null, "vsettings": [{"__ref": "I1jlMt7wJJwQ"}], "__type": "TplComponent"}, "94ZUXjmsf-k2": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "VzOj7MJunWpa", "parent": null, "locked": null, "vsettings": [{"__ref": "Z7l3o20uZbU_"}], "__type": "TplComponent"}, "MjsUAeLQzHM5": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "-VKmrZQkdr1R", "parent": null, "locked": null, "vsettings": [{"__ref": "R-xZZEu4hNMe"}], "__type": "TplComponent"}, "6CuKyE62f44F": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "o_4zqbpqbu1e", "parent": null, "locked": null, "vsettings": [{"__ref": "BERZWhjHPhcg"}], "__type": "TplComponent"}, "qmuK6kDWgE7H": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "q7naTqU-pzkj", "parent": null, "locked": null, "vsettings": [{"__ref": "kEn_YnguDVv3"}], "__type": "TplComponent"}, "HPcMXTcK1OoE": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "bLFedoNm9V1D", "parent": null, "locked": null, "vsettings": [{"__ref": "5Rrt22FU74_-"}], "__type": "TplComponent"}, "CJgNcbPn5y5c": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "zMzYeSh6o5lU", "parent": null, "locked": null, "vsettings": [{"__ref": "sQFPFAquW2pg"}], "__type": "TplComponent"}, "OL_gJa72Dx9k": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "87SFzWtRK5Vt", "parent": null, "locked": null, "vsettings": [{"__ref": "PUfh50667S9u"}], "__type": "TplComponent"}, "MdMjsJ2S4mzz": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "KUQq3CH9kibs", "parent": null, "locked": null, "vsettings": [{"__ref": "ZSxsHdvVG9Qt"}], "__type": "TplComponent"}, "-yGSZMLam63k": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "UCk4fPPn4AFX", "parent": null, "locked": null, "vsettings": [{"__ref": "8efn_v6swmMj"}], "__type": "TplComponent"}, "kUUE6tJlhOng": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "3REQOZI7WGUr", "parent": null, "locked": null, "vsettings": [{"__ref": "VTIrRkgfv6ES"}], "__type": "TplComponent"}, "_6lZMITxCP4V": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "m-4N6YqUH4_Q", "parent": null, "locked": null, "vsettings": [{"__ref": "aUsD7vQmv6uA"}], "__type": "TplComponent"}, "HwjjD8NxJ0hl": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "sBc_7F5JVNne", "parent": null, "locked": null, "vsettings": [{"__ref": "tGqDRejtybH7"}], "__type": "TplComponent"}, "9po8muiSBww1": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "j7glsKbdcx4I", "parent": null, "locked": null, "vsettings": [{"__ref": "xguhwfPVUKcR"}], "__type": "TplComponent"}, "BVYoTFzST1y6": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "vXWpNx3RoYzg", "parent": null, "locked": null, "vsettings": [{"__ref": "Nz5POT-9sJWR"}], "__type": "TplComponent"}, "w9yYxu4cVYhg": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "JE-guF44f4eO", "parent": null, "locked": null, "vsettings": [{"__ref": "QakvMcMN7CFW"}], "__type": "TplComponent"}, "QlWUNwDB5zIA": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "5pAWBNNOvANn", "parent": null, "locked": null, "vsettings": [{"__ref": "2UYZbjtXxqZi"}], "__type": "TplComponent"}, "dMqCF4SKpORR": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "gBa-p4OEkrhV", "parent": null, "locked": null, "vsettings": [{"__ref": "LTLJ4GT0Ti83"}], "__type": "TplComponent"}, "uXfSXhoz-uTr": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "seLSLhYN_z6x", "parent": null, "locked": null, "vsettings": [{"__ref": "yfcm0gDjyYYw"}], "__type": "TplComponent"}, "gb-Meo9Kcr0r": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "BswTP7Y2tHGw", "parent": null, "locked": null, "vsettings": [{"__ref": "VzMfwfi5XC0B"}], "__type": "TplComponent"}, "UQD2OlbHYLEI": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "ntkF8s-JK8m-", "parent": null, "locked": null, "vsettings": [{"__ref": "wo19vhkhlCAJ"}], "__type": "TplComponent"}, "3PhcfQgDAWo8": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {"outerHTML": {"__ref": "eOQrmoOjyoAe"}}, "rs": {"__ref": "NUpwhGdEj4dY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UmkJQHlu0Xzq": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {}, "rs": {"__ref": "1AN-NuyOaLub"}, "dataCond": null, "dataRep": null, "text": {"__ref": "Ui2_nhaGK9Be"}, "columnsConfig": null, "__type": "VariantSetting"}, "vFFl9zwDW2BW": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {"outerHTML": {"__ref": "llCEadWXq_qs"}}, "rs": {"__ref": "PEo2yZwC2YMB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gC1jNr_6z5el": {"asset": {"__ref": "QRd8r00wuTBP"}, "__type": "ImageAssetRef"}, "IytuzLxHlgs9": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "wF2kvmVcKD4L": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4yQ96WTp85wj": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "SdaOAYnnKkNb": {"asset": {"__ref": "gGwu7LpANEdW"}, "__type": "ImageAssetRef"}, "aQuH6AmLvq6S": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "xoTVK2WE7BEO": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "tHnmsHowBQws"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4Swx55wXZJ3h": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "X8c5kCxFbv-P"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LlYD-nJQHXvD": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "RkhOyhgY0wJZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7L4tD1nx7hyZ": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "EVRkazzPuQ_x"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "j5zIHrK5LB4Y": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "XC5lxNukQ8_6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4OjAfXH33QL_": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "6LIN-b2_GV8F"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "svaS4Kl7nUaf": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "1DXKW5XXyE19"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5LMx1aDRviCH": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "99gyTT5Xyztu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JxFP9WDt6DxF": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "jaA4gTN29QDp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "96B_74j8ROOO": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "kd4lbWNL3wDU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VbgGK_juEk7E": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "iE_wVPPFQ5vx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Al2wQvkrzk0l": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "7UIpN8F41RKu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DbRUPxROJJC9": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "IUb0J5Fl29pY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PcO4q223qCf_": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "cTtkUfQgUt-b"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fRml2nmHyKGR": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "pa_d4q3nVvGZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MdAuh4ZYNy_0": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "M12a_-PsGvmu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OEmBFBByx54x": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "TWnrOhaQjhIM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PALxHZ1gcI7S": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "GKqxv9TbhsOr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RD7fx1LjExVh": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "_-ezZNJ3HRM3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VCOA0nKU04lI": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "3cvyPQZBv7dh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bpPd_Rh4QRWY": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "7QNDXvMwxnQo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SciUwKtc5skW": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "zELfQCZ1sC4V"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1PdhAzv2hTfQ": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "pidZezwS7Qo9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JEkjtymogTbd": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "91RNV4mDGyjf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FbKFswDHeJpy": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "hcJzT6Z7clV0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pYcbu8-nS_Je": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "aG23mWS6W_1l"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "p2jjZk5ZND61": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "MhWeNMqMLDtz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yWaO2C9t3_jg": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "4MOyC-dh7ARl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HEaXROruwu_s": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "vFOUYpz3i4rz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yXyHqyMbD_lO": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "7Mk7t826Bh0h"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B8Ia_gV66CQ9": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "vgjrP6gnh_lR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2TFS3KlGdfAx": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "dDbrhqnbK6F6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qDESGa-raRP6": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "K2W9YTxTdln2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dJZHbIC3zwYI": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "O-WHsMQZ4mZ3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I1jlMt7wJJwQ": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "ux01MgynpiKN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Z7l3o20uZbU_": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "lYfJ_MlyeTVf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "R-xZZEu4hNMe": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "JzQRnOZor1IA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BERZWhjHPhcg": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "NDH0GG9WybsT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kEn_YnguDVv3": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "is948C8VW9hl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5Rrt22FU74_-": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "CJBOt24HWqvG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sQFPFAquW2pg": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "I_VG3Q6tBhT1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PUfh50667S9u": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "dz0g36lfFkuu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZSxsHdvVG9Qt": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "s6Orm6Crs0jO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8efn_v6swmMj": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "IQy1lFwq9xph"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VTIrRkgfv6ES": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "8LdcfqOMvmeL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aUsD7vQmv6uA": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "hI9qCRtBSIzg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tGqDRejtybH7": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "hRGnX6JEZds0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xguhwfPVUKcR": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "-roHNTjCgQ7v"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Nz5POT-9sJWR": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "31rJmxdWIRQZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QakvMcMN7CFW": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "piPiEISW6X-g"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2UYZbjtXxqZi": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "tEoGqG4HIr5r"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LTLJ4GT0Ti83": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "Eaw0yxx_yLqB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yfcm0gDjyYYw": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "hB93d7TadwbI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VzMfwfi5XC0B": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "Eke01Jd2iBQd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wo19vhkhlCAJ": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "38WlcB1fpYEI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eOQrmoOjyoAe": {"asset": {"__ref": "QRd8r00wuTBP"}, "__type": "ImageAssetRef"}, "NUpwhGdEj4dY": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "1AN-NuyOaLub": {"values": {}, "mixins": [], "__type": "RuleSet"}, "llCEadWXq_qs": {"asset": {"__ref": "gGwu7LpANEdW"}, "__type": "ImageAssetRef"}, "PEo2yZwC2YMB": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "tHnmsHowBQws": {"values": {}, "mixins": [], "__type": "RuleSet"}, "X8c5kCxFbv-P": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RkhOyhgY0wJZ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EVRkazzPuQ_x": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XC5lxNukQ8_6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6LIN-b2_GV8F": {"values": {}, "mixins": [], "__type": "RuleSet"}, "1DXKW5XXyE19": {"values": {}, "mixins": [], "__type": "RuleSet"}, "99gyTT5Xyztu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "jaA4gTN29QDp": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kd4lbWNL3wDU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "iE_wVPPFQ5vx": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7UIpN8F41RKu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "IUb0J5Fl29pY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cTtkUfQgUt-b": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pa_d4q3nVvGZ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "M12a_-PsGvmu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "TWnrOhaQjhIM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "GKqxv9TbhsOr": {"values": {}, "mixins": [], "__type": "RuleSet"}, "_-ezZNJ3HRM3": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3cvyPQZBv7dh": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7QNDXvMwxnQo": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zELfQCZ1sC4V": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pidZezwS7Qo9": {"values": {}, "mixins": [], "__type": "RuleSet"}, "91RNV4mDGyjf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "hcJzT6Z7clV0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aG23mWS6W_1l": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MhWeNMqMLDtz": {"values": {}, "mixins": [], "__type": "RuleSet"}, "4MOyC-dh7ARl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vFOUYpz3i4rz": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7Mk7t826Bh0h": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vgjrP6gnh_lR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dDbrhqnbK6F6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "K2W9YTxTdln2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "O-WHsMQZ4mZ3": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ux01MgynpiKN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lYfJ_MlyeTVf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "JzQRnOZor1IA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NDH0GG9WybsT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "is948C8VW9hl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CJBOt24HWqvG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "I_VG3Q6tBhT1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dz0g36lfFkuu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "s6Orm6Crs0jO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "IQy1lFwq9xph": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8LdcfqOMvmeL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "hI9qCRtBSIzg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "hRGnX6JEZds0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "-roHNTjCgQ7v": {"values": {}, "mixins": [], "__type": "RuleSet"}, "31rJmxdWIRQZ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "piPiEISW6X-g": {"values": {}, "mixins": [], "__type": "RuleSet"}, "tEoGqG4HIr5r": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Eaw0yxx_yLqB": {"values": {}, "mixins": [], "__type": "RuleSet"}, "hB93d7TadwbI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Eke01Jd2iBQd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "38WlcB1fpYEI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2jLXJd5sNBOP": {"name": null, "component": {"__xref": {"uuid": "a3a6a4ab-6ef7-466e-8589-a26854d0253c", "iid": "SLyd6sgbmCFe"}}, "uuid": "lpY9xF4FqK9k", "parent": {"__ref": "-e-kDsxR2GzL"}, "locked": null, "vsettings": [{"__ref": "qs0SFKOU3iJ1"}], "__type": "TplComponent"}, "qs0SFKOU3iJ1": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [{"__ref": "holSXBXiXIos"}, {"__ref": "eh7B5EYZRfWV"}, {"__ref": "2H_ZkXm2T_Kp"}], "attrs": {}, "rs": {"__ref": "seQJ54Ev0pFZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "seQJ54Ev0pFZ": {"values": {"max-width": "100%", "object-fit": "cover", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "XW2TiUsYm85q": {"param": {"__ref": "aERZn6kZAKWt"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "WLoJoMRB2EpP"}, "tplNode": null, "implicitState": null, "__type": "State"}, "aERZn6kZAKWt": {"type": {"__ref": "GSEIR_hXfR_0"}, "state": {"__ref": "XW2TiUsYm85q"}, "variable": {"__ref": "AvwS6VLX0uOs"}, "uuid": "kU0FhM2aImNY", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "ac1M7fGnoNaN"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "WLoJoMRB2EpP": {"type": {"__ref": "ed3ePTSVEIM7"}, "state": {"__ref": "XW2TiUsYm85q"}, "variable": {"__ref": "hWhXsm_YsGiT"}, "uuid": "gErzXwBOjC5i", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "AvwS6VLX0uOs": {"name": "count", "uuid": "d2omZW9-qi9b", "__type": "Var"}, "ed3ePTSVEIM7": {"name": "func", "params": [{"__ref": "7q4LsdH_wx4j"}], "__type": "FunctionType"}, "hWhXsm_YsGiT": {"name": "On count change", "uuid": "l6U6naz_vEq5", "__type": "Var"}, "7q4LsdH_wx4j": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "Rqz45eWvqBjh"}, "__type": "ArgType"}, "Rqz45eWvqBjh": {"name": "text", "__type": "Text"}, "GSEIR_hXfR_0": {"name": "num", "__type": "<PERSON><PERSON>"}, "ac1M7fGnoNaN": {"code": "0", "fallback": null, "__type": "CustomCode"}, "NKuXf5GFanNB": {"param": {"__ref": "OchNuHXqMdub"}, "accessType": "private", "variableType": "boolean", "onChangeParam": {"__ref": "6OsrOJ6N6WFL"}, "tplNode": null, "implicitState": null, "__type": "State"}, "OchNuHXqMdub": {"type": {"__ref": "yNJJWaDZ6MXG"}, "state": {"__ref": "NKuXf5GFanNB"}, "variable": {"__ref": "6dozTA0hiKcw"}, "uuid": "E0XZypNryGin", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "7MsyOdzfEHRr"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "6OsrOJ6N6WFL": {"type": {"__ref": "sGz9TTJT09RG"}, "state": {"__ref": "NKuXf5GFanNB"}, "variable": {"__ref": "BdzG4IGuIdd7"}, "uuid": "3qchOOGQmopF", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "6dozTA0hiKcw": {"name": "isRunning", "uuid": "aN-8-MJIO-pO", "__type": "Var"}, "sGz9TTJT09RG": {"name": "func", "params": [{"__ref": "MHqPH7TwYj63"}], "__type": "FunctionType"}, "BdzG4IGuIdd7": {"name": "On isRunning change", "uuid": "yBVLTeJF-HF1", "__type": "Var"}, "MHqPH7TwYj63": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "NuK66YDH0K02"}, "__type": "ArgType"}, "NuK66YDH0K02": {"name": "text", "__type": "Text"}, "yNJJWaDZ6MXG": {"name": "bool", "__type": "BoolType"}, "7MsyOdzfEHRr": {"code": "false", "fallback": null, "__type": "CustomCode"}, "xrvo5giyNP-U": {"expr": {"__ref": "T8P5prZo0rJT"}, "html": false, "__type": "ExprText"}, "T8P5prZo0rJT": {"path": ["$state", "count"], "fallback": {"__ref": "VzVI3ZLuqJEI"}, "__type": "ObjectPath"}, "VzVI3ZLuqJEI": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "YCRVce7q8h5_": {"tpl": [{"__ref": "xrHQgmA-tzMG"}], "__type": "RenderExpr"}, "Ui2_nhaGK9Be": {"expr": {"__ref": "IXcBC5gls2Y4"}, "html": false, "__type": "ExprText"}, "IXcBC5gls2Y4": {"code": "(\"Start\")", "fallback": {"__ref": "BMYSnloPUwM3"}, "__type": "CustomCode"}, "BMYSnloPUwM3": {"code": "\"Button\"", "fallback": null, "__type": "CustomCode"}, "49p4EKOX7Dnq": {"interactions": [{"__ref": "q6joFSkODHUK"}], "__type": "EventHandler"}, "q6joFSkODHUK": {"interactionName": "Update isRunning", "actionName": "updateVariable", "args": [{"__ref": "QkSQYXBGNSxn"}, {"__ref": "ydSwAg4doWcK"}, {"__ref": "3_V0d8-QhLNh"}], "condExpr": null, "conditionalMode": "always", "uuid": "Rnn-2DIZLk8S", "parent": {"__ref": "49p4EKOX7Dnq"}, "__type": "Interaction"}, "VYihUN7FnqOO": {"path": ["$state", "isRunning"], "fallback": null, "__type": "ObjectPath"}, "nPp0Th4lqiJ7": {"code": "0", "fallback": null, "__type": "CustomCode"}, "QkSQYXBGNSxn": {"name": "variable", "expr": {"__ref": "VYihUN7FnqOO"}, "__type": "NameArg"}, "ydSwAg4doWcK": {"name": "operation", "expr": {"__ref": "nPp0Th4lqiJ7"}, "__type": "NameArg"}, "3_V0d8-QhLNh": {"name": "value", "expr": {"__ref": "v1-vz7KryICq"}, "__type": "NameArg"}, "v1-vz7KryICq": {"code": "(true\n)", "fallback": null, "__type": "CustomCode"}, "8DN-CfwZDomb": {"name": null, "component": {"__ref": "M3D_20Qsreym"}, "uuid": "8nEjyn5Xvw3w", "parent": {"__ref": "gd-yb2DGhEn2"}, "locked": null, "vsettings": [{"__ref": "LP4gO1GhADcl"}], "__type": "TplComponent"}, "LP4gO1GhADcl": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [{"__ref": "oioTZOPNiAQ1"}, {"__ref": "AAXJTvXWBBko"}, {"__ref": "ZcsE40xiKDGX"}], "attrs": {"onClick": {"__ref": "XjBLg3IvH8RH"}}, "rs": {"__ref": "k8dB-PkLu2Jq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oioTZOPNiAQ1": {"param": {"__ref": "cP-XNqw5CqOP"}, "expr": {"__ref": "R73vDAvBbZVD"}, "__type": "Arg"}, "AAXJTvXWBBko": {"param": {"__ref": "soQ0vUU8_0Gw"}, "expr": {"__ref": "zzHSLcf04H0k"}, "__type": "Arg"}, "ZcsE40xiKDGX": {"param": {"__ref": "vdXCJxTWWPks"}, "expr": {"__ref": "mJqidwfEWSQW"}, "__type": "Arg"}, "XjBLg3IvH8RH": {"interactions": [{"__ref": "AN1-pLQUxSAg"}], "__type": "EventHandler"}, "k8dB-PkLu2Jq": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "R73vDAvBbZVD": {"tpl": [{"__ref": "xGoFiO6nImoW"}], "__type": "VirtualRenderExpr"}, "zzHSLcf04H0k": {"tpl": [{"__ref": "UzRkttkdBF6k"}], "__type": "RenderExpr"}, "mJqidwfEWSQW": {"tpl": [{"__ref": "Up4_6cCa5Wjk"}], "__type": "VirtualRenderExpr"}, "AN1-pLQUxSAg": {"interactionName": "Update isRunning", "actionName": "updateVariable", "args": [{"__ref": "WVLa5QCQcpOy"}, {"__ref": "D6RJKNE535xh"}, {"__ref": "0TpP-onHp0LU"}], "condExpr": null, "conditionalMode": "always", "uuid": "YXsVWYRbPMND", "parent": {"__ref": "XjBLg3IvH8RH"}, "__type": "Interaction"}, "xGoFiO6nImoW": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "kLWcsgi87pd5", "parent": {"__ref": "8DN-CfwZDomb"}, "locked": null, "vsettings": [{"__ref": "g9eanqgJfCFH"}], "__type": "TplTag"}, "UzRkttkdBF6k": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "63mLpip9qtrX", "parent": {"__ref": "8DN-CfwZDomb"}, "locked": null, "vsettings": [{"__ref": "7RF76BH_ixIU"}], "__type": "TplTag"}, "Up4_6cCa5Wjk": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "V3Pk1N1HWGFL", "parent": {"__ref": "8DN-CfwZDomb"}, "locked": null, "vsettings": [{"__ref": "Hl-Ie2EoOuaQ"}], "__type": "TplTag"}, "g9eanqgJfCFH": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {"outerHTML": {"__ref": "Y0KGA_FUMGTK"}}, "rs": {"__ref": "YrOh8-Z8YyoA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7RF76BH_ixIU": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {}, "rs": {"__ref": "ZBdUKiUlFVSO"}, "dataCond": null, "dataRep": null, "text": {"__ref": "lodEmPX2mA2_"}, "columnsConfig": null, "__type": "VariantSetting"}, "Hl-Ie2EoOuaQ": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {"outerHTML": {"__ref": "aN_pAOyIasb_"}}, "rs": {"__ref": "2I6kH5Y_3AUY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vw5LqtSmxB7W": {"path": ["$state", "isRunning"], "fallback": null, "__type": "ObjectPath"}, "Y0KGA_FUMGTK": {"asset": {"__ref": "QRd8r00wuTBP"}, "__type": "ImageAssetRef"}, "YrOh8-Z8YyoA": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "ZBdUKiUlFVSO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aN_pAOyIasb_": {"asset": {"__ref": "gGwu7LpANEdW"}, "__type": "ImageAssetRef"}, "2I6kH5Y_3AUY": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "lodEmPX2mA2_": {"expr": {"__ref": "CRmPnArr04eT"}, "html": false, "__type": "ExprText"}, "CRmPnArr04eT": {"code": "(\"Stop\")", "fallback": {"__ref": "D1VvoQ9Qbk9S"}, "__type": "CustomCode"}, "D1VvoQ9Qbk9S": {"code": "\"Button\"", "fallback": null, "__type": "CustomCode"}, "fjqxls01YS9n": {"code": "0", "fallback": null, "__type": "CustomCode"}, "WVLa5QCQcpOy": {"name": "variable", "expr": {"__ref": "vw5LqtSmxB7W"}, "__type": "NameArg"}, "D6RJKNE535xh": {"name": "operation", "expr": {"__ref": "fjqxls01YS9n"}, "__type": "NameArg"}, "0TpP-onHp0LU": {"name": "value", "expr": {"__ref": "dtt7jlUs_Elb"}, "__type": "NameArg"}, "dtt7jlUs_Elb": {"code": "(false)", "fallback": null, "__type": "CustomCode"}, "holSXBXiXIos": {"param": {"__xref": {"uuid": "a3a6a4ab-6ef7-466e-8589-a26854d0253c", "iid": "_7PzVYmZcrwV"}}, "expr": {"__ref": "H7v8YO11w_kg"}, "__type": "Arg"}, "H7v8YO11w_kg": {"path": ["$state", "isRunning"], "fallback": {"__ref": "bt_aSZrnEasn"}, "__type": "ObjectPath"}, "bt_aSZrnEasn": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "NyHtd6BFEt5h": {"uuid": "eTfyGiyTCN5a", "name": "search.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iPgogIDxwYXRoIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS41IiBkPSJNMTkuMjUgMTkuMjVMMTUuNSAxNS41TTQuNzUgMTFhNi4yNSA2LjI1IDAgMTExMi41IDAgNi4yNSA2LjI1IDAgMDEtMTIuNSAweiIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": null, "__type": "ImageAsset"}, "SxIYAk_gqRFL": {"uuid": "e3mq0cfBy87E", "name": "TextInput", "params": [{"__ref": "AA2ytnpnVE0x"}, {"__ref": "6ycRxt5EwiC1"}, {"__ref": "qBkIjGM0CE-l"}, {"__ref": "_VP1Ssy0wcLN"}, {"__ref": "Vitv-fhFftBQ"}, {"__ref": "zwcdl5Q7qASk"}, {"__ref": "hMt3T7xwFuxL"}, {"__ref": "TREKFE1pKtOl"}, {"__ref": "bZVOMjId1bbz"}, {"__ref": "7AASHXUKc8oX"}, {"__ref": "sC0BfsAw9fYF"}, {"__ref": "bXbREtM5ZZfr"}, {"__ref": "82WAfWlbr6XD"}, {"__ref": "utAYx4iaWJCN"}, {"__ref": "Hvo_FSYeEhXJ"}, {"__ref": "XoAxZthQeikp"}, {"__ref": "h-1PSw2BRFHM"}, {"__ref": "Fz4cdwKJ9n48"}, {"__ref": "OgFh1-IvxeTL"}, {"__ref": "IMHnu2eKyjcf"}], "states": [{"__ref": "Y7OGG3m4QO54"}, {"__ref": "EQv8JF29U5D9"}, {"__ref": "D2Ch71lFWZ9p"}, {"__ref": "tMz12DfqkE39"}, {"__ref": "FscQ0vFa48pB"}, {"__ref": "rc_J_fVB9UHQ"}], "tplTree": {"__ref": "hHHD7YvD6p0X"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "AeF-ZcfsmqiI"}, {"__ref": "nsqywtL8e_i2"}, {"__ref": "mZ61Fzq6V8wx"}, {"__ref": "vE-s9RcGBOxA"}, {"__ref": "QjPLchsmNmoh"}, {"__ref": "B0y4N0dBd-DZ"}], "variantGroups": [{"__ref": "WQoLuHdsA5JA"}, {"__ref": "klbQglf23kJq"}, {"__ref": "hADfV0e-xpY0"}, {"__ref": "gHGFxwuPOAht"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "3IM6X54iyAz3"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true, "__type": "Component", "updatedAt": null}, "i2_aBIwJZ9Cw": {"component": {"__ref": "SxIYAk_gqRFL"}, "matrix": {"__ref": "rgMX8o7udK3u"}, "customMatrix": {"__ref": "IdYZfmo1w0Wl"}, "__type": "ComponentArena"}, "cjMIn7vVuo6Z": {"name": "TextInput", "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "KXtyale3jGzv", "parent": {"__ref": "-e-kDsxR2GzL"}, "locked": null, "vsettings": [{"__ref": "L3FN-cGqecJh"}], "__type": "TplComponent"}, "pu9Wxw_mOInY": {"param": {"__ref": "5GGxOT7fD_P3"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "vlk7b-ofyDUi"}, "tplNode": {"__ref": "cjMIn7vVuo6Z"}, "implicitState": {"__ref": "FscQ0vFa48pB"}, "__type": "State"}, "5GGxOT7fD_P3": {"type": {"__ref": "9PWdGdzq-hk_"}, "state": {"__ref": "pu9Wxw_mOInY"}, "variable": {"__ref": "Aokzbpt6AX40"}, "uuid": "lzH7m2-E8d0V", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "vlk7b-ofyDUi": {"type": {"__ref": "hmAmO9UIviyE"}, "state": {"__ref": "pu9Wxw_mOInY"}, "variable": {"__ref": "z4wrOerFVJco"}, "uuid": "t8Vso4ICwKEL", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "AA2ytnpnVE0x": {"type": {"__ref": "1n_PfCtWSpEN"}, "variable": {"__ref": "YvkWXVOaoRIB"}, "uuid": "oCaqM4053XU4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "FeEk1PLfldO-"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "6ycRxt5EwiC1": {"type": {"__ref": "1WnCdi80HFEr"}, "tplSlot": {"__ref": "H-7QgYI-VTx-"}, "variable": {"__ref": "zHhnyaK0-51-"}, "uuid": "rrcLjdnNjtUf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "qBkIjGM0CE-l": {"type": {"__ref": "VERpwvKqDlfv"}, "tplSlot": {"__ref": "AEVlDo3heR1k"}, "variable": {"__ref": "1VljIh3nslKH"}, "uuid": "NMZeJ-NLTxSO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "_VP1Ssy0wcLN": {"type": {"__ref": "lT0rHeCNXDVd"}, "state": {"__ref": "Y7OGG3m4QO54"}, "variable": {"__ref": "SGV0TZs5Y191"}, "uuid": "S99LJxlzgzO9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "Vitv-fhFftBQ": {"type": {"__ref": "Bkw2RXrkwdWa"}, "state": {"__ref": "EQv8JF29U5D9"}, "variable": {"__ref": "5R4_aOVeKUMT"}, "uuid": "cpkZKPVn5ihv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "zwcdl5Q7qASk": {"type": {"__ref": "otmWd0dDCXcu"}, "state": {"__ref": "D2Ch71lFWZ9p"}, "variable": {"__ref": "bLC1b02iEMKe"}, "uuid": "o1xIxGhpO0iu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "hMt3T7xwFuxL": {"type": {"__ref": "Hl5iJkYidRSv"}, "state": {"__ref": "tMz12DfqkE39"}, "variable": {"__ref": "Zknp7V4BqRht"}, "uuid": "UzG99GiYY5DU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "TREKFE1pKtOl": {"type": {"__ref": "5BMaNFu2LKMb"}, "state": {"__ref": "FscQ0vFa48pB"}, "variable": {"__ref": "UVSU1SZW_i3y"}, "uuid": "AAHABgZ7HR43", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "UwbqkE82b_0N"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "bZVOMjId1bbz": {"type": {"__ref": "21WgVskk5UjF"}, "variable": {"__ref": "rfQNZPTKOWMe"}, "uuid": "wvv6vFECikN-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "7AASHXUKc8oX": {"type": {"__ref": "kewJwmVRLYEU"}, "variable": {"__ref": "fbijpZLA2YTg"}, "uuid": "8_63w328mb4V", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "sC0BfsAw9fYF": {"type": {"__ref": "LSxkm3NQjlcu"}, "variable": {"__ref": "3zirtup1EdB5"}, "uuid": "83_Q-xkw_Foa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "bXbREtM5ZZfr": {"type": {"__ref": "PjojBWfPXeIe"}, "variable": {"__ref": "06s5JjBvJY6n"}, "uuid": "qS0bi2Ims0OR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "82WAfWlbr6XD": {"type": {"__ref": "dymL16OJpzXO"}, "variable": {"__ref": "r3PWewa-Ffy1"}, "uuid": "Rlw_v-cFyrEx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "utAYx4iaWJCN": {"type": {"__ref": "YfSE-a_WkoBC"}, "variable": {"__ref": "ZTNRLI-9IJVs"}, "uuid": "83Ysr8tvE2Cf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam"}, "Hvo_FSYeEhXJ": {"type": {"__ref": "itH92-_M6-KR"}, "state": {"__ref": "Y7OGG3m4QO54"}, "variable": {"__ref": "gtr_h1n9AoRf"}, "uuid": "n5Xv7ck2c6XV", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "XoAxZthQeikp": {"type": {"__ref": "OyFcy-5wSkeh"}, "state": {"__ref": "EQv8JF29U5D9"}, "variable": {"__ref": "00baJi8mm0GB"}, "uuid": "mMt5q9p_f3mv", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "h-1PSw2BRFHM": {"type": {"__ref": "qjpvkiHawn1z"}, "state": {"__ref": "D2Ch71lFWZ9p"}, "variable": {"__ref": "G4fwTNdG5udo"}, "uuid": "RuyL2iBxD-mL", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "Fz4cdwKJ9n48": {"type": {"__ref": "hUgGo_SxFm5O"}, "state": {"__ref": "tMz12DfqkE39"}, "variable": {"__ref": "-pNKY-bPcpDS"}, "uuid": "UdY9shrno78Y", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "OgFh1-IvxeTL": {"type": {"__ref": "NyoJrnarGZLD"}, "state": {"__ref": "rc_J_fVB9UHQ"}, "variable": {"__ref": "lwokl7otGZ4L"}, "uuid": "tdXG499khm-o", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "IMHnu2eKyjcf": {"type": {"__ref": "MN6NkToZlNp4"}, "state": {"__ref": "rc_J_fVB9UHQ"}, "variable": {"__ref": "Bmmv-eX-20CS"}, "uuid": "5fiNGk4vQCuX", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "Y7OGG3m4QO54": {"variantGroup": {"__ref": "WQoLuHdsA5JA"}, "param": {"__ref": "_VP1Ssy0wcLN"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "Hvo_FSYeEhXJ"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "EQv8JF29U5D9": {"variantGroup": {"__ref": "klbQglf23kJq"}, "param": {"__ref": "Vitv-fhFftBQ"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "XoAxZthQeikp"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "D2Ch71lFWZ9p": {"variantGroup": {"__ref": "hADfV0e-xpY0"}, "param": {"__ref": "zwcdl5Q7qASk"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "h-1PSw2BRFHM"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "tMz12DfqkE39": {"variantGroup": {"__ref": "gHGFxwuPOAht"}, "param": {"__ref": "hMt3T7xwFuxL"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "Fz4cdwKJ9n48"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "FscQ0vFa48pB": {"variableType": "text", "name": "value", "param": {"__ref": "TREKFE1pKtOl"}, "accessType": "writable", "onChangeParam": {"__ref": "82WAfWlbr6XD"}, "tplNode": null, "implicitState": null, "__type": "NamedState"}, "rc_J_fVB9UHQ": {"variableType": "text", "name": "Value", "param": {"__ref": "OgFh1-IvxeTL"}, "accessType": "private", "onChangeParam": {"__ref": "IMHnu2eKyjcf"}, "tplNode": {"__ref": "YYWrVnw8Ms6k"}, "implicitState": null, "__type": "NamedState"}, "hHHD7YvD6p0X": {"tag": "div", "name": null, "children": [{"__ref": "gzU8G1M0dBHp"}, {"__ref": "YYWrVnw8Ms6k"}, {"__ref": "wzq3k0v_LE3_"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BwDXsLCPunVV", "parent": null, "locked": null, "vsettings": [{"__ref": "TRvjpbC7biYr"}, {"__ref": "uQCZ_SAOhQS-"}, {"__ref": "-AAc2-Qx891R"}, {"__ref": "9spSfNnyE68d"}, {"__ref": "8rSvKc_7F045"}, {"__ref": "OoQrEAMB1VLA"}, {"__ref": "T9MdMAckiJCB"}], "__type": "TplTag"}, "AeF-ZcfsmqiI": {"uuid": "9oiMS4AAUNKo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "nsqywtL8e_i2": {"uuid": "q1ue7rWcWKTd", "name": "", "selectors": [":focus"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "YYWrVnw8Ms6k"}, "__type": "<PERSON><PERSON><PERSON>"}, "mZ61Fzq6V8wx": {"uuid": "WaD5GmZ_c_dC", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "vE-s9RcGBOxA": {"uuid": "6-QDt-6pTxcN", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "QjPLchsmNmoh": {"uuid": "p81OYgmXYh-5", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "B0y4N0dBd-DZ": {"uuid": "Q6Rw8iIqk9-5", "name": "", "selectors": ["::placeholder"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "YYWrVnw8Ms6k"}, "__type": "<PERSON><PERSON><PERSON>"}, "WQoLuHdsA5JA": {"type": "component", "param": {"__ref": "_VP1Ssy0wcLN"}, "linkedState": {"__ref": "Y7OGG3m4QO54"}, "uuid": "UtxDkRi_i4yV", "variants": [{"__ref": "SwGoFElUiDfB"}], "multi": false, "__type": "ComponentVariantGroup"}, "klbQglf23kJq": {"type": "component", "param": {"__ref": "Vitv-fhFftBQ"}, "linkedState": {"__ref": "EQv8JF29U5D9"}, "uuid": "U2EV3vfnAj2k", "variants": [{"__ref": "9pZrwTMscDML"}], "multi": false, "__type": "ComponentVariantGroup"}, "hADfV0e-xpY0": {"type": "component", "param": {"__ref": "zwcdl5Q7qASk"}, "linkedState": {"__ref": "D2Ch71lFWZ9p"}, "uuid": "QCZedXKTjPm-", "variants": [{"__ref": "4ocP4fyAdtx5"}], "multi": false, "__type": "ComponentVariantGroup"}, "gHGFxwuPOAht": {"type": "component", "param": {"__ref": "hMt3T7xwFuxL"}, "linkedState": {"__ref": "tMz12DfqkE39"}, "uuid": "gAz35hFNnfTf", "variants": [{"__ref": "Hom0u9iJgkB-"}], "multi": false, "__type": "ComponentVariantGroup"}, "3IM6X54iyAz3": {"type": "text-input", "__type": "PlumeInfo"}, "rgMX8o7udK3u": {"rows": [{"__ref": "BexU7_5zEOGS"}, {"__ref": "spU0kKaYJz3i"}, {"__ref": "XBMkD68D-zEK"}, {"__ref": "x50dCLiZXGv2"}, {"__ref": "QPoyurd_WBOJ"}], "__type": "ArenaFrameGrid"}, "IdYZfmo1w0Wl": {"rows": [{"__ref": "8B83e-K4LWC-"}], "__type": "ArenaFrameGrid"}, "L3FN-cGqecJh": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [{"__ref": "GBkwlJZd1csA"}, {"__ref": "FqMun6ZYHVqX"}, {"__ref": "cfkVonQRMpD6"}, {"__ref": "fFReiHjoO6_V"}, {"__ref": "7h1S-pOZDB0b"}], "attrs": {}, "rs": {"__ref": "pMyywXaa5Ajk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9PWdGdzq-hk_": {"name": "text", "__type": "Text"}, "Aokzbpt6AX40": {"name": "TextInput value", "uuid": "CAp5sZ2VgN-e", "__type": "Var"}, "hmAmO9UIviyE": {"name": "func", "params": [{"__ref": "_Ol1jYZqxIjq"}], "__type": "FunctionType"}, "z4wrOerFVJco": {"name": "On TextInput value change", "uuid": "lnxbvNbOv9-F", "__type": "Var"}, "1n_PfCtWSpEN": {"name": "text", "__type": "Text"}, "YvkWXVOaoRIB": {"name": "placeholder", "uuid": "d3g2PuVmP_Wt", "__type": "Var"}, "FeEk1PLfldO-": {"code": "\"Enter something…\"", "fallback": null, "__type": "CustomCode"}, "1WnCdi80HFEr": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "zHhnyaK0-51-": {"name": "end icon", "uuid": "HFXkQSNuHb4r", "__type": "Var"}, "VERpwvKqDlfv": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "1VljIh3nslKH": {"name": "start icon", "uuid": "FyH29QiQWA4S", "__type": "Var"}, "lT0rHeCNXDVd": {"name": "any", "__type": "AnyType"}, "SGV0TZs5Y191": {"name": "Show Start Icon", "uuid": "uQ74V4Ma506S", "__type": "Var"}, "Bkw2RXrkwdWa": {"name": "any", "__type": "AnyType"}, "5R4_aOVeKUMT": {"name": "Show End Icon", "uuid": "UbCkSOQqhDVs", "__type": "Var"}, "otmWd0dDCXcu": {"name": "any", "__type": "AnyType"}, "bLC1b02iEMKe": {"name": "Is Disabled", "uuid": "qBAvvlDTO_2d", "__type": "Var"}, "Hl5iJkYidRSv": {"name": "any", "__type": "AnyType"}, "Zknp7V4BqRht": {"name": "Color", "uuid": "dWt59rNW3NHm", "__type": "Var"}, "5BMaNFu2LKMb": {"name": "text", "__type": "Text"}, "UVSU1SZW_i3y": {"name": "value", "uuid": "coKlcMBdj1ha", "__type": "Var"}, "UwbqkE82b_0N": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "21WgVskk5UjF": {"name": "text", "__type": "Text"}, "rfQNZPTKOWMe": {"name": "name", "uuid": "Fa7wDtfwAsqT", "__type": "Var"}, "kewJwmVRLYEU": {"name": "bool", "__type": "BoolType"}, "fbijpZLA2YTg": {"name": "required", "uuid": "BQ0MMwnPhcUT", "__type": "Var"}, "LSxkm3NQjlcu": {"name": "text", "__type": "Text"}, "3zirtup1EdB5": {"name": "aria-label", "uuid": "S-y46eOEbjA9", "__type": "Var"}, "PjojBWfPXeIe": {"name": "text", "__type": "Text"}, "06s5JjBvJY6n": {"name": "aria-<PERSON>by", "uuid": "mebXk4ybWaRM", "__type": "Var"}, "dymL16OJpzXO": {"name": "func", "params": [{"__ref": "a_j8pAzAygyk"}], "__type": "FunctionType"}, "r3PWewa-Ffy1": {"name": "onChange", "uuid": "Y7vO2K93B4km", "__type": "Var"}, "YfSE-a_WkoBC": {"name": "choice", "options": ["text", "password", "hidden", "number", "date", "datetime-local", "time", "email", "tel"], "__type": "Choice"}, "ZTNRLI-9IJVs": {"name": "type", "uuid": "bqwgWa4LFRRz", "__type": "Var"}, "itH92-_M6-KR": {"name": "func", "params": [{"__ref": "tnJk0TRKFplH"}], "__type": "FunctionType"}, "gtr_h1n9AoRf": {"name": "On Show Start Icon change", "uuid": "BkCVUEzQT5CN", "__type": "Var"}, "OyFcy-5wSkeh": {"name": "func", "params": [{"__ref": "0XnlqWfYZLcA"}], "__type": "FunctionType"}, "00baJi8mm0GB": {"name": "On Show End Icon change", "uuid": "w26B-NkNikX2", "__type": "Var"}, "qjpvkiHawn1z": {"name": "func", "params": [{"__ref": "HUFFIR_qWshg"}], "__type": "FunctionType"}, "G4fwTNdG5udo": {"name": "On Is Disabled change", "uuid": "z6HT6QSPfZx2", "__type": "Var"}, "hUgGo_SxFm5O": {"name": "func", "params": [{"__ref": "V8OVbShNSg_2"}], "__type": "FunctionType"}, "-pNKY-bPcpDS": {"name": "On Color change", "uuid": "8kadmNKQth_W", "__type": "Var"}, "NyoJrnarGZLD": {"name": "text", "__type": "Text"}, "lwokl7otGZ4L": {"name": "input Value", "uuid": "deye2jSUSg_M", "__type": "Var"}, "MN6NkToZlNp4": {"name": "func", "params": [{"__ref": "7cOZMWYK3KSo"}], "__type": "FunctionType"}, "Bmmv-eX-20CS": {"name": "On input Value change", "uuid": "mEYeQw0Xe62m", "__type": "Var"}, "gzU8G1M0dBHp": {"tag": "div", "name": "start icon container", "children": [{"__ref": "AEVlDo3heR1k"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "LBl6ZhgP_Y4y", "parent": {"__ref": "hHHD7YvD6p0X"}, "locked": null, "vsettings": [{"__ref": "PhLj5NRMH8Ob"}, {"__ref": "VqLPpsdQMiaG"}, {"__ref": "60RUCKf8rRyA"}, {"__ref": "2dW9OrAT3SIN"}, {"__ref": "eEHTGHaticyp"}, {"__ref": "1SvBN8m28ft_"}], "__type": "TplTag"}, "YYWrVnw8Ms6k": {"tag": "input", "name": "input", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "iVHYvkjsq_jm", "parent": {"__ref": "hHHD7YvD6p0X"}, "locked": null, "vsettings": [{"__ref": "x4XrGf852v_g"}, {"__ref": "Y6zMucZltyOL"}, {"__ref": "qKOIGBbKdjFS"}, {"__ref": "qoSTK-bjGlUD"}, {"__ref": "Si8nSQkmeTte"}, {"__ref": "89_ffI4pruNl"}, {"__ref": "8hAfiRX7-8WR"}, {"__ref": "cRP2VKrt8bnH"}, {"__ref": "hiCVFxCLoOO4"}, {"__ref": "5kZ6CMnnPKf3"}, {"__ref": "wHt2Kz8d4gsh"}, {"__ref": "jmS4Oh3J-eI7"}], "__type": "TplTag"}, "wzq3k0v_LE3_": {"tag": "div", "name": "end icon container", "children": [{"__ref": "H-7QgYI-VTx-"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KBBPdGgFMqMz", "parent": {"__ref": "hHHD7YvD6p0X"}, "locked": null, "vsettings": [{"__ref": "r2c-rGIEB3Td"}, {"__ref": "4PTXLRefgxSx"}, {"__ref": "IhNsd296kcOB"}], "__type": "TplTag"}, "TRvjpbC7biYr": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {}, "rs": {"__ref": "TlAuSkvgfur9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uQCZ_SAOhQS-": {"variants": [{"__ref": "mZ61Fzq6V8wx"}], "args": [], "attrs": {}, "rs": {"__ref": "Sr9mzaHK1M8J"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-AAc2-Qx891R": {"variants": [{"__ref": "vE-s9RcGBOxA"}], "args": [], "attrs": {}, "rs": {"__ref": "WbMySUxlCkg-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9spSfNnyE68d": {"variants": [{"__ref": "QjPLchsmNmoh"}], "args": [], "attrs": {}, "rs": {"__ref": "mkNrIlDDiqnF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8rSvKc_7F045": {"variants": [{"__ref": "4ocP4fyAdtx5"}], "args": [], "attrs": {}, "rs": {"__ref": "M4Scf0UWLnid"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OoQrEAMB1VLA": {"variants": [{"__ref": "SwGoFElUiDfB"}], "args": [], "attrs": {}, "rs": {"__ref": "QnhDV7FVPChV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "T9MdMAckiJCB": {"variants": [{"__ref": "Hom0u9iJgkB-"}], "args": [], "attrs": {}, "rs": {"__ref": "Pbz4O5D71SxM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SwGoFElUiDfB": {"uuid": "_CxwFtzd2htW", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "WQoLuHdsA5JA"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "9pZrwTMscDML": {"uuid": "NyfU77zdzGeh", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "klbQglf23kJq"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4ocP4fyAdtx5": {"uuid": "3Kn7QBZoD-nM", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "hADfV0e-xpY0"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Hom0u9iJgkB-": {"uuid": "LF27WELVWLgU", "name": "Dark", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "gHGFxwuPOAht"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "BexU7_5zEOGS": {"cols": [{"__ref": "IfYOPUP0COEB"}, {"__ref": "pL7I5HbTL7Er"}, {"__ref": "xSZ8dyL6HwY4"}, {"__ref": "emZwwI7adsl6"}], "rowKey": null, "__type": "ArenaFrameRow"}, "spU0kKaYJz3i": {"cols": [{"__ref": "Jj7NjwcQKnHz"}], "rowKey": {"__ref": "WQoLuHdsA5JA"}, "__type": "ArenaFrameRow"}, "XBMkD68D-zEK": {"cols": [{"__ref": "qckFzbIbLhbp"}], "rowKey": {"__ref": "klbQglf23kJq"}, "__type": "ArenaFrameRow"}, "x50dCLiZXGv2": {"cols": [{"__ref": "anQq_MHjYR0b"}], "rowKey": {"__ref": "hADfV0e-xpY0"}, "__type": "ArenaFrameRow"}, "QPoyurd_WBOJ": {"cols": [{"__ref": "Pt1T4byBGTFO"}], "rowKey": {"__ref": "gHGFxwuPOAht"}, "__type": "ArenaFrameRow"}, "8B83e-K4LWC-": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "GBkwlJZd1csA": {"param": {"__ref": "qBkIjGM0CE-l"}, "expr": {"__ref": "93E9sJtt_MuO"}, "__type": "Arg"}, "FqMun6ZYHVqX": {"param": {"__ref": "6ycRxt5EwiC1"}, "expr": {"__ref": "2_Ej9uUONVra"}, "__type": "Arg"}, "pMyywXaa5Ajk": {"values": {"max-width": "100%", "position": "relative", "width": "200px"}, "mixins": [], "__type": "RuleSet"}, "_Ol1jYZqxIjq": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "Bwtc26yBcqRY"}, "__type": "ArgType"}, "a_j8pAzAygyk": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "b6pmQCUs0ZOG"}, "__type": "ArgType"}, "tnJk0TRKFplH": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "3hxQdlDm8TN9"}, "__type": "ArgType"}, "0XnlqWfYZLcA": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "4nyY_v8rg6md"}, "__type": "ArgType"}, "HUFFIR_qWshg": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "jiek_Vaa7KiW"}, "__type": "ArgType"}, "V8OVbShNSg_2": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "1_6qGZ-iVnJj"}, "__type": "ArgType"}, "7cOZMWYK3KSo": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "o46cg5PN_7NM"}, "__type": "ArgType"}, "AEVlDo3heR1k": {"param": {"__ref": "qBkIjGM0CE-l"}, "defaultContents": [{"__ref": "WLEnMbvUh_tW"}], "uuid": "F_pf4A3ZU7k-", "parent": {"__ref": "gzU8G1M0dBHp"}, "locked": null, "vsettings": [{"__ref": "sFnQloGxYVKd"}, {"__ref": "tmJdD-miYfaR"}, {"__ref": "2YfaHtliSos_"}], "__type": "TplSlot"}, "PhLj5NRMH8Ob": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {}, "rs": {"__ref": "7enbYMBt5BQc"}, "dataCond": {"__ref": "_DHT6bAyzUSn"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VqLPpsdQMiaG": {"variants": [{"__ref": "QjPLchsmNmoh"}], "args": [], "attrs": {}, "rs": {"__ref": "KnQU81tFc4qT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "60RUCKf8rRyA": {"variants": [{"__ref": "SwGoFElUiDfB"}], "args": [], "attrs": {}, "rs": {"__ref": "UExwvFwj8Huv"}, "dataCond": {"__ref": "tP5FhNkS8U9v"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2dW9OrAT3SIN": {"variants": [{"__ref": "mZ61Fzq6V8wx"}], "args": [], "attrs": {}, "rs": {"__ref": "W_HHTfYteWoK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eEHTGHaticyp": {"variants": [{"__ref": "4ocP4fyAdtx5"}], "args": [], "attrs": {}, "rs": {"__ref": "RhEpTwNIm0kp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1SvBN8m28ft_": {"variants": [{"__ref": "Hom0u9iJgkB-"}], "args": [], "attrs": {}, "rs": {"__ref": "60et-wWp3Oc1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x4XrGf852v_g": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {"placeholder": {"__ref": "j8EKFEMXGfCs"}, "value": {"__ref": "ib8vI4uE5Xcc"}, "name": {"__ref": "DCPZqsMOCpvc"}, "aria-label": {"__ref": "QtklMaXfk5ei"}, "aria-labelledby": {"__ref": "feeq-QQ4gsRO"}, "required": {"__ref": "dPbLBT6FcTxG"}, "type": {"__ref": "sDqQmyXVIogI"}}, "rs": {"__ref": "RkQkw9BIzUq1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Y6zMucZltyOL": {"variants": [{"__ref": "nsqywtL8e_i2"}], "args": [], "attrs": {}, "rs": {"__ref": "bKIXx2XThbLG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qKOIGBbKdjFS": {"variants": [{"__ref": "4ocP4fyAdtx5"}], "args": [], "attrs": {"disabled": {"__ref": "ahE2caCx3m4G"}}, "rs": {"__ref": "EWLy1mYXQHsg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qoSTK-bjGlUD": {"variants": [{"__ref": "B0y4N0dBd-DZ"}], "args": [], "attrs": {}, "rs": {"__ref": "EXHcKyXcuC7p"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Si8nSQkmeTte": {"variants": [{"__ref": "SwGoFElUiDfB"}], "args": [], "attrs": {}, "rs": {"__ref": "1Cg5ykT3HMm9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "89_ffI4pruNl": {"variants": [{"__ref": "mZ61Fzq6V8wx"}], "args": [], "attrs": {}, "rs": {"__ref": "LHwp5-4CQKo7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8hAfiRX7-8WR": {"variants": [{"__ref": "QjPLchsmNmoh"}], "args": [], "attrs": {}, "rs": {"__ref": "xx-2ctnddsZM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cRP2VKrt8bnH": {"variants": [{"__ref": "vE-s9RcGBOxA"}], "args": [], "attrs": {}, "rs": {"__ref": "9M353BxnAx8c"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hiCVFxCLoOO4": {"variants": [{"__ref": "vE-s9RcGBOxA"}, {"__ref": "nsqywtL8e_i2"}], "args": [], "attrs": {}, "rs": {"__ref": "MUNEqc5rQJz0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5kZ6CMnnPKf3": {"variants": [{"__ref": "vE-s9RcGBOxA"}, {"__ref": "B0y4N0dBd-DZ"}], "args": [], "attrs": {}, "rs": {"__ref": "n00W6ONDquBI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wHt2Kz8d4gsh": {"variants": [{"__ref": "Hom0u9iJgkB-"}], "args": [], "attrs": {}, "rs": {"__ref": "JnJUJ06aouQU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jmS4Oh3J-eI7": {"variants": [{"__ref": "Hom0u9iJgkB-"}, {"__ref": "B0y4N0dBd-DZ"}], "args": [], "attrs": {}, "rs": {"__ref": "FjeozSoKCMoY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "H-7QgYI-VTx-": {"param": {"__ref": "6ycRxt5EwiC1"}, "defaultContents": [{"__ref": "wiLJDtjqgdfn"}], "uuid": "BU0-3D56WDb-", "parent": {"__ref": "wzq3k0v_LE3_"}, "locked": null, "vsettings": [{"__ref": "2N3rybWyu-AU"}, {"__ref": "bieLrKNma4Rp"}, {"__ref": "griHAwYHKd6l"}], "__type": "TplSlot"}, "r2c-rGIEB3Td": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {}, "rs": {"__ref": "D2soKsPMmLlW"}, "dataCond": {"__ref": "F48-T85s88-B"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4PTXLRefgxSx": {"variants": [{"__ref": "9pZrwTMscDML"}], "args": [], "attrs": {}, "rs": {"__ref": "xpuTDnDO4B4C"}, "dataCond": {"__ref": "-VnVsMZd2DX2"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IhNsd296kcOB": {"variants": [{"__ref": "Hom0u9iJgkB-"}], "args": [], "attrs": {}, "rs": {"__ref": "rgS1fwXSXI_S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TlAuSkvgfur9": {"values": {"display": "flex", "flex-direction": "row", "width": "stretch", "height": "wrap", "align-items": "center", "justify-content": "flex-start", "border-top-color": "#DBDBD7", "border-right-color": "#DBDBD7", "border-bottom-color": "#DBDBD7", "border-left-color": "#DBDBD7", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "background": "linear-gradient(#FFFFFF, #FFFFFF)", "position": "sticky", "padding-top": "7px", "padding-right": "11px", "padding-bottom": "7px", "padding-left": "11px"}, "mixins": [], "__type": "RuleSet"}, "Sr9mzaHK1M8J": {"values": {"border-top-color": "#C8C7C1", "border-right-color": "#C8C7C1", "border-bottom-color": "#C8C7C1", "border-left-color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "WbMySUxlCkg-": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "mkNrIlDDiqnF": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "M4Scf0UWLnid": {"values": {}, "mixins": [], "__type": "RuleSet"}, "QnhDV7FVPChV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Pbz4O5D71SxM": {"values": {"background": "linear-gradient(#232320, #232320)", "border-top-color": "#717069", "border-right-color": "#717069", "border-bottom-color": "#717069", "border-left-color": "#717069"}, "mixins": [], "__type": "RuleSet"}, "IfYOPUP0COEB": {"frame": {"__ref": "NIkCD4s3Rrvt"}, "cellKey": {"__ref": "AeF-ZcfsmqiI"}, "__type": "ArenaFrameCell"}, "pL7I5HbTL7Er": {"frame": {"__ref": "B5GcB6EYijSG"}, "cellKey": {"__ref": "mZ61Fzq6V8wx"}, "__type": "ArenaFrameCell"}, "xSZ8dyL6HwY4": {"frame": {"__ref": "-o8u9-Cqpf3x"}, "cellKey": {"__ref": "vE-s9RcGBOxA"}, "__type": "ArenaFrameCell"}, "emZwwI7adsl6": {"frame": {"__ref": "P5MNpgNsOzzP"}, "cellKey": {"__ref": "QjPLchsmNmoh"}, "__type": "ArenaFrameCell"}, "Jj7NjwcQKnHz": {"frame": {"__ref": "gsqw215Rlvxc"}, "cellKey": {"__ref": "SwGoFElUiDfB"}, "__type": "ArenaFrameCell"}, "qckFzbIbLhbp": {"frame": {"__ref": "fkFB04Lg9VfW"}, "cellKey": {"__ref": "9pZrwTMscDML"}, "__type": "ArenaFrameCell"}, "anQq_MHjYR0b": {"frame": {"__ref": "51Agq0-18M9l"}, "cellKey": {"__ref": "4ocP4fyAdtx5"}, "__type": "ArenaFrameCell"}, "Pt1T4byBGTFO": {"frame": {"__ref": "a2qaFLrZ2p0y"}, "cellKey": {"__ref": "Hom0u9iJgkB-"}, "__type": "ArenaFrameCell"}, "93E9sJtt_MuO": {"tpl": [{"__ref": "727-aP5i3s9c"}], "__type": "VirtualRenderExpr"}, "2_Ej9uUONVra": {"tpl": [{"__ref": "EKaTltUHfGZl"}], "__type": "VirtualRenderExpr"}, "Bwtc26yBcqRY": {"name": "text", "__type": "Text"}, "b6pmQCUs0ZOG": {"name": "any", "__type": "AnyType"}, "3hxQdlDm8TN9": {"name": "any", "__type": "AnyType"}, "4nyY_v8rg6md": {"name": "any", "__type": "AnyType"}, "jiek_Vaa7KiW": {"name": "any", "__type": "AnyType"}, "1_6qGZ-iVnJj": {"name": "any", "__type": "AnyType"}, "o46cg5PN_7NM": {"name": "text", "__type": "Text"}, "WLEnMbvUh_tW": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "XBrnyKjz855v", "parent": {"__ref": "AEVlDo3heR1k"}, "locked": null, "vsettings": [{"__ref": "Xbrl4-ieRCpW"}], "__type": "TplTag"}, "sFnQloGxYVKd": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {}, "rs": {"__ref": "2ed7OM5wcN9T"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tmJdD-miYfaR": {"variants": [{"__ref": "SwGoFElUiDfB"}], "args": [], "attrs": {}, "rs": {"__ref": "tgcIw0r0_FGx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2YfaHtliSos_": {"variants": [{"__ref": "Hom0u9iJgkB-"}], "args": [], "attrs": {}, "rs": {"__ref": "v_q9X7nj5ZkC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7enbYMBt5BQc": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-right": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "_DHT6bAyzUSn": {"code": "true", "fallback": null, "__type": "CustomCode"}, "KnQU81tFc4qT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "UExwvFwj8Huv": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "tP5FhNkS8U9v": {"code": "true", "fallback": null, "__type": "CustomCode"}, "W_HHTfYteWoK": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RhEpTwNIm0kp": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60et-wWp3Oc1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "j8EKFEMXGfCs": {"variable": {"__ref": "YvkWXVOaoRIB"}, "__type": "VarRef"}, "ib8vI4uE5Xcc": {"variable": {"__ref": "UVSU1SZW_i3y"}, "__type": "VarRef"}, "DCPZqsMOCpvc": {"variable": {"__ref": "rfQNZPTKOWMe"}, "__type": "VarRef"}, "QtklMaXfk5ei": {"variable": {"__ref": "3zirtup1EdB5"}, "__type": "VarRef"}, "feeq-QQ4gsRO": {"variable": {"__ref": "06s5JjBvJY6n"}, "__type": "VarRef"}, "dPbLBT6FcTxG": {"variable": {"__ref": "fbijpZLA2YTg"}, "__type": "VarRef"}, "sDqQmyXVIogI": {"variable": {"__ref": "ZTNRLI-9IJVs"}, "__type": "VarRef"}, "RkQkw9BIzUq1": {"values": {"width": "stretch", "left": "auto", "top": "auto", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "bKIXx2XThbLG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ahE2caCx3m4G": {"code": "true", "fallback": null, "__type": "CustomCode"}, "EWLy1mYXQHsg": {"values": {"cursor": "not-allowed"}, "mixins": [], "__type": "RuleSet"}, "EXHcKyXcuC7p": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "1Cg5ykT3HMm9": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LHwp5-4CQKo7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xx-2ctnddsZM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9M353BxnAx8c": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MUNEqc5rQJz0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "n00W6ONDquBI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "JnJUJ06aouQU": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "FjeozSoKCMoY": {"values": {"color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "wiLJDtjqgdfn": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "RNFlXT_8SJHC", "parent": {"__ref": "H-7QgYI-VTx-"}, "locked": null, "vsettings": [{"__ref": "BZBbbBX9zF2M"}], "__type": "TplTag"}, "2N3rybWyu-AU": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {}, "rs": {"__ref": "adKZlWEWRw33"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bieLrKNma4Rp": {"variants": [{"__ref": "9pZrwTMscDML"}], "args": [], "attrs": {}, "rs": {"__ref": "rYEVanKKe9rl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "griHAwYHKd6l": {"variants": [{"__ref": "Hom0u9iJgkB-"}], "args": [], "attrs": {}, "rs": {"__ref": "qSB1YUuHe1vH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "D2soKsPMmLlW": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-left": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "F48-T85s88-B": {"code": "true", "fallback": null, "__type": "CustomCode"}, "xpuTDnDO4B4C": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "-VnVsMZd2DX2": {"code": "true", "fallback": null, "__type": "CustomCode"}, "rgS1fwXSXI_S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NIkCD4s3Rrvt": {"uuid": "AUDIGcx4WE22", "width": 1180, "height": 540, "container": {"__ref": "yE6YKGNxDm11"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "AeF-ZcfsmqiI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "B5GcB6EYijSG": {"uuid": "IGeclPzyQ1vT", "width": 1180, "height": 540, "container": {"__ref": "DaToKlJ_nHDK"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "mZ61Fzq6V8wx"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "-o8u9-Cqpf3x": {"uuid": "viaQJ6nSTyNE", "width": 1180, "height": 540, "container": {"__ref": "lNdoGTwNiuHF"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "vE-s9RcGBOxA"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "P5MNpgNsOzzP": {"uuid": "8WzmPsmsHyNW", "width": 1180, "height": 540, "container": {"__ref": "1kWWEYzXHW-_"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "QjPLchsmNmoh"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "gsqw215Rlvxc": {"uuid": "7t3hHBwJLA3p", "width": 1180, "height": 540, "container": {"__ref": "8-k8p17DVqOi"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "SwGoFElUiDfB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "fkFB04Lg9VfW": {"uuid": "QBJRZ2HgHHvJ", "width": 1180, "height": 540, "container": {"__ref": "s0l2x7T1sJEr"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "9pZrwTMscDML"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "51Agq0-18M9l": {"uuid": "zkn1Dt62uYCK", "width": 1180, "height": 540, "container": {"__ref": "ImmWwXnJ1P_3"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "4ocP4fyAdtx5"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "a2qaFLrZ2p0y": {"uuid": "_090llks-MWV", "width": 1180, "height": 540, "container": {"__ref": "R6xxUA5BocV8"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Hom0u9iJgkB-"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "727-aP5i3s9c": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "ADC7YJr0cULe", "parent": {"__ref": "cjMIn7vVuo6Z"}, "locked": null, "vsettings": [{"__ref": "26sFcygkoSUV"}], "__type": "TplTag"}, "EKaTltUHfGZl": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "9GSI5M1k3h0m", "parent": {"__ref": "cjMIn7vVuo6Z"}, "locked": null, "vsettings": [{"__ref": "JyMIdLA-FItB"}], "__type": "TplTag"}, "Xbrl4-ieRCpW": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {"outerHTML": {"__ref": "7V9LWgQJtlQS"}}, "rs": {"__ref": "2Q0sNVg-PNa9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2ed7OM5wcN9T": {"values": {}, "mixins": [], "__type": "RuleSet"}, "tgcIw0r0_FGx": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "v_q9X7nj5ZkC": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "BZBbbBX9zF2M": {"variants": [{"__ref": "AeF-ZcfsmqiI"}], "args": [], "attrs": {"outerHTML": {"__ref": "rTedpwS7IvoX"}}, "rs": {"__ref": "aq3MiFdF1e-O"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "adKZlWEWRw33": {"values": {}, "mixins": [], "__type": "RuleSet"}, "rYEVanKKe9rl": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "qSB1YUuHe1vH": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "yE6YKGNxDm11": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "x6cENL6O3Dcp", "parent": null, "locked": null, "vsettings": [{"__ref": "Tr9U-RzSlNrx"}], "__type": "TplComponent"}, "DaToKlJ_nHDK": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "O0KC0E0DkKl9", "parent": null, "locked": null, "vsettings": [{"__ref": "Jr2b7vMSM2K4"}], "__type": "TplComponent"}, "lNdoGTwNiuHF": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "ECim4BD3QQ2a", "parent": null, "locked": null, "vsettings": [{"__ref": "KKiX-X3zAFlI"}], "__type": "TplComponent"}, "1kWWEYzXHW-_": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "VZ-8DWepDCFA", "parent": null, "locked": null, "vsettings": [{"__ref": "cc4-a_pyvnNl"}], "__type": "TplComponent"}, "8-k8p17DVqOi": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "CxiJNwipyQvi", "parent": null, "locked": null, "vsettings": [{"__ref": "0r75Rm1oOWn3"}], "__type": "TplComponent"}, "s0l2x7T1sJEr": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "hK9xZ8nnGmoV", "parent": null, "locked": null, "vsettings": [{"__ref": "qtSNX3gI3Z1S"}], "__type": "TplComponent"}, "ImmWwXnJ1P_3": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "YECYoDVPp7-t", "parent": null, "locked": null, "vsettings": [{"__ref": "yEqbPGQCVIOC"}], "__type": "TplComponent"}, "R6xxUA5BocV8": {"name": null, "component": {"__ref": "SxIYAk_gqRFL"}, "uuid": "ZB3TEL0nhgjU", "parent": null, "locked": null, "vsettings": [{"__ref": "02UJ2XMFiv-t"}], "__type": "TplComponent"}, "26sFcygkoSUV": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {"outerHTML": {"__ref": "S0YxqexVLnSU"}}, "rs": {"__ref": "w2yGbJXWzO67"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JyMIdLA-FItB": {"variants": [{"__ref": "8fMTSV9zRIab"}], "args": [], "attrs": {"outerHTML": {"__ref": "v43CtRvxElV5"}}, "rs": {"__ref": "1BeaqFj0I6i2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7V9LWgQJtlQS": {"asset": {"__ref": "NyHtd6BFEt5h"}, "__type": "ImageAssetRef"}, "2Q0sNVg-PNa9": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "rTedpwS7IvoX": {"asset": {"__ref": "QRd8r00wuTBP"}, "__type": "ImageAssetRef"}, "aq3MiFdF1e-O": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "Tr9U-RzSlNrx": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "Fi9yhCLa9CIY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Jr2b7vMSM2K4": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "1V0VFgks4-QU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KKiX-X3zAFlI": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "60IKpeBgqHf2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cc4-a_pyvnNl": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "YTyBTj2ISybN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0r75Rm1oOWn3": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "tHVPuK4I8DnY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qtSNX3gI3Z1S": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "u5xA6EKo0x4r"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yEqbPGQCVIOC": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "OYBKMNz_HTTW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "02UJ2XMFiv-t": {"variants": [{"__ref": "VIosIrbQRYdm"}], "args": [], "attrs": {}, "rs": {"__ref": "722MeDSFvoCf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "S0YxqexVLnSU": {"asset": {"__ref": "NyHtd6BFEt5h"}, "__type": "ImageAssetRef"}, "w2yGbJXWzO67": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "v43CtRvxElV5": {"asset": {"__ref": "QRd8r00wuTBP"}, "__type": "ImageAssetRef"}, "1BeaqFj0I6i2": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "Fi9yhCLa9CIY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "1V0VFgks4-QU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "60IKpeBgqHf2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "YTyBTj2ISybN": {"values": {}, "mixins": [], "__type": "RuleSet"}, "tHVPuK4I8DnY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "u5xA6EKo0x4r": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OYBKMNz_HTTW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "722MeDSFvoCf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cfkVonQRMpD6": {"param": {"__ref": "82WAfWlbr6XD"}, "expr": {"__ref": "zcY9Kki437w3"}, "__type": "Arg"}, "zcY9Kki437w3": {"interactions": [{"__ref": "6yvWk9NghHoF"}], "__type": "EventHandler"}, "6yvWk9NghHoF": {"interactionName": "Update interval", "actionName": "updateVariable", "args": [{"__ref": "29J_nYEbok94"}, {"__ref": "ePdAXcxHzCrw"}], "condExpr": null, "conditionalMode": "always", "uuid": "6nm1OpymA3-7", "parent": {"__ref": "zcY9Kki437w3"}, "__type": "Interaction"}, "pE9TD64c6fXz": {"code": "0", "fallback": null, "__type": "CustomCode"}, "l6S6hzREslsO": {"param": {"__ref": "_rc4bseF3ywA"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "aF6PWOvrgEb0"}, "tplNode": null, "implicitState": null, "__type": "State"}, "_rc4bseF3ywA": {"type": {"__ref": "OSHxWDcGVGB4"}, "state": {"__ref": "l6S6hzREslsO"}, "variable": {"__ref": "pwjn_CkRBHLy"}, "uuid": "uNuMr0J4sE9J", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "kTT9tYpeH-U3"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "aF6PWOvrgEb0": {"type": {"__ref": "FcVd0yAnzceq"}, "state": {"__ref": "l6S6hzREslsO"}, "variable": {"__ref": "dLofGUNAxMcu"}, "uuid": "J2-n5NoqVMDS", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "pwjn_CkRBHLy": {"name": "interval", "uuid": "SHHOozFzYWee", "__type": "Var"}, "FcVd0yAnzceq": {"name": "func", "params": [{"__ref": "nUgL9g9Ty35g"}], "__type": "FunctionType"}, "dLofGUNAxMcu": {"name": "On interval change", "uuid": "DmgQKeXyCs0k", "__type": "Var"}, "nUgL9g9Ty35g": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "D0eRsEx1MnJA"}, "__type": "ArgType"}, "D0eRsEx1MnJA": {"name": "text", "__type": "Text"}, "OSHxWDcGVGB4": {"name": "num", "__type": "<PERSON><PERSON>"}, "kTT9tYpeH-U3": {"code": "1", "fallback": null, "__type": "CustomCode"}, "eh7B5EYZRfWV": {"param": {"__xref": {"uuid": "a3a6a4ab-6ef7-466e-8589-a26854d0253c", "iid": "OO-7SJp1Y0tQ"}}, "expr": {"__ref": "mLWebkrUPclH"}, "__type": "Arg"}, "mLWebkrUPclH": {"path": ["$state", "interval"], "fallback": {"__ref": "LQSbBZe6bgVj"}, "__type": "ObjectPath"}, "LQSbBZe6bgVj": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "2H_ZkXm2T_Kp": {"param": {"__xref": {"uuid": "a3a6a4ab-6ef7-466e-8589-a26854d0253c", "iid": "02FCmET1mnT7"}}, "expr": {"__ref": "8cbEH-V4_HQg"}, "__type": "Arg"}, "8cbEH-V4_HQg": {"interactions": [{"__ref": "eDg0CykSQ9zW"}], "__type": "EventHandler"}, "eDg0CykSQ9zW": {"interactionName": "Update count", "actionName": "updateVariable", "args": [{"__ref": "IpLGrOE0vLVM"}, {"__ref": "PO0FuS0Puw1y"}], "condExpr": null, "conditionalMode": "always", "uuid": "l55cLNaugrFm", "parent": {"__ref": "8cbEH-V4_HQg"}, "__type": "Interaction"}, "il2vqXNjh-Fd": {"path": ["$state", "count"], "fallback": null, "__type": "ObjectPath"}, "IpLGrOE0vLVM": {"name": "variable", "expr": {"__ref": "il2vqXNjh-Fd"}, "__type": "NameArg"}, "PO0FuS0Puw1y": {"name": "operation", "expr": {"__ref": "PXjNcJ6YopnS"}, "__type": "NameArg"}, "PXjNcJ6YopnS": {"code": "2", "fallback": null, "__type": "CustomCode"}, "29J_nYEbok94": {"name": "variable", "expr": {"__ref": "Pi4FVt1qctLx"}, "__type": "NameArg"}, "ePdAXcxHzCrw": {"name": "operation", "expr": {"__ref": "pE9TD64c6fXz"}, "__type": "NameArg"}, "Pi4FVt1qctLx": {"path": ["$state", "interval"], "fallback": null, "__type": "ObjectPath"}, "fFReiHjoO6_V": {"param": {"__ref": "TREKFE1pKtOl"}, "expr": {"__ref": "1gC7SVhku9u3"}, "__type": "Arg"}, "1sX6mExR1oCR": {"path": ["$state", "interval"], "fallback": {"__ref": "qDw_65WWANkK"}, "__type": "ObjectPath"}, "qDw_65WWANkK": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "1gC7SVhku9u3": {"text": ["", {"__ref": "1sX6mExR1oCR"}, ""], "__type": "TemplatedString"}, "eijBcVWYGtVg": {"text": ["count-text"], "__type": "TemplatedString"}, "7h1S-pOZDB0b": {"param": {"__ref": "bZVOMjId1bbz"}, "expr": {"__ref": "ppmsVxHk1e_-"}, "__type": "Arg"}, "ppmsVxHk1e_-": {"text": ["interval"], "__type": "TemplatedString"}}, "deps": ["a3a6a4ab-6ef7-466e-8589-a26854d0253c"], "version": "246-add-component-updated-at"}]]