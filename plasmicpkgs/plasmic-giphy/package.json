{"name": "@plasmicpkgs/plasmic-giphy", "version": "0.0.54", "description": "Plasmic Giphy components.", "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/plasmic-giphy.esm.js", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"react": ">=16"}, "size-limit": [{"path": "dist/plasmic-giphy.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/plasmic-giphy.esm.js", "limit": "10 KB"}], "devDependencies": {"@plasmicapp/host": "1.0.222", "@size-limit/preset-small-lib": "^7.0.8", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "husky": "^7.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^7.0.8", "tsdx": "^0.14.1", "tslib": "^2.3.1"}, "dependencies": {"@giphy/js-fetch-api": "^5.1.0", "@giphy/js-types": "^4.4.0", "@giphy/react-components": "^9.0.1", "change-case": "^4.1.2"}}