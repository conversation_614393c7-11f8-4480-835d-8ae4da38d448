{"name": "@plasmicpkgs/react-quill", "version": "1.0.85", "description": "Plasmic registration call for react-quill WYSIWYG Editor", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/react-quill.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/react-quill.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/react-quill.esm.js", "limit": "10 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.222", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "publishConfig": {"access": "public"}, "dependencies": {"react-quill": "^2.0.0"}}