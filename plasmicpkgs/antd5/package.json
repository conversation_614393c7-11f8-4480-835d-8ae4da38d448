{"name": "@plasmicpkgs/antd5", "version": "0.0.295", "description": "Plasmic registration calls for antd components", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/antd.esm.js", "nx": {"targets": {"build": {"inputs": ["{projectRoot}/**/*", "!{projectRoot}/**/dist/**/*", "!{projectRoot}/skinny/**/*"], "outputs": ["{projectRoot}/**/dist/**/*", "{projectRoot}/skinny/**/*"]}}}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/antd.esm.js"}, "./skinny/*": {"types": "./skinny/*.d.ts", "require": "./skinny/*.cjs.js", "import": "./skinny/*.esm.js"}}, "files": ["dist", "skinny"], "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}, {"path": "dist/antd.esm.js", "limit": "10 KB"}], "scripts": {"build": "rollup -c rollup.config.mjs && yarn tsc --emitDeclarationOnly --declaration src/index.ts --incremental --tsBuildInfoFile ./dist/.tsbuildinfo  --skipLibCheck --jsx react --lib dom,esnext --esModuleInterop --strict --outDir ./dist/ && cp ./dist/*.d.ts skinny/ && rm skinny/index.d.ts", "prepublishOnly": "npm run build", "clean": "rm -rf dist/ skinny/*.ts skinny/*.map skinny/*.js", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build", "test-storybook": "test-storybook", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test"}, "dependencies": {"antd": "^5.12.7", "classnames": "^2.3.2", "dayjs": "^1.11.10", "fast-deep-equal": "^3.1.3", "lodash": "^4.17.21"}, "devDependencies": {"@babel/preset-env": "^7.22.15", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.22.15", "@plasmicapp/data-sources": "0.1.186", "@plasmicapp/host": "1.0.222", "@plasmicapp/query": "0.1.79", "@plasmicapp/react-web": "0.2.393", "@rollup/plugin-commonjs": "^11.0.0", "@rollup/plugin-json": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "@types/lodash": "^4.14.200", "@types/node": "^14.0.26", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "glob": "^8.1.0", "rc-input-number": "^8.1.0", "rc-menu": "^9.12.2", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.10.1", "rollup-plugin-dts": "^5.0.0", "rollup-plugin-esbuild": "^5.0.0", "rollup-plugin-replace-imports": "^1.0.0"}, "peerDependencies": {"@plasmicapp/data-sources": ">=0.1.0", "@plasmicapp/host": ">=1.0.0", "@plasmicapp/query": ">=0.1.0", "react": ">=16.8.0"}, "publishConfig": {"access": "public"}}