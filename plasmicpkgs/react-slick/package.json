{"name": "@plasmicpkgs/react-slick", "version": "0.0.243", "description": "Plasmic registration call for the React Slick Slider component", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/react-slick.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/react-slick.cjs.production.min.js", "limit": "20 KB"}, {"path": "dist/react-slick.esm.js", "limit": "20 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.222", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "@types/react-slick": "^0.23.7", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"@seznam/compose-react-refs": "^1.0.6", "react-slick": "^0.28.1", "slick-carousel": "^1.8.1"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}