{"name": "@plasmicpkgs/plasmic-link-preview", "version": "1.0.119", "description": "A React component that renders beautiful, fully-customizable link previews.", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/plasmic-link-preview.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/plasmic-link-preview.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/plasmic-link-preview.esm.js", "limit": "10 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/data-sources": "0.1.141", "@plasmicapp/host": "1.0.222", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "peerDependencies": {"@plasmicapp/data-sources": ">=0.1.52", "@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "publishConfig": {"access": "public"}, "dependencies": {"node-html-parser": "^6.1.11"}}