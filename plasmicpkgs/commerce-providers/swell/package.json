{"name": "@plasmicpkgs/commerce-swell", "version": "0.0.229", "description": "Plasmic registration calls for swell commerce provider", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/commerce-swell.esm.js", "files": ["dist"], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.222", "@size-limit/preset-small-lib": "^4.11.0", "@types/debounce": "^1.2.3", "@types/js-cookie": "^3.0.1", "@types/node": "^14.0.26", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^4.11.0", "tsdx": "^0.14.1"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0"}, "dependencies": {"@plasmicpkgs/commerce": "0.0.220", "@types/react": "^18.0.27", "debounce": "^1.2.1", "js-cookie": "^3.0.5", "swell-js": "^3.13.0"}}