{"name": "@plasmicpkgs/commerce-local", "version": "0.0.220", "description": "Plasmic registration calls for local provider components", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/commerce-local.esm.js", "files": ["dist"], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "tsdx test", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.222", "@size-limit/preset-small-lib": "^4.11.0", "@types/js-cookie": "^3.0.1", "@types/node": "^14.0.26", "js-cookie": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^4.11.0", "tsdx": "^0.14.1"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0"}, "dependencies": {"@plasmicpkgs/commerce": "0.0.220", "@types/react": "^18.0.27"}}