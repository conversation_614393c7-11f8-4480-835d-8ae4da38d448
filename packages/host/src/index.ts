import * as PlasmicQuery from "@plasmicapp/query";
import * as React from "react";
import * as ReactDOM from "react-dom";
import * as ReactDOMClient from "react-dom/client";
import * as jsxDevRuntime from "react/jsx-dev-runtime";
import * as jsxRuntime from "react/jsx-runtime";
import { registerRenderErrorListener, setPlasmicRootNode } from "./canvas-host";
import * as hostModule from "./exports";
import { setRepeatedElementFn } from "./repeatedElement";
// version.ts is automatically generated by `yarn build` and not committed.
import { hostVersion } from "./version";

// All exports must come from "./exports"
export * from "./exports";

const root = globalThis as any;

if (root.__Sub == null) {
  root.__Sub = {
    React,
    ReactDOM,
    ReactDOMClient,
    jsxRuntime,
    jsxDevRuntime,
    PlasmicQuery,
    hostModule,
    hostVersion,
    hostUtils: {
      setPlasmicRootNode,
      registerRenderErrorListener,
      setRepeatedElementFn,
    },

    // TODO: Remove below fields for v2
    // For backwards compatibility:
    setPlasmicRootNode,
    registerRenderErrorListener,
    setRepeatedElementFn,
    ...hostModule,
  };
} else if (root.__Sub.hostVersion !== hostVersion) {
  console.warn(
    `Encountered likely duplicate host version: ${root.__Sub.hostVersion} vs ${hostVersion}`
  );
  root.__Sub.duplicateHostVersions = root.__Sub.duplicateHostVersions ?? [];
  root.__Sub.duplicateHostVersions.push(hostVersion);
}
