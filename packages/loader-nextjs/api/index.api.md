## API Report File for "@plasmicapp/loader-nextjs"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

/// <reference types="node" />

import { CodeComponentMeta } from '@plasmicapp/loader-react';
import { ComponentLookupSpec } from '@plasmicapp/loader-react';
import { ComponentMeta } from '@plasmicapp/loader-react/react-server-conditional';
import { ComponentRenderData } from '@plasmicapp/loader-react/react-server-conditional';
import { CustomFunctionMeta } from '@plasmicapp/loader-react';
import { DataCtxReader } from '@plasmicapp/loader-react';
import { DataProvider } from '@plasmicapp/loader-react';
import { FetchComponentDataOpts as FetchComponentDataOpts_2 } from '@plasmicapp/loader-react';
import { GlobalActionsContext } from '@plasmicapp/loader-react';
import { GlobalActionsProvider } from '@plasmicapp/loader-react';
import { IncomingMessage } from 'http';
import { InitOptions } from '@plasmicapp/loader-react/react-server-conditional';
import { InternalPlasmicComponentLoader } from '@plasmicapp/loader-react';
import { PageMeta } from '@plasmicapp/loader-react/react-server-conditional';
import { PageMetadata } from '@plasmicapp/loader-react/react-server-conditional';
import { PageParamsProvider } from '@plasmicapp/loader-react';
import { PlasmicCanvasContext } from '@plasmicapp/loader-react';
import { PlasmicCanvasHost } from '@plasmicapp/loader-react';
import { PlasmicComponent } from '@plasmicapp/loader-react';
import { PlasmicComponentLoader } from '@plasmicapp/loader-react';
import { plasmicPrepass } from '@plasmicapp/loader-react';
import { PlasmicRootProvider as PlasmicRootProvider_2 } from '@plasmicapp/loader-react';
import { PlasmicTranslator } from '@plasmicapp/loader-react';
import { PlasmicTranslatorContext } from '@plasmicapp/loader-react';
import { PropType } from '@plasmicapp/loader-react';
import * as React_2 from 'react';
import { repeatedElement } from '@plasmicapp/loader-react';
import { ServerResponse } from 'http';
import { TokenRegistration } from '@plasmicapp/loader-react';
import { useDataEnv } from '@plasmicapp/loader-react';
import { usePlasmicCanvasComponentInfo } from '@plasmicapp/loader-react';
import { usePlasmicCanvasContext } from '@plasmicapp/loader-react';
import { usePlasmicComponent } from '@plasmicapp/loader-react';
import { usePlasmicQueryData } from '@plasmicapp/loader-react';
import { useSelector } from '@plasmicapp/loader-react';
import { useSelectors } from '@plasmicapp/loader-react';

export { CodeComponentMeta }

export { ComponentMeta }

export { ComponentRenderData }

export { CustomFunctionMeta }

export { DataCtxReader }

export { DataProvider }

// @public
export function extractPlasmicQueryData(element: React_2.ReactElement): Promise<Record<string, any>>;

// @public (undocumented)
export interface FetchComponentDataOpts extends FetchComponentDataOpts_2 {
    deferChunks?: boolean;
}

export { GlobalActionsContext }

export { GlobalActionsProvider }

export { InitOptions }

// @public (undocumented)
export function initPlasmicLoader(opts: NextInitOptions): NextJsPlasmicComponentLoader;

// @public (undocumented)
export interface NextInitOptions extends InitOptions {
    nextNavigation?: {
        notFound: unknown;
        redirect: unknown;
        useParams: unknown;
        usePathname: unknown;
        useRouter: unknown;
        useSearchParams: unknown;
    };
}

// @public (undocumented)
export class NextJsPlasmicComponentLoader extends PlasmicComponentLoader {
    constructor(internal: InternalPlasmicComponentLoader);
    // (undocumented)
    fetchComponentData(...specs: ComponentLookupSpec[]): Promise<ComponentRenderData>;
    // (undocumented)
    fetchComponentData(specs: ComponentLookupSpec[], opts?: FetchComponentDataOpts): Promise<ComponentRenderData>;
    // (undocumented)
    getActiveVariation(opts: {
        req?: ServerRequest;
        res?: ServerResponse;
        known?: Record<string, string>;
        traits: Record<string, string | number | boolean>;
    }): Promise<Record<string, string>>;
    // (undocumented)
    maybeFetchComponentData(specs: ComponentLookupSpec[], opts?: FetchComponentDataOpts): Promise<ComponentRenderData | null>;
    // (undocumented)
    maybeFetchComponentData(...specs: ComponentLookupSpec[]): Promise<ComponentRenderData | null>;
}

export { PageMeta }

export { PageMetadata }

export { PageParamsProvider }

export { PlasmicCanvasContext }

export { PlasmicCanvasHost }

export { PlasmicComponent }

export { plasmicPrepass }

// @public (undocumented)
export function PlasmicRootProvider(props: Omit<React_2.ComponentProps<typeof PlasmicRootProvider_2>, "Head"> & {
    skipChunks?: boolean;
}): React_2.JSX.Element;

export { PlasmicTranslator }

export { PlasmicTranslatorContext }

export { PropType }

export { repeatedElement }

export { TokenRegistration }

export { useDataEnv }

export { usePlasmicCanvasComponentInfo }

export { usePlasmicCanvasContext }

export { usePlasmicComponent }

export { usePlasmicQueryData }

export { useSelector }

export { useSelectors }

// (No @packageDocumentation comment for this package)

```
