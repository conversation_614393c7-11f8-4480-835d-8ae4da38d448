## API Report File for "@plasmicapp/loader-nextjs"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

/// <reference types="node" />

import { ComponentMeta } from '@plasmicapp/loader-react/react-server-conditional';
import { ComponentRenderData } from '@plasmicapp/loader-react/react-server-conditional';
import type { IncomingMessage } from 'http';
import { InitOptions } from '@plasmicapp/loader-react/react-server-conditional';
import { InternalPlasmicComponentLoader } from '@plasmicapp/loader-react/react-server';
import { PageMeta } from '@plasmicapp/loader-react/react-server-conditional';
import { PageMetadata } from '@plasmicapp/loader-react/react-server-conditional';
import { PlasmicComponentLoader } from '@plasmicapp/loader-react/react-server';
import type { ServerResponse } from 'http';

export { ComponentMeta }

export { ComponentRenderData }

export { InitOptions }

// @public (undocumented)
export function initPlasmicLoader(opts: NextInitOptions): NextJsPlasmicComponentLoader;

// @public (undocumented)
export interface NextInitOptions extends InitOptions {
    nextNavigation?: {
        notFound: unknown;
        redirect: unknown;
        useParams: unknown;
        usePathname: unknown;
        useRouter: unknown;
        useSearchParams: unknown;
    };
}

// @public (undocumented)
export class NextJsPlasmicComponentLoader extends PlasmicComponentLoader {
    constructor(internal: InternalPlasmicComponentLoader);
    // (undocumented)
    getActiveVariation(opts: {
        req?: ServerRequest;
        res?: ServerResponse;
        known?: Record<string, string>;
        traits: Record<string, string | number | boolean>;
    }): Promise<Record<string, string>>;
}

export { PageMeta }

export { PageMetadata }

// (No @packageDocumentation comment for this package)

```
