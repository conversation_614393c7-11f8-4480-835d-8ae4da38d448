{"name": "@plasmicapp/loader-react", "version": "2.0.0-test.0", "description": "Plasmic loader SDK for React", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/plasmicapp/plasmic.git", "directory": "packages/loader-react"}, "types": "./dist/index.d.ts", "main": "./dist/index.js", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}, "./react-server": {"types": "./dist/react-server.d.ts", "import": "./dist/react-server.esm.js", "require": "./dist/react-server.js"}, "./react-server-conditional": {"react-server": {"types": "./dist/react-server.d.ts", "import": "./dist/react-server.esm.js", "require": "./dist/react-server.js"}, "default": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}}, "files": ["dist", "index.d.ts", "react-server.d.ts", "react-server-conditional.d.ts"], "engines": {"node": ">=12"}, "scripts": {"build": "yarn build:types && yarn build:index && yarn build:react-server", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.ts --use-client", "build:react-server": "node ../../build.mjs ./src/react-server.ts", "yalcp": "yalc publish --push", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test", "coverage": "TEST_CWD=`pwd` yarn --cwd=../.. test --coverage --passWithNoTests", "lint": "eslint", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh", "size": "size-limit", "analyze": "size-limit --why"}, "dependencies": {"@plasmicapp/data-sources-context": "0.1.21", "@plasmicapp/host": "2.0.0-test.0", "@plasmicapp/loader-core": "1.0.137", "@plasmicapp/loader-fetcher": "1.0.55", "@plasmicapp/loader-splits": "1.0.64", "@plasmicapp/prepass": "1.0.19", "@plasmicapp/query": "0.1.79", "pascalcase": "^1.0.0", "server-only": "0.0.1"}, "devDependencies": {"@types/node": "^22", "@types/pascalcase": "^1.0.0", "@types/react": "^18", "@types/react-dom": "^18", "react": "18.3.1", "react-dom": "18.3.1"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "size-limit": [{"path": "dist/index.js", "limit": "15 KB"}, {"path": "dist/react-server.js", "limit": "15 KB"}], "gitHead": "fa53f7d79f0e26d8b061102fda0c06788da6f8a7"}