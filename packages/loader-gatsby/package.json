{"name": "@plasmicapp/loader-gatsby", "version": "1.0.394", "types": "./dist/index.d.ts", "main": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./gatsby-node": {"types": "./dist/gatsby-node.d.ts", "default": "./dist/gatsby-node.js"}, "./gatsby-ssr": {"types": "./dist/gatsby-ssr.d.ts", "default": "./dist/gatsby-ssr.js"}, "./package.json": "./package.json"}, "license": "MIT", "files": ["dist", "gatsby-node.js", "gatsby-ssr.js"], "scripts": {"build": "yarn build:types && yarn build:index && yarn build:gatsby-node && yarn build:gatsby-ssr", "build:types": "yarn tsc", "build:index": "node ../../build.mjs src/index.ts --no-esm", "build:gatsby-node": "node ../../build.mjs src/gatsby-node.ts --no-esm", "build:gatsby-ssr": "node ../../build.mjs src/gatsby-ssr.ts --no-esm", "yalcp": "yalc publish --push", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh"}, "keywords": ["gatsby", "gatsby-plugin"], "size-limit": [{"path": "dist/index.js", "limit": "15 KB"}, {"path": "dist/gatsby-node.js", "limit": "15 KB"}, {"path": "dist/gatsby-ssr.js", "limit": "15 KB"}], "peerDependencies": {"gatsby": "^2.0.15 || ^3.0.0 || ^4.0.0 || ^5.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/react-dom": "^18.2.14", "gatsby": "^5.11.0"}, "dependencies": {"@plasmicapp/loader-core": "1.0.137", "@plasmicapp/loader-react": "1.0.391", "@plasmicapp/watcher": "1.0.84", "lodash": "^4.17.21"}}