name: analytics-rproxy CI

on:
  push:
    paths:
      - "platform/pnpm-*.yaml"
      - "platform/analytics-rproxy/**"

jobs:
  verify-analytics-rproxy:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        with:
          package_json_file: platform/analytics-rproxy/package.json
          run_install: false

      - uses: actions/setup-node@v4
        with:
          node-version: 18.19.0
          cache: pnpm
          cache-dependency-path: platform/pnpm-lock.yaml

      - working-directory: platform/analytics-rproxy
        run: pnpm install && pnpm run ci
